<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('currency_id')->constrained()->onDelete('cascade');
            $table->string('wallet_number')->unique();
            $table->decimal('balance', 15, 2)->default(0.00);
            $table->decimal('pending_balance', 15, 2)->default(0.00);
            $table->decimal('frozen_balance', 15, 2)->default(0.00);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_crypto_wallet')->default(false);
            $table->string('crypto_address')->nullable()->unique();
            $table->string('private_key_encrypted')->nullable();
            $table->string('public_key')->nullable();
            $table->string('qr_code_path')->nullable();
            $table->decimal('daily_limit', 15, 2)->default(10000);
            $table->decimal('monthly_limit', 15, 2)->default(100000);
            $table->decimal('total_received', 15, 2)->default(0.00);
            $table->decimal('total_sent', 15, 2)->default(0.00);
            $table->integer('transaction_count')->default(0);
            $table->timestamp('last_transaction_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->unique(['user_id', 'currency_id']);
            $table->index(['is_active', 'is_crypto_wallet']);
            $table->index('wallet_number');
            $table->index('crypto_address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallets');
    }
};
