// PWA functionality for Money Transfer App
class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isOnline = navigator.onLine;
        this.notificationPermission = 'default';
        this.serviceWorkerRegistration = null;
        
        this.init();
    }

    async init() {
        console.log('PWA Manager: Initializing...');
        
        // Register service worker
        await this.registerServiceWorker();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Check for app updates
        this.checkForUpdates();
        
        // Initialize notifications
        await this.initializeNotifications();
        
        // Setup offline detection
        this.setupOfflineDetection();
        
        // Initialize background sync
        this.initializeBackgroundSync();
        
        console.log('PWA Manager: Initialized successfully');
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });
                
                this.serviceWorkerRegistration = registration;
                console.log('Service Worker registered successfully:', registration.scope);
                
                // Listen for service worker updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });
                
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }

    setupEventListeners() {
        // Install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });

        // App installed
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.hideInstallButton();
            this.trackEvent('pwa_installed');
        });

        // Visibility change
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.syncWhenVisible();
            }
        });

        // Online/offline events
        window.addEventListener('online', () => {
            this.handleOnline();
        });

        window.addEventListener('offline', () => {
            this.handleOffline();
        });
    }

    showInstallButton() {
        const installButton = document.getElementById('install-button');
        if (installButton) {
            installButton.style.display = 'block';
            installButton.addEventListener('click', () => this.installApp());
        }
    }

    hideInstallButton() {
        const installButton = document.getElementById('install-button');
        if (installButton) {
            installButton.style.display = 'none';
        }
    }

    async installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('User accepted the install prompt');
                this.trackEvent('pwa_install_accepted');
            } else {
                console.log('User dismissed the install prompt');
                this.trackEvent('pwa_install_dismissed');
            }
            
            this.deferredPrompt = null;
        }
    }

    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'update-banner';
        updateBanner.innerHTML = `
            <div class="update-content">
                <span>تحديث جديد متاح</span>
                <button onclick="pwaManager.applyUpdate()" class="btn btn-primary btn-sm">تحديث</button>
                <button onclick="this.parentElement.parentElement.remove()" class="btn btn-secondary btn-sm">لاحقاً</button>
            </div>
        `;
        
        document.body.appendChild(updateBanner);
    }

    applyUpdate() {
        if (this.serviceWorkerRegistration && this.serviceWorkerRegistration.waiting) {
            this.serviceWorkerRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            window.location.reload();
        }
    }

    async checkForUpdates() {
        if (this.serviceWorkerRegistration) {
            try {
                await this.serviceWorkerRegistration.update();
                console.log('Checked for service worker updates');
            } catch (error) {
                console.error('Failed to check for updates:', error);
            }
        }
    }

    async initializeNotifications() {
        if ('Notification' in window) {
            this.notificationPermission = Notification.permission;
            
            if (this.notificationPermission === 'default') {
                this.showNotificationPermissionRequest();
            } else if (this.notificationPermission === 'granted') {
                await this.subscribeToPushNotifications();
            }
        }
    }

    showNotificationPermissionRequest() {
        const permissionBanner = document.createElement('div');
        permissionBanner.className = 'permission-banner';
        permissionBanner.innerHTML = `
            <div class="permission-content">
                <span>تفعيل الإشعارات للحصول على تحديثات فورية</span>
                <button onclick="pwaManager.requestNotificationPermission()" class="btn btn-primary btn-sm">تفعيل</button>
                <button onclick="this.parentElement.parentElement.remove()" class="btn btn-secondary btn-sm">لا شكراً</button>
            </div>
        `;
        
        document.body.appendChild(permissionBanner);
    }

    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            this.notificationPermission = permission;
            
            if (permission === 'granted') {
                await this.subscribeToPushNotifications();
                this.showNotification('تم تفعيل الإشعارات', 'ستحصل الآن على إشعارات فورية');
                document.querySelector('.permission-banner')?.remove();
            }
        } catch (error) {
            console.error('Failed to request notification permission:', error);
        }
    }

    async subscribeToPushNotifications() {
        if (this.serviceWorkerRegistration && 'pushManager' in this.serviceWorkerRegistration) {
            try {
                const subscription = await this.serviceWorkerRegistration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: this.urlBase64ToUint8Array(window.vapidPublicKey || '')
                });
                
                // Send subscription to server
                await this.sendSubscriptionToServer(subscription);
                console.log('Push notification subscription successful');
                
            } catch (error) {
                console.error('Failed to subscribe to push notifications:', error);
            }
        }
    }

    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/api/push-subscriptions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: JSON.stringify(subscription)
            });
            
            if (!response.ok) {
                throw new Error('Failed to send subscription to server');
            }
        } catch (error) {
            console.error('Error sending subscription to server:', error);
        }
    }

    showNotification(title, body, options = {}) {
        if (this.notificationPermission === 'granted') {
            const notification = new Notification(title, {
                body,
                icon: '/images/icons/icon-192x192.png',
                badge: '/images/icons/badge-72x72.png',
                ...options
            });
            
            notification.onclick = () => {
                window.focus();
                notification.close();
            };
        }
    }

    setupOfflineDetection() {
        this.updateOnlineStatus();
        
        // Update UI based on connection status
        const offlineIndicator = document.getElementById('offline-indicator');
        if (offlineIndicator) {
            offlineIndicator.style.display = this.isOnline ? 'none' : 'block';
        }
    }

    handleOnline() {
        this.isOnline = true;
        console.log('App is online');
        
        // Hide offline indicator
        const offlineIndicator = document.getElementById('offline-indicator');
        if (offlineIndicator) {
            offlineIndicator.style.display = 'none';
        }
        
        // Sync offline data
        this.syncOfflineData();
        
        // Show online notification
        this.showNotification('متصل', 'تم استعادة الاتصال بالإنترنت');
    }

    handleOffline() {
        this.isOnline = false;
        console.log('App is offline');
        
        // Show offline indicator
        const offlineIndicator = document.getElementById('offline-indicator');
        if (offlineIndicator) {
            offlineIndicator.style.display = 'block';
        }
        
        // Show offline notification
        this.showNotification('غير متصل', 'تم فقدان الاتصال بالإنترنت. بعض الميزات قد لا تعمل.');
    }

    updateOnlineStatus() {
        this.isOnline = navigator.onLine;
    }

    async syncOfflineData() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            try {
                await this.serviceWorkerRegistration.sync.register('background-sync-transactions');
                console.log('Background sync registered for transactions');
            } catch (error) {
                console.error('Background sync registration failed:', error);
            }
        }
    }

    syncWhenVisible() {
        if (this.isOnline) {
            this.syncOfflineData();
            this.checkForUpdates();
        }
    }

    initializeBackgroundSync() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            console.log('Background sync is supported');
            
            // Register periodic sync for exchange rates
            if ('periodicSync' in this.serviceWorkerRegistration) {
                this.serviceWorkerRegistration.periodicSync.register('exchange-rates-sync', {
                    minInterval: 24 * 60 * 60 * 1000 // 24 hours
                }).catch(error => {
                    console.error('Periodic sync registration failed:', error);
                });
            }
        }
    }

    // Utility functions
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    trackEvent(eventName, eventData = {}) {
        // Analytics tracking
        if (window.gtag) {
            window.gtag('event', eventName, eventData);
        }
        
        console.log('Event tracked:', eventName, eventData);
    }

    // Public API methods
    async cacheUrl(url) {
        if (this.serviceWorkerRegistration) {
            this.serviceWorkerRegistration.active.postMessage({
                type: 'CACHE_URLS',
                urls: [url]
            });
        }
    }

    async clearCache() {
        if (this.serviceWorkerRegistration) {
            this.serviceWorkerRegistration.active.postMessage({
                type: 'CLEAR_CACHE'
            });
        }
    }

    isInstalled() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone ||
               document.referrer.includes('android-app://');
    }

    getInstallationStatus() {
        return {
            isInstalled: this.isInstalled(),
            canInstall: !!this.deferredPrompt,
            isOnline: this.isOnline,
            notificationPermission: this.notificationPermission
        };
    }
}

// Initialize PWA Manager
let pwaManager;

document.addEventListener('DOMContentLoaded', () => {
    pwaManager = new PWAManager();
    
    // Expose to global scope for debugging
    window.pwaManager = pwaManager;
});

// Add CSS for PWA UI elements
const pwaStyles = `
    .update-banner, .permission-banner {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #2563eb;
        color: white;
        padding: 12px;
        z-index: 9999;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .update-content, .permission-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1200px;
        margin: 0 auto;
        gap: 12px;
    }
    
    #offline-indicator {
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: #dc3545;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 1000;
        display: none;
    }
    
    #install-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #2563eb;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        display: none;
        z-index: 1000;
    }
    
    #install-button:hover {
        background: #1d4ed8;
    }
    
    @media (max-width: 768px) {
        .update-content, .permission-content {
            flex-direction: column;
            gap: 8px;
        }
        
        #install-button {
            bottom: 80px;
            right: 16px;
            left: 16px;
            width: auto;
        }
    }
`;

// Inject PWA styles
const styleSheet = document.createElement('style');
styleSheet.textContent = pwaStyles;
document.head.appendChild(styleSheet);
