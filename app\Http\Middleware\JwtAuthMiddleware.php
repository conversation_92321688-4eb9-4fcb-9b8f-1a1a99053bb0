<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\JwtService;
use App\Services\AuditLogService;
use Illuminate\Support\Facades\Log;

class JwtAuthMiddleware
{
    protected JwtService $jwtService;

    public function __construct(JwtService $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        try {
            // Extract token from request
            $token = $this->extractToken($request);

            if (!$token) {
                return $this->unauthorizedResponse('Token not provided');
            }

            // Validate token
            $validation = $this->jwtService->validateToken($token);

            if (!$validation['valid']) {
                return $this->unauthorizedResponse($validation['error'], $validation['code']);
            }

            $user = $validation['user'];
            $payload = $validation['payload'];
            $session = $validation['session'];

            // Check if user is active
            if ($user->status !== 'active') {
                return $this->unauthorizedResponse('User account is not active');
            }

            // Check permissions if specified
            if (!empty($permissions)) {
                $userPermissions = $payload['permissions'] ?? [];
                
                if (!$this->hasRequiredPermissions($userPermissions, $permissions)) {
                    return $this->forbiddenResponse('Insufficient permissions');
                }
            }

            // Check for suspicious activity
            $suspiciousFlags = $session->detectSuspiciousActivity();
            if (!empty($suspiciousFlags)) {
                $this->logSuspiciousActivity($user, $session, $suspiciousFlags, $request);
                
                // If high risk, require additional verification
                if (in_array('multiple_concurrent_sessions', $suspiciousFlags) || 
                    in_array('suspicious_location', $suspiciousFlags)) {
                    return $this->suspiciousActivityResponse($suspiciousFlags);
                }
            }

            // Set authenticated user and session in request
            $request->merge([
                'auth_user' => $user,
                'auth_session' => $session,
                'auth_payload' => $payload,
                'auth_token' => $token,
            ]);

            // Set user for Laravel's auth system
            auth()->setUser($user);

            // Log successful authentication
            $this->logSuccessfulAuth($user, $session, $request);

            return $next($request);

        } catch (\Exception $e) {
            Log::error('JWT Authentication error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return $this->unauthorizedResponse('Authentication failed');
        }
    }

    /**
     * Extract JWT token from request
     */
    private function extractToken(Request $request): ?string
    {
        // Check Authorization header
        $authHeader = $request->header('Authorization');
        if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }

        // Check query parameter (for WebSocket connections)
        if ($request->query('token')) {
            return $request->query('token');
        }

        // Check cookie (for web requests)
        if ($request->cookie('access_token')) {
            return $request->cookie('access_token');
        }

        return null;
    }

    /**
     * Check if user has required permissions
     */
    private function hasRequiredPermissions(array $userPermissions, array $requiredPermissions): bool
    {
        // If user has 'admin' permission, allow everything
        if (in_array('admin', $userPermissions)) {
            return true;
        }

        // Check if user has any of the required permissions
        foreach ($requiredPermissions as $permission) {
            if (in_array($permission, $userPermissions)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log successful authentication
     */
    private function logSuccessfulAuth($user, $session, Request $request): void
    {
        AuditLogService::logUserAction(
            'jwt_auth_success',
            'Successful JWT authentication',
            [
                'session_id' => $session->session_id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'endpoint' => $request->path(),
                'method' => $request->method(),
            ],
            $user->id
        );
    }

    /**
     * Log suspicious activity
     */
    private function logSuspiciousActivity($user, $session, array $flags, Request $request): void
    {
        AuditLogService::logUserAction(
            'jwt_suspicious_activity',
            'Suspicious activity detected during JWT authentication',
            [
                'session_id' => $session->session_id,
                'suspicious_flags' => $flags,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'endpoint' => $request->path(),
            ],
            $user->id
        );

        // Send alert to security team
        Log::warning('Suspicious JWT authentication activity', [
            'user_id' => $user->id,
            'session_id' => $session->session_id,
            'flags' => $flags,
            'ip' => $request->ip(),
        ]);
    }

    /**
     * Return unauthorized response
     */
    private function unauthorizedResponse(string $message, string $code = 'UNAUTHORIZED'): Response
    {
        return response()->json([
            'success' => false,
            'error' => $message,
            'code' => $code,
            'timestamp' => now()->toISOString(),
        ], 401);
    }

    /**
     * Return forbidden response
     */
    private function forbiddenResponse(string $message): Response
    {
        return response()->json([
            'success' => false,
            'error' => $message,
            'code' => 'FORBIDDEN',
            'timestamp' => now()->toISOString(),
        ], 403);
    }

    /**
     * Return suspicious activity response
     */
    private function suspiciousActivityResponse(array $flags): Response
    {
        return response()->json([
            'success' => false,
            'error' => 'Suspicious activity detected. Additional verification required.',
            'code' => 'SUSPICIOUS_ACTIVITY',
            'flags' => $flags,
            'timestamp' => now()->toISOString(),
        ], 403);
    }
}

/**
 * JWT Auth Middleware for API routes
 */
class JwtApiAuthMiddleware extends JwtAuthMiddleware
{
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        // Add API-specific headers
        $response = parent::handle($request, $next, ...$permissions);

        if ($response->getStatusCode() === 200) {
            // Add rate limit headers
            $user = $request->get('auth_user');
            if ($user) {
                $response->headers->set('X-RateLimit-Limit', '1000');
                $response->headers->set('X-RateLimit-Remaining', '999');
                $response->headers->set('X-RateLimit-Reset', now()->addHour()->timestamp);
            }
        }

        return $response;
    }
}

/**
 * JWT Auth Middleware for admin routes
 */
class JwtAdminAuthMiddleware extends JwtAuthMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        return parent::handle($request, $next, 'admin');
    }
}

/**
 * JWT Auth Middleware for verified users only
 */
class JwtVerifiedUserMiddleware extends JwtAuthMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = parent::handle($request, $next, 'verified_user');

        // Additional check for KYC completion
        if ($response->getStatusCode() === 200) {
            $user = $request->get('auth_user');
            if ($user && !$user->hasCompletedKyc()) {
                return response()->json([
                    'success' => false,
                    'error' => 'KYC verification required',
                    'code' => 'KYC_REQUIRED',
                    'kyc_status' => $user->kyc_status,
                    'completion_percentage' => $user->getKycCompletionPercentage(),
                ], 403);
            }
        }

        return $response;
    }
}
