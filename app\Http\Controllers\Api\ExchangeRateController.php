<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ExchangeRate;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class ExchangeRateController extends Controller
{
    /**
     * Get all active exchange rates.
     */
    public function index(Request $request): JsonResponse
    {
        $query = ExchangeRate::with(['fromCurrency', 'toCurrency'])
            ->where('is_active', true);

        // Filter by source
        if ($request->has('source')) {
            $query->where('source', $request->source);
        }

        // Filter by currency
        if ($request->has('currency')) {
            $currency = Currency::where('code', $request->currency)->first();
            if ($currency) {
                $query->where(function ($q) use ($currency) {
                    $q->where('from_currency_id', $currency->id)
                      ->orWhere('to_currency_id', $currency->id);
                });
            }
        }

        $rates = $query->orderBy('last_updated', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $rates,
            'last_updated' => $rates->max('last_updated'),
        ]);
    }

    /**
     * Get exchange rate between two currencies.
     */
    public function getRate($from, $to): JsonResponse
    {
        $fromCurrency = Currency::where('code', $from)->firstOrFail();
        $toCurrency = Currency::where('code', $to)->firstOrFail();

        if ($fromCurrency->id === $toCurrency->id) {
            return response()->json([
                'success' => true,
                'data' => [
                    'from_currency' => $fromCurrency->code,
                    'to_currency' => $toCurrency->code,
                    'rate' => 1.0,
                    'buy_rate' => 1.0,
                    'sell_rate' => 1.0,
                    'spread' => 0.0,
                    'last_updated' => now(),
                ],
            ]);
        }

        $exchangeRate = ExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->where('is_active', true)
            ->first();

        if (!$exchangeRate) {
            // Try to find reverse rate
            $reverseRate = ExchangeRate::where('from_currency_id', $toCurrency->id)
                ->where('to_currency_id', $fromCurrency->id)
                ->where('is_active', true)
                ->first();

            if ($reverseRate) {
                $rate = 1 / $reverseRate->rate;
                $buyRate = 1 / $reverseRate->sell_rate;
                $sellRate = 1 / $reverseRate->buy_rate;
                $spread = $reverseRate->spread;
                $lastUpdated = $reverseRate->last_updated;
            } else {
                // Calculate via USD
                $fromToUsd = $fromCurrency->rate_to_usd;
                $toToUsd = $toCurrency->rate_to_usd;
                $rate = $toToUsd / $fromToUsd;
                $buyRate = $rate * 1.005;
                $sellRate = $rate * 0.995;
                $spread = 0.01;
                $lastUpdated = now();
            }
        } else {
            $rate = $exchangeRate->rate;
            $buyRate = $exchangeRate->buy_rate;
            $sellRate = $exchangeRate->sell_rate;
            $spread = $exchangeRate->spread;
            $lastUpdated = $exchangeRate->last_updated;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'from_currency' => $fromCurrency->code,
                'to_currency' => $toCurrency->code,
                'rate' => $rate,
                'buy_rate' => $buyRate,
                'sell_rate' => $sellRate,
                'spread' => $spread,
                'last_updated' => $lastUpdated,
                'is_real_time' => $exchangeRate ? true : false,
            ],
        ]);
    }

    /**
     * Calculate exchange for amount.
     */
    public function calculate(Request $request): JsonResponse
    {
        $request->validate([
            'from_currency' => 'required|exists:currencies,code',
            'to_currency' => 'required|exists:currencies,code',
            'amount' => 'required|numeric|min:0.01',
            'type' => 'in:buy,sell',
        ]);

        $fromCurrency = Currency::where('code', $request->from_currency)->first();
        $toCurrency = Currency::where('code', $request->to_currency)->first();
        $type = $request->type ?? 'buy';

        // Get exchange rate
        $rateResponse = $this->getRate($request->from_currency, $request->to_currency);
        $rateData = $rateResponse->getData()->data;

        $rate = $type === 'buy' ? $rateData->buy_rate : $rateData->sell_rate;
        $convertedAmount = $request->amount * $rate;

        // Calculate fees
        $feePercentage = 0.005; // 0.5%
        $fixedFee = 1.0;
        $feeAmount = max(($convertedAmount * $feePercentage), $fixedFee);
        $netAmount = $convertedAmount - $feeAmount;

        return response()->json([
            'success' => true,
            'data' => [
                'from_currency' => $request->from_currency,
                'to_currency' => $request->to_currency,
                'amount' => $request->amount,
                'exchange_rate' => $rate,
                'converted_amount' => round($convertedAmount, $toCurrency->decimal_places),
                'fee_percentage' => $feePercentage,
                'fee_amount' => round($feeAmount, $toCurrency->decimal_places),
                'net_amount' => round($netAmount, $toCurrency->decimal_places),
                'type' => $type,
                'timestamp' => now(),
            ],
        ]);
    }

    /**
     * Get exchange rate history.
     */
    public function history($from, $to, Request $request): JsonResponse
    {
        $fromCurrency = Currency::where('code', $from)->firstOrFail();
        $toCurrency = Currency::where('code', $to)->firstOrFail();

        $days = $request->get('days', 30);
        $days = min($days, 365); // Max 1 year

        // In a real implementation, you would store historical rates
        // For now, we'll generate mock historical data
        $history = collect();
        $baseRate = $fromCurrency->rate_to_usd / $toCurrency->rate_to_usd;

        for ($i = $days; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $variation = (rand(-500, 500) / 10000); // ±5% variation
            $rate = $baseRate * (1 + $variation);

            $history->push([
                'date' => $date->format('Y-m-d'),
                'rate' => round($rate, 6),
                'high' => round($rate * 1.02, 6),
                'low' => round($rate * 0.98, 6),
                'volume' => rand(100000, 1000000),
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'from_currency' => $from,
                'to_currency' => $to,
                'period_days' => $days,
                'history' => $history,
                'current_rate' => $history->last()['rate'],
                'change_percentage' => (($history->last()['rate'] - $history->first()['rate']) / $history->first()['rate']) * 100,
            ],
        ]);
    }

    /**
     * Get real-time rates from external API.
     */
    public function realTimeRates(): JsonResponse
    {
        $cacheKey = 'real_time_rates';
        $rates = Cache::remember($cacheKey, 300, function () { // Cache for 5 minutes
            return $this->fetchExternalRates();
        });

        return response()->json([
            'success' => true,
            'data' => $rates,
            'source' => 'external_api',
            'cached_until' => now()->addMinutes(5),
        ]);
    }

    /**
     * Get currency trends.
     */
    public function trends(): JsonResponse
    {
        $currencies = Currency::active()->get();
        $trends = [];

        foreach ($currencies as $currency) {
            if ($currency->code === 'USD') continue; // Skip base currency

            // Mock trend data - in real implementation, calculate from historical data
            $trends[] = [
                'currency' => $currency->code,
                'name' => $currency->name,
                'current_rate' => $currency->rate_to_usd,
                'change_24h' => rand(-10, 15) / 100, // -10% to +15%
                'change_7d' => rand(-20, 25) / 100,
                'change_30d' => rand(-30, 40) / 100,
                'volume_24h' => rand(1000000, 50000000),
                'market_cap' => $currency->is_crypto ? rand(1000000000, 100000000000) : null,
                'is_trending_up' => rand(0, 1) === 1,
            ];
        }

        // Sort by 24h change
        usort($trends, function ($a, $b) {
            return $b['change_24h'] <=> $a['change_24h'];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'top_gainers' => array_slice($trends, 0, 5),
                'top_losers' => array_slice(array_reverse($trends), 0, 5),
                'most_volatile' => array_slice($trends, 0, 10),
                'timestamp' => now(),
            ],
        ]);
    }

    /**
     * Fetch rates from external API (mock implementation).
     */
    private function fetchExternalRates(): array
    {
        // In real implementation, fetch from actual API like:
        // - Fixer.io
        // - CurrencyAPI
        // - ExchangeRate-API
        // - CoinGecko (for crypto)

        $baseCurrencies = ['USD', 'EUR', 'GBP', 'SAR', 'AED'];
        $rates = [];

        foreach ($baseCurrencies as $base) {
            foreach ($baseCurrencies as $target) {
                if ($base !== $target) {
                    $rates[] = [
                        'from' => $base,
                        'to' => $target,
                        'rate' => rand(50, 500) / 100, // Mock rate
                        'timestamp' => now(),
                    ];
                }
            }
        }

        return $rates;
    }

    /**
     * Update exchange rates from external source.
     */
    public function updateRates(): JsonResponse
    {
        $updated = 0;
        $errors = [];

        try {
            $externalRates = $this->fetchExternalRates();

            foreach ($externalRates as $rateData) {
                $fromCurrency = Currency::where('code', $rateData['from'])->first();
                $toCurrency = Currency::where('code', $rateData['to'])->first();

                if ($fromCurrency && $toCurrency) {
                    ExchangeRate::updateOrCreate(
                        [
                            'from_currency_id' => $fromCurrency->id,
                            'to_currency_id' => $toCurrency->id,
                        ],
                        [
                            'rate' => $rateData['rate'],
                            'buy_rate' => $rateData['rate'] * 1.005,
                            'sell_rate' => $rateData['rate'] * 0.995,
                            'spread' => 0.01,
                            'source' => 'external_api',
                            'last_updated' => now(),
                            'is_active' => true,
                        ]
                    );
                    $updated++;
                }
            }

            // Clear cache
            Cache::forget('real_time_rates');

        } catch (\Exception $e) {
            $errors[] = $e->getMessage();
        }

        return response()->json([
            'success' => empty($errors),
            'message' => "Updated {$updated} exchange rates",
            'updated_count' => $updated,
            'errors' => $errors,
            'timestamp' => now(),
        ]);
    }
}
