*.log
.DS_Store
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
/.fleet
/.idea
/.nova
/.phpunit.cache
/.vscode
/.zed
/auth.json
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
Homestead.json
Homestead.yaml
Thumbs.db
npm-debug.log
yarn-error.log

# Financial System Specific
/storage/app/documents/*
/storage/app/receipts/*
/storage/app/reports/*
/storage/app/backups/*
/storage/app/kyc/*
/storage/app/exports/*
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*

# Security
*.pem
*.crt
*.p12
*.pfx
private_keys/
certificates/

# Database
*.sql
*.sqlite
*.db

# Docker
docker-compose.override.yml
.docker/

# IDE
*.swp
*.swo
*~

# Temporary files
tmp/
temp/
.tmp/

# Backup files
*.bak
*.backup

# API Documentation
/public/docs/api/

# Test coverage
/coverage/
/tests/coverage/

# Package managers
composer.phar

# Local configuration
/config/local/
.env.local
.env.testing
