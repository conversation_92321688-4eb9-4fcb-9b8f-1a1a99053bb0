<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transaction_id',
        'parent_payment_id',
        'gateway',
        'gateway_reference',
        'amount',
        'currency',
        'status',
        'type',
        'payment_data',
        'gateway_response',
        'processed_at',
        'failed_at',
        'error_code',
        'error_message',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_data' => 'array',
        'gateway_response' => 'array',
        'metadata' => 'array',
        'processed_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    /**
     * Get the transaction that owns this payment
     */
    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Get the parent payment transaction (for refunds)
     */
    public function parentPayment()
    {
        return $this->belongsTo(PaymentTransaction::class, 'parent_payment_id');
    }

    /**
     * Get child payment transactions (refunds)
     */
    public function childPayments()
    {
        return $this->hasMany(PaymentTransaction::class, 'parent_payment_id');
    }

    /**
     * Get the payment method
     */
    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class, 'gateway', 'type');
    }

    /**
     * Check if payment is successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment failed
     */
    public function isFailed(): bool
    {
        return in_array($this->status, ['failed', 'cancelled', 'rejected']);
    }

    /**
     * Check if payment is refundable
     */
    public function isRefundable(): bool
    {
        return $this->status === 'completed' && $this->type !== 'refund';
    }

    /**
     * Get total refunded amount
     */
    public function getTotalRefundedAmount(): float
    {
        return $this->childPayments()
            ->where('type', 'refund')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Get remaining refundable amount
     */
    public function getRemainingRefundableAmount(): float
    {
        if (!$this->isRefundable()) {
            return 0;
        }
        
        $totalRefunded = abs($this->getTotalRefundedAmount());
        return max(0, $this->amount - $totalRefunded);
    }

    /**
     * Mark payment as completed
     */
    public function markAsCompleted(array $gatewayResponse = []): void
    {
        $this->update([
            'status' => 'completed',
            'processed_at' => now(),
            'gateway_response' => array_merge($this->gateway_response ?? [], $gatewayResponse),
        ]);
    }

    /**
     * Mark payment as failed
     */
    public function markAsFailed(string $errorCode, string $errorMessage, array $gatewayResponse = []): void
    {
        $this->update([
            'status' => 'failed',
            'failed_at' => now(),
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'gateway_response' => array_merge($this->gateway_response ?? [], $gatewayResponse),
        ]);
    }

    /**
     * Get formatted amount with currency
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    /**
     * Get processing duration in minutes
     */
    public function getProcessingDurationAttribute(): ?int
    {
        if (!$this->processed_at) {
            return null;
        }
        
        return $this->created_at->diffInMinutes($this->processed_at);
    }

    /**
     * Scope for successful payments
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed payments
     */
    public function scopeFailed($query)
    {
        return $query->whereIn('status', ['failed', 'cancelled', 'rejected']);
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for refunds
     */
    public function scopeRefunds($query)
    {
        return $query->where('type', 'refund');
    }

    /**
     * Scope for payments (not refunds)
     */
    public function scopePayments($query)
    {
        return $query->where('type', '!=', 'refund')->orWhereNull('type');
    }

    /**
     * Scope for specific gateway
     */
    public function scopeGateway($query, string $gateway)
    {
        return $query->where('gateway', $gateway);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get payment statistics
     */
    public static function getStatistics(array $filters = []): array
    {
        $query = static::query();
        
        // Apply filters
        if (isset($filters['gateway'])) {
            $query->where('gateway', $filters['gateway']);
        }
        
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }
        
        return [
            'total_transactions' => $query->count(),
            'successful_transactions' => $query->clone()->successful()->count(),
            'failed_transactions' => $query->clone()->failed()->count(),
            'pending_transactions' => $query->clone()->pending()->count(),
            'total_amount' => $query->clone()->successful()->sum('amount'),
            'average_amount' => $query->clone()->successful()->avg('amount'),
            'success_rate' => $query->count() > 0 
                ? ($query->clone()->successful()->count() / $query->count()) * 100 
                : 0,
        ];
    }
}
