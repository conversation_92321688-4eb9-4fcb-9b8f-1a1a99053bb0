<template>
  <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
    <!-- Transaction Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3 rtl:space-x-reverse">
        <div class="text-2xl">
          {{ getTransactionTypeIcon(transaction.type) }}
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900">
            {{ $t(`transactions.type.${transaction.type}`) }}
          </h3>
          <p class="text-sm text-gray-500">
            {{ transaction.reference_number }}
          </p>
        </div>
      </div>
      
      <div class="text-right rtl:text-left">
        <div class="text-lg font-bold text-gray-900">
          {{ formatCurrency(transaction.amount, transaction.currency.code) }}
        </div>
        <div class="text-sm text-gray-500">
          {{ formatDate(transaction.created_at) }}
        </div>
      </div>
    </div>

    <!-- Transaction Status -->
    <div class="flex items-center justify-between mb-4">
      <span 
        :class="getTransactionStatusColor(transaction.status)"
        class="px-3 py-1 rounded-full text-xs font-medium"
      >
        {{ $t(`transactions.status.${transaction.status}`) }}
      </span>
      
      <div v-if="transaction.fee > 0" class="text-sm text-gray-500">
        {{ $t('transactions.fee') }}: {{ formatCurrency(transaction.fee, transaction.currency.code) }}
      </div>
    </div>

    <!-- Receiver Information (for transfers) -->
    <div v-if="transaction.type === 'transfer' && transaction.receiver_name" class="mb-4">
      <div class="text-sm text-gray-600">
        <span class="font-medium">{{ $t('transactions.receiver') }}:</span>
        {{ transaction.receiver_name }}
      </div>
      <div v-if="transaction.receiver_phone" class="text-sm text-gray-500">
        {{ formatPhoneNumber(transaction.receiver_phone) }}
      </div>
    </div>

    <!-- Payment Method -->
    <div class="mb-4">
      <div class="text-sm text-gray-600">
        <span class="font-medium">{{ $t('transactions.payment_method') }}:</span>
        {{ $t(`transactions.payment_methods.${transaction.payment_method}`) }}
      </div>
    </div>

    <!-- Notes -->
    <div v-if="transaction.notes" class="mb-4">
      <div class="text-sm text-gray-600">
        <span class="font-medium">{{ $t('common.notes') }}:</span>
        {{ transaction.notes }}
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
      <div class="flex space-x-2 rtl:space-x-reverse">
        <button
          @click="viewDetails"
          class="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          {{ $t('common.view') }}
        </button>
        
        <button
          v-if="canCancel"
          @click="cancelTransaction"
          class="text-red-600 hover:text-red-800 text-sm font-medium"
        >
          {{ $t('transactions.cancel_transaction') }}
        </button>
        
        <button
          @click="copyReference"
          class="text-gray-600 hover:text-gray-800 text-sm font-medium"
        >
          {{ $t('common.copy') }}
        </button>
      </div>
      
      <div v-if="showProgress" class="flex items-center space-x-2 rtl:space-x-reverse">
        <div class="w-4 h-4">
          <svg class="animate-spin h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <span class="text-sm text-gray-500">{{ $t('common.loading') }}</span>
      </div>
    </div>

    <!-- Progress Bar for Processing Transactions -->
    <div v-if="transaction.status === 'processing'" class="mt-4">
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-500"
          :style="{ width: progressPercentage + '%' }"
        ></div>
      </div>
      <div class="text-xs text-gray-500 mt-1 text-center">
        {{ $t('transactions.status.processing') }}...
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { router } from '@inertiajs/vue3'

const props = defineProps({
  transaction: {
    type: Object,
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['cancelled', 'updated'])

const showProgress = ref(false)
const progressPercentage = ref(0)
let progressInterval = null

// Computed properties
const canCancel = computed(() => {
  return props.transaction.status === 'pending' && 
         props.transaction.sender_id === window.auth?.user?.id
})

// Methods
const viewDetails = () => {
  router.visit(`/transactions/${props.transaction.id}`)
}

const cancelTransaction = async () => {
  if (!confirm('هل أنت متأكد من إلغاء هذه المعاملة؟')) {
    return
  }

  showProgress.value = true
  
  try {
    await router.put(`/transactions/${props.transaction.id}/cancel`, {}, {
      onSuccess: () => {
        emit('cancelled', props.transaction.id)
        showSuccess('تم إلغاء المعاملة بنجاح')
      },
      onError: (errors) => {
        handleApiError({ response: { data: { errors } } })
      },
      onFinish: () => {
        showProgress.value = false
      }
    })
  } catch (error) {
    handleApiError(error)
    showProgress.value = false
  }
}

const copyReference = async () => {
  await copyToClipboard(props.transaction.reference_number)
}

const formatPhoneNumber = (phone) => {
  if (!phone) return ''
  
  // Remove all non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, '')
  
  // Format based on country code
  if (cleaned.startsWith('+966')) {
    // Saudi Arabia format
    const number = cleaned.substring(4)
    return `+966 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`
  }
  
  return cleaned
}

const getTransactionTypeIcon = (type) => {
  const icons = {
    transfer: '💸',
    deposit: '💰',
    withdrawal: '🏧',
    exchange: '💱'
  }
  return icons[type] || '💳'
}

const getTransactionStatusColor = (status) => {
  const colors = {
    pending: 'text-yellow-600 bg-yellow-100',
    processing: 'text-blue-600 bg-blue-100',
    completed: 'text-green-600 bg-green-100',
    failed: 'text-red-600 bg-red-100',
    cancelled: 'text-gray-600 bg-gray-100',
    blocked: 'text-red-800 bg-red-200'
  }
  return colors[status] || 'text-gray-600 bg-gray-100'
}

const formatCurrency = (amount, currency = 'SAR') => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount)
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    showSuccess('تم النسخ إلى الحافظة')
  } catch (err) {
    showError('فشل في النسخ')
  }
}

const showSuccess = (message) => {
  // This would use the toast notification system
  console.log('Success:', message)
}

const showError = (message) => {
  // This would use the toast notification system
  console.log('Error:', message)
}

const handleApiError = (error) => {
  if (error.response) {
    const { status, data } = error.response
    
    if (status === 422 && data.errors) {
      Object.values(data.errors).flat().forEach(message => {
        showError(message)
      })
    } else if (data.message) {
      showError(data.message)
    } else {
      showError('حدث خطأ ما')
    }
  } else {
    showError('خطأ في الشبكة')
  }
}

// Lifecycle hooks
onMounted(() => {
  if (props.transaction.status === 'processing') {
    // Simulate progress for processing transactions
    progressInterval = setInterval(() => {
      if (progressPercentage.value < 90) {
        progressPercentage.value += Math.random() * 10
      }
    }, 1000)
  }
})

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
})
</script>

<style scoped>
/* Add any component-specific styles here */
.rtl {
  direction: rtl;
}

.rtl .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
</style>
