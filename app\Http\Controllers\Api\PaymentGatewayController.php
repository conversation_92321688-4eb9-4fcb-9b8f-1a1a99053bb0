<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PaymentGateway;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class PaymentGatewayController extends Controller
{
    /**
     * Get all active payment gateways.
     */
    public function index(Request $request): JsonResponse
    {
        $query = PaymentGateway::where('is_active', true);

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by country support
        if ($request->has('country')) {
            $query->whereJsonContains('supported_countries', $request->country);
        }

        // Filter by currency support
        if ($request->has('currency')) {
            $query->whereJsonContains('supported_currencies', $request->currency);
        }

        $gateways = $query->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'data' => $gateways->map(function ($gateway) {
                return [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'code' => $gateway->code,
                    'type' => $gateway->type,
                    'supported_countries' => $gateway->supported_countries,
                    'supported_currencies' => $gateway->supported_currencies,
                    'min_amount' => $gateway->min_amount,
                    'max_amount' => $gateway->max_amount,
                    'fee_percentage' => $gateway->fee_percentage,
                    'fixed_fee' => $gateway->fixed_fee,
                    'processing_time_minutes' => $gateway->processing_time_minutes,
                    'logo_url' => $gateway->logo_url,
                    'description' => $gateway->description,
                ];
            }),
        ]);
    }

    /**
     * Get payment gateway details.
     */
    public function show($id): JsonResponse
    {
        $gateway = PaymentGateway::findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $gateway->id,
                'name' => $gateway->name,
                'code' => $gateway->code,
                'type' => $gateway->type,
                'is_active' => $gateway->is_active,
                'supported_countries' => $gateway->supported_countries,
                'supported_currencies' => $gateway->supported_currencies,
                'min_amount' => $gateway->min_amount,
                'max_amount' => $gateway->max_amount,
                'fee_percentage' => $gateway->fee_percentage,
                'fixed_fee' => $gateway->fixed_fee,
                'processing_time_minutes' => $gateway->processing_time_minutes,
                'environment' => $gateway->environment,
                'logo_url' => $gateway->logo_url,
                'description' => $gateway->description,
            ],
        ]);
    }

    /**
     * Calculate fees for a payment gateway.
     */
    public function calculateFees(Request $request): JsonResponse
    {
        $request->validate([
            'gateway_id' => 'required|exists:payment_gateways,id',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|size:3',
        ]);

        $gateway = PaymentGateway::findOrFail($request->gateway_id);

        // Check if gateway supports the currency
        if (!$gateway->supportsCurrency($request->currency)) {
            return response()->json([
                'success' => false,
                'message' => 'Currency not supported by this gateway',
            ], 400);
        }

        // Check amount limits
        if (!$gateway->isAmountValid($request->amount)) {
            return response()->json([
                'success' => false,
                'message' => "Amount must be between {$gateway->min_amount} and {$gateway->max_amount}",
            ], 400);
        }

        $feeAmount = $gateway->calculateFee($request->amount);
        $netAmount = $request->amount - $feeAmount;

        return response()->json([
            'success' => true,
            'data' => [
                'gateway' => $gateway->only(['id', 'name', 'code']),
                'amount' => $request->amount,
                'currency' => $request->currency,
                'fee_amount' => round($feeAmount, 2),
                'net_amount' => round($netAmount, 2),
                'fee_breakdown' => [
                    'percentage_fee' => round($request->amount * $gateway->fee_percentage, 2),
                    'fixed_fee' => $gateway->fixed_fee,
                ],
                'processing_time' => $gateway->processing_time_minutes . ' minutes',
            ],
        ]);
    }

    /**
     * Get available gateways for specific criteria.
     */
    public function available(Request $request): JsonResponse
    {
        $request->validate([
            'country' => 'required|string|size:3',
            'currency' => 'required|string|size:3',
            'amount' => 'required|numeric|min:0.01',
            'type' => 'nullable|string',
        ]);

        $gateways = PaymentGateway::where('is_active', true)
            ->whereJsonContains('supported_countries', $request->country)
            ->whereJsonContains('supported_currencies', $request->currency)
            ->where('min_amount', '<=', $request->amount)
            ->where('max_amount', '>=', $request->amount)
            ->when($request->type, function ($query, $type) {
                return $query->where('type', $type);
            })
            ->get()
            ->map(function ($gateway) use ($request) {
                $feeAmount = $gateway->calculateFee($request->amount);
                return [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'code' => $gateway->code,
                    'type' => $gateway->type,
                    'fee_amount' => round($feeAmount, 2),
                    'net_amount' => round($request->amount - $feeAmount, 2),
                    'processing_time_minutes' => $gateway->processing_time_minutes,
                    'logo_url' => $gateway->logo_url,
                ];
            })
            ->sortBy('fee_amount')
            ->values();

        return response()->json([
            'success' => true,
            'data' => $gateways,
            'criteria' => [
                'country' => $request->country,
                'currency' => $request->currency,
                'amount' => $request->amount,
                'type' => $request->type,
            ],
            'total_available' => $gateways->count(),
        ]);
    }

    /**
     * PayPal webhook handler.
     */
    public function paypalWebhook(Request $request): JsonResponse
    {
        Log::info('PayPal webhook received', $request->all());

        try {
            $eventType = $request->input('event_type');
            $resource = $request->input('resource');

            switch ($eventType) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    $this->handlePaymentCompleted($resource, 'paypal');
                    break;
                case 'PAYMENT.CAPTURE.DENIED':
                    $this->handlePaymentFailed($resource, 'paypal');
                    break;
                default:
                    Log::info('Unhandled PayPal event type: ' . $eventType);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('PayPal webhook error: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Stripe webhook handler.
     */
    public function stripeWebhook(Request $request): JsonResponse
    {
        Log::info('Stripe webhook received', $request->all());

        try {
            $event = $request->all();
            $eventType = $event['type'];
            $data = $event['data']['object'];

            switch ($eventType) {
                case 'payment_intent.succeeded':
                    $this->handlePaymentCompleted($data, 'stripe');
                    break;
                case 'payment_intent.payment_failed':
                    $this->handlePaymentFailed($data, 'stripe');
                    break;
                default:
                    Log::info('Unhandled Stripe event type: ' . $eventType);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Stripe webhook error: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Wise webhook handler.
     */
    public function wiseWebhook(Request $request): JsonResponse
    {
        Log::info('Wise webhook received', $request->all());

        try {
            $event = $request->all();
            $eventType = $event['event_type'];
            $data = $event['data'];

            switch ($eventType) {
                case 'transfer.state_change':
                    if ($data['current_state'] === 'outgoing_payment_sent') {
                        $this->handlePaymentCompleted($data, 'wise');
                    } elseif ($data['current_state'] === 'cancelled') {
                        $this->handlePaymentFailed($data, 'wise');
                    }
                    break;
                default:
                    Log::info('Unhandled Wise event type: ' . $eventType);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Wise webhook error: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Blockchain webhook handler.
     */
    public function blockchainWebhook(Request $request): JsonResponse
    {
        Log::info('Blockchain webhook received', $request->all());

        try {
            $data = $request->all();
            $transactionHash = $data['hash'] ?? null;
            $confirmations = $data['confirmations'] ?? 0;

            if ($transactionHash && $confirmations >= 3) {
                $this->handleBlockchainConfirmation($transactionHash, $confirmations);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Blockchain webhook error: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Handle successful payment.
     */
    private function handlePaymentCompleted($data, $gateway): void
    {
        $gatewayTransactionId = $this->extractTransactionId($data, $gateway);
        
        if ($gatewayTransactionId) {
            $transaction = Transaction::where('gateway_transaction_id', $gatewayTransactionId)->first();
            
            if ($transaction && $transaction->status === 'pending') {
                $transaction->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                ]);

                // Update wallet balances
                if ($transaction->receiverWallet) {
                    $transaction->receiverWallet->addFunds($transaction->net_amount);
                }

                Log::info("Payment completed for transaction: {$transaction->transaction_number}");
            }
        }
    }

    /**
     * Handle failed payment.
     */
    private function handlePaymentFailed($data, $gateway): void
    {
        $gatewayTransactionId = $this->extractTransactionId($data, $gateway);
        
        if ($gatewayTransactionId) {
            $transaction = Transaction::where('gateway_transaction_id', $gatewayTransactionId)->first();
            
            if ($transaction && $transaction->status === 'pending') {
                $transaction->update([
                    'status' => 'failed',
                ]);

                // Unfreeze sender funds
                if ($transaction->senderWallet) {
                    $transaction->senderWallet->unfreezeFunds($transaction->amount);
                }

                Log::info("Payment failed for transaction: {$transaction->transaction_number}");
            }
        }
    }

    /**
     * Handle blockchain confirmation.
     */
    private function handleBlockchainConfirmation($transactionHash, $confirmations): void
    {
        $transaction = Transaction::where('blockchain_hash', $transactionHash)->first();
        
        if ($transaction) {
            $transaction->update([
                'blockchain_confirmations' => $confirmations,
                'status' => $confirmations >= 3 ? 'completed' : 'processing',
                'completed_at' => $confirmations >= 3 ? now() : null,
            ]);

            if ($confirmations >= 3 && $transaction->receiverWallet) {
                $transaction->receiverWallet->addFunds($transaction->net_amount);
            }

            Log::info("Blockchain confirmation updated for transaction: {$transaction->transaction_number}");
        }
    }

    /**
     * Extract transaction ID from gateway data.
     */
    private function extractTransactionId($data, $gateway): ?string
    {
        switch ($gateway) {
            case 'paypal':
                return $data['id'] ?? null;
            case 'stripe':
                return $data['id'] ?? null;
            case 'wise':
                return $data['id'] ?? null;
            default:
                return null;
        }
    }
}
