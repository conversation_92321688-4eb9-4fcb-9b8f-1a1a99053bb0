<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', [
                'national_id', 'passport', 'driving_license', 'utility_bill', 
                'bank_statement', 'selfie', 'address_proof', 'income_proof',
                'business_license', 'tax_certificate', 'other'
            ]);
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('file_path');
            $table->string('file_name');
            $table->bigInteger('file_size'); // File size in bytes
            $table->string('mime_type');
            $table->enum('status', ['active', 'inactive', 'expired'])->default('active');
            $table->enum('verification_status', ['pending', 'verified', 'rejected'])->default('pending');
            $table->timestamp('verified_at')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('rejection_reason')->nullable();
            $table->date('expiry_date')->nullable();
            $table->boolean('is_sensitive')->default(true);
            $table->json('metadata')->nullable(); // Additional document metadata
            $table->timestamps();
            
            $table->index(['user_id', 'type']);
            $table->index(['verification_status', 'created_at']);
            $table->index('expiry_date');
            $table->index(['status', 'verification_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
