<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'type',
        'is_active',
        'supported_countries',
        'supported_currencies',
        'min_amount',
        'max_amount',
        'fee_percentage',
        'fixed_fee',
        'configuration',
        'api_endpoint',
        'webhook_url',
        'environment',
        'processing_time_minutes',
        'description',
        'logo_url',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'supported_countries' => 'array',
        'supported_currencies' => 'array',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'fee_percentage' => 'decimal:4',
        'fixed_fee' => 'decimal:2',
        'configuration' => 'array',
        'processing_time_minutes' => 'integer',
    ];

    /**
     * Scope for active gateways.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if gateway supports country.
     */
    public function supportsCountry(string $countryCode): bool
    {
        return in_array($countryCode, $this->supported_countries ?? []);
    }

    /**
     * Check if gateway supports currency.
     */
    public function supportsCurrency(string $currencyCode): bool
    {
        return in_array($currencyCode, $this->supported_currencies ?? []);
    }

    /**
     * Calculate total fee for amount.
     */
    public function calculateFee(float $amount): float
    {
        return ($amount * $this->fee_percentage) + $this->fixed_fee;
    }

    /**
     * Check if amount is within limits.
     */
    public function isAmountValid(float $amount): bool
    {
        return $amount >= $this->min_amount && $amount <= $this->max_amount;
    }
}
