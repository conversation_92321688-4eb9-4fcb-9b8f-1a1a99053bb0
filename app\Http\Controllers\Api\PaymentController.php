<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PaymentGatewayService;
use App\Models\Transaction;
use App\Models\PaymentMethod;
use App\Models\PaymentTransaction;
use App\Exceptions\PaymentException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    protected PaymentGatewayService $paymentService;

    public function __construct(PaymentGatewayService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Get available payment methods
     */
    public function getPaymentMethods(Request $request): JsonResponse
    {
        $request->validate([
            'country_code' => 'required|string|size:2',
            'currency_code' => 'required|string|size:3',
            'amount' => 'nullable|numeric|min:1',
        ]);

        try {
            $paymentMethods = $this->paymentService->getAvailablePaymentMethods(
                $request->country_code,
                $request->currency_code
            );

            // Filter by amount if provided
            if ($request->amount) {
                $paymentMethods = array_filter($paymentMethods, function($method) use ($request) {
                    return (!$method['min_amount'] || $request->amount >= $method['min_amount']) &&
                           (!$method['max_amount'] || $request->amount <= $method['max_amount']);
                });
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'payment_methods' => array_values($paymentMethods),
                    'country_code' => $request->country_code,
                    'currency_code' => $request->currency_code,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment methods',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate payment fees
     */
    public function calculateFees(Request $request): JsonResponse
    {
        $request->validate([
            'payment_method' => 'required|string',
            'amount' => 'required|numeric|min:1',
        ]);

        try {
            $fees = $this->paymentService->calculatePaymentFees(
                $request->payment_method,
                $request->amount
            );

            return response()->json([
                'success' => true,
                'data' => $fees,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getErrorCode(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate fees',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process payment for transaction
     */
    public function processPayment(Request $request): JsonResponse
    {
        $request->validate([
            'transaction_id' => 'required|string|exists:transactions,transaction_id',
            'payment_method' => 'required|string',
            'payment_data' => 'required|array',
        ]);

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $transaction = Transaction::where('transaction_id', $request->transaction_id)
                ->where('user_id', $user->id)
                ->where('status', 'pending')
                ->firstOrFail();

            // Process payment
            $result = $this->paymentService->processPayment($transaction, [
                'payment_method' => $request->payment_method,
                'payment_data' => $request->payment_data,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'data' => [
                    'payment_result' => $result,
                    'transaction_id' => $transaction->transaction_id,
                    'status' => $result['status'],
                ],
            ]);

        } catch (PaymentException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getErrorCode(),
                'context' => $e->getContext(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(Request $request): JsonResponse
    {
        $request->validate([
            'payment_reference' => 'required|string',
            'gateway' => 'required|string',
        ]);

        try {
            $result = $this->paymentService->verifyPayment(
                $request->payment_reference,
                $request->gateway
            );

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getErrorCode(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Request payment refund
     */
    public function requestRefund(Request $request): JsonResponse
    {
        $request->validate([
            'transaction_id' => 'required|string|exists:transactions,transaction_id',
            'amount' => 'nullable|numeric|min:0.01',
            'reason' => 'required|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $transaction = Transaction::where('transaction_id', $request->transaction_id)
                ->where('user_id', $user->id)
                ->firstOrFail();

            // Check if refund is allowed
            if (!in_array($transaction->status, ['completed', 'processing'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Refund not allowed for this transaction status',
                ], 400);
            }

            // Process refund
            $result = $this->paymentService->refundPayment(
                $transaction,
                $request->amount
            );

            // Update transaction status
            $transaction->update([
                'status' => 'refund_requested',
                'refund_reason' => $request->reason,
                'refund_requested_at' => now(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Refund processed successfully',
                'data' => [
                    'refund_result' => $result,
                    'transaction_id' => $transaction->transaction_id,
                ],
            ]);

        } catch (PaymentException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getErrorCode(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Refund processing failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payment history
     */
    public function getPaymentHistory(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $query = PaymentTransaction::whereHas('transaction', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->with('transaction');

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('gateway')) {
            $query->where('gateway', $request->gateway);
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        $payments = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => [
                'payments' => $payments->items(),
                'pagination' => [
                    'current_page' => $payments->currentPage(),
                    'last_page' => $payments->lastPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                ],
            ],
        ]);
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStatistics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');

        $startDate = match($period) {
            'day' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };

        $stats = PaymentTransaction::whereHas('transaction', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->where('created_at', '>=', $startDate)
            ->selectRaw('
                COUNT(*) as total_payments,
                COUNT(CASE WHEN status = "completed" THEN 1 END) as successful_payments,
                COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_payments,
                COUNT(CASE WHEN type = "refund" THEN 1 END) as refunds,
                SUM(CASE WHEN status = "completed" AND type != "refund" THEN amount ELSE 0 END) as total_amount_paid,
                SUM(CASE WHEN status = "completed" AND type = "refund" THEN ABS(amount) ELSE 0 END) as total_amount_refunded,
                AVG(CASE WHEN status = "completed" AND type != "refund" THEN amount ELSE NULL END) as average_payment_amount
            ')
            ->first();

        // Get payment method breakdown
        $methodBreakdown = PaymentTransaction::whereHas('transaction', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->where('type', '!=', 'refund')
            ->selectRaw('gateway, COUNT(*) as count, SUM(amount) as total_amount')
            ->groupBy('gateway')
            ->orderBy('count', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'period' => $period,
                'start_date' => $startDate->toDateString(),
                'end_date' => now()->toDateString(),
                'statistics' => $stats,
                'payment_method_breakdown' => $methodBreakdown,
            ],
        ]);
    }

    /**
     * Handle payment webhook
     */
    public function handleWebhook(Request $request, string $gateway): JsonResponse
    {
        try {
            $result = $this->paymentService->handleWebhook($gateway, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully',
                'data' => $result,
            ]);
        } catch (PaymentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getErrorCode(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test payment gateway connection
     */
    public function testGateway(Request $request): JsonResponse
    {
        $request->validate([
            'gateway' => 'required|string',
        ]);

        try {
            $gateway = $this->paymentService->getGateway($request->gateway);
            $isConnected = $gateway->testConnection();

            return response()->json([
                'success' => true,
                'data' => [
                    'gateway' => $request->gateway,
                    'is_connected' => $isConnected,
                    'configuration' => $gateway->getConfiguration(),
                    'tested_at' => now(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gateway test failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
