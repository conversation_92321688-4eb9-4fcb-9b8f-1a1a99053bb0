<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Currency;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Get transaction reports.
     */
    public function transactions(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|string',
            'type' => 'nullable|string',
            'currency' => 'nullable|string',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : now()->subMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : now();

        $query = Transaction::where(function ($q) use ($user) {
            $q->where('sender_id', $user->id)
              ->orWhere('receiver_id', $user->id);
        })
        ->whereBetween('created_at', [$startDate, $endDate]);

        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->type) {
            $query->where('type', $request->type);
        }

        if ($request->currency) {
            $currency = Currency::where('code', $request->currency)->first();
            if ($currency) {
                $query->where('currency_id', $currency->id);
            }
        }

        $transactions = $query->with(['currency', 'targetCurrency', 'sender', 'receiver'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Calculate summary statistics
        $summary = [
            'total_transactions' => $transactions->count(),
            'total_sent' => $transactions->where('sender_id', $user->id)->sum('amount'),
            'total_received' => $transactions->where('receiver_id', $user->id)->sum('net_amount'),
            'total_fees' => $transactions->where('sender_id', $user->id)->sum('total_fees'),
            'completed_transactions' => $transactions->where('status', 'completed')->count(),
            'pending_transactions' => $transactions->where('status', 'pending')->count(),
            'failed_transactions' => $transactions->where('status', 'failed')->count(),
            'by_currency' => $transactions->groupBy('currency.code')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('amount'),
                ];
            }),
            'by_type' => $transactions->groupBy('type')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('amount'),
                ];
            }),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'transactions' => $transactions,
                'summary' => $summary,
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                ],
            ],
        ]);
    }

    /**
     * Get wallet reports.
     */
    public function wallets(Request $request): JsonResponse
    {
        $user = $request->user();

        $wallets = $user->wallets()->with(['currency'])->get();

        $walletReports = $wallets->map(function ($wallet) {
            return [
                'wallet' => $wallet,
                'statistics' => [
                    'current_balance' => $wallet->balance,
                    'available_balance' => $wallet->available_balance,
                    'pending_balance' => $wallet->pending_balance,
                    'frozen_balance' => $wallet->frozen_balance,
                    'total_received' => $wallet->total_received,
                    'total_sent' => $wallet->total_sent,
                    'transaction_count' => $wallet->transaction_count,
                    'last_transaction_at' => $wallet->last_transaction_at,
                ],
                'monthly_activity' => $this->getWalletMonthlyActivity($wallet),
                'recent_transactions' => $wallet->sentTransactions()
                    ->union($wallet->receivedTransactions())
                    ->orderBy('created_at', 'desc')
                    ->limit(5)
                    ->get(),
            ];
        });

        $totalBalance = $wallets->sum('balance');
        $totalPending = $wallets->sum('pending_balance');

        return response()->json([
            'success' => true,
            'data' => [
                'wallets' => $walletReports,
                'summary' => [
                    'total_wallets' => $wallets->count(),
                    'active_wallets' => $wallets->where('is_active', true)->count(),
                    'total_balance_usd' => $this->convertToUSD($totalBalance),
                    'total_pending_usd' => $this->convertToUSD($totalPending),
                    'crypto_wallets' => $wallets->where('is_crypto_wallet', true)->count(),
                    'fiat_wallets' => $wallets->where('is_crypto_wallet', false)->count(),
                ],
            ],
        ]);
    }

    /**
     * Get monthly summary report.
     */
    public function monthlySummary(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        // Transaction statistics
        $transactions = Transaction::where(function ($q) use ($user) {
            $q->where('sender_id', $user->id)
              ->orWhere('receiver_id', $user->id);
        })
        ->whereBetween('created_at', [$startDate, $endDate])
        ->get();

        $sentTransactions = $transactions->where('sender_id', $user->id);
        $receivedTransactions = $transactions->where('receiver_id', $user->id);

        // Daily breakdown
        $dailyBreakdown = [];
        for ($day = 1; $day <= $endDate->day; $day++) {
            $date = Carbon::create($year, $month, $day);
            $dayTransactions = $transactions->filter(function ($t) use ($date) {
                return $t->created_at->isSameDay($date);
            });

            $dailyBreakdown[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $day,
                'transactions_count' => $dayTransactions->count(),
                'sent_amount' => $dayTransactions->where('sender_id', $user->id)->sum('amount'),
                'received_amount' => $dayTransactions->where('receiver_id', $user->id)->sum('net_amount'),
                'fees_paid' => $dayTransactions->where('sender_id', $user->id)->sum('total_fees'),
            ];
        }

        $summary = [
            'period' => [
                'month' => $month,
                'year' => $year,
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
            ],
            'totals' => [
                'transactions_count' => $transactions->count(),
                'sent_count' => $sentTransactions->count(),
                'received_count' => $receivedTransactions->count(),
                'total_sent' => $sentTransactions->sum('amount'),
                'total_received' => $receivedTransactions->sum('net_amount'),
                'total_fees' => $sentTransactions->sum('total_fees'),
                'net_flow' => $receivedTransactions->sum('net_amount') - $sentTransactions->sum('amount'),
            ],
            'by_status' => [
                'completed' => $transactions->where('status', 'completed')->count(),
                'pending' => $transactions->where('status', 'pending')->count(),
                'failed' => $transactions->where('status', 'failed')->count(),
                'cancelled' => $transactions->where('status', 'cancelled')->count(),
            ],
            'by_type' => $transactions->groupBy('type')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('amount'),
                ];
            }),
            'by_currency' => $transactions->groupBy('currency.code')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('amount'),
                ];
            }),
            'top_destinations' => $sentTransactions->groupBy('receiver_country_id')
                ->map(function ($group) {
                    return [
                        'count' => $group->count(),
                        'total_amount' => $group->sum('amount'),
                        'country' => $group->first()->receiverCountry,
                    ];
                })
                ->sortByDesc('total_amount')
                ->take(5)
                ->values(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'summary' => $summary,
                'daily_breakdown' => $dailyBreakdown,
            ],
        ]);
    }

    /**
     * Export transactions to CSV.
     */
    public function exportTransactions(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'format' => 'in:csv,excel,pdf',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : now()->subMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : now();
        $format = $request->format ?? 'csv';

        $transactions = Transaction::where(function ($q) use ($user) {
            $q->where('sender_id', $user->id)
              ->orWhere('receiver_id', $user->id);
        })
        ->whereBetween('created_at', [$startDate, $endDate])
        ->with(['currency', 'targetCurrency', 'sender', 'receiver'])
        ->orderBy('created_at', 'desc')
        ->get();

        // In a real implementation, generate actual file
        $filename = "transactions_{$user->id}_{$startDate->format('Y-m-d')}_{$endDate->format('Y-m-d')}.{$format}";
        $downloadUrl = "/downloads/{$filename}";

        return response()->json([
            'success' => true,
            'data' => [
                'filename' => $filename,
                'download_url' => $downloadUrl,
                'format' => $format,
                'record_count' => $transactions->count(),
                'generated_at' => now(),
                'expires_at' => now()->addHours(24),
            ],
        ]);
    }

    /**
     * Export wallets to CSV.
     */
    public function exportWallets(Request $request): JsonResponse
    {
        $user = $request->user();
        $format = $request->get('format', 'csv');

        $wallets = $user->wallets()->with(['currency'])->get();

        // In a real implementation, generate actual file
        $filename = "wallets_{$user->id}_" . now()->format('Y-m-d') . ".{$format}";
        $downloadUrl = "/downloads/{$filename}";

        return response()->json([
            'success' => true,
            'data' => [
                'filename' => $filename,
                'download_url' => $downloadUrl,
                'format' => $format,
                'record_count' => $wallets->count(),
                'generated_at' => now(),
                'expires_at' => now()->addHours(24),
            ],
        ]);
    }

    /**
     * Get wallet monthly activity.
     */
    private function getWalletMonthlyActivity(Wallet $wallet): array
    {
        $startDate = now()->startOfMonth();
        $endDate = now()->endOfMonth();

        $sentTransactions = $wallet->sentTransactions()
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $receivedTransactions = $wallet->receivedTransactions()
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        return [
            'sent_count' => $sentTransactions->count(),
            'received_count' => $receivedTransactions->count(),
            'sent_amount' => $sentTransactions->sum('amount'),
            'received_amount' => $receivedTransactions->sum('net_amount'),
            'net_flow' => $receivedTransactions->sum('net_amount') - $sentTransactions->sum('amount'),
        ];
    }

    /**
     * Convert amount to USD (simplified).
     */
    private function convertToUSD($amount): float
    {
        // In a real implementation, use actual exchange rates
        return $amount; // Assuming already in USD for simplicity
    }
}
