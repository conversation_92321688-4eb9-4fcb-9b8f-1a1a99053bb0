<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->string('code', 3)->unique(); // USD, EUR, SAR, etc.
            $table->string('symbol', 10); // $, €, ﷼, etc.
            $table->boolean('is_crypto')->default(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_base_currency')->default(false);
            $table->decimal('rate_to_usd', 15, 8)->default(1.00000000);
            $table->integer('decimal_places')->default(2);
            $table->string('crypto_network')->nullable(); // ethereum, bitcoin, etc.
            $table->string('contract_address')->nullable(); // for ERC-20 tokens
            $table->string('icon_url')->nullable();
            $table->json('supported_countries')->nullable();
            $table->decimal('min_transfer_amount', 15, 2)->default(1);
            $table->decimal('max_transfer_amount', 15, 2)->default(1000000);
            $table->timestamps();
            
            $table->index(['is_active', 'is_crypto']);
            $table->index('is_base_currency');
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
