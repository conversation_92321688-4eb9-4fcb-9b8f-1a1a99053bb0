<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\TransactionController;
use App\Http\Controllers\Api\WalletController;
use App\Http\Controllers\Api\CurrencyController;
use App\Http\Controllers\Api\CountryController;
use App\Http\Controllers\Api\BranchController;
use App\Http\Controllers\Api\ExchangeRateController;
use App\Http\Controllers\Api\PaymentGatewayController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\DocumentController;
use App\Http\Controllers\AgentController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check route
Route::get('health', function () {
    return response()->json([
        'success' => true,
        'status' => 'healthy',
        'system' => [
            'name' => 'Mony Transfer API',
            'version' => '1.0.0',
            'environment' => app()->environment(),
            'timestamp' => now()->toISOString(),
        ],
        'services' => [
            'database' => 'connected',
            'cache' => 'operational',
            'queue' => 'operational',
            'storage' => 'operational',
        ],
        'statistics' => [
            'uptime' => 'operational',
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
            'peak_memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . ' MB',
        ],
    ]);
});

// Public routes
Route::prefix('v1')->group(function () {

    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
    });

    // Public data routes
    Route::get('countries', [CountryController::class, 'index']);
    Route::get('countries/{id}', [CountryController::class, 'show']);
    Route::get('currencies', [CurrencyController::class, 'index']);
    Route::get('currencies/{id}', [CurrencyController::class, 'show']);
    Route::get('exchange-rates', [ExchangeRateController::class, 'index']);
    Route::get('exchange-rates/{from}/{to}', [ExchangeRateController::class, 'getRate']);
    Route::get('branches', [BranchController::class, 'index']);
    Route::get('branches/{id}', [BranchController::class, 'show']);
    Route::get('branches/nearby', [BranchController::class, 'nearby']);
    Route::get('payment-gateways', [PaymentGatewayController::class, 'index']);

    // System status
    Route::get('status', function () {
        return response()->json([
            'success' => true,
            'message' => 'Mony Transfer Global Financial System is running',
            'version' => '1.0.0',
            'timestamp' => now(),
        ]);
    });
});

// Protected routes
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::post('enable-2fa', [AuthController::class, 'enableTwoFactor']);
        Route::post('disable-2fa', [AuthController::class, 'disableTwoFactor']);
        Route::post('verify-2fa', [AuthController::class, 'verifyTwoFactor']);
    });

    // User management routes
    Route::prefix('user')->group(function () {
        Route::get('profile', [UserController::class, 'profile']);
        Route::put('profile', [UserController::class, 'updateProfile']);
        Route::post('change-password', [UserController::class, 'changePassword']);
        Route::post('upload-avatar', [UserController::class, 'uploadAvatar']);
        Route::get('notifications', [UserController::class, 'notifications']);
        Route::put('notifications/{id}/read', [UserController::class, 'markNotificationRead']);
        Route::put('notifications/mark-all-read', [UserController::class, 'markAllNotificationsRead']);
        Route::get('activity-log', [UserController::class, 'activityLog']);
        Route::delete('account', [UserController::class, 'deleteAccount']);
    });

    // Document management routes
    Route::prefix('documents')->group(function () {
        Route::get('/', [DocumentController::class, 'index']);
        Route::post('/', [DocumentController::class, 'store']);
        Route::get('types', [DocumentController::class, 'types']);
        Route::get('kyc-status', [DocumentController::class, 'kycStatus']);
        Route::get('{id}', [DocumentController::class, 'show']);
        Route::put('{id}', [DocumentController::class, 'update']);
        Route::delete('{id}', [DocumentController::class, 'destroy']);
        Route::get('{id}/download', [DocumentController::class, 'download']);
        Route::get('{id}/preview', [DocumentController::class, 'preview']);
    });

    // Wallet routes
    Route::prefix('wallets')->group(function () {
        Route::get('/', [WalletController::class, 'index']);
        Route::post('/', [WalletController::class, 'store']);
        Route::get('{id}', [WalletController::class, 'show']);
        Route::put('{id}', [WalletController::class, 'update']);
        Route::get('{id}/balance', [WalletController::class, 'balance']);
        Route::get('{id}/statistics', [WalletController::class, 'statistics']);
        Route::post('{id}/add-funds', [WalletController::class, 'addFunds']);
        Route::post('{id}/freeze', [WalletController::class, 'freeze']);
        Route::post('{id}/unfreeze', [WalletController::class, 'unfreeze']);
    });

    // Transaction routes
    Route::prefix('transactions')->group(function () {
        Route::get('/', [TransactionController::class, 'index']);
        Route::post('/', [TransactionController::class, 'store']);
        Route::get('quote', [TransactionController::class, 'getQuote']);
        Route::get('supported-options', [TransactionController::class, 'getSupportedOptions']);
        Route::get('statistics', [TransactionController::class, 'statistics']);
        Route::get('{transactionId}', [TransactionController::class, 'show']);
        Route::post('{transactionId}/cancel', [TransactionController::class, 'cancel']);
        Route::post('{id}/confirm', [TransactionController::class, 'confirm']);
        Route::post('{id}/reject', [TransactionController::class, 'reject']);
        Route::get('{id}/receipt', [TransactionController::class, 'receipt']);
        Route::get('{id}/track', [TransactionController::class, 'track']);
    });

    // Exchange rate routes
    Route::prefix('exchange-rates')->group(function () {
        Route::post('calculate', [ExchangeRateController::class, 'calculate']);
        Route::get('history/{from}/{to}', [ExchangeRateController::class, 'history']);
    });

    // Notification routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::get('{id}', [NotificationController::class, 'show']);
        Route::put('{id}/read', [NotificationController::class, 'markAsRead']);
        Route::put('mark-all-read', [NotificationController::class, 'markAllAsRead']);
        Route::delete('{id}', [NotificationController::class, 'destroy']);
    });

    // Report routes
    Route::prefix('reports')->group(function () {
        Route::get('transactions', [ReportController::class, 'transactions']);
        Route::get('wallets', [ReportController::class, 'wallets']);
        Route::get('monthly-summary', [ReportController::class, 'monthlySummary']);
        Route::get('export/transactions', [ReportController::class, 'exportTransactions']);
        Route::get('export/wallets', [ReportController::class, 'exportWallets']);
    });

    // KYC/AML routes
    Route::prefix('kyc')->group(function () {
        Route::post('upload-document', [AuthController::class, 'uploadKycDocument']);
        Route::get('status', [AuthController::class, 'kycStatus']);
        Route::post('submit-verification', [AuthController::class, 'submitVerification']);
    });

    // Crypto routes
    Route::prefix('crypto')->group(function () {
        Route::get('wallets', [WalletController::class, 'cryptoWallets']);
        Route::post('wallets', [WalletController::class, 'createCryptoWallet']);
        Route::get('prices', [CurrencyController::class, 'cryptoPrices']);
        Route::post('transfer', [TransactionController::class, 'cryptoTransfer']);
        Route::get('blockchain/{hash}', [TransactionController::class, 'blockchainStatus']);
    });
});

// Admin routes
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'admin'])->group(function () {
    
    // User management
    Route::prefix('users')->group(function () {
        Route::get('/', [AdminController::class, 'users']);
        Route::get('{id}', [AdminController::class, 'showUser']);
        Route::put('{id}/status', [AdminController::class, 'updateUserStatus']);
        Route::put('{id}/limits', [AdminController::class, 'updateUserLimits']);
        Route::get('{id}/transactions', [AdminController::class, 'userTransactions']);
        Route::post('{id}/verify-kyc', [AdminController::class, 'verifyKyc']);
        Route::post('{id}/verify-aml', [AdminController::class, 'verifyAml']);
    });

    // Transaction management
    Route::prefix('transactions')->group(function () {
        Route::get('/', [AdminController::class, 'allTransactions']);
        Route::get('pending', [AdminController::class, 'pendingTransactions']);
        Route::get('suspicious', [AdminController::class, 'suspiciousTransactions']);
        Route::put('{id}/approve', [AdminController::class, 'approveTransaction']);
        Route::put('{id}/reject', [AdminController::class, 'rejectTransaction']);
        Route::put('{id}/flag', [AdminController::class, 'flagTransaction']);
    });

    // System management
    Route::prefix('system')->group(function () {
        Route::get('statistics', [AdminController::class, 'systemStatistics']);
        Route::get('audit-logs', [AdminController::class, 'auditLogs']);
        Route::post('exchange-rates/update', [AdminController::class, 'updateExchangeRates']);
        Route::get('fraud-alerts', [AdminController::class, 'fraudAlerts']);
        Route::put('fraud-alerts/{id}/resolve', [AdminController::class, 'resolveFraudAlert']);
    });

    // Branch management
    Route::prefix('branches')->group(function () {
        Route::post('/', [AdminController::class, 'createBranch']);
        Route::put('{id}', [AdminController::class, 'updateBranch']);
        Route::delete('{id}', [AdminController::class, 'deleteBranch']);
        Route::get('{id}/statistics', [AdminController::class, 'branchStatistics']);
    });

    // Currency management
    Route::prefix('currencies')->group(function () {
        Route::post('/', [AdminController::class, 'createCurrency']);
        Route::put('{id}', [AdminController::class, 'updateCurrency']);
        Route::put('{id}/toggle-status', [AdminController::class, 'toggleCurrencyStatus']);
    });

    // Payment gateway management
    Route::prefix('payment-gateways')->group(function () {
        Route::post('/', [AdminController::class, 'createPaymentGateway']);
        Route::put('{id}', [AdminController::class, 'updatePaymentGateway']);
        Route::put('{id}/toggle-status', [AdminController::class, 'toggleGatewayStatus']);
        Route::post('{id}/test', [AdminController::class, 'testPaymentGateway']);
    });

    // Reports
    Route::prefix('reports')->group(function () {
        Route::get('daily-summary', [AdminController::class, 'dailySummary']);
        Route::get('monthly-summary', [AdminController::class, 'monthlySummary']);
        Route::get('revenue', [AdminController::class, 'revenueReport']);
        Route::get('compliance', [AdminController::class, 'complianceReport']);
        Route::get('risk-analysis', [AdminController::class, 'riskAnalysisReport']);
    });
});

// Agent routes
Route::prefix('v1/agent')->middleware(['auth:sanctum', 'agent'])->group(function () {
    Route::get('dashboard', [AgentController::class, 'dashboard']);
    Route::get('transactions', [AgentController::class, 'transactions']);
    Route::post('transactions/{id}/process', [AgentController::class, 'processTransaction']);
    Route::get('customers', [AgentController::class, 'customers']);
    Route::post('customers/{id}/verify', [AgentController::class, 'verifyCustomer']);
    Route::get('commission', [AgentController::class, 'commission']);
});

// Webhook routes (for payment gateways)
Route::prefix('webhooks')->group(function () {
    Route::post('paypal', [PaymentGatewayController::class, 'paypalWebhook']);
    Route::post('stripe', [PaymentGatewayController::class, 'stripeWebhook']);
    Route::post('wise', [PaymentGatewayController::class, 'wiseWebhook']);
    Route::post('blockchain', [PaymentGatewayController::class, 'blockchainWebhook']);
});

// Health check
Route::get('health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now(),
        'version' => '1.0.0',
    ]);
});

// Advanced Health Check Routes
Route::get('/health/detailed', [\App\Http\Controllers\HealthController::class, 'detailed'])->name('health.detailed');

// Dashboard Routes (Admin only)
Route::middleware(['auth:sanctum', 'admin'])->prefix('dashboard')->group(function () {
    Route::get('/overview', [\App\Http\Controllers\DashboardController::class, 'overview'])->name('dashboard.overview');
    Route::get('/financial', [\App\Http\Controllers\DashboardController::class, 'financial'])->name('dashboard.financial');
    Route::get('/security', [\App\Http\Controllers\DashboardController::class, 'security'])->name('dashboard.security');
    Route::get('/performance', [\App\Http\Controllers\DashboardController::class, 'performance'])->name('dashboard.performance');
    Route::get('/users', [\App\Http\Controllers\DashboardController::class, 'users'])->name('dashboard.users');
    Route::get('/real-time', [\App\Http\Controllers\DashboardController::class, 'realTime'])->name('dashboard.real-time');
});

// Metrics Routes (Admin only)
Route::middleware(['auth:sanctum', 'admin'])->prefix('metrics')->group(function () {
    Route::get('/collect', function () {
        $metricsService = app(\App\Services\MetricsService::class);
        $metrics = $metricsService->collectAllMetrics();
        $metricsService->storeMetrics($metrics);

        return response()->json([
            'success' => true,
            'data' => $metrics,
            'message' => 'Metrics collected successfully'
        ]);
    })->name('metrics.collect');

    Route::get('/dashboard', function () {
        $metricsService = app(\App\Services\MetricsService::class);
        $metrics = $metricsService->getDashboardMetrics();

        return response()->json([
            'success' => true,
            'data' => $metrics
        ]);
    })->name('metrics.dashboard');

    Route::get('/range', function () {
        $start = request('start') ? new \DateTime(request('start')) : now()->subHour();
        $end = request('end') ? new \DateTime(request('end')) : now();

        $metricsService = app(\App\Services\MetricsService::class);
        $metrics = $metricsService->getMetricsForRange($start, $end);

        return response()->json([
            'success' => true,
            'data' => $metrics,
            'range' => [
                'start' => $start->format('Y-m-d H:i:s'),
                'end' => $end->format('Y-m-d H:i:s'),
            ]
        ]);
    })->name('metrics.range');
});

// Backup Routes (Admin only)
Route::middleware(['auth:sanctum', 'admin'])->prefix('backup')->group(function () {
    Route::post('/create', function () {
        $backupService = app(\App\Services\BackupService::class);

        try {
            $result = $backupService->createFullBackup();

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Backup created successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Backup failed: ' . $e->getMessage()
            ], 500);
        }
    })->name('backup.create');

    Route::post('/restore/{backupName}', function (string $backupName) {
        $backupService = app(\App\Services\BackupService::class);
        $components = request('components', []);

        try {
            $result = $backupService->restoreFromBackup($backupName, $components);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Restore completed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Restore failed: ' . $e->getMessage()
            ], 500);
        }
    })->name('backup.restore');

    Route::get('/verify/{backupName}', function (string $backupName) {
        $backupService = app(\App\Services\BackupService::class);

        try {
            $result = $backupService->verifyBackupIntegrity($backupName);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Verification failed: ' . $e->getMessage()
            ], 500);
        }
    })->name('backup.verify');

    Route::delete('/cleanup', function () {
        $backupService = app(\App\Services\BackupService::class);

        try {
            $result = $backupService->cleanupOldBackups();

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Cleanup completed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cleanup failed: ' . $e->getMessage()
            ], 500);
        }
    })->name('backup.cleanup');
});

// Blockchain Routes
Route::middleware(['auth:sanctum'])->prefix('blockchain')->group(function () {
    Route::post('/wallet/generate', function () {
        $cryptocurrency = request('cryptocurrency');
        $blockchainService = app(\App\Services\BlockchainService::class);

        try {
            $wallet = $blockchainService->generateWalletAddress($cryptocurrency);

            return response()->json([
                'success' => true,
                'data' => $wallet,
                'message' => 'Wallet generated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    })->name('blockchain.wallet.generate');

    Route::get('/wallet/balance', function () {
        $cryptocurrency = request('cryptocurrency');
        $address = request('address');
        $blockchainService = app(\App\Services\BlockchainService::class);

        try {
            $balance = $blockchainService->getWalletBalance($cryptocurrency, $address);

            return response()->json([
                'success' => true,
                'data' => $balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    })->name('blockchain.wallet.balance');

    Route::post('/transaction/send', function () {
        $transactionData = request()->all();
        $blockchainService = app(\App\Services\BlockchainService::class);

        try {
            $result = $blockchainService->sendTransaction($transactionData);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Transaction sent successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    })->name('blockchain.transaction.send');

    Route::get('/transaction/status', function () {
        $cryptocurrency = request('cryptocurrency');
        $txHash = request('tx_hash');
        $blockchainService = app(\App\Services\BlockchainService::class);

        try {
            $status = $blockchainService->getTransactionStatus($cryptocurrency, $txHash);

            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    })->name('blockchain.transaction.status');

    Route::get('/fees/{cryptocurrency}', function (string $cryptocurrency) {
        $blockchainService = app(\App\Services\BlockchainService::class);

        try {
            $fees = $blockchainService->getNetworkFees($cryptocurrency);

            return response()->json([
                'success' => true,
                'data' => $fees
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    })->name('blockchain.fees');

    Route::get('/price/{cryptocurrency}', function (string $cryptocurrency) {
        $blockchainService = app(\App\Services\BlockchainService::class);

        try {
            $price = $blockchainService->getCryptocurrencyPrice($cryptocurrency);

            return response()->json([
                'success' => true,
                'data' => $price
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    })->name('blockchain.price');

    Route::post('/address/validate', function () {
        $cryptocurrency = request('cryptocurrency');
        $address = request('address');
        $blockchainService = app(\App\Services\BlockchainService::class);

        $isValid = $blockchainService->validateAddress($cryptocurrency, $address);

        return response()->json([
            'success' => true,
            'data' => [
                'is_valid' => $isValid,
                'cryptocurrency' => $cryptocurrency,
                'address' => $address
            ]
        ]);
    })->name('blockchain.address.validate');
});
