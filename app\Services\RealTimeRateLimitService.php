<?php

namespace App\Services;

use App\Models\RateLimit;
use App\Models\ApiKey;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class RealTimeRateLimitService
{
    private Redis $redis;
    private array $defaultLimits;

    public function __construct()
    {
        $this->redis = Redis::connection();
        $this->defaultLimits = config('rate_limits', [
            'ip' => [
                'requests_per_minute' => 60,
                'requests_per_hour' => 1000,
                'requests_per_day' => 10000,
            ],
            'user' => [
                'requests_per_minute' => 120,
                'requests_per_hour' => 2000,
                'requests_per_day' => 20000,
            ],
            'api_key' => [
                'requests_per_minute' => 300,
                'requests_per_hour' => 5000,
                'requests_per_day' => 50000,
            ],
            'transaction' => [
                'requests_per_minute' => 5,
                'requests_per_hour' => 50,
                'requests_per_day' => 200,
            ],
        ]);
    }

    /**
     * Check and apply rate limit
     */
    public function checkRateLimit(
        string $identifier,
        string $type,
        string $endpoint = 'general',
        array $customLimits = []
    ): array {
        $limits = array_merge($this->defaultLimits[$type] ?? $this->defaultLimits['ip'], $customLimits);
        
        $results = [
            'allowed' => true,
            'limits' => [],
            'blocked_until' => null,
            'retry_after' => 0,
        ];

        // Check each time window
        foreach ($limits as $window => $limit) {
            $windowSeconds = $this->getWindowSeconds($window);
            $result = $this->checkWindow($identifier, $type, $endpoint, $windowSeconds, $limit);
            
            $results['limits'][$window] = $result;
            
            if (!$result['allowed']) {
                $results['allowed'] = false;
                $results['blocked_until'] = $result['reset_at'];
                $results['retry_after'] = max($results['retry_after'], $result['retry_after']);
            }
        }

        // Log rate limit check
        $this->logRateLimitCheck($identifier, $type, $endpoint, $results);

        return $results;
    }

    /**
     * Check rate limit for specific time window
     */
    private function checkWindow(
        string $identifier,
        string $type,
        string $endpoint,
        int $windowSeconds,
        int $limit
    ): array {
        $key = $this->getRedisKey($identifier, $type, $endpoint, $windowSeconds);
        $now = time();
        $windowStart = $now - $windowSeconds;

        // Use Redis sorted set to track requests in time window
        $pipe = $this->redis->pipeline();
        
        // Remove old entries
        $pipe->zremrangebyscore($key, 0, $windowStart);
        
        // Count current requests
        $pipe->zcard($key);
        
        // Add current request
        $pipe->zadd($key, $now, uniqid());
        
        // Set expiry
        $pipe->expire($key, $windowSeconds + 60);
        
        $results = $pipe->exec();
        $currentCount = $results[1];

        $allowed = $currentCount <= $limit;
        $remaining = max(0, $limit - $currentCount);
        $resetAt = $now + $windowSeconds;
        $retryAfter = $allowed ? 0 : $this->calculateRetryAfter($key, $windowSeconds, $limit);

        // If limit exceeded, record in database for persistent tracking
        if (!$allowed) {
            $this->recordRateLimitViolation($identifier, $type, $endpoint, $windowSeconds, $limit, $currentCount);
        }

        return [
            'allowed' => $allowed,
            'limit' => $limit,
            'current' => $currentCount,
            'remaining' => $remaining,
            'reset_at' => $resetAt,
            'retry_after' => $retryAfter,
            'window_seconds' => $windowSeconds,
        ];
    }

    /**
     * Calculate retry after seconds
     */
    private function calculateRetryAfter(string $key, int $windowSeconds, int $limit): int
    {
        // Get the oldest request in the current window
        $oldestRequests = $this->redis->zrange($key, 0, 0, 'WITHSCORES');
        
        if (empty($oldestRequests)) {
            return $windowSeconds;
        }

        $oldestTimestamp = (int) array_values($oldestRequests)[0];
        $retryAfter = ($oldestTimestamp + $windowSeconds) - time();
        
        return max(1, $retryAfter);
    }

    /**
     * Record rate limit violation in database
     */
    private function recordRateLimitViolation(
        string $identifier,
        string $type,
        string $endpoint,
        int $windowSeconds,
        int $limit,
        int $currentCount
    ): void {
        try {
            RateLimit::recordAttempt(
                $identifier,
                $type,
                $limit,
                $windowSeconds,
                $endpoint,
                [
                    'violation' => true,
                    'current_count' => $currentCount,
                    'exceeded_by' => $currentCount - $limit,
                    'timestamp' => now()->toISOString(),
                ]
            );
        } catch (\Exception $e) {
            Log::error('Failed to record rate limit violation', [
                'identifier' => $identifier,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Apply rate limit for IP address
     */
    public function checkIpRateLimit(string $ip, string $endpoint = 'general'): array
    {
        return $this->checkRateLimit($ip, 'ip', $endpoint);
    }

    /**
     * Apply rate limit for user
     */
    public function checkUserRateLimit(int $userId, string $endpoint = 'general'): array
    {
        return $this->checkRateLimit("user_{$userId}", 'user', $endpoint);
    }

    /**
     * Apply rate limit for API key
     */
    public function checkApiKeyRateLimit(string $apiKeyPrefix, string $endpoint = 'general'): array
    {
        // Get API key limits from database
        $apiKey = ApiKey::where('key_prefix', $apiKeyPrefix)->first();
        
        $customLimits = [];
        if ($apiKey) {
            $customLimits = [
                'requests_per_minute' => $apiKey->rate_limit_per_minute,
                'requests_per_hour' => $apiKey->rate_limit_per_hour,
                'requests_per_day' => $apiKey->rate_limit_per_day,
            ];
        }

        return $this->checkRateLimit("api_{$apiKeyPrefix}", 'api_key', $endpoint, $customLimits);
    }

    /**
     * Apply rate limit for transactions
     */
    public function checkTransactionRateLimit(int $userId): array
    {
        return $this->checkRateLimit("transaction_{$userId}", 'transaction', 'transactions');
    }

    /**
     * Block identifier for specific duration
     */
    public function blockIdentifier(
        string $identifier,
        string $type,
        int $durationSeconds,
        string $reason = 'Rate limit exceeded'
    ): void {
        $blockKey = "rate_limit_block:{$type}:{$identifier}";
        
        $blockData = [
            'blocked_at' => time(),
            'blocked_until' => time() + $durationSeconds,
            'reason' => $reason,
            'duration' => $durationSeconds,
        ];

        $this->redis->setex($blockKey, $durationSeconds, json_encode($blockData));

        // Log blocking
        Log::warning('Identifier blocked due to rate limiting', [
            'identifier' => $identifier,
            'type' => $type,
            'duration' => $durationSeconds,
            'reason' => $reason,
        ]);

        // Record in database
        AuditLogService::logSystemAction(
            'rate_limit_block',
            "Identifier blocked: {$identifier}",
            [
                'identifier' => $identifier,
                'type' => $type,
                'duration_seconds' => $durationSeconds,
                'reason' => $reason,
                'blocked_until' => date('Y-m-d H:i:s', time() + $durationSeconds),
            ]
        );
    }

    /**
     * Check if identifier is blocked
     */
    public function isBlocked(string $identifier, string $type): array
    {
        $blockKey = "rate_limit_block:{$type}:{$identifier}";
        $blockData = $this->redis->get($blockKey);

        if (!$blockData) {
            return ['blocked' => false];
        }

        $data = json_decode($blockData, true);
        $now = time();

        if ($now >= $data['blocked_until']) {
            // Block expired, remove it
            $this->redis->del($blockKey);
            return ['blocked' => false];
        }

        return [
            'blocked' => true,
            'blocked_at' => $data['blocked_at'],
            'blocked_until' => $data['blocked_until'],
            'reason' => $data['reason'],
            'remaining_seconds' => $data['blocked_until'] - $now,
        ];
    }

    /**
     * Unblock identifier
     */
    public function unblockIdentifier(string $identifier, string $type): bool
    {
        $blockKey = "rate_limit_block:{$type}:{$identifier}";
        $removed = $this->redis->del($blockKey);

        if ($removed) {
            AuditLogService::logSystemAction(
                'rate_limit_unblock',
                "Identifier unblocked: {$identifier}",
                [
                    'identifier' => $identifier,
                    'type' => $type,
                    'unblocked_at' => now()->toISOString(),
                ]
            );
        }

        return $removed > 0;
    }

    /**
     * Get rate limit statistics
     */
    public function getStatistics(): array
    {
        $stats = [
            'active_limits' => 0,
            'blocked_identifiers' => 0,
            'top_violators' => [],
            'by_type' => [],
        ];

        // Get blocked identifiers
        $blockKeys = $this->redis->keys('rate_limit_block:*');
        $stats['blocked_identifiers'] = count($blockKeys);

        // Get statistics from database
        $dbStats = RateLimit::getStatistics();
        $stats = array_merge($stats, $dbStats);

        return $stats;
    }

    /**
     * Clean expired rate limit data
     */
    public function cleanExpiredData(): int
    {
        $cleaned = 0;

        // Clean Redis keys (they should auto-expire, but let's be sure)
        $keys = $this->redis->keys('rate_limit:*');
        foreach ($keys as $key) {
            if ($this->redis->ttl($key) < 0) {
                $this->redis->del($key);
                $cleaned++;
            }
        }

        // Clean database records
        $cleaned += RateLimit::cleanExpired();

        return $cleaned;
    }

    /**
     * Get Redis key for rate limiting
     */
    private function getRedisKey(string $identifier, string $type, string $endpoint, int $windowSeconds): string
    {
        return "rate_limit:{$type}:{$identifier}:{$endpoint}:{$windowSeconds}";
    }

    /**
     * Convert window name to seconds
     */
    private function getWindowSeconds(string $window): int
    {
        return match($window) {
            'requests_per_minute' => 60,
            'requests_per_hour' => 3600,
            'requests_per_day' => 86400,
            default => 3600,
        };
    }

    /**
     * Log rate limit check
     */
    private function logRateLimitCheck(string $identifier, string $type, string $endpoint, array $results): void
    {
        if (!$results['allowed']) {
            Log::info('Rate limit exceeded', [
                'identifier' => $identifier,
                'type' => $type,
                'endpoint' => $endpoint,
                'retry_after' => $results['retry_after'],
            ]);
        }
    }
}
