<?php

namespace App\Jobs;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendTransactionEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Transaction $transaction;
    protected string $type;
    protected ?int $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(Transaction $transaction, string $type, int $userId = null)
    {
        $this->transaction = $transaction;
        $this->type = $type;
        $this->userId = $userId;
        
        $this->onQueue('emails');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $user = $this->userId ? User::find($this->userId) : $this->transaction->sender;
            
            if (!$user || !$user->email) {
                Log::warning('Cannot send email - user or email not found', [
                    'transaction_id' => $this->transaction->id,
                    'user_id' => $this->userId,
                ]);
                return;
            }

            $emailData = $this->prepareEmailData($user);
            
            // In a real implementation, you would use actual mail classes
            // For now, we'll just log the email content
            Log::info('Transaction email sent', [
                'transaction_id' => $this->transaction->id,
                'user_id' => $user->id,
                'email' => $user->email,
                'type' => $this->type,
                'subject' => $emailData['subject'],
            ]);

            // Simulate sending email
            $this->sendEmail($user, $emailData);

        } catch (\Exception $e) {
            Log::error('Failed to send transaction email', [
                'transaction_id' => $this->transaction->id,
                'type' => $this->type,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Prepare email data based on transaction type.
     */
    private function prepareEmailData(User $user): array
    {
        $transaction = $this->transaction;
        $isReceiver = $user->id === $transaction->receiver_id;

        switch ($this->type) {
            case 'initiated':
                return [
                    'subject' => 'Transaction Initiated - ' . $transaction->transaction_number,
                    'template' => 'emails.transaction.initiated',
                    'data' => [
                        'user' => $user,
                        'transaction' => $transaction,
                        'message' => 'Your money transfer has been initiated and is being processed.',
                    ],
                ];

            case 'completed':
                if ($isReceiver) {
                    return [
                        'subject' => 'Money Received - ' . $transaction->transaction_number,
                        'template' => 'emails.transaction.received',
                        'data' => [
                            'user' => $user,
                            'transaction' => $transaction,
                            'message' => 'You have received money from ' . $transaction->sender_name,
                        ],
                    ];
                } else {
                    return [
                        'subject' => 'Transaction Completed - ' . $transaction->transaction_number,
                        'template' => 'emails.transaction.completed',
                        'data' => [
                            'user' => $user,
                            'transaction' => $transaction,
                            'message' => 'Your money transfer has been completed successfully.',
                        ],
                    ];
                }

            case 'failed':
                return [
                    'subject' => 'Transaction Failed - ' . $transaction->transaction_number,
                    'template' => 'emails.transaction.failed',
                    'data' => [
                        'user' => $user,
                        'transaction' => $transaction,
                        'message' => 'Your transaction could not be processed. Funds have been returned to your wallet.',
                    ],
                ];

            case 'cancelled':
                return [
                    'subject' => 'Transaction Cancelled - ' . $transaction->transaction_number,
                    'template' => 'emails.transaction.cancelled',
                    'data' => [
                        'user' => $user,
                        'transaction' => $transaction,
                        'message' => 'Your transaction has been cancelled. Funds have been returned to your wallet.',
                    ],
                ];

            case 'blocked':
                return [
                    'subject' => 'Transaction Blocked - ' . $transaction->transaction_number,
                    'template' => 'emails.transaction.blocked',
                    'data' => [
                        'user' => $user,
                        'transaction' => $transaction,
                        'message' => 'Your transaction has been blocked for security reasons. Please contact support.',
                    ],
                ];

            case 'review_required':
                return [
                    'subject' => 'Transaction Under Review - ' . $transaction->transaction_number,
                    'template' => 'emails.transaction.review',
                    'data' => [
                        'user' => $user,
                        'transaction' => $transaction,
                        'message' => 'Your transaction is under review for security purposes. We will notify you once complete.',
                    ],
                ];

            default:
                return [
                    'subject' => 'Transaction Update - ' . $transaction->transaction_number,
                    'template' => 'emails.transaction.general',
                    'data' => [
                        'user' => $user,
                        'transaction' => $transaction,
                        'message' => 'Your transaction status has been updated.',
                    ],
                ];
        }
    }

    /**
     * Send the actual email.
     */
    private function sendEmail(User $user, array $emailData): void
    {
        // In a real implementation, you would use Laravel's Mail facade
        // with proper Mailable classes and email templates
        
        // Example:
        // Mail::to($user->email)->send(new TransactionMail($emailData));
        
        // For now, we'll simulate the email sending
        $emailContent = $this->generateEmailContent($emailData);
        
        Log::info('Email content generated', [
            'to' => $user->email,
            'subject' => $emailData['subject'],
            'content_length' => strlen($emailContent),
        ]);
    }

    /**
     * Generate email content (simplified).
     */
    private function generateEmailContent(array $emailData): string
    {
        $transaction = $emailData['data']['transaction'];
        $user = $emailData['data']['user'];
        $message = $emailData['data']['message'];

        return "
Dear {$user->first_name},

{$message}

Transaction Details:
- Transaction Number: {$transaction->transaction_number}
- Amount: {$transaction->currency->symbol} {$transaction->amount}
- Status: {$transaction->status}
- Date: {$transaction->created_at->format('Y-m-d H:i:s')}

Thank you for using Mony Transfer Global Financial System.

Best regards,
Mony Transfer Team
        ";
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Transaction email job failed permanently', [
            'transaction_id' => $this->transaction->id,
            'type' => $this->type,
            'user_id' => $this->userId,
            'error' => $exception->getMessage(),
        ]);
    }

    /**
     * Get the number of times the job may be attempted.
     */
    public function tries(): int
    {
        return 3;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [10, 30, 60]; // 10 seconds, 30 seconds, 1 minute
    }
}
