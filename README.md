# Mony Transfer Global Financial System

## نظام موني ترانسفير المالي العالمي

نظام مالي متكامل لتحويل الأموال والعملات الرقمية مع ميزات أمان متقدمة وكشف الاحتيال.

## المميزات الرئيسية

### 🏦 إدارة المحافظ المالية
- محافظ متعددة العملات (فيات وعملات رقمية)
- إدارة الأرصدة والحدود اليومية/الشهرية
- تجميد وإلغاء تجميد الأموال
- تتبع المعاملات والإحصائيات

### 💸 تحويل الأموال
- تحويلات محلية ودولية
- دعم العملات المتعددة
- أسعار صرف في الوقت الفعلي
- رسوم تنافسية ومرونة في الدفع

### 🔐 الأمان وكشف الاحتيال
- نظام كشف الاحتيال بالذكاء الاصطناعي
- تحليل المخاطر في الوقت الفعلي
- مراقبة الأنشطة المشبوهة
- التحقق من الهوية (KYC/AML)

### 🌐 العملات الرقمية
- دعم Bitcoin, Ethereum, USDT وغيرها
- محافظ عملات رقمية آمنة
- تتبع المعاملات على البلوك تشين
- أسعار العملات الرقمية المحدثة

### 📊 التقارير والإحصائيات
- تقارير مالية شاملة
- إحصائيات المعاملات
- تحليل الأداء
- تصدير البيانات

### 🔔 الإشعارات
- إشعارات فورية للمعاملات
- تنبيهات الأمان
- إشعارات البريد الإلكتروني والرسائل النصية
- إشعارات في الوقت الفعلي

## التقنيات المستخدمة

- **Backend**: Laravel 11 (PHP 8.2+)
- **Database**: MySQL 8.0+
- **Cache**: Redis
- **Queue**: Laravel Queue (Database/Redis)
- **Authentication**: Laravel Sanctum
- **Real-time**: Laravel Broadcasting (Pusher)
- **File Storage**: Laravel Storage
- **Logging**: Laravel Logging (Multi-channel)

## متطلبات النظام

- PHP 8.2 أو أحدث
- MySQL 8.0 أو أحدث
- Redis 6.0 أو أحدث
- Composer 2.0 أو أحدث
- Node.js 18 أو أحدث (للواجهة الأمامية)

## التثبيت

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/mony-transfer.git
cd mony-transfer
```

### 2. تثبيت التبعيات
```bash
composer install
npm install
```

### 3. إعداد البيئة
```bash
cp .env.example .env
php artisan key:generate
```

### 4. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p -e "CREATE DATABASE mony_transfer;"

# تحديث ملف .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mony_transfer
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. تشغيل الهجرات والبذور
```bash
php artisan migrate
php artisan db:seed
```

### 6. إعداد التخزين
```bash
php artisan storage:link
```

### 7. تشغيل الخادم
```bash
# تشغيل خادم Laravel
php artisan serve

# تشغيل Queue Workers
php artisan queue:work

# تشغيل المهام المجدولة
php artisan schedule:work
```

## إعداد الخدمات الخارجية

### بوابات الدفع
```env
# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# Stripe
STRIPE_KEY=your_stripe_key
STRIPE_SECRET=your_stripe_secret

# Wise
WISE_API_KEY=your_wise_api_key
WISE_ENVIRONMENT=sandbox
```

### أسعار الصرف
```env
# Fixer.io
FIXER_IO_API_KEY=your_fixer_api_key

# CoinGecko
COINGECKO_API_KEY=your_coingecko_api_key
```

### الإشعارات
```env
# Twilio SMS
TWILIO_SID=your_twilio_sid
TWILIO_TOKEN=your_twilio_token
TWILIO_FROM=your_twilio_number

# Pusher
PUSHER_APP_ID=your_pusher_app_id
PUSHER_APP_KEY=your_pusher_key
PUSHER_APP_SECRET=your_pusher_secret
PUSHER_APP_CLUSTER=mt1
```

## استخدام API

### المصادقة
```bash
# تسجيل مستخدم جديد
POST /api/v1/auth/register
{
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "+966501234567",
    "country_id": 1
}

# تسجيل الدخول
POST /api/v1/auth/login
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

### المحافظ
```bash
# الحصول على المحافظ
GET /api/v1/wallets
Authorization: Bearer {token}

# إنشاء محفظة جديدة
POST /api/v1/wallets
{
    "currency_id": 1,
    "name": "محفظة الدولار الأمريكي"
}
```

### المعاملات
```bash
# إنشاء معاملة جديدة
POST /api/v1/transactions
{
    "type": "transfer",
    "currency_id": 1,
    "amount": 1000,
    "receiver_name": "محمد أحمد",
    "receiver_phone": "+966501234568",
    "receiver_country_id": 1,
    "purpose": "مساعدة عائلية"
}

# تتبع المعاملة
GET /api/v1/transactions/{id}/track
```

## الأوامر المخصصة

```bash
# تحديث أسعار الصرف
php artisan financial:update-rates

# معالجة المعاملات المعلقة
php artisan financial:process-pending

# عرض إحصائيات النظام
php artisan financial:stats

# عرض حالة النظام
php artisan financial:status
```

## المراقبة والسجلات

### ملفات السجلات
- `storage/logs/laravel.log` - السجل العام
- `storage/logs/financial.log` - العمليات المالية
- `storage/logs/transactions.log` - المعاملات
- `storage/logs/security.log` - الأمان
- `storage/logs/fraud.log` - كشف الاحتيال
- `storage/logs/audit.log` - سجل التدقيق

### المراقبة
```bash
# مراقبة السجلات
tail -f storage/logs/financial.log

# مراقبة Queue
php artisan queue:monitor

# مراقبة الأداء
php artisan horizon:status
```

## الاختبار

```bash
# تشغيل جميع الاختبارات
php artisan test

# اختبارات محددة
php artisan test --filter TransactionTest

# اختبارات مع التغطية
php artisan test --coverage
```

## النشر

### متطلبات الإنتاج
```bash
# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache

# تحديث Composer للإنتاج
composer install --optimize-autoloader --no-dev
```

### Docker
```bash
# بناء الصورة
docker build -t mony-transfer .

# تشغيل الحاوية
docker run -p 8000:8000 mony-transfer
```

## الأمان

### أفضل الممارسات
- استخدام HTTPS في الإنتاج
- تشفير البيانات الحساسة
- تحديث التبعيات بانتظام
- مراقبة السجلات الأمنية
- تطبيق حدود المعدل (Rate Limiting)

### التحقق من الهوية
- KYC (Know Your Customer)
- AML (Anti-Money Laundering)
- التحقق بخطوتين (2FA)
- التحقق من الهاتف والبريد الإلكتروني

## المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE).

## الدعم

للحصول على الدعم، يرجى التواصل معنا:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567
- الموقع: https://monytransfer.com

## الإصدارات

### الإصدار 1.0.0
- إطلاق النظام الأساسي
- دعم التحويلات المحلية والدولية
- نظام كشف الاحتيال
- دعم العملات الرقمية
- واجهة إدارية شاملة

---

© 2024 Mony Transfer Global Financial System. جميع الحقوق محفوظة.
