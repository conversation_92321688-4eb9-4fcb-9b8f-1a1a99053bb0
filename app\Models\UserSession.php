<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class UserSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'device_id',
        'device_name',
        'device_type',
        'browser',
        'platform',
        'ip_address',
        'country',
        'city',
        'latitude',
        'longitude',
        'user_agent',
        'is_active',
        'is_trusted',
        'last_activity',
        'expires_at',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_trusted' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'last_activity' => 'datetime',
        'expires_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the session
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get active sessions
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>', now());
            });
    }

    /**
     * Scope to get expired sessions
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to get trusted sessions
     */
    public function scopeTrusted(Builder $query): Builder
    {
        return $query->where('is_trusted', true);
    }

    /**
     * Scope to filter by device type
     */
    public function scopeByDeviceType(Builder $query, string $type): Builder
    {
        return $query->where('device_type', $type);
    }

    /**
     * Scope to filter by IP address
     */
    public function scopeByIp(Builder $query, string $ip): Builder
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * Create a new session
     */
    public static function createSession(
        int $userId,
        string $sessionId,
        array $deviceInfo,
        array $locationInfo = []
    ): self {
        return self::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'device_id' => $deviceInfo['device_id'] ?? null,
            'device_name' => $deviceInfo['device_name'] ?? null,
            'device_type' => $deviceInfo['device_type'] ?? null,
            'browser' => $deviceInfo['browser'] ?? null,
            'platform' => $deviceInfo['platform'] ?? null,
            'ip_address' => $deviceInfo['ip_address'] ?? request()->ip(),
            'country' => $locationInfo['country'] ?? null,
            'city' => $locationInfo['city'] ?? null,
            'latitude' => $locationInfo['latitude'] ?? null,
            'longitude' => $locationInfo['longitude'] ?? null,
            'user_agent' => $deviceInfo['user_agent'] ?? request()->userAgent(),
            'is_active' => true,
            'is_trusted' => false,
            'last_activity' => now(),
            'expires_at' => now()->addDays(30),
            'metadata' => $deviceInfo['metadata'] ?? [],
        ]);
    }

    /**
     * Update session activity
     */
    public function updateActivity(): void
    {
        $this->update([
            'last_activity' => now(),
            'is_active' => true,
        ]);
    }

    /**
     * Terminate session
     */
    public function terminate(): void
    {
        $this->update([
            'is_active' => false,
            'expires_at' => now(),
        ]);
    }

    /**
     * Mark session as trusted
     */
    public function markAsTrusted(): void
    {
        $this->update(['is_trusted' => true]);
    }

    /**
     * Check if session is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if session is from suspicious location
     */
    public function isSuspiciousLocation(): bool
    {
        if (!$this->user) {
            return false;
        }

        // Get user's recent sessions from different locations
        $recentSessions = self::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->where('created_at', '>=', now()->subDays(7))
            ->whereNotNull('country')
            ->get();

        // If user has no recent sessions, not suspicious
        if ($recentSessions->isEmpty()) {
            return false;
        }

        // Check if current location is different from recent locations
        $recentCountries = $recentSessions->pluck('country')->unique();
        
        return $this->country && !$recentCountries->contains($this->country);
    }

    /**
     * Get session statistics for user
     */
    public static function getUserStats(int $userId): array
    {
        $sessions = self::where('user_id', $userId)->get();
        
        return [
            'total_sessions' => $sessions->count(),
            'active_sessions' => $sessions->where('is_active', true)->count(),
            'trusted_sessions' => $sessions->where('is_trusted', true)->count(),
            'unique_devices' => $sessions->whereNotNull('device_id')->pluck('device_id')->unique()->count(),
            'unique_ips' => $sessions->pluck('ip_address')->unique()->count(),
            'unique_countries' => $sessions->whereNotNull('country')->pluck('country')->unique()->count(),
            'last_activity' => $sessions->max('last_activity'),
        ];
    }

    /**
     * Clean expired sessions
     */
    public static function cleanExpired(): int
    {
        return self::expired()->delete();
    }

    /**
     * Terminate all user sessions except current
     */
    public static function terminateAllExcept(int $userId, string $currentSessionId): int
    {
        return self::where('user_id', $userId)
            ->where('session_id', '!=', $currentSessionId)
            ->update([
                'is_active' => false,
                'expires_at' => now(),
            ]);
    }

    /**
     * Get concurrent sessions for user
     */
    public static function getConcurrentSessions(int $userId): int
    {
        return self::where('user_id', $userId)
            ->active()
            ->where('last_activity', '>=', now()->subMinutes(5))
            ->count();
    }

    /**
     * Detect suspicious activity
     */
    public function detectSuspiciousActivity(): array
    {
        $flags = [];

        // Check for multiple concurrent sessions
        $concurrentSessions = self::getConcurrentSessions($this->user_id);
        if ($concurrentSessions > 3) {
            $flags[] = 'multiple_concurrent_sessions';
        }

        // Check for suspicious location
        if ($this->isSuspiciousLocation()) {
            $flags[] = 'suspicious_location';
        }

        // Check for rapid session creation
        $recentSessions = self::where('user_id', $this->user_id)
            ->where('created_at', '>=', now()->subHour())
            ->count();
        
        if ($recentSessions > 5) {
            $flags[] = 'rapid_session_creation';
        }

        // Check for unusual device
        $knownDevices = self::where('user_id', $this->user_id)
            ->where('is_trusted', true)
            ->whereNotNull('device_id')
            ->pluck('device_id');
        
        if ($this->device_id && !$knownDevices->contains($this->device_id)) {
            $flags[] = 'unknown_device';
        }

        return $flags;
    }
}
