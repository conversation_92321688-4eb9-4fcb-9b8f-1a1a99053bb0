<template>
  <AppLayout :title="$t('dashboard.title')">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">
              {{ $t('dashboard.welcome', { name: $page.props.auth.user.first_name }) }}
            </h1>
            <p class="mt-1 text-sm text-gray-600">
              {{ formatDate(new Date(), { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) }}
            </p>
          </div>
          
          <!-- Quick Actions -->
          <div class="flex space-x-3 rtl:space-x-reverse">
            <button
              @click="createTransaction"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <span class="mr-2 rtl:ml-2">💸</span>
              {{ $t('dashboard.send_money') }}
            </button>
            
            <button
              @click="addMoney"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <span class="mr-2 rtl:ml-2">💰</span>
              {{ $t('dashboard.add_money') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                  <span class="text-blue-600">💰</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1 rtl:mr-5 rtl:ml-0">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ $t('dashboard.wallet_balance') }}
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatCurrency(stats.total_balance, 'SAR') }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                  <span class="text-green-600">📈</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1 rtl:mr-5 rtl:ml-0">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ $t('dashboard.total_sent') }}
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatCurrency(stats.total_sent, 'SAR') }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                  <span class="text-purple-600">📊</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1 rtl:mr-5 rtl:ml-0">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ $t('dashboard.monthly_volume') }}
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatCurrency(stats.monthly_volume, 'SAR') }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                  <span class="text-yellow-600">⏳</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1 rtl:mr-5 rtl:ml-0">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ $t('dashboard.pending_transactions') }}
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ stats.pending_transactions }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-8">
          <!-- My Wallets -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900">
                  {{ $t('wallets.my_wallets') }}
                </h2>
                <button
                  @click="createWallet"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  {{ $t('wallets.create_wallet') }}
                </button>
              </div>
            </div>
            
            <div class="p-6">
              <div v-if="wallets.length === 0" class="text-center py-8">
                <div class="text-gray-400 text-6xl mb-4">💳</div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                  {{ $t('wallets.no_wallets') }}
                </h3>
                <p class="text-gray-500 mb-4">
                  {{ $t('wallets.create_first_wallet') }}
                </p>
                <button
                  @click="createWallet"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
                >
                  {{ $t('wallets.create_wallet') }}
                </button>
              </div>
              
              <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <WalletCard
                  v-for="wallet in wallets"
                  :key="wallet.id"
                  :wallet="wallet"
                  :show-recent-transactions="false"
                  @action="handleWalletAction"
                />
              </div>
            </div>
          </div>

          <!-- Recent Transactions -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900">
                  {{ $t('dashboard.recent_transactions') }}
                </h2>
                <Link
                  :href="route('transactions.index')"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  {{ $t('dashboard.view_all_transactions') }}
                </Link>
              </div>
            </div>
            
            <div class="p-6">
              <div v-if="recentTransactions.length === 0" class="text-center py-8">
                <div class="text-gray-400 text-6xl mb-4">📋</div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                  {{ $t('transactions.no_transactions') }}
                </h3>
                <p class="text-gray-500 mb-4">
                  {{ $t('transactions.start_first_transaction') }}
                </p>
                <button
                  @click="createTransaction"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
                >
                  {{ $t('transactions.new_transaction') }}
                </button>
              </div>
              
              <div v-else class="space-y-4">
                <TransactionCard
                  v-for="transaction in recentTransactions"
                  :key="transaction.id"
                  :transaction="transaction"
                  @cancelled="handleTransactionCancelled"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-8">
          <!-- Exchange Rates -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">
                {{ $t('dashboard.exchange_rates') }}
              </h2>
            </div>
            
            <div class="p-6">
              <div class="space-y-4">
                <div
                  v-for="rate in exchangeRates"
                  :key="rate.currency"
                  class="flex items-center justify-between"
                >
                  <div class="flex items-center space-x-3 rtl:space-x-reverse">
                    <span class="text-2xl">{{ getCurrencyIcon(rate.currency) }}</span>
                    <div>
                      <div class="font-medium">{{ rate.currency }}</div>
                      <div class="text-sm text-gray-500">{{ rate.name }}</div>
                    </div>
                  </div>
                  
                  <div class="text-right rtl:text-left">
                    <div class="font-medium">{{ formatNumber(rate.rate) }}</div>
                    <div 
                      :class="rate.change >= 0 ? 'text-green-600' : 'text-red-600'"
                      class="text-sm"
                    >
                      {{ rate.change >= 0 ? '+' : '' }}{{ formatNumber(rate.change) }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">
                {{ $t('dashboard.quick_actions') }}
              </h2>
            </div>
            
            <div class="p-6">
              <div class="space-y-3">
                <button
                  @click="createTransaction"
                  class="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-lg shadow-sm bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                >
                  <span class="mr-3 rtl:ml-3">💸</span>
                  {{ $t('dashboard.send_money') }}
                </button>
                
                <button
                  @click="addMoney"
                  class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  <span class="mr-3 rtl:ml-3">💰</span>
                  {{ $t('dashboard.add_money') }}
                </button>
                
                <button
                  @click="withdrawMoney"
                  class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  <span class="mr-3 rtl:ml-3">🏧</span>
                  {{ $t('dashboard.withdraw_money') }}
                </button>
              </div>
            </div>
          </div>

          <!-- Support -->
          <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
            <h3 class="text-lg font-semibold mb-2">{{ $t('support.need_help') }}</h3>
            <p class="text-sm opacity-90 mb-4">
              {{ $t('support.contact_description') }}
            </p>
            <button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all">
              {{ $t('support.contact_us') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { router, Link } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import WalletCard from '@/Components/WalletCard.vue'
import TransactionCard from '@/Components/TransactionCard.vue'

const props = defineProps({
  stats: Object,
  wallets: Array,
  recentTransactions: Array,
  exchangeRates: Array
})

// Methods
const formatCurrency = (amount, currency = 'SAR') => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount)
}

const formatNumber = (number) => {
  return new Intl.NumberFormat('ar-SA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  }).format(number)
}

const formatDate = (date, options = {}) => {
  return new Intl.DateTimeFormat('ar-SA', options).format(date)
}

const getCurrencyIcon = (currencyCode) => {
  const icons = {
    'SAR': '🇸🇦',
    'USD': '💵',
    'EUR': '💶',
    'GBP': '💷',
    'AED': '🇦🇪'
  }
  return icons[currencyCode] || '💰'
}

const createTransaction = () => {
  router.visit('/transactions/create')
}

const addMoney = () => {
  router.visit('/wallets/deposit')
}

const withdrawMoney = () => {
  router.visit('/wallets/withdraw')
}

const createWallet = () => {
  router.visit('/wallets/create')
}

const handleWalletAction = (action) => {
  console.log('Wallet action:', action)
}

const handleTransactionCancelled = (transactionId) => {
  // Remove cancelled transaction from the list
  const index = props.recentTransactions.findIndex(t => t.id === transactionId)
  if (index !== -1) {
    props.recentTransactions.splice(index, 1)
  }
}
</script>
