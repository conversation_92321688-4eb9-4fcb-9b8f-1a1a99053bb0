<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>تأكيد البريد الإلكتروني - نظام التحويلات المالية</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .verify-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            margin: 20px;
        }
        
        .verify-header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d6f 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .verify-body {
            padding: 40px;
            text-align: center;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .email-icon {
            font-size: 4rem;
            color: #2c5aa0;
            margin-bottom: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d6f 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }
        
        .btn-outline-secondary {
            border-radius: 12px;
            padding: 15px 30px;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
        }
        
        .verification-steps {
            text-align: right;
            margin: 30px 0;
        }
        
        .verification-step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .step-number {
            background: #2c5aa0;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-left: 15px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <!-- Header -->
        <div class="verify-header">
            <div class="logo">
                <i class="bi bi-currency-exchange me-2"></i>
                موني ترانسفير
            </div>
            <p class="mb-0">تأكيد البريد الإلكتروني</p>
        </div>
        
        <!-- Body -->
        <div class="verify-body">
            <div class="email-icon">
                <i class="bi bi-envelope-check"></i>
            </div>
            
            <h3 class="mb-3">تحقق من بريدك الإلكتروني</h3>
            
            <p class="text-muted mb-4">
                لقد أرسلنا رابط التأكيد إلى بريدك الإلكتروني:
                <br>
                <strong>{{ Auth::user()->email }}</strong>
            </p>
            
            <!-- Success Message -->
            @if (session('status') == 'verification-link-sent')
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    تم إرسال رابط تأكيد جديد إلى بريدك الإلكتروني
                </div>
            @endif
            
            <!-- Error Messages -->
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <!-- Verification Steps -->
            <div class="verification-steps">
                <div class="verification-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>تحقق من صندوق الوارد</strong>
                        <br>
                        <small class="text-muted">ابحث عن رسالة من موني ترانسفير</small>
                    </div>
                </div>
                
                <div class="verification-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>اضغط على رابط التأكيد</strong>
                        <br>
                        <small class="text-muted">اضغط على الرابط الموجود في الرسالة</small>
                    </div>
                </div>
                
                <div class="verification-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>ابدأ استخدام النظام</strong>
                        <br>
                        <small class="text-muted">ستتمكن من الوصول لجميع الميزات</small>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="d-grid gap-2 mb-4">
                <form method="POST" action="{{ route('verification.send') }}">
                    @csrf
                    <button type="submit" class="btn btn-primary w-100" id="resendBtn">
                        <span class="btn-text">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            إعادة إرسال رابط التأكيد
                        </span>
                        <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                    </button>
                </form>
                
                <button type="button" class="btn btn-outline-secondary w-100" onclick="checkVerification()">
                    <i class="bi bi-check-circle me-1"></i>
                    لقد قمت بالتأكيد بالفعل
                </button>
            </div>
            
            <!-- Help Section -->
            <div class="text-center">
                <p class="text-muted mb-2">لم تستلم الرسالة؟</p>
                <ul class="list-unstyled text-muted small">
                    <li>• تحقق من مجلد الرسائل غير المرغوب فيها (Spam)</li>
                    <li>• تأكد من صحة عنوان البريد الإلكتروني</li>
                    <li>• انتظر بضع دقائق ثم حاول مرة أخرى</li>
                </ul>
            </div>
            
            <!-- Logout -->
            <div class="text-center mt-4">
                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-link text-muted">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        تسجيل الخروج
                    </button>
                </form>
            </div>
            
            <!-- Skip Verification (for demo) -->
            <div class="text-center mt-3">
                <small class="text-muted">
                    للأغراض التجريبية: 
                    <a href="{{ route('dashboard.skip.verification') }}" class="text-decoration-none">
                        تخطي التحقق والمتابعة
                    </a>
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Handle resend form submission
        document.querySelector('form').addEventListener('submit', function() {
            const btn = document.getElementById('resendBtn');
            const btnText = btn.querySelector('.btn-text');
            const spinner = btn.querySelector('.spinner-border');
            
            btn.disabled = true;
            btnText.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>جاري الإرسال...';
            spinner.classList.remove('d-none');
        });
        
        // Check verification status
        function checkVerification() {
            fetch('/email/verify/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.verified) {
                    window.location.href = '/dashboard';
                } else {
                    alert('لم يتم التحقق من بريدك الإلكتروني بعد. يرجى التحقق من صندوق الوارد.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء التحقق من الحالة');
            });
        }
        
        // Auto-refresh verification status every 30 seconds
        setInterval(function() {
            fetch('/email/verify/status')
                .then(response => response.json())
                .then(data => {
                    if (data.verified) {
                        window.location.href = '/dashboard';
                    }
                })
                .catch(error => {
                    console.error('Auto-check error:', error);
                });
        }, 30000);
    </script>
</body>
</html>
