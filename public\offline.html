<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - نظام التحويلات المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .offline-icon {
            font-size: 5rem;
            color: #6c757d;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .offline-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        
        .features-list {
            text-align: right;
            margin-top: 2rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: #6c757d;
        }
        
        .feature-icon {
            color: #28a745;
            margin-left: 0.5rem;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-offline {
            background-color: #dc3545;
            color: white;
        }
        
        .status-online {
            background-color: #28a745;
            color: white;
        }
        
        .loading-spinner {
            display: none;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <div class="connection-status status-offline" id="connectionStatus">
        <i class="bi bi-wifi-off me-1"></i>
        غير متصل بالإنترنت
    </div>

    <div class="offline-container">
        <div class="offline-icon">
            <i class="bi bi-wifi-off"></i>
        </div>
        
        <h2 class="offline-title">غير متصل بالإنترنت</h2>
        
        <p class="offline-message">
            يبدو أنك غير متصل بالإنترنت حالياً. يرجى التحقق من اتصالك بالشبكة والمحاولة مرة أخرى.
        </p>
        
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
        
        <button class="btn retry-btn" onclick="retryConnection()">
            <i class="bi bi-arrow-clockwise me-1"></i>
            إعادة المحاولة
        </button>
        
        <div class="features-list">
            <h5 class="mb-3">الميزات المتاحة في وضع عدم الاتصال:</h5>
            <div class="feature-item">
                <i class="bi bi-check-circle feature-icon"></i>
                عرض التحويلات المحفوظة مسبقاً
            </div>
            <div class="feature-item">
                <i class="bi bi-check-circle feature-icon"></i>
                الوصول إلى معلومات الحساب
            </div>
            <div class="feature-item">
                <i class="bi bi-check-circle feature-icon"></i>
                حفظ التحويلات الجديدة للإرسال لاحقاً
            </div>
            <div class="feature-item">
                <i class="bi bi-check-circle feature-icon"></i>
                عرض أسعار الصرف المحفوظة
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusElement.innerHTML = '<i class="bi bi-wifi me-1"></i>متصل بالإنترنت';
                
                // Auto-retry when connection is restored
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                statusElement.className = 'connection-status status-offline';
                statusElement.innerHTML = '<i class="bi bi-wifi-off me-1"></i>غير متصل بالإنترنت';
            }
        }

        // Retry connection
        function retryConnection() {
            const loadingSpinner = document.getElementById('loadingSpinner');
            const retryBtn = document.querySelector('.retry-btn');
            
            loadingSpinner.style.display = 'block';
            retryBtn.disabled = true;
            retryBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>جاري المحاولة...';
            
            // Try to reload the page
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Register service worker if available
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered successfully');
                })
                .catch(error => {
                    console.log('Service Worker registration failed');
                });
        }

        // Handle back button
        window.addEventListener('popstate', function(event) {
            if (navigator.onLine) {
                window.location.href = '/dashboard';
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // F5 or Ctrl+R to retry
            if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
                event.preventDefault();
                retryConnection();
            }
            
            // Escape to go back
            if (event.key === 'Escape') {
                if (navigator.onLine) {
                    window.history.back();
                }
            }
        });

        // Auto-check connection every 5 seconds
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
