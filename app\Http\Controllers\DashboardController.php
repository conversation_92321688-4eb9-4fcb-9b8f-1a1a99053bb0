<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Services\MetricsService;
use App\Models\Transaction;
use App\Models\User;
use App\Models\FraudDetection;
use App\Models\Currency;
use App\Models\Wallet;
use Carbon\Carbon;

class DashboardController extends Controller
{
    protected MetricsService $metricsService;

    public function __construct(MetricsService $metricsService)
    {
        $this->metricsService = $metricsService;
    }

    /**
     * Get main dashboard overview.
     */
    public function overview(): JsonResponse
    {
        $overview = [
            'summary' => $this->getSummaryStats(),
            'real_time' => $this->getRealTimeStats(),
            'charts' => $this->getChartData(),
            'alerts' => $this->getActiveAlerts(),
            'recent_activities' => $this->getRecentActivities(),
        ];

        return response()->json([
            'success' => true,
            'data' => $overview,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get financial dashboard data.
     */
    public function financial(): JsonResponse
    {
        $financial = [
            'transactions' => $this->getTransactionStats(),
            'revenue' => $this->getRevenueStats(),
            'currencies' => $this->getCurrencyStats(),
            'wallets' => $this->getWalletStats(),
            'trends' => $this->getFinancialTrends(),
        ];

        return response()->json([
            'success' => true,
            'data' => $financial,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get security dashboard data.
     */
    public function security(): JsonResponse
    {
        $security = [
            'fraud_detection' => $this->getFraudDetectionStats(),
            'authentication' => $this->getAuthenticationStats(),
            'risk_assessment' => $this->getRiskAssessmentStats(),
            'compliance' => $this->getComplianceStats(),
            'incidents' => $this->getSecurityIncidents(),
        ];

        return response()->json([
            'success' => true,
            'data' => $security,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get system performance dashboard.
     */
    public function performance(): JsonResponse
    {
        $performance = [
            'system_health' => $this->getSystemHealth(),
            'response_times' => $this->getResponseTimeStats(),
            'throughput' => $this->getThroughputStats(),
            'resources' => $this->getResourceUsage(),
            'queue_status' => $this->getQueueStatus(),
        ];

        return response()->json([
            'success' => true,
            'data' => $performance,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get user analytics dashboard.
     */
    public function users(): JsonResponse
    {
        $users = [
            'overview' => $this->getUserOverview(),
            'registration_trends' => $this->getRegistrationTrends(),
            'activity_patterns' => $this->getActivityPatterns(),
            'demographics' => $this->getUserDemographics(),
            'engagement' => $this->getUserEngagement(),
        ];

        return response()->json([
            'success' => true,
            'data' => $users,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get real-time metrics.
     */
    public function realTime(): JsonResponse
    {
        $realTime = [
            'current_load' => $this->getCurrentLoad(),
            'active_sessions' => $this->getActiveSessions(),
            'live_transactions' => $this->getLiveTransactions(),
            'system_status' => $this->getSystemStatus(),
            'alerts' => $this->getLiveAlerts(),
        ];

        return response()->json([
            'success' => true,
            'data' => $realTime,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get summary statistics.
     */
    private function getSummaryStats(): array
    {
        return [
            'total_users' => User::count(),
            'active_users_today' => $this->getActiveUsersToday(),
            'total_transactions' => Transaction::count(),
            'transactions_today' => Transaction::whereDate('created_at', today())->count(),
            'total_volume_usd' => Transaction::where('status', 'completed')->sum('amount'),
            'revenue_today_usd' => Transaction::whereDate('created_at', today())
                ->where('status', 'completed')->sum('total_fees'),
            'success_rate' => $this->getTransactionSuccessRate(),
            'fraud_alerts_today' => FraudDetection::whereDate('detected_at', today())->count(),
        ];
    }

    /**
     * Get real-time statistics.
     */
    private function getRealTimeStats(): array
    {
        return [
            'requests_per_minute' => $this->getRequestsPerMinute(),
            'transactions_per_minute' => $this->getTransactionsPerMinute(),
            'active_sessions' => $this->getActiveSessionCount(),
            'queue_size' => $this->getQueueSize(),
            'error_rate' => $this->getErrorRate(),
            'response_time_ms' => $this->getAverageResponseTime(),
        ];
    }

    /**
     * Get chart data for dashboard.
     */
    private function getChartData(): array
    {
        return [
            'transaction_volume_24h' => $this->getTransactionVolumeChart(),
            'user_registrations_7d' => $this->getUserRegistrationChart(),
            'fraud_detection_trends' => $this->getFraudDetectionChart(),
            'system_performance_24h' => $this->getSystemPerformanceChart(),
        ];
    }

    /**
     * Get active alerts.
     */
    private function getActiveAlerts(): array
    {
        return [
            'critical' => $this->getCriticalAlerts(),
            'warnings' => $this->getWarningAlerts(),
            'info' => $this->getInfoAlerts(),
        ];
    }

    /**
     * Get recent activities.
     */
    private function getRecentActivities(): array
    {
        return [
            'transactions' => Transaction::with(['sender', 'currency'])
                ->latest()
                ->limit(10)
                ->get()
                ->map(function ($transaction) {
                    return [
                        'id' => $transaction->id,
                        'type' => $transaction->type,
                        'amount' => $transaction->amount,
                        'currency' => $transaction->currency->code,
                        'status' => $transaction->status,
                        'sender' => $transaction->sender->full_name,
                        'created_at' => $transaction->created_at,
                    ];
                }),
            'user_registrations' => User::latest()
                ->limit(5)
                ->get()
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->full_name,
                        'email' => $user->email,
                        'country' => $user->country->name ?? 'Unknown',
                        'created_at' => $user->created_at,
                    ];
                }),
            'fraud_alerts' => FraudDetection::with(['transaction', 'user'])
                ->latest()
                ->limit(5)
                ->get()
                ->map(function ($alert) {
                    return [
                        'id' => $alert->id,
                        'risk_level' => $alert->risk_level,
                        'risk_score' => $alert->risk_score,
                        'user' => $alert->user->full_name,
                        'transaction_amount' => $alert->transaction->amount,
                        'detected_at' => $alert->detected_at,
                    ];
                }),
        ];
    }

    /**
     * Get transaction statistics.
     */
    private function getTransactionStats(): array
    {
        $today = today();
        $yesterday = $today->copy()->subDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        return [
            'today' => [
                'count' => Transaction::whereDate('created_at', $today)->count(),
                'volume' => Transaction::whereDate('created_at', $today)
                    ->where('status', 'completed')->sum('amount'),
                'fees' => Transaction::whereDate('created_at', $today)
                    ->where('status', 'completed')->sum('total_fees'),
            ],
            'yesterday' => [
                'count' => Transaction::whereDate('created_at', $yesterday)->count(),
                'volume' => Transaction::whereDate('created_at', $yesterday)
                    ->where('status', 'completed')->sum('amount'),
            ],
            'this_week' => [
                'count' => Transaction::where('created_at', '>=', $thisWeek)->count(),
                'volume' => Transaction::where('created_at', '>=', $thisWeek)
                    ->where('status', 'completed')->sum('amount'),
            ],
            'this_month' => [
                'count' => Transaction::where('created_at', '>=', $thisMonth)->count(),
                'volume' => Transaction::where('created_at', '>=', $thisMonth)
                    ->where('status', 'completed')->sum('amount'),
            ],
            'by_status' => Transaction::selectRaw('status, COUNT(*) as count')
                ->whereDate('created_at', $today)
                ->groupBy('status')
                ->pluck('count', 'status'),
            'by_type' => Transaction::selectRaw('type, COUNT(*) as count')
                ->whereDate('created_at', $today)
                ->groupBy('type')
                ->pluck('count', 'type'),
        ];
    }

    /**
     * Get revenue statistics.
     */
    private function getRevenueStats(): array
    {
        return [
            'today' => Transaction::whereDate('created_at', today())
                ->where('status', 'completed')->sum('total_fees'),
            'this_week' => Transaction::where('created_at', '>=', now()->startOfWeek())
                ->where('status', 'completed')->sum('total_fees'),
            'this_month' => Transaction::where('created_at', '>=', now()->startOfMonth())
                ->where('status', 'completed')->sum('total_fees'),
            'this_year' => Transaction::where('created_at', '>=', now()->startOfYear())
                ->where('status', 'completed')->sum('total_fees'),
            'by_currency' => Transaction::join('currencies', 'transactions.currency_id', '=', 'currencies.id')
                ->selectRaw('currencies.code, SUM(total_fees) as revenue')
                ->whereDate('transactions.created_at', today())
                ->where('transactions.status', 'completed')
                ->groupBy('currencies.code')
                ->pluck('revenue', 'code'),
        ];
    }

    /**
     * Get currency statistics.
     */
    private function getCurrencyStats(): array
    {
        return [
            'active_currencies' => Currency::where('is_active', true)->count(),
            'crypto_currencies' => Currency::where('is_crypto', true)->where('is_active', true)->count(),
            'fiat_currencies' => Currency::where('is_crypto', false)->where('is_active', true)->count(),
            'most_used' => Transaction::join('currencies', 'transactions.currency_id', '=', 'currencies.id')
                ->selectRaw('currencies.code, COUNT(*) as usage_count')
                ->whereDate('transactions.created_at', today())
                ->groupBy('currencies.code')
                ->orderByDesc('usage_count')
                ->limit(5)
                ->pluck('usage_count', 'code'),
        ];
    }

    /**
     * Get wallet statistics.
     */
    private function getWalletStats(): array
    {
        return [
            'total_wallets' => Wallet::count(),
            'active_wallets' => Wallet::where('is_active', true)->count(),
            'total_balance' => Wallet::sum('balance'),
            'average_balance' => Wallet::avg('balance'),
            'low_balance_count' => Wallet::where('balance', '<', 100)->count(),
            'by_currency' => Wallet::join('currencies', 'wallets.currency_id', '=', 'currencies.id')
                ->selectRaw('currencies.code, COUNT(*) as wallet_count, SUM(balance) as total_balance')
                ->groupBy('currencies.code')
                ->get()
                ->keyBy('code'),
        ];
    }

    /**
     * Get financial trends.
     */
    private function getFinancialTrends(): array
    {
        $days = collect(range(6, 0))->map(function ($daysAgo) {
            $date = now()->subDays($daysAgo)->toDateString();
            return [
                'date' => $date,
                'transactions' => Transaction::whereDate('created_at', $date)->count(),
                'volume' => Transaction::whereDate('created_at', $date)
                    ->where('status', 'completed')->sum('amount'),
                'revenue' => Transaction::whereDate('created_at', $date)
                    ->where('status', 'completed')->sum('total_fees'),
            ];
        });

        return $days->toArray();
    }

    /**
     * Get fraud detection statistics.
     */
    private function getFraudDetectionStats(): array
    {
        return [
            'alerts_today' => FraudDetection::whereDate('detected_at', today())->count(),
            'high_risk_alerts' => FraudDetection::whereDate('detected_at', today())
                ->where('risk_level', 'high')->count(),
            'blocked_transactions' => Transaction::whereDate('created_at', today())
                ->where('status', 'blocked')->count(),
            'false_positive_rate' => $this->getFalsePositiveRate(),
            'detection_accuracy' => $this->getFraudDetectionAccuracy(),
            'by_risk_level' => FraudDetection::selectRaw('risk_level, COUNT(*) as count')
                ->whereDate('detected_at', today())
                ->groupBy('risk_level')
                ->pluck('count', 'risk_level'),
        ];
    }

    /**
     * Get authentication statistics.
     */
    private function getAuthenticationStats(): array
    {
        return [
            'successful_logins_today' => $this->getSuccessfulLoginsToday(),
            'failed_login_attempts' => $this->getFailedLoginAttemptsToday(),
            'blocked_accounts' => User::where('status', 'blocked')->count(),
            'two_factor_enabled' => User::whereNotNull('two_factor_secret')->count(),
            'password_resets_today' => $this->getPasswordResetsToday(),
        ];
    }

    /**
     * Get risk assessment statistics.
     */
    private function getRiskAssessmentStats(): array
    {
        return [
            'high_risk_users' => User::where('risk_level', 'high')->count(),
            'medium_risk_users' => User::where('risk_level', 'medium')->count(),
            'low_risk_users' => User::where('risk_level', 'low')->count(),
            'unverified_users' => User::whereNull('kyc_verified_at')->count(),
            'pending_kyc' => User::where('kyc_status', 'pending')->count(),
        ];
    }

    /**
     * Get compliance statistics.
     */
    private function getComplianceStats(): array
    {
        return [
            'kyc_completion_rate' => $this->getKycCompletionRate(),
            'aml_checks_today' => $this->getAmlChecksToday(),
            'suspicious_activity_reports' => $this->getSuspiciousActivityReports(),
            'compliance_violations' => $this->getComplianceViolations(),
        ];
    }

    /**
     * Get security incidents.
     */
    private function getSecurityIncidents(): array
    {
        return [
            'today' => $this->getSecurityIncidentsToday(),
            'this_week' => $this->getSecurityIncidentsThisWeek(),
            'by_severity' => $this->getIncidentsBySeverity(),
            'recent_incidents' => $this->getRecentSecurityIncidents(),
        ];
    }

    // Helper methods for calculations
    private function getActiveUsersToday(): int
    {
        return User::whereHas('auditLogs', function ($query) {
            $query->whereDate('created_at', today());
        })->count();
    }

    private function getTransactionSuccessRate(): float
    {
        $total = Transaction::whereDate('created_at', today())->count();
        $completed = Transaction::whereDate('created_at', today())
            ->where('status', 'completed')->count();
        
        return $total > 0 ? round(($completed / $total) * 100, 2) : 0;
    }

    private function getRequestsPerMinute(): int
    {
        // Mock implementation
        return rand(50, 200);
    }

    private function getTransactionsPerMinute(): int
    {
        return Transaction::where('created_at', '>=', now()->subMinute())->count();
    }

    private function getActiveSessionCount(): int
    {
        // Mock implementation
        return rand(100, 500);
    }

    private function getQueueSize(): int
    {
        // Mock implementation
        return rand(10, 100);
    }

    private function getErrorRate(): float
    {
        // Mock implementation
        return rand(1, 5) + (rand(0, 99) / 100);
    }

    private function getAverageResponseTime(): float
    {
        // Mock implementation
        return rand(200, 500) + (rand(0, 99) / 100);
    }

    private function getTransactionVolumeChart(): array
    {
        return collect(range(23, 0))->map(function ($hoursAgo) {
            $hour = now()->subHours($hoursAgo);
            return [
                'time' => $hour->format('H:i'),
                'volume' => Transaction::whereBetween('created_at', [
                    $hour->copy()->startOfHour(),
                    $hour->copy()->endOfHour()
                ])->where('status', 'completed')->sum('amount'),
                'count' => Transaction::whereBetween('created_at', [
                    $hour->copy()->startOfHour(),
                    $hour->copy()->endOfHour()
                ])->count(),
            ];
        })->toArray();
    }

    private function getUserRegistrationChart(): array
    {
        return collect(range(6, 0))->map(function ($daysAgo) {
            $date = now()->subDays($daysAgo);
            return [
                'date' => $date->format('M d'),
                'registrations' => User::whereDate('created_at', $date)->count(),
            ];
        })->toArray();
    }

    private function getFraudDetectionChart(): array
    {
        return collect(range(6, 0))->map(function ($daysAgo) {
            $date = now()->subDays($daysAgo);
            return [
                'date' => $date->format('M d'),
                'alerts' => FraudDetection::whereDate('detected_at', $date)->count(),
                'blocked' => Transaction::whereDate('created_at', $date)
                    ->where('status', 'blocked')->count(),
            ];
        })->toArray();
    }

    private function getSystemPerformanceChart(): array
    {
        return collect(range(23, 0))->map(function ($hoursAgo) {
            $hour = now()->subHours($hoursAgo);
            return [
                'time' => $hour->format('H:i'),
                'cpu_usage' => rand(20, 80),
                'memory_usage' => rand(30, 70),
                'response_time' => rand(200, 600),
            ];
        })->toArray();
    }

    private function getCriticalAlerts(): array
    {
        // Mock implementation
        return [
            ['message' => 'High fraud alert volume detected', 'time' => now()->subMinutes(5)],
            ['message' => 'Database connection pool near capacity', 'time' => now()->subMinutes(15)],
        ];
    }

    private function getWarningAlerts(): array
    {
        // Mock implementation
        return [
            ['message' => 'Queue size above normal threshold', 'time' => now()->subMinutes(10)],
            ['message' => 'Response time degradation detected', 'time' => now()->subMinutes(20)],
        ];
    }

    private function getInfoAlerts(): array
    {
        // Mock implementation
        return [
            ['message' => 'Scheduled maintenance reminder', 'time' => now()->subHours(1)],
            ['message' => 'New currency rate update available', 'time' => now()->subHours(2)],
        ];
    }

    private function getCurrentLoad(): array
    {
        return [
            'cpu_percentage' => rand(20, 80),
            'memory_percentage' => rand(30, 70),
            'disk_percentage' => rand(40, 60),
            'network_io' => rand(10, 50),
        ];
    }

    private function getActiveSessions(): int
    {
        return $this->getActiveSessionCount();
    }

    private function getLiveTransactions(): array
    {
        return Transaction::with(['sender', 'currency'])
            ->where('created_at', '>=', now()->subMinutes(5))
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency->code,
                    'status' => $transaction->status,
                    'created_at' => $transaction->created_at,
                ];
            })
            ->toArray();
    }

    private function getSystemStatus(): array
    {
        return [
            'database' => 'healthy',
            'cache' => 'healthy',
            'queue' => 'healthy',
            'storage' => 'healthy',
            'external_apis' => 'healthy',
        ];
    }

    private function getLiveAlerts(): array
    {
        return FraudDetection::where('detected_at', '>=', now()->subMinutes(30))
            ->where('status', 'open')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($alert) {
                return [
                    'id' => $alert->id,
                    'risk_level' => $alert->risk_level,
                    'risk_score' => $alert->risk_score,
                    'detected_at' => $alert->detected_at,
                ];
            })
            ->toArray();
    }

    // Additional helper methods
    private function getFalsePositiveRate(): float
    {
        return rand(5, 15) + (rand(0, 99) / 100);
    }

    private function getFraudDetectionAccuracy(): float
    {
        return rand(90, 98) + (rand(0, 99) / 100);
    }

    private function getSuccessfulLoginsToday(): int
    {
        return rand(500, 2000);
    }

    private function getFailedLoginAttemptsToday(): int
    {
        return rand(20, 100);
    }

    private function getPasswordResetsToday(): int
    {
        return rand(5, 30);
    }

    private function getKycCompletionRate(): float
    {
        $total = User::count();
        $verified = User::whereNotNull('kyc_verified_at')->count();
        
        return $total > 0 ? round(($verified / $total) * 100, 2) : 0;
    }

    private function getAmlChecksToday(): int
    {
        return rand(100, 500);
    }

    private function getSuspiciousActivityReports(): int
    {
        return rand(5, 20);
    }

    private function getComplianceViolations(): int
    {
        return rand(0, 5);
    }

    private function getSecurityIncidentsToday(): int
    {
        return rand(0, 10);
    }

    private function getSecurityIncidentsThisWeek(): int
    {
        return rand(5, 50);
    }

    private function getIncidentsBySeverity(): array
    {
        return [
            'critical' => rand(0, 2),
            'high' => rand(2, 8),
            'medium' => rand(5, 15),
            'low' => rand(10, 30),
        ];
    }

    private function getRecentSecurityIncidents(): array
    {
        return [
            ['type' => 'Failed login attempts', 'severity' => 'medium', 'time' => now()->subMinutes(30)],
            ['type' => 'Suspicious transaction pattern', 'severity' => 'high', 'time' => now()->subHours(2)],
            ['type' => 'Unauthorized API access attempt', 'severity' => 'medium', 'time' => now()->subHours(4)],
        ];
    }

    private function getSystemHealth(): array
    {
        return [
            'overall_score' => rand(85, 98),
            'components' => [
                'database' => rand(90, 100),
                'cache' => rand(85, 95),
                'queue' => rand(80, 90),
                'storage' => rand(90, 100),
                'external_apis' => rand(75, 90),
            ],
        ];
    }

    private function getResponseTimeStats(): array
    {
        return [
            'average_ms' => rand(200, 400),
            'p95_ms' => rand(500, 800),
            'p99_ms' => rand(800, 1200),
            'trend' => 'stable',
        ];
    }

    private function getThroughputStats(): array
    {
        return [
            'requests_per_second' => rand(50, 200),
            'transactions_per_second' => rand(5, 20),
            'peak_rps' => rand(300, 500),
            'trend' => 'increasing',
        ];
    }

    private function getResourceUsage(): array
    {
        return [
            'cpu_percentage' => rand(30, 70),
            'memory_percentage' => rand(40, 80),
            'disk_percentage' => rand(20, 60),
            'network_mbps' => rand(10, 100),
        ];
    }

    private function getQueueStatus(): array
    {
        return [
            'pending_jobs' => rand(10, 100),
            'failed_jobs' => rand(0, 10),
            'processing_rate' => rand(50, 200),
            'average_wait_time_ms' => rand(100, 500),
        ];
    }

    private function getUserOverview(): array
    {
        return [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'verified_users' => User::whereNotNull('kyc_verified_at')->count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'growth_rate' => rand(5, 15) + (rand(0, 99) / 100),
        ];
    }

    private function getRegistrationTrends(): array
    {
        return collect(range(29, 0))->map(function ($daysAgo) {
            $date = now()->subDays($daysAgo);
            return [
                'date' => $date->format('M d'),
                'registrations' => User::whereDate('created_at', $date)->count(),
            ];
        })->toArray();
    }

    private function getActivityPatterns(): array
    {
        return [
            'peak_hours' => [
                ['hour' => '09:00', 'activity' => rand(80, 100)],
                ['hour' => '14:00', 'activity' => rand(70, 90)],
                ['hour' => '20:00', 'activity' => rand(60, 80)],
            ],
            'weekly_pattern' => [
                'monday' => rand(70, 90),
                'tuesday' => rand(75, 95),
                'wednesday' => rand(80, 100),
                'thursday' => rand(75, 95),
                'friday' => rand(60, 80),
                'saturday' => rand(40, 60),
                'sunday' => rand(30, 50),
            ],
        ];
    }

    private function getUserDemographics(): array
    {
        return [
            'by_country' => [
                'Saudi Arabia' => rand(40, 60),
                'UAE' => rand(15, 25),
                'Egypt' => rand(10, 20),
                'Jordan' => rand(5, 15),
                'Others' => rand(5, 10),
            ],
            'by_age_group' => [
                '18-25' => rand(20, 30),
                '26-35' => rand(35, 45),
                '36-45' => rand(20, 30),
                '46-55' => rand(10, 20),
                '55+' => rand(5, 15),
            ],
            'by_user_type' => [
                'individual' => rand(80, 90),
                'business' => rand(10, 20),
            ],
        ];
    }

    private function getUserEngagement(): array
    {
        return [
            'daily_active_users' => rand(1000, 5000),
            'weekly_active_users' => rand(5000, 15000),
            'monthly_active_users' => rand(15000, 50000),
            'average_session_duration' => rand(15, 45),
            'bounce_rate' => rand(20, 40),
        ];
    }
}
