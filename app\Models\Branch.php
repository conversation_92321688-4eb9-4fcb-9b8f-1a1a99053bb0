<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Branch extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'code',
        'country_id',
        'city_ar',
        'city_en',
        'address_ar',
        'address_en',
        'phone',
        'email',
        'latitude',
        'longitude',
        'is_active',
        'is_main_branch',
        'working_hours',
        'services',
        'cash_limit',
        'supported_currencies',
        'manager_name',
        'manager_phone',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_active' => 'boolean',
        'is_main_branch' => 'boolean',
        'working_hours' => 'array',
        'services' => 'array',
        'cash_limit' => 'decimal:2',
        'supported_currencies' => 'array',
    ];

    /**
     * Get the country that the branch belongs to.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the users assigned to this branch.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the transactions processed at this branch.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the branch name based on current locale.
     */
    public function getNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Get the city name based on current locale.
     */
    public function getCityAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->city_ar : $this->city_en;
    }

    /**
     * Get the address based on current locale.
     */
    public function getAddressAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->address_ar : $this->address_en;
    }

    /**
     * Scope a query to only include active branches.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include main branches.
     */
    public function scopeMain($query)
    {
        return $query->where('is_main_branch', true);
    }

    /**
     * Check if branch supports a currency.
     */
    public function supportsCurrency(string $currencyCode): bool
    {
        return in_array($currencyCode, $this->supported_currencies ?? []);
    }

    /**
     * Check if branch offers a service.
     */
    public function offersService(string $service): bool
    {
        return in_array($service, $this->services ?? []);
    }

    /**
     * Get distance from coordinates.
     */
    public function getDistanceFrom(float $latitude, float $longitude): float
    {
        if (!$this->latitude || !$this->longitude) {
            return 0;
        }

        $earthRadius = 6371; // km

        $latDelta = deg2rad($latitude - $this->latitude);
        $lonDelta = deg2rad($longitude - $this->longitude);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($this->latitude)) * cos(deg2rad($latitude)) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}
