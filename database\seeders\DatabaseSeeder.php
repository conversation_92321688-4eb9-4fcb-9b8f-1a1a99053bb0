<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Transaction;
use App\Models\Wallet;
use App\Models\PaymentGateway;
use App\Models\ExchangeRate;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run system settings and basic data first
        $this->call([
            SystemSettingsSeeder::class,
            CountriesSeeder::class,
            CurrenciesSeeder::class,
            ExchangeRatesSeeder::class,
        ]);

        // Create test users
        User::factory()->create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'role' => 'admin',
            'kyc_level' => 'enhanced',
            'kyc_status' => 'approved',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        User::factory()->create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966507654321',
            'role' => 'user',
            'kyc_level' => 'basic',
            'kyc_status' => 'pending',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create additional test data if in development
        if (app()->environment('local', 'development')) {
            // Create more test users
            User::factory(10)->create();

            // Create test transactions
            Transaction::factory(20)->create();

            // Create test wallets for users
            $users = User::all();
            foreach ($users as $user) {
                Wallet::factory(2)->create(['user_id' => $user->id]);
            }

            // Create test payment gateways
            PaymentGateway::factory(5)->active()->create();
        }
    }
}
