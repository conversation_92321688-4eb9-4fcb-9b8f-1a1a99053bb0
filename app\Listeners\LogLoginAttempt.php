<?php

namespace App\Listeners;

use App\Events\LoginAttempt;
use App\Models\AuditLog;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class LogLoginAttempt implements ShouldQueue
{
    use InteractsWithQueue;

    protected NotificationService $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     */
    public function handle(LoginAttempt $event): void
    {
        try {
            $this->logAttempt($event);
            
            if ($event->successful) {
                $this->handleSuccessfulLogin($event);
            } else {
                $this->handleFailedLogin($event);
            }

        } catch (\Exception $e) {
            Log::error('Failed to log login attempt', [
                'email' => $event->email,
                'successful' => $event->successful,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Log the login attempt.
     */
    protected function logAttempt(LoginAttempt $event): void
    {
        $severity = $event->successful ? 'info' : 'warning';
        
        // Increase severity for suspicious attempts
        if ($event->attemptData['is_suspicious'] ?? false) {
            $severity = 'error';
        }

        AuditLog::create([
            'user_id' => $event->user?->id,
            'event_type' => 'login_attempt',
            'action' => $event->successful ? 'login_success' : 'login_failed',
            'description' => $this->buildDescription($event),
            'ip_address' => $event->attemptData['ip_address'] ?? request()->ip(),
            'user_agent' => $event->attemptData['user_agent'] ?? request()->userAgent(),
            'session_id' => session()->getId(),
            'severity' => $severity,
            'metadata' => [
                'email' => $event->email,
                'successful' => $event->successful,
                'country' => $event->attemptData['country'] ?? null,
                'city' => $event->attemptData['city'] ?? null,
                'device_type' => $event->attemptData['device_type'] ?? null,
                'is_suspicious' => $event->attemptData['is_suspicious'] ?? false,
                'is_new_device' => $event->attemptData['is_new_device'] ?? false,
                'failure_reason' => $event->attemptData['failure_reason'] ?? null,
                'timestamp' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Handle successful login.
     */
    protected function handleSuccessfulLogin(LoginAttempt $event): void
    {
        if (!$event->user) {
            return;
        }

        // Update user's last login information
        $event->user->update([
            'last_login_at' => now(),
            'last_login_ip' => $event->attemptData['ip_address'] ?? request()->ip(),
            'login_history' => $this->updateLoginHistory($event->user, $event->attemptData),
        ]);

        // Send notification for new device login
        if ($event->attemptData['is_new_device'] ?? false) {
            $this->sendNewDeviceNotification($event);
        }

        // Send notification for suspicious login
        if ($event->attemptData['is_suspicious'] ?? false) {
            $this->sendSuspiciousLoginNotification($event);
        }

        Log::info('User logged in successfully', [
            'user_id' => $event->user->id,
            'email' => $event->email,
            'ip_address' => $event->attemptData['ip_address'] ?? request()->ip(),
            'is_new_device' => $event->attemptData['is_new_device'] ?? false,
            'is_suspicious' => $event->attemptData['is_suspicious'] ?? false,
        ]);
    }

    /**
     * Handle failed login.
     */
    protected function handleFailedLogin(LoginAttempt $event): void
    {
        Log::warning('Login attempt failed', [
            'email' => $event->email,
            'ip_address' => $event->attemptData['ip_address'] ?? request()->ip(),
            'failure_reason' => $event->attemptData['failure_reason'] ?? 'Invalid credentials',
            'is_suspicious' => $event->attemptData['is_suspicious'] ?? false,
        ]);

        // Check for brute force attempts
        $this->checkBruteForceAttempts($event);

        // Send security alert for suspicious failed attempts
        if ($event->attemptData['is_suspicious'] ?? false) {
            $this->sendFailedLoginSecurityAlert($event);
        }
    }

    /**
     * Build description for audit log.
     */
    protected function buildDescription(LoginAttempt $event): string
    {
        $action = $event->successful ? 'successful login' : 'failed login attempt';
        $location = $event->attemptData['country'] ?? 'Unknown location';
        
        $description = "User {$action} from {$location}";
        
        if ($event->attemptData['is_new_device'] ?? false) {
            $description .= ' (new device)';
        }
        
        if ($event->attemptData['is_suspicious'] ?? false) {
            $description .= ' (suspicious)';
        }
        
        if (!$event->successful && isset($event->attemptData['failure_reason'])) {
            $description .= '. Reason: ' . $event->attemptData['failure_reason'];
        }

        return $description;
    }

    /**
     * Update user's login history.
     */
    protected function updateLoginHistory($user, array $attemptData): array
    {
        $loginHistory = $user->login_history ?? [];
        
        $newEntry = [
            'timestamp' => now()->toISOString(),
            'ip_address' => $attemptData['ip_address'] ?? request()->ip(),
            'user_agent' => $attemptData['user_agent'] ?? request()->userAgent(),
            'country' => $attemptData['country'] ?? null,
            'city' => $attemptData['city'] ?? null,
            'device_type' => $attemptData['device_type'] ?? null,
            'is_new_device' => $attemptData['is_new_device'] ?? false,
        ];

        // Add new entry and keep only last 50 entries
        array_unshift($loginHistory, $newEntry);
        return array_slice($loginHistory, 0, 50);
    }

    /**
     * Send new device notification.
     */
    protected function sendNewDeviceNotification(LoginAttempt $event): void
    {
        $this->notificationService->sendSecurityAlert($event->user, 'login_from_new_device', [
            'ip_address' => $event->attemptData['ip_address'] ?? request()->ip(),
            'location' => $event->attemptData['country'] ?? 'Unknown',
            'device_type' => $event->attemptData['device_type'] ?? 'Unknown',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Send suspicious login notification.
     */
    protected function sendSuspiciousLoginNotification(LoginAttempt $event): void
    {
        $this->notificationService->sendSecurityAlert($event->user, 'suspicious_activity', [
            'activity_type' => 'suspicious_login',
            'ip_address' => $event->attemptData['ip_address'] ?? request()->ip(),
            'location' => $event->attemptData['country'] ?? 'Unknown',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Check for brute force attempts.
     */
    protected function checkBruteForceAttempts(LoginAttempt $event): void
    {
        $ipAddress = $event->attemptData['ip_address'] ?? request()->ip();
        
        // Count failed attempts from this IP in the last hour
        $recentFailures = AuditLog::where('event_type', 'login_attempt')
                                 ->where('action', 'login_failed')
                                 ->where('ip_address', $ipAddress)
                                 ->where('created_at', '>=', now()->subHour())
                                 ->count();

        if ($recentFailures >= 5) { // 5 failed attempts in 1 hour
            Log::alert('Potential brute force attack detected', [
                'ip_address' => $ipAddress,
                'failed_attempts' => $recentFailures,
                'email' => $event->email,
            ]);

            // Send alert to security team
            $this->sendBruteForceAlert($event, $recentFailures);
        }
    }

    /**
     * Send brute force alert.
     */
    protected function sendBruteForceAlert(LoginAttempt $event, int $attemptCount): void
    {
        // This would send an alert to admin/security team
        Log::alert('Brute force attack alert sent', [
            'ip_address' => $event->attemptData['ip_address'] ?? request()->ip(),
            'attempt_count' => $attemptCount,
            'target_email' => $event->email,
        ]);
    }

    /**
     * Send security alert for failed login.
     */
    protected function sendFailedLoginSecurityAlert(LoginAttempt $event): void
    {
        if ($event->user) {
            $this->notificationService->sendSecurityAlert($event->user, 'suspicious_activity', [
                'activity_type' => 'failed_login_attempt',
                'ip_address' => $event->attemptData['ip_address'] ?? request()->ip(),
                'location' => $event->attemptData['country'] ?? 'Unknown',
                'timestamp' => now()->toISOString(),
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(LoginAttempt $event, \Throwable $exception): void
    {
        Log::error('LogLoginAttempt listener failed', [
            'email' => $event->email,
            'successful' => $event->successful,
            'error' => $exception->getMessage(),
        ]);
    }
}
