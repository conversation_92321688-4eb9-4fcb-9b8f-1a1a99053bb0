<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Services\BlockchainService;
use App\Models\Currency;

class UpdateCryptoPricesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'crypto:update-prices 
                            {--currency=all : Specific cryptocurrency to update (BTC, ETH, etc.)}
                            {--force : Force update even if recently updated}
                            {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     */
    protected $description = 'Update cryptocurrency prices from external APIs';

    protected BlockchainService $blockchainService;

    public function __construct(BlockchainService $blockchainService)
    {
        parent::__construct();
        $this->blockchainService = $blockchainService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $currency = $this->option('currency');
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        $this->info("Starting cryptocurrency prices update...");
        
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No changes will be made");
        }

        try {
            if ($currency === 'all') {
                $result = $this->updateAllCryptocurrencies($dryRun);
            } else {
                $result = $this->updateSpecificCryptocurrency($currency, $dryRun);
            }

            $this->displayResults($result);

            Log::info('Cryptocurrency prices update completed', [
                'currency' => $currency,
                'result' => $result,
                'dry_run' => $dryRun,
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->error("Cryptocurrency prices update failed: " . $e->getMessage());
            Log::error('Cryptocurrency prices update failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return 1;
        }
    }

    /**
     * Update all cryptocurrencies.
     */
    protected function updateAllCryptocurrencies(bool $dryRun): array
    {
        if ($dryRun) {
            $cryptocurrencies = Currency::where('is_crypto', true)
                ->where('is_active', true)
                ->pluck('code')
                ->toArray();

            $mockResults = [];
            foreach ($cryptocurrencies as $crypto) {
                $mockResults[$crypto] = [
                    'status' => 'dry_run',
                    'price' => 'N/A',
                    'change_24h' => 'N/A',
                ];
            }

            return $mockResults;
        }

        return $this->blockchainService->updateAllCryptocurrencyPrices();
    }

    /**
     * Update specific cryptocurrency.
     */
    protected function updateSpecificCryptocurrency(string $currency, bool $dryRun): array
    {
        $crypto = Currency::where('code', strtoupper($currency))
            ->where('is_crypto', true)
            ->first();

        if (!$crypto) {
            throw new \Exception("Cryptocurrency not found: {$currency}");
        }

        if ($dryRun) {
            return [
                $currency => [
                    'status' => 'dry_run',
                    'price' => 'N/A',
                    'change_24h' => 'N/A',
                ]
            ];
        }

        try {
            $priceData = $this->blockchainService->getCryptocurrencyPrice($currency);
            
            $crypto->update([
                'rate_to_usd' => $priceData['price_usd'],
                'last_updated' => now(),
            ]);

            return [
                $currency => [
                    'status' => 'success',
                    'price' => $priceData['price_usd'],
                    'change_24h' => $priceData['change_24h'],
                ]
            ];

        } catch (\Exception $e) {
            return [
                $currency => [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ]
            ];
        }
    }

    /**
     * Display update results.
     */
    protected function displayResults(array $results): void
    {
        $this->info("\n=== Cryptocurrency Prices Update Results ===");
        
        $successCount = 0;
        $errorCount = 0;

        foreach ($results as $currency => $result) {
            $status = $result['status'];
            $icon = match($status) {
                'success' => '✅',
                'error' => '❌',
                'dry_run' => '⚠️',
                default => '❓'
            };
            
            $this->line("{$icon} {$currency}: {$status}");
            
            if (isset($result['price']) && $result['price'] !== 'N/A') {
                $this->line("   Price: $" . number_format($result['price'], 2));
            }
            
            if (isset($result['change_24h']) && $result['change_24h'] !== 'N/A') {
                $change = $result['change_24h'];
                $changeColor = $change >= 0 ? 'info' : 'error';
                $changeSymbol = $change >= 0 ? '+' : '';
                $this->line("   24h Change: {$changeSymbol}" . number_format($change, 2) . "%");
            }
            
            if (isset($result['error'])) {
                $this->line("   Error: {$result['error']}");
                $errorCount++;
            } else if ($status === 'success') {
                $successCount++;
            }
        }
        
        $this->line("");
        $this->info("Summary: {$successCount} successful, {$errorCount} errors");
    }
}
