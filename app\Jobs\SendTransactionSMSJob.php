<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\SMSService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendTransactionSMSJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected User $user;
    protected array $data;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, array $data)
    {
        $this->user = $user;
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(SMSService $smsService): void
    {
        try {
            if (!$this->user->phone) {
                Log::warning('User has no phone number for SMS', [
                    'user_id' => $this->user->id,
                ]);
                return;
            }

            $message = $this->buildSMSMessage();
            
            $result = $smsService->sendSMS(
                $this->user->phone,
                $message,
                $this->data['type'] ?? 'transaction'
            );

            if ($result['success']) {
                Log::info('SMS sent successfully', [
                    'user_id' => $this->user->id,
                    'phone' => $this->user->phone,
                    'type' => $this->data['type'] ?? 'transaction',
                    'message_id' => $result['message_id'] ?? null,
                ]);
            } else {
                Log::error('Failed to send SMS', [
                    'user_id' => $this->user->id,
                    'phone' => $this->user->phone,
                    'error' => $result['error'] ?? 'Unknown error',
                ]);
            }

        } catch (\Exception $e) {
            Log::error('SMS job failed', [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
                'data' => $this->data,
            ]);
            
            throw $e;
        }
    }

    /**
     * Build SMS message based on type.
     */
    protected function buildSMSMessage(): string
    {
        $type = $this->data['type'] ?? 'transaction';
        
        return match ($type) {
            'transaction_created' => $this->buildTransactionCreatedMessage(),
            'transaction_completed' => $this->buildTransactionCompletedMessage(),
            'transaction_failed' => $this->buildTransactionFailedMessage(),
            'transaction_blocked' => $this->buildTransactionBlockedMessage(),
            'fraud_alert' => $this->buildFraudAlertMessage(),
            'security_alert' => $this->buildSecurityAlertMessage(),
            'kyc_required' => $this->buildKYCRequiredMessage(),
            'verification_code' => $this->buildVerificationCodeMessage(),
            default => $this->buildGenericMessage(),
        };
    }

    /**
     * Build transaction created SMS message.
     */
    protected function buildTransactionCreatedMessage(): string
    {
        $amount = $this->data['amount'] ?? '';
        $currency = $this->data['currency'] ?? '';
        $reference = $this->data['reference_number'] ?? '';
        
        return "Mony Transfer: تم إنشاء معاملة جديدة بقيمة {$amount} {$currency}. رقم المرجع: {$reference}";
    }

    /**
     * Build transaction completed SMS message.
     */
    protected function buildTransactionCompletedMessage(): string
    {
        $amount = $this->data['amount'] ?? '';
        $currency = $this->data['currency'] ?? '';
        $reference = $this->data['reference_number'] ?? '';
        
        return "Mony Transfer: تمت معاملتك بنجاح! المبلغ: {$amount} {$currency}. رقم المرجع: {$reference}";
    }

    /**
     * Build transaction failed SMS message.
     */
    protected function buildTransactionFailedMessage(): string
    {
        $amount = $this->data['amount'] ?? '';
        $currency = $this->data['currency'] ?? '';
        $reason = $this->data['reason'] ?? 'خطأ في النظام';
        
        return "Mony Transfer: فشلت معاملتك بقيمة {$amount} {$currency}. السبب: {$reason}";
    }

    /**
     * Build transaction blocked SMS message.
     */
    protected function buildTransactionBlockedMessage(): string
    {
        $amount = $this->data['amount'] ?? '';
        $currency = $this->data['currency'] ?? '';
        
        return "Mony Transfer: تم حظر معاملتك بقيمة {$amount} {$currency} لأسباب أمنية. يرجى التواصل معنا.";
    }

    /**
     * Build fraud alert SMS message.
     */
    protected function buildFraudAlertMessage(): string
    {
        $transactionId = $this->data['transaction_id'] ?? '';
        
        return "Mony Transfer: تنبيه أمني! تم اكتشاف نشاط مشبوه في حسابك. المعاملة: {$transactionId}. يرجى التواصل معنا فوراً.";
    }

    /**
     * Build security alert SMS message.
     */
    protected function buildSecurityAlertMessage(): string
    {
        $alertType = $this->data['alert_type'] ?? 'نشاط مشبوه';
        
        return "Mony Transfer: تنبيه أمني! {$alertType}. إذا لم تكن أنت، يرجى تغيير كلمة المرور فوراً.";
    }

    /**
     * Build KYC required SMS message.
     */
    protected function buildKYCRequiredMessage(): string
    {
        return "Mony Transfer: يرجى إكمال التحقق من الهوية (KYC) لمتابعة استخدام خدماتنا.";
    }

    /**
     * Build verification code SMS message.
     */
    protected function buildVerificationCodeMessage(): string
    {
        $code = $this->data['verification_code'] ?? '';
        
        return "Mony Transfer: رمز التحقق الخاص بك: {$code}. لا تشارك هذا الرمز مع أحد.";
    }

    /**
     * Build generic SMS message.
     */
    protected function buildGenericMessage(): string
    {
        $title = $this->data['title'] ?? 'إشعار من Mony Transfer';
        $message = $this->data['message'] ?? '';
        
        return "Mony Transfer: {$title}. {$message}";
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SendTransactionSMSJob failed permanently', [
            'user_id' => $this->user->id,
            'phone' => $this->user->phone,
            'data' => $this->data,
            'error' => $exception->getMessage(),
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'sms',
            'notification',
            'user:' . $this->user->id,
            'type:' . ($this->data['type'] ?? 'unknown'),
        ];
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(5);
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [10, 30, 60]; // Retry after 10s, 30s, then 60s
    }
}
