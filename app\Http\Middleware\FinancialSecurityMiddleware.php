<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class FinancialSecurityMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check rate limiting
        if ($this->isRateLimited($request)) {
            Log::warning('Rate limit exceeded', [
                'ip' => $request->ip(),
                'user_id' => $request->user()?->id,
                'route' => $request->route()?->getName(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Too many requests. Please try again later.',
                'error_code' => 'RATE_LIMIT_EXCEEDED',
            ], 429);
        }

        // Check for suspicious patterns
        if ($this->isSuspiciousRequest($request)) {
            Log::warning('Suspicious request detected', [
                'ip' => $request->ip(),
                'user_id' => $request->user()?->id,
                'route' => $request->route()?->getName(),
                'user_agent' => $request->userAgent(),
                'payload' => $request->all(),
            ]);

            // Don't block immediately, but log for analysis
        }

        // Check IP whitelist for admin routes
        if ($this->isAdminRoute($request) && !$this->isWhitelistedIP($request)) {
            Log::critical('Unauthorized admin access attempt', [
                'ip' => $request->ip(),
                'user_id' => $request->user()?->id,
                'route' => $request->route()?->getName(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Access denied.',
                'error_code' => 'ACCESS_DENIED',
            ], 403);
        }

        // Log financial operations
        if ($this->isFinancialOperation($request)) {
            Log::channel('financial')->info('Financial operation', [
                'user_id' => $request->user()?->id,
                'ip' => $request->ip(),
                'route' => $request->route()?->getName(),
                'method' => $request->method(),
                'timestamp' => now(),
            ]);
        }

        return $next($request);
    }

    /**
     * Check if request is rate limited.
     */
    private function isRateLimited(Request $request): bool
    {
        $key = $this->getRateLimitKey($request);
        $maxAttempts = $this->getMaxAttempts($request);
        $decayMinutes = $this->getDecayMinutes($request);

        return RateLimiter::tooManyAttempts($key, $maxAttempts);
    }

    /**
     * Get rate limit key.
     */
    private function getRateLimitKey(Request $request): string
    {
        $user = $request->user();
        $route = $request->route()?->getName();

        if ($user) {
            return "user:{$user->id}:route:{$route}";
        }

        return "ip:{$request->ip()}:route:{$route}";
    }

    /**
     * Get max attempts based on route.
     */
    private function getMaxAttempts(Request $request): int
    {
        $route = $request->route()?->getName();

        $limits = [
            'auth.login' => 5,
            'auth.register' => 3,
            'transactions.create' => 10,
            'transactions.send' => 5,
            'wallets.transfer' => 10,
            'admin.*' => 20,
        ];

        foreach ($limits as $pattern => $limit) {
            if (fnmatch($pattern, $route)) {
                return $limit;
            }
        }

        return 60; // Default limit
    }

    /**
     * Get decay minutes based on route.
     */
    private function getDecayMinutes(Request $request): int
    {
        $route = $request->route()?->getName();

        if (str_starts_with($route, 'auth.')) {
            return 15; // 15 minutes for auth routes
        }

        if (str_starts_with($route, 'transactions.')) {
            return 5; // 5 minutes for transaction routes
        }

        return 1; // 1 minute default
    }

    /**
     * Check if request is suspicious.
     */
    private function isSuspiciousRequest(Request $request): bool
    {
        // Check for SQL injection patterns
        $payload = json_encode($request->all());
        $sqlPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
            '/update\s+set/i',
            '/exec\s*\(/i',
            '/script\s*>/i',
        ];

        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $payload)) {
                return true;
            }
        }

        // Check for unusual user agent
        $userAgent = $request->userAgent();
        $suspiciousAgents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'curl',
            'wget',
            'python-requests',
        ];

        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                return true;
            }
        }

        // Check for unusual request patterns
        if ($this->hasUnusualRequestPattern($request)) {
            return true;
        }

        return false;
    }

    /**
     * Check for unusual request patterns.
     */
    private function hasUnusualRequestPattern(Request $request): bool
    {
        // Check for too many parameters
        if (count($request->all()) > 50) {
            return true;
        }

        // Check for very long parameter values
        foreach ($request->all() as $value) {
            if (is_string($value) && strlen($value) > 10000) {
                return true;
            }
        }

        // Check for binary data in unexpected places
        foreach ($request->all() as $value) {
            if (is_string($value) && !mb_check_encoding($value, 'UTF-8')) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if route is admin route.
     */
    private function isAdminRoute(Request $request): bool
    {
        $route = $request->route()?->getName();
        return str_starts_with($route, 'admin.') || 
               str_contains($request->path(), '/admin/');
    }

    /**
     * Check if IP is whitelisted for admin access.
     */
    private function isWhitelistedIP(Request $request): bool
    {
        $whitelist = config('financial.admin_ip_whitelist', [
            '127.0.0.1',
            '::1',
            '***********/24',
            '10.0.0.0/8',
        ]);

        $clientIP = $request->ip();

        foreach ($whitelist as $allowedIP) {
            if ($this->ipInRange($clientIP, $allowedIP)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP is in range.
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $mask) = explode('/', $range);
        
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return $this->ipv4InRange($ip, $subnet, $mask);
        }

        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            return $this->ipv6InRange($ip, $subnet, $mask);
        }

        return false;
    }

    /**
     * Check if IPv4 is in range.
     */
    private function ipv4InRange(string $ip, string $subnet, int $mask): bool
    {
        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - $mask);

        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }

    /**
     * Check if IPv6 is in range.
     */
    private function ipv6InRange(string $ip, string $subnet, int $mask): bool
    {
        $ipBin = inet_pton($ip);
        $subnetBin = inet_pton($subnet);

        if ($ipBin === false || $subnetBin === false) {
            return false;
        }

        $bytes = $mask >> 3;
        $bits = $mask & 7;

        for ($i = 0; $i < $bytes; $i++) {
            if ($ipBin[$i] !== $subnetBin[$i]) {
                return false;
            }
        }

        if ($bits > 0) {
            $maskByte = 0xFF << (8 - $bits);
            return (ord($ipBin[$bytes]) & $maskByte) === (ord($subnetBin[$bytes]) & $maskByte);
        }

        return true;
    }

    /**
     * Check if request is a financial operation.
     */
    private function isFinancialOperation(Request $request): bool
    {
        $route = $request->route()?->getName();
        
        $financialRoutes = [
            'transactions.*',
            'wallets.*',
            'transfers.*',
            'payments.*',
            'exchange.*',
        ];

        foreach ($financialRoutes as $pattern) {
            if (fnmatch($pattern, $route)) {
                return true;
            }
        }

        return false;
    }
}
