<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Transaction;
use App\Models\Recipient;
use App\Models\Country;

try {
    echo "🚀 Adding Simple Demo Data\n";
    echo "==========================\n\n";
    
    // Get current user
    $user = User::where('email', '<EMAIL>')->first();
    if (!$user) {
        echo "❌ Admin user not found!\n";
        exit(1);
    }
    
    // Get existing countries
    $countries = Country::all();
    if ($countries->isEmpty()) {
        echo "❌ No countries found!\n";
        exit(1);
    }
    
    $saudiArabia = $countries->first();
    echo "✅ Using country: {$saudiArabia->name_en}\n";
    
    // Create simple recipients
    $recipientNames = [
        'أحمد محمد',
        'فاطمة علي', 
        'محمد عبدالله',
        'سارة أحمد',
        'علي حسن'
    ];
    
    foreach ($recipientNames as $index => $name) {
        $names = explode(' ', $name);
        Recipient::updateOrCreate([
            'user_id' => $user->id,
            'first_name' => $names[0],
            'last_name' => $names[1] ?? '',
        ], [
            'email' => strtolower(str_replace(' ', '.', $name)) . '@example.com',
            'phone' => '+966501234' . str_pad($index, 3, '0', STR_PAD_LEFT),
            'country_id' => $saudiArabia->id,
            'relationship' => 'family',
            'is_active' => true,
        ]);
    }
    
    echo "✅ Recipients created\n";
    
    // Create simple transactions
    $recipients = Recipient::where('user_id', $user->id)->get();
    
    if ($recipients->isEmpty()) {
        echo "❌ No recipients found!\n";
        exit(1);
    }
    
    $statuses = ['completed', 'pending', 'processing'];
    
    for ($i = 1; $i <= 10; $i++) {
        $recipient = $recipients->random();
        $amount = rand(100, 1000);
        $status = $statuses[array_rand($statuses)];
        $createdAt = now()->subDays(rand(0, 15));
        
        Transaction::updateOrCreate([
            'transaction_id' => 'DEMO' . str_pad($i, 4, '0', STR_PAD_LEFT),
        ], [
            'user_id' => $user->id,
            'recipient_id' => $recipient->id,
            'sender_name' => $user->first_name . ' ' . $user->last_name,
            'sender_phone' => $user->phone ?? '+966501234567',
            'sender_email' => $user->email,
            'sender_country_id' => $saudiArabia->id,
            'recipient_name' => $recipient->first_name . ' ' . $recipient->last_name,
            'recipient_phone' => $recipient->phone,
            'recipient_email' => $recipient->email,
            'recipient_country_id' => $recipient->country_id,
            'amount' => $amount,
            'currency_from' => 'SAR',
            'currency_to' => 'USD',
            'exchange_rate' => 3.75,
            'fee' => $amount * 0.02,
            'total_amount' => $amount * 1.02,
            'payment_method' => 'bank_transfer',
            'purpose' => 'family_support',
            'status' => $status,
            'notes' => 'تحويل تجريبي',
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
            'completed_at' => $status === 'completed' ? $createdAt->addMinutes(30) : null,
        ]);
    }
    
    echo "✅ Transactions created\n";
    
    // Get stats
    $totalTransactions = Transaction::where('user_id', $user->id)->count();
    $completedTransactions = Transaction::where('user_id', $user->id)->where('status', 'completed')->count();
    $pendingTransactions = Transaction::where('user_id', $user->id)->where('status', 'pending')->count();
    $totalAmount = Transaction::where('user_id', $user->id)->where('status', 'completed')->sum('amount');
    
    echo "\n📊 Dashboard Stats:\n";
    echo "===================\n";
    echo "📈 Total Transactions: {$totalTransactions}\n";
    echo "✅ Completed: {$completedTransactions}\n";
    echo "⏳ Pending: {$pendingTransactions}\n";
    echo "💰 Total Amount: " . number_format($totalAmount, 2) . " SAR\n";
    echo "👥 Recipients: " . $recipients->count() . "\n";
    
    echo "\n🎯 Test Dashboard:\n";
    echo "==================\n";
    echo "🔗 URL: http://localhost:8000/dashboard\n";
    echo "👤 Login: <EMAIL> / password123\n";
    
    echo "\n✅ Demo data ready!\n";
    echo "🚀 Dashboard should now be fast and responsive!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
