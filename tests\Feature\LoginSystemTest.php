<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class LoginSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a country for users
        $this->country = Country::create([
            'name_ar' => 'المملكة العربية السعودية',
            'name_en' => 'Saudi Arabia',
            'code' => 'SAU',
            'iso2' => 'SA',
            'phone_code' => '+966',
            'currency_code' => 'SAR',
            'is_active' => true,
            'supports_transfers' => true,
        ]);
    }

    public function test_admin_can_login_successfully()
    {
        // Create admin user
        $admin = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);

        // Test login
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'first_name',
                            'last_name',
                            'email',
                            'user_type',
                        ],
                        'token',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'user' => [
                            'email' => '<EMAIL>',
                            'user_type' => 'admin',
                        ],
                    ],
                ]);

        $this->assertNotEmpty($response->json('data.token'));
    }

    public function test_customer_can_login_successfully()
    {
        // Create customer user
        $customer = User::create([
            'first_name' => 'Test',
            'last_name' => 'Customer',
            'email' => '<EMAIL>',
            'phone' => '+966507654321',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);

        // Test login
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'user' => [
                            'email' => '<EMAIL>',
                            'user_type' => 'customer',
                        ],
                    ],
                ]);
    }

    public function test_login_fails_with_wrong_password()
    {
        // Create user
        User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => Hash::make('correct_password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
        ]);

        // Test login with wrong password
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong_password',
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                ]);
    }

    public function test_login_fails_with_nonexistent_email()
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                ]);
    }

    public function test_login_requires_email_and_password()
    {
        // Test without email
        $response = $this->postJson('/api/v1/auth/login', [
            'password' => 'password',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);

        // Test without password
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    public function test_suspended_user_cannot_login()
    {
        // Create suspended user
        User::create([
            'first_name' => 'Suspended',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'status' => 'suspended',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                ]);
    }

    public function test_user_can_access_protected_routes_with_token()
    {
        // Create user
        $user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
        ]);

        // Login to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('data.token');

        // Test accessing protected route
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/user/profile');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'email' => '<EMAIL>',
                    ],
                ]);
    }

    public function test_user_cannot_access_protected_routes_without_token()
    {
        $response = $this->getJson('/api/v1/user/profile');

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                ]);
    }

    public function test_user_can_logout()
    {
        // Create and login user
        $user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
        ]);

        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('data.token');

        // Test logout
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);

        // Test that token is no longer valid (skip this test for now as tokens are deleted completely)
        // Note: Since we delete all tokens, we can't test the specific token anymore
    }
}
