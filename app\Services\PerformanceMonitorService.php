<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class PerformanceMonitorService
{
    /**
     * Monitor system performance metrics
     */
    public static function getSystemMetrics(): array
    {
        try {
            $metrics = [
                'timestamp' => now()->toISOString(),
                'database' => self::getDatabaseMetrics(),
                'cache' => self::getCacheMetrics(),
                'memory' => self::getMemoryMetrics(),
                'response_time' => self::getResponseTimeMetrics(),
                'error_rate' => self::getErrorRateMetrics(),
                'active_users' => self::getActiveUsersMetrics(),
                'transaction_metrics' => self::getTransactionMetrics(),
            ];

            // Store metrics for historical analysis
            self::storeMetrics($metrics);

            return $metrics;
        } catch (\Exception $e) {
            Log::error('Failed to get system metrics', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Get database performance metrics
     */
    private static function getDatabaseMetrics(): array
    {
        try {
            $startTime = microtime(true);
            
            // Test database connection
            DB::connection()->getPdo();
            $connectionTime = (microtime(true) - $startTime) * 1000;

            // Get database size
            $databaseSize = DB::select("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ")[0]->size_mb ?? 0;

            // Get slow queries count
            $slowQueries = DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'")[0]->Value ?? 0;

            // Get active connections
            $connections = DB::select("SHOW STATUS LIKE 'Threads_connected'")[0]->Value ?? 0;

            return [
                'connection_time_ms' => round($connectionTime, 2),
                'database_size_mb' => $databaseSize,
                'slow_queries' => (int)$slowQueries,
                'active_connections' => (int)$connections,
                'status' => $connectionTime < 100 ? 'healthy' : 'slow',
            ];
        } catch (\Exception $e) {
            return [
                'connection_time_ms' => null,
                'database_size_mb' => null,
                'slow_queries' => null,
                'active_connections' => null,
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get cache performance metrics
     */
    private static function getCacheMetrics(): array
    {
        try {
            $startTime = microtime(true);
            
            // Test cache operation
            $testKey = 'performance_test_' . time();
            Cache::put($testKey, 'test_value', 60);
            $value = Cache::get($testKey);
            Cache::forget($testKey);
            
            $operationTime = (microtime(true) - $startTime) * 1000;

            // Get cache statistics
            $stats = Cache::getRedis()->info();
            
            return [
                'operation_time_ms' => round($operationTime, 2),
                'hit_rate' => self::calculateCacheHitRate($stats),
                'memory_usage_mb' => round(($stats['used_memory'] ?? 0) / 1024 / 1024, 2),
                'connected_clients' => $stats['connected_clients'] ?? 0,
                'status' => $operationTime < 50 ? 'healthy' : 'slow',
            ];
        } catch (\Exception $e) {
            return [
                'operation_time_ms' => null,
                'hit_rate' => null,
                'memory_usage_mb' => null,
                'connected_clients' => null,
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get memory usage metrics
     */
    private static function getMemoryMetrics(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        // Convert memory limit to bytes
        $memoryLimitBytes = self::convertToBytes($memoryLimit);
        
        return [
            'current_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'peak_usage_mb' => round($memoryPeak / 1024 / 1024, 2),
            'limit_mb' => round($memoryLimitBytes / 1024 / 1024, 2),
            'usage_percentage' => round(($memoryUsage / $memoryLimitBytes) * 100, 2),
            'status' => ($memoryUsage / $memoryLimitBytes) < 0.8 ? 'healthy' : 'high',
        ];
    }

    /**
     * Get response time metrics
     */
    private static function getResponseTimeMetrics(): array
    {
        try {
            // Get average response time from logs (simplified)
            $avgResponseTime = Cache::remember('avg_response_time', 300, function () {
                // This would typically analyze access logs
                return rand(50, 200); // Demo value
            });

            return [
                'average_ms' => $avgResponseTime,
                'status' => $avgResponseTime < 200 ? 'fast' : ($avgResponseTime < 500 ? 'moderate' : 'slow'),
            ];
        } catch (\Exception $e) {
            return [
                'average_ms' => null,
                'status' => 'error',
            ];
        }
    }

    /**
     * Get error rate metrics
     */
    private static function getErrorRateMetrics(): array
    {
        try {
            // Get error count from logs (simplified)
            $errorCount = Cache::remember('error_count_24h', 300, function () {
                // This would typically analyze error logs
                return rand(0, 10); // Demo value
            });

            $totalRequests = Cache::remember('total_requests_24h', 300, function () {
                return rand(1000, 5000); // Demo value
            });

            $errorRate = $totalRequests > 0 ? ($errorCount / $totalRequests) * 100 : 0;

            return [
                'error_count_24h' => $errorCount,
                'total_requests_24h' => $totalRequests,
                'error_rate_percentage' => round($errorRate, 2),
                'status' => $errorRate < 1 ? 'healthy' : ($errorRate < 5 ? 'moderate' : 'high'),
            ];
        } catch (\Exception $e) {
            return [
                'error_count_24h' => null,
                'total_requests_24h' => null,
                'error_rate_percentage' => null,
                'status' => 'error',
            ];
        }
    }

    /**
     * Get active users metrics
     */
    private static function getActiveUsersMetrics(): array
    {
        try {
            $activeUsers1h = DB::table('users')
                ->where('last_activity', '>=', now()->subHour())
                ->count();

            $activeUsers24h = DB::table('users')
                ->where('last_activity', '>=', now()->subDay())
                ->count();

            $totalUsers = DB::table('users')->count();

            return [
                'active_1h' => $activeUsers1h,
                'active_24h' => $activeUsers24h,
                'total_users' => $totalUsers,
                'activity_rate_24h' => $totalUsers > 0 ? round(($activeUsers24h / $totalUsers) * 100, 2) : 0,
            ];
        } catch (\Exception $e) {
            return [
                'active_1h' => null,
                'active_24h' => null,
                'total_users' => null,
                'activity_rate_24h' => null,
            ];
        }
    }

    /**
     * Get transaction metrics
     */
    private static function getTransactionMetrics(): array
    {
        try {
            $transactions24h = DB::table('transactions')
                ->where('created_at', '>=', now()->subDay())
                ->count();

            $successfulTransactions = DB::table('transactions')
                ->where('created_at', '>=', now()->subDay())
                ->where('status', 'completed')
                ->count();

            $failedTransactions = DB::table('transactions')
                ->where('created_at', '>=', now()->subDay())
                ->where('status', 'failed')
                ->count();

            $successRate = $transactions24h > 0 ? ($successfulTransactions / $transactions24h) * 100 : 0;

            $totalVolume = DB::table('transactions')
                ->where('created_at', '>=', now()->subDay())
                ->where('status', 'completed')
                ->sum('amount');

            return [
                'total_24h' => $transactions24h,
                'successful_24h' => $successfulTransactions,
                'failed_24h' => $failedTransactions,
                'success_rate_percentage' => round($successRate, 2),
                'total_volume_24h' => round($totalVolume, 2),
                'status' => $successRate > 95 ? 'excellent' : ($successRate > 90 ? 'good' : 'needs_attention'),
            ];
        } catch (\Exception $e) {
            return [
                'total_24h' => null,
                'successful_24h' => null,
                'failed_24h' => null,
                'success_rate_percentage' => null,
                'total_volume_24h' => null,
                'status' => 'error',
            ];
        }
    }

    /**
     * Store metrics for historical analysis
     */
    private static function storeMetrics(array $metrics): void
    {
        try {
            DB::table('system_metrics')->insert([
                'timestamp' => now(),
                'metrics_data' => json_encode($metrics),
                'created_at' => now(),
            ]);

            // Keep only last 30 days of metrics
            DB::table('system_metrics')
                ->where('created_at', '<', now()->subDays(30))
                ->delete();
        } catch (\Exception $e) {
            Log::error('Failed to store metrics', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Calculate cache hit rate
     */
    private static function calculateCacheHitRate(array $stats): float
    {
        $hits = $stats['keyspace_hits'] ?? 0;
        $misses = $stats['keyspace_misses'] ?? 0;
        $total = $hits + $misses;
        
        return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
    }

    /**
     * Convert memory limit string to bytes
     */
    private static function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int)$value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Get system health status
     */
    public static function getHealthStatus(): array
    {
        $metrics = self::getSystemMetrics();
        
        $healthChecks = [
            'database' => $metrics['database']['status'] ?? 'unknown',
            'cache' => $metrics['cache']['status'] ?? 'unknown',
            'memory' => $metrics['memory']['status'] ?? 'unknown',
            'response_time' => $metrics['response_time']['status'] ?? 'unknown',
            'error_rate' => $metrics['error_rate']['status'] ?? 'unknown',
            'transactions' => $metrics['transaction_metrics']['status'] ?? 'unknown',
        ];

        $healthyCount = count(array_filter($healthChecks, fn($status) => in_array($status, ['healthy', 'fast', 'excellent', 'good'])));
        $totalChecks = count($healthChecks);
        $healthPercentage = round(($healthyCount / $totalChecks) * 100, 2);

        $overallStatus = 'healthy';
        if ($healthPercentage < 50) {
            $overallStatus = 'critical';
        } elseif ($healthPercentage < 80) {
            $overallStatus = 'warning';
        }

        return [
            'overall_status' => $overallStatus,
            'health_percentage' => $healthPercentage,
            'checks' => $healthChecks,
            'timestamp' => now()->toISOString(),
        ];
    }
}
