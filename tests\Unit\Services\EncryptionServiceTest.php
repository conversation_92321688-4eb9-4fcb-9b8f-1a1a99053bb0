<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\EncryptionService;

class EncryptionServiceTest extends TestCase
{
    /**
     * Test encryption and decryption of sensitive data
     */
    public function test_encrypt_and_decrypt_sensitive_data(): void
    {
        $originalData = 'sensitive_bank_account_123456789';
        
        // Test encryption
        $encryptedData = EncryptionService::encryptSensitiveData($originalData);
        $this->assertNotEquals($originalData, $encryptedData);
        $this->assertNotEmpty($encryptedData);
        
        // Test decryption
        $decryptedData = EncryptionService::decryptSensitiveData($encryptedData);
        $this->assertEquals($originalData, $decryptedData);
    }

    /**
     * Test hashing sensitive data
     */
    public function test_hash_sensitive_data(): void
    {
        $data = 'sensitive_data_to_hash';
        
        $hash1 = EncryptionService::hashSensitiveData($data);
        $hash2 = EncryptionService::hashSensitiveData($data);
        
        // Same data should produce same hash
        $this->assertEquals($hash1, $hash2);
        
        // Hash should be different from original data
        $this->assertNotEquals($data, $hash1);
        
        // Hash should be 64 characters (SHA256)
        $this->assertEquals(64, strlen($hash1));
    }

    /**
     * Test masking sensitive data
     */
    public function test_mask_sensitive_data(): void
    {
        $data = '1234567890';
        $masked = EncryptionService::maskSensitiveData($data, 4);
        
        $this->assertEquals('******7890', $masked);
        $this->assertEquals(10, strlen($masked));
    }

    /**
     * Test masking phone number
     */
    public function test_mask_phone_number(): void
    {
        $phone = '+966501234567';
        $masked = EncryptionService::maskPhoneNumber($phone);
        
        $this->assertStringContainsString('+966', $masked);
        $this->assertStringContainsString('4567', $masked);
        $this->assertStringContainsString('*', $masked);
    }

    /**
     * Test masking email address
     */
    public function test_mask_email(): void
    {
        $email = '<EMAIL>';
        $masked = EncryptionService::maskEmail($email);
        
        $this->assertEquals('us**@example.com', $masked);
        $this->assertStringContainsString('@example.com', $masked);
    }

    /**
     * Test masking bank account
     */
    public function test_mask_bank_account(): void
    {
        $account = '****************';
        $masked = EncryptionService::maskBankAccount($account);
        
        $this->assertEquals('************3456', $masked);
        $this->assertEquals(16, strlen($masked));
    }

    /**
     * Test generating secure token
     */
    public function test_generate_secure_token(): void
    {
        $token1 = EncryptionService::generateSecureToken(32);
        $token2 = EncryptionService::generateSecureToken(32);
        
        // Tokens should be different
        $this->assertNotEquals($token1, $token2);
        
        // Token should be 32 characters
        $this->assertEquals(32, strlen($token1));
        
        // Token should be hexadecimal
        $this->assertMatchesRegularExpression('/^[a-f0-9]+$/', $token1);
    }

    /**
     * Test data integrity validation
     */
    public function test_validate_data_integrity(): void
    {
        $data = 'important_data_to_validate';
        $hash = EncryptionService::hashSensitiveData($data);
        
        // Valid data should pass validation
        $this->assertTrue(EncryptionService::validateDataIntegrity($data, $hash));
        
        // Modified data should fail validation
        $this->assertFalse(EncryptionService::validateDataIntegrity($data . '_modified', $hash));
    }

    /**
     * Test encryption with empty data
     */
    public function test_encrypt_empty_data(): void
    {
        $this->expectException(\Exception::class);
        EncryptionService::encryptSensitiveData('');
    }

    /**
     * Test decryption with invalid data
     */
    public function test_decrypt_invalid_data(): void
    {
        $this->expectException(\Exception::class);
        EncryptionService::decryptSensitiveData('invalid_encrypted_data');
    }
}
