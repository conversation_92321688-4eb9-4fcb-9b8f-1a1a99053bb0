<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Services\MetricsService;
use App\Services\BackupService;
use App\Services\BlockchainService;
use App\Models\AuditLog;
use App\Models\Notification;
use App\Models\FraudDetection;
use Carbon\Carbon;

class SystemMaintenanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'system:maintenance 
                            {--type=all : Type of maintenance (all, cleanup, backup, optimize, security)}
                            {--force : Force maintenance even if system is busy}
                            {--dry-run : Show what would be done without executing}';

    /**
     * The console command description.
     */
    protected $description = 'Perform system maintenance tasks';

    protected MetricsService $metricsService;
    protected BackupService $backupService;
    protected BlockchainService $blockchainService;

    public function __construct(
        MetricsService $metricsService,
        BackupService $backupService,
        BlockchainService $blockchainService
    ) {
        parent::__construct();
        $this->metricsService = $metricsService;
        $this->backupService = $backupService;
        $this->blockchainService = $blockchainService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        $this->info("Starting system maintenance: {$type}");
        
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No changes will be made");
        }

        try {
            // Check if system is busy (unless forced)
            if (!$force && $this->isSystemBusy()) {
                $this->error("System is busy. Use --force to override or wait for lower activity.");
                return 1;
            }

            $startTime = microtime(true);
            $results = [];

            switch ($type) {
                case 'all':
                    $results = $this->performFullMaintenance($dryRun);
                    break;
                case 'cleanup':
                    $results = $this->performCleanup($dryRun);
                    break;
                case 'backup':
                    $results = $this->performBackup($dryRun);
                    break;
                case 'optimize':
                    $results = $this->performOptimization($dryRun);
                    break;
                case 'security':
                    $results = $this->performSecurityMaintenance($dryRun);
                    break;
                default:
                    $this->error("Unknown maintenance type: {$type}");
                    return 1;
            }

            $duration = round(microtime(true) - $startTime, 2);
            
            $this->displayResults($results, $duration);
            
            if (!$dryRun) {
                $this->logMaintenanceActivity($type, $results, $duration);
            }

            $this->info("System maintenance completed successfully in {$duration} seconds");
            
            return 0;

        } catch (\Exception $e) {
            $this->error("Maintenance failed: " . $e->getMessage());
            Log::error("System maintenance failed", [
                'type' => $type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return 1;
        }
    }

    /**
     * Perform full maintenance.
     */
    protected function performFullMaintenance(bool $dryRun): array
    {
        $this->info("Performing full system maintenance...");
        
        $results = [
            'cleanup' => $this->performCleanup($dryRun),
            'optimization' => $this->performOptimization($dryRun),
            'security' => $this->performSecurityMaintenance($dryRun),
            'backup' => $this->performBackup($dryRun),
        ];

        return $results;
    }

    /**
     * Perform cleanup tasks.
     */
    protected function performCleanup(bool $dryRun): array
    {
        $this->info("Performing cleanup tasks...");
        
        $results = [
            'old_logs' => $this->cleanupOldLogs($dryRun),
            'expired_sessions' => $this->cleanupExpiredSessions($dryRun),
            'old_notifications' => $this->cleanupOldNotifications($dryRun),
            'resolved_fraud_alerts' => $this->cleanupResolvedFraudAlerts($dryRun),
            'cache_cleanup' => $this->cleanupCache($dryRun),
            'temp_files' => $this->cleanupTempFiles($dryRun),
        ];

        return $results;
    }

    /**
     * Perform backup tasks.
     */
    protected function performBackup(bool $dryRun): array
    {
        $this->info("Performing backup tasks...");
        
        if ($dryRun) {
            return [
                'full_backup' => ['status' => 'skipped', 'reason' => 'dry run'],
                'cleanup_old_backups' => ['status' => 'skipped', 'reason' => 'dry run'],
            ];
        }

        $results = [];

        try {
            $this->info("Creating full system backup...");
            $backupResult = $this->backupService->createFullBackup();
            $results['full_backup'] = [
                'status' => 'success',
                'backup_name' => $backupResult['backup_name'],
                'size' => $backupResult['total_size'],
                'duration' => $backupResult['duration'],
            ];
        } catch (\Exception $e) {
            $results['full_backup'] = [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        try {
            $this->info("Cleaning up old backups...");
            $cleanupResult = $this->backupService->cleanupOldBackups();
            $results['cleanup_old_backups'] = [
                'status' => 'success',
                'cleaned_count' => count($cleanupResult['local']),
            ];
        } catch (\Exception $e) {
            $results['cleanup_old_backups'] = [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        return $results;
    }

    /**
     * Perform optimization tasks.
     */
    protected function performOptimization(bool $dryRun): array
    {
        $this->info("Performing optimization tasks...");
        
        $results = [
            'database_optimization' => $this->optimizeDatabase($dryRun),
            'cache_optimization' => $this->optimizeCache($dryRun),
            'config_cache' => $this->updateConfigCache($dryRun),
            'route_cache' => $this->updateRouteCache($dryRun),
            'view_cache' => $this->updateViewCache($dryRun),
        ];

        return $results;
    }

    /**
     * Perform security maintenance.
     */
    protected function performSecurityMaintenance(bool $dryRun): array
    {
        $this->info("Performing security maintenance...");
        
        $results = [
            'expired_tokens' => $this->cleanupExpiredTokens($dryRun),
            'failed_jobs' => $this->cleanupFailedJobs($dryRun),
            'security_scan' => $this->performSecurityScan($dryRun),
            'update_crypto_prices' => $this->updateCryptocurrencyPrices($dryRun),
        ];

        return $results;
    }

    /**
     * Check if system is busy.
     */
    protected function isSystemBusy(): bool
    {
        // Check active transactions
        $activeTransactions = DB::table('transactions')
            ->where('status', 'processing')
            ->count();

        if ($activeTransactions > 10) {
            return true;
        }

        // Check queue size
        $queueSize = DB::table('jobs')->count();
        
        if ($queueSize > 1000) {
            return true;
        }

        // Check system load
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            if ($load[0] > 2.0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Cleanup old log files.
     */
    protected function cleanupOldLogs(bool $dryRun): array
    {
        $retentionDays = config('logging.retention_days', 30);
        $cutoffDate = Carbon::now()->subDays($retentionDays);
        
        $count = AuditLog::where('created_at', '<', $cutoffDate)->count();
        
        if (!$dryRun && $count > 0) {
            AuditLog::where('created_at', '<', $cutoffDate)->delete();
        }

        return [
            'status' => 'success',
            'deleted_count' => $count,
            'cutoff_date' => $cutoffDate->toDateString(),
        ];
    }

    /**
     * Cleanup expired sessions.
     */
    protected function cleanupExpiredSessions(bool $dryRun): array
    {
        $count = 0;
        
        try {
            $count = DB::table('sessions')
                ->where('last_activity', '<', time() - config('session.lifetime', 120) * 60)
                ->count();
            
            if (!$dryRun && $count > 0) {
                DB::table('sessions')
                    ->where('last_activity', '<', time() - config('session.lifetime', 120) * 60)
                    ->delete();
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        return [
            'status' => 'success',
            'deleted_count' => $count,
        ];
    }

    /**
     * Cleanup old notifications.
     */
    protected function cleanupOldNotifications(bool $dryRun): array
    {
        $retentionDays = config('notifications.retention_days', 90);
        $cutoffDate = Carbon::now()->subDays($retentionDays);
        
        $count = Notification::where('created_at', '<', $cutoffDate)->count();
        
        if (!$dryRun && $count > 0) {
            Notification::where('created_at', '<', $cutoffDate)->delete();
        }

        return [
            'status' => 'success',
            'deleted_count' => $count,
            'cutoff_date' => $cutoffDate->toDateString(),
        ];
    }

    /**
     * Cleanup resolved fraud alerts.
     */
    protected function cleanupResolvedFraudAlerts(bool $dryRun): array
    {
        $retentionDays = config('fraud_detection.monitoring.auto_resolve_low_risk_days', 30);
        $cutoffDate = Carbon::now()->subDays($retentionDays);
        
        $count = FraudDetection::where('status', 'resolved')
            ->where('resolved_at', '<', $cutoffDate)
            ->count();
        
        if (!$dryRun && $count > 0) {
            FraudDetection::where('status', 'resolved')
                ->where('resolved_at', '<', $cutoffDate)
                ->delete();
        }

        return [
            'status' => 'success',
            'deleted_count' => $count,
            'cutoff_date' => $cutoffDate->toDateString(),
        ];
    }

    /**
     * Cleanup cache.
     */
    protected function cleanupCache(bool $dryRun): array
    {
        if ($dryRun) {
            return ['status' => 'skipped', 'reason' => 'dry run'];
        }

        try {
            Cache::flush();
            
            return [
                'status' => 'success',
                'action' => 'cache flushed',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Cleanup temporary files.
     */
    protected function cleanupTempFiles(bool $dryRun): array
    {
        $tempPaths = [
            storage_path('framework/cache/data'),
            storage_path('framework/sessions'),
            storage_path('framework/views'),
            storage_path('logs/temp'),
        ];

        $deletedCount = 0;
        $errors = [];

        foreach ($tempPaths as $path) {
            if (!is_dir($path)) {
                continue;
            }

            try {
                $files = glob($path . '/*');
                $oldFiles = array_filter($files, function ($file) {
                    return is_file($file) && (time() - filemtime($file)) > 86400; // 24 hours
                });

                if (!$dryRun) {
                    foreach ($oldFiles as $file) {
                        if (unlink($file)) {
                            $deletedCount++;
                        }
                    }
                } else {
                    $deletedCount += count($oldFiles);
                }
            } catch (\Exception $e) {
                $errors[] = "Error cleaning {$path}: " . $e->getMessage();
            }
        }

        return [
            'status' => empty($errors) ? 'success' : 'partial',
            'deleted_count' => $deletedCount,
            'errors' => $errors,
        ];
    }

    /**
     * Optimize database.
     */
    protected function optimizeDatabase(bool $dryRun): array
    {
        if ($dryRun) {
            return ['status' => 'skipped', 'reason' => 'dry run'];
        }

        try {
            // Get all tables
            $tables = DB::select('SHOW TABLES');
            $optimizedTables = [];

            foreach ($tables as $table) {
                $tableName = array_values((array) $table)[0];
                
                try {
                    DB::statement("OPTIMIZE TABLE `{$tableName}`");
                    $optimizedTables[] = $tableName;
                } catch (\Exception $e) {
                    // Continue with other tables
                }
            }

            return [
                'status' => 'success',
                'optimized_tables' => count($optimizedTables),
                'tables' => $optimizedTables,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Optimize cache.
     */
    protected function optimizeCache(bool $dryRun): array
    {
        if ($dryRun) {
            return ['status' => 'skipped', 'reason' => 'dry run'];
        }

        try {
            // Clear expired cache entries
            Cache::flush();
            
            // Warm up important cache entries
            $this->warmUpCache();

            return [
                'status' => 'success',
                'action' => 'cache optimized and warmed up',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Update config cache.
     */
    protected function updateConfigCache(bool $dryRun): array
    {
        if ($dryRun) {
            return ['status' => 'skipped', 'reason' => 'dry run'];
        }

        try {
            $this->call('config:cache');
            
            return [
                'status' => 'success',
                'action' => 'config cache updated',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Update route cache.
     */
    protected function updateRouteCache(bool $dryRun): array
    {
        if ($dryRun) {
            return ['status' => 'skipped', 'reason' => 'dry run'];
        }

        try {
            $this->call('route:cache');
            
            return [
                'status' => 'success',
                'action' => 'route cache updated',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Update view cache.
     */
    protected function updateViewCache(bool $dryRun): array
    {
        if ($dryRun) {
            return ['status' => 'skipped', 'reason' => 'dry run'];
        }

        try {
            $this->call('view:cache');
            
            return [
                'status' => 'success',
                'action' => 'view cache updated',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Cleanup expired tokens.
     */
    protected function cleanupExpiredTokens(bool $dryRun): array
    {
        $count = 0;
        
        try {
            $count = DB::table('personal_access_tokens')
                ->where('expires_at', '<', now())
                ->count();
            
            if (!$dryRun && $count > 0) {
                DB::table('personal_access_tokens')
                    ->where('expires_at', '<', now())
                    ->delete();
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        return [
            'status' => 'success',
            'deleted_count' => $count,
        ];
    }

    /**
     * Cleanup failed jobs.
     */
    protected function cleanupFailedJobs(bool $dryRun): array
    {
        $retentionDays = 7;
        $cutoffDate = Carbon::now()->subDays($retentionDays);
        
        $count = DB::table('failed_jobs')
            ->where('failed_at', '<', $cutoffDate)
            ->count();
        
        if (!$dryRun && $count > 0) {
            DB::table('failed_jobs')
                ->where('failed_at', '<', $cutoffDate)
                ->delete();
        }

        return [
            'status' => 'success',
            'deleted_count' => $count,
            'cutoff_date' => $cutoffDate->toDateString(),
        ];
    }

    /**
     * Perform security scan.
     */
    protected function performSecurityScan(bool $dryRun): array
    {
        // Mock security scan - in production, integrate with actual security tools
        $issues = [
            'weak_passwords' => rand(0, 5),
            'suspicious_logins' => rand(0, 10),
            'outdated_sessions' => rand(0, 20),
        ];

        return [
            'status' => 'success',
            'issues_found' => array_sum($issues),
            'details' => $issues,
        ];
    }

    /**
     * Update cryptocurrency prices.
     */
    protected function updateCryptocurrencyPrices(bool $dryRun): array
    {
        if ($dryRun) {
            return ['status' => 'skipped', 'reason' => 'dry run'];
        }

        try {
            $results = $this->blockchainService->updateAllCryptocurrencyPrices();
            
            $successCount = count(array_filter($results, fn($r) => $r['status'] === 'success'));
            $errorCount = count(array_filter($results, fn($r) => $r['status'] === 'error'));

            return [
                'status' => $errorCount === 0 ? 'success' : 'partial',
                'updated_count' => $successCount,
                'error_count' => $errorCount,
                'details' => $results,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Warm up cache with important data.
     */
    protected function warmUpCache(): void
    {
        // Cache frequently accessed data
        $this->call('cache:warm-up', ['--routes' => ['api/v1/currencies', 'api/v1/countries']]);
    }

    /**
     * Display maintenance results.
     */
    protected function displayResults(array $results, float $duration): void
    {
        $this->info("\n=== Maintenance Results ===");
        $this->info("Duration: {$duration} seconds\n");

        foreach ($results as $category => $categoryResults) {
            $this->info("📋 {$category}:");
            
            if (is_array($categoryResults)) {
                foreach ($categoryResults as $task => $result) {
                    $status = $result['status'] ?? 'unknown';
                    $icon = $status === 'success' ? '✅' : ($status === 'error' ? '❌' : '⚠️');
                    
                    $this->line("  {$icon} {$task}: {$status}");
                    
                    if (isset($result['deleted_count'])) {
                        $this->line("    Deleted: {$result['deleted_count']} items");
                    }
                    
                    if (isset($result['error'])) {
                        $this->error("    Error: {$result['error']}");
                    }
                }
            }
            
            $this->line("");
        }
    }

    /**
     * Log maintenance activity.
     */
    protected function logMaintenanceActivity(string $type, array $results, float $duration): void
    {
        AuditLog::create([
            'user_id' => null,
            'event_type' => 'system_maintenance',
            'action' => $type,
            'resource_type' => 'system',
            'resource_id' => null,
            'old_values' => null,
            'new_values' => json_encode($results),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'System Maintenance Command',
            'duration_ms' => $duration * 1000,
            'status_code' => 200,
        ]);

        Log::info("System maintenance completed", [
            'type' => $type,
            'duration' => $duration,
            'results' => $results,
        ]);
    }
}
