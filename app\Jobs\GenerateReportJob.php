<?php

namespace App\Jobs;

use App\Models\Report;
use App\Services\ReportService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $reportId;
    protected array $parameters;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(int $reportId, array $parameters = [])
    {
        $this->reportId = $reportId;
        $this->parameters = $parameters;
    }

    /**
     * Execute the job.
     */
    public function handle(ReportService $reportService): void
    {
        try {
            $report = Report::findOrFail($this->reportId);
            
            Log::info('Starting report generation', [
                'report_id' => $this->reportId,
                'type' => $report->type,
                'parameters' => $this->parameters,
            ]);

            // Update report status to processing
            $report->update(['status' => 'processing']);

            // Generate report based on type
            $generatedReport = match ($report->type) {
                'financial' => $reportService->generateFinancialReport($this->parameters),
                'transactions' => $reportService->generateTransactionReport($this->parameters),
                'fraud' => $reportService->generateFraudReport($this->parameters),
                'user_activity' => $reportService->generateUserActivityReport($this->parameters),
                default => throw new \InvalidArgumentException("Unknown report type: {$report->type}"),
            };

            Log::info('Report generated successfully', [
                'report_id' => $this->reportId,
                'generated_report_id' => $generatedReport->id,
                'file_path' => $generatedReport->file_path,
                'file_size' => $generatedReport->file_size,
            ]);

        } catch (\Exception $e) {
            Log::error('Report generation failed', [
                'report_id' => $this->reportId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Mark report as failed
            $report = Report::find($this->reportId);
            if ($report) {
                $report->markAsFailed($e->getMessage());
            }

            throw $e;
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('GenerateReportJob failed permanently', [
            'report_id' => $this->reportId,
            'parameters' => $this->parameters,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Mark report as failed
        $report = Report::find($this->reportId);
        if ($report) {
            $report->markAsFailed($exception->getMessage());
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $report = Report::find($this->reportId);
        
        return [
            'report',
            'report:' . $this->reportId,
            'type:' . ($report?->type ?? 'unknown'),
            'user:' . ($report?->user_id ?? 'system'),
        ];
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHour(); // Allow retries for up to 1 hour
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 60, 120]; // Retry after 30s, 60s, then 120s
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        // Add rate limiting to prevent too many report generations at once
        return [
            // new \Illuminate\Queue\Middleware\RateLimited('reports'),
        ];
    }

    /**
     * Determine if the job should be retried.
     */
    public function shouldRetry(\Throwable $exception): bool
    {
        // Don't retry for certain types of exceptions
        $nonRetryableExceptions = [
            \InvalidArgumentException::class,
            \BadMethodCallException::class,
        ];

        foreach ($nonRetryableExceptions as $exceptionClass) {
            if ($exception instanceof $exceptionClass) {
                return false;
            }
        }

        // Don't retry if we've exceeded memory limit
        if (str_contains($exception->getMessage(), 'memory')) {
            return false;
        }

        return true;
    }

    /**
     * Create a job for scheduled report generation.
     */
    public static function createForScheduledReport(Report $report): self
    {
        $parameters = array_merge($report->parameters ?? [], [
            'name' => $report->name,
            'format' => $report->format,
            'is_scheduled' => true,
        ]);

        return new self($report->id, $parameters);
    }

    /**
     * Create a job for on-demand report generation.
     */
    public static function createForOnDemandReport(Report $report, array $additionalParameters = []): self
    {
        $parameters = array_merge($report->parameters ?? [], $additionalParameters, [
            'name' => $report->name,
            'format' => $report->format,
            'is_scheduled' => false,
        ]);

        return new self($report->id, $parameters);
    }
}
