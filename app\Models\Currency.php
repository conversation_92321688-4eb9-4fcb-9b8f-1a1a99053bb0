<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Currency extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'code',
        'symbol',
        'is_crypto',
        'is_active',
        'is_base_currency',
        'rate_to_usd',
        'decimal_places',
        'crypto_network',
        'contract_address',
        'icon_url',
        'supported_countries',
        'min_transfer_amount',
        'max_transfer_amount',
    ];

    protected $casts = [
        'is_crypto' => 'boolean',
        'is_active' => 'boolean',
        'is_base_currency' => 'boolean',
        'rate_to_usd' => 'decimal:8',
        'decimal_places' => 'integer',
        'supported_countries' => 'array',
        'min_transfer_amount' => 'decimal:2',
        'max_transfer_amount' => 'decimal:2',
    ];

    /**
     * Get the wallets for the currency.
     */
    public function wallets(): HasMany
    {
        return $this->hasMany(Wallet::class);
    }

    /**
     * Get the transactions for the currency.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the target transactions for the currency.
     */
    public function targetTransactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'target_currency_id');
    }

    /**
     * Get exchange rates from this currency.
     */
    public function exchangeRatesFrom(): HasMany
    {
        return $this->hasMany(ExchangeRate::class, 'from_currency_id');
    }

    /**
     * Get exchange rates to this currency.
     */
    public function exchangeRatesTo(): HasMany
    {
        return $this->hasMany(ExchangeRate::class, 'to_currency_id');
    }

    /**
     * Scope a query to only include active currencies.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include crypto currencies.
     */
    public function scopeCrypto($query)
    {
        return $query->where('is_crypto', true);
    }

    /**
     * Scope a query to only include fiat currencies.
     */
    public function scopeFiat($query)
    {
        return $query->where('is_crypto', false);
    }

    /**
     * Scope a query to get the base currency.
     */
    public function scopeBase($query)
    {
        return $query->where('is_base_currency', true);
    }

    /**
     * Get the currency name based on current locale.
     */
    public function getNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Format amount according to currency decimal places.
     */
    public function formatAmount($amount): string
    {
        return number_format($amount, $this->decimal_places, '.', ',');
    }

    /**
     * Get exchange rate to another currency.
     */
    public function getExchangeRateTo(Currency $targetCurrency): ?float
    {
        $rate = ExchangeRate::where('from_currency_id', $this->id)
            ->where('to_currency_id', $targetCurrency->id)
            ->where('is_active', true)
            ->first();

        return $rate ? $rate->rate : null;
    }

    /**
     * Check if currency is supported in a country.
     */
    public function isSupportedInCountry(string $countryCode): bool
    {
        return in_array($countryCode, $this->supported_countries ?? []);
    }
}
