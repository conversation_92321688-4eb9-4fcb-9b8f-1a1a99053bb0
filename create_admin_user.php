<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Country;

try {
    $country = Country::first();
    
    if (!$country) {
        echo "No countries found. Please run seeders first.\n";
        exit(1);
    }
    
    $user = User::create([
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>',
        'phone' => '+966501234567',
        'password' => bcrypt('password123'),
        'user_type' => 'admin',
        'status' => 'active',
        'country_id' => $country->id,
        'email_verified_at' => now(),
        'phone_verified_at' => now(),
        'kyc_level' => 'premium',
        'kyc_status' => 'verified',
        'kyc_verified_at' => now()
    ]);
    
    echo "✅ Admin user created successfully!\n";
    echo "Email: " . $user->email . "\n";
    echo "Password: password123\n";
    echo "Type: " . $user->user_type . "\n";
    
    // Create a regular user too
    $regularUser = User::create([
        'first_name' => 'سارة',
        'last_name' => 'أحمد',
        'email' => '<EMAIL>',
        'phone' => '+966507654321',
        'password' => bcrypt('password123'),
        'user_type' => 'customer',
        'status' => 'active',
        'country_id' => $country->id,
        'email_verified_at' => now(),
        'phone_verified_at' => now(),
        'kyc_level' => 'basic',
        'kyc_status' => 'verified',
        'kyc_verified_at' => now()
    ]);
    
    echo "✅ Regular user created successfully!\n";
    echo "Email: " . $regularUser->email . "\n";
    echo "Password: password123\n";
    echo "Type: " . $regularUser->user_type . "\n";
    
} catch (Exception $e) {
    echo "❌ Error creating users: " . $e->getMessage() . "\n";
    exit(1);
}
