<?php

namespace App\Console\Commands;

use App\Models\Transaction;
use App\Jobs\ProcessTransactionJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessPendingTransactionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'financial:process-pending 
                            {--limit=50 : Maximum number of transactions to process}
                            {--older-than=5 : Process transactions older than X minutes}
                            {--dry-run : Show what would be processed without actually processing}';

    /**
     * The console command description.
     */
    protected $description = 'Process pending transactions that are ready for completion';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $limit = (int) $this->option('limit');
        $olderThan = (int) $this->option('older-than');
        $dryRun = $this->option('dry-run');

        $this->info('Processing pending transactions...');
        $this->info("Limit: {$limit}");
        $this->info("Older than: {$olderThan} minutes");
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No transactions will be actually processed');
        }

        try {
            // Get pending transactions
            $transactions = $this->getPendingTransactions($limit, $olderThan);

            if ($transactions->isEmpty()) {
                $this->info('No pending transactions found to process.');
                return self::SUCCESS;
            }

            $this->info("Found {$transactions->count()} pending transactions");

            if ($dryRun) {
                $this->showTransactionsSummary($transactions);
                return self::SUCCESS;
            }

            // Process transactions
            $processed = 0;
            $failed = 0;

            $bar = $this->output->createProgressBar($transactions->count());
            $bar->start();

            foreach ($transactions as $transaction) {
                try {
                    // Dispatch processing job
                    ProcessTransactionJob::dispatch($transaction);
                    $processed++;
                    
                    $this->line(" Processing: {$transaction->transaction_number}");
                    
                } catch (\Exception $e) {
                    $failed++;
                    $this->error(" Failed: {$transaction->transaction_number} - {$e->getMessage()}");
                    
                    Log::error('Failed to dispatch transaction processing job', [
                        'transaction_id' => $transaction->id,
                        'transaction_number' => $transaction->transaction_number,
                        'error' => $e->getMessage(),
                    ]);
                }

                $bar->advance();
            }

            $bar->finish();
            $this->newLine(2);

            $this->info("Processing completed!");
            $this->info("Processed: {$processed}");
            
            if ($failed > 0) {
                $this->warn("Failed: {$failed}");
            }

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Command failed: ' . $e->getMessage());
            Log::error('Process pending transactions command failed', [
                'error' => $e->getMessage(),
                'limit' => $limit,
                'older_than' => $olderThan,
            ]);
            
            return self::FAILURE;
        }
    }

    /**
     * Get pending transactions to process.
     */
    private function getPendingTransactions(int $limit, int $olderThan)
    {
        return Transaction::where('status', 'pending')
            ->where('created_at', '<=', now()->subMinutes($olderThan))
            ->whereNull('processed_at')
            ->with(['currency', 'sender', 'receiver'])
            ->orderBy('created_at', 'asc')
            ->limit($limit)
            ->get();
    }

    /**
     * Show summary of transactions that would be processed.
     */
    private function showTransactionsSummary($transactions): void
    {
        $this->newLine();
        $this->info('Transactions that would be processed:');
        
        $headers = ['ID', 'Number', 'Amount', 'Currency', 'Sender', 'Created'];
        $rows = [];

        foreach ($transactions as $transaction) {
            $rows[] = [
                $transaction->id,
                $transaction->transaction_number,
                number_format($transaction->amount, 2),
                $transaction->currency->code,
                $transaction->sender->full_name,
                $transaction->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $this->table($headers, $rows);

        // Summary statistics
        $totalAmount = $transactions->sum('amount');
        $currencies = $transactions->groupBy('currency.code');

        $this->newLine();
        $this->info('Summary:');
        $this->info("Total transactions: {$transactions->count()}");
        $this->info("Total amount: " . number_format($totalAmount, 2));
        $this->info("Currencies involved: " . $currencies->keys()->implode(', '));

        foreach ($currencies as $currencyCode => $currencyTransactions) {
            $currencyTotal = $currencyTransactions->sum('amount');
            $this->info("  {$currencyCode}: " . number_format($currencyTotal, 2) . " ({$currencyTransactions->count()} transactions)");
        }
    }
}
