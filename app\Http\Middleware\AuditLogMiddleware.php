<?php

namespace App\Http\Middleware;

use App\Models\AuditLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class AuditLogMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        
        // Process the request
        $response = $next($request);
        
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2); // milliseconds

        // Log the request if it's auditable
        if ($this->shouldAudit($request, $response)) {
            $this->createAuditLog($request, $response, $duration);
        }

        return $response;
    }

    /**
     * Determine if the request should be audited.
     */
    private function shouldAudit(Request $request, Response $response): bool
    {
        // Always audit financial operations
        if ($this->isFinancialOperation($request)) {
            return true;
        }

        // Always audit admin operations
        if ($this->isAdminOperation($request)) {
            return true;
        }

        // Always audit authentication operations
        if ($this->isAuthOperation($request)) {
            return true;
        }

        // Audit failed requests
        if ($response->getStatusCode() >= 400) {
            return true;
        }

        // Audit sensitive data access
        if ($this->isSensitiveDataAccess($request)) {
            return true;
        }

        return false;
    }

    /**
     * Create audit log entry.
     */
    private function createAuditLog(Request $request, Response $response, float $duration): void
    {
        try {
            $user = $request->user();
            $route = $request->route();
            
            $auditData = [
                'user_id' => $user?->id,
                'event_type' => $this->getEventType($request),
                'action' => $this->getAction($request),
                'model_type' => $this->getModelType($request),
                'model_id' => $this->getModelId($request),
                'description' => $this->getDescription($request, $response),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'request_method' => $request->method(),
                'request_url' => $request->fullUrl(),
                'route_name' => $route?->getName(),
                'status_code' => $response->getStatusCode(),
                'duration_ms' => $duration,
                'metadata' => $this->getMetadata($request, $response),
            ];

            // Filter sensitive data
            $auditData = $this->filterSensitiveData($auditData, $request);

            AuditLog::create($auditData);

        } catch (\Exception $e) {
            Log::error('Failed to create audit log', [
                'error' => $e->getMessage(),
                'request_url' => $request->fullUrl(),
                'user_id' => $request->user()?->id,
            ]);
        }
    }

    /**
     * Get event type from request.
     */
    private function getEventType(Request $request): string
    {
        $route = $request->route()?->getName();

        if (str_starts_with($route, 'auth.')) {
            return 'authentication';
        }

        if (str_starts_with($route, 'admin.')) {
            return 'admin_operation';
        }

        if (str_starts_with($route, 'transactions.')) {
            return 'transaction';
        }

        if (str_starts_with($route, 'wallets.')) {
            return 'wallet_operation';
        }

        if (str_starts_with($route, 'users.')) {
            return 'user_management';
        }

        return 'general';
    }

    /**
     * Get action from request.
     */
    private function getAction(Request $request): string
    {
        $method = $request->method();
        
        switch ($method) {
            case 'GET':
                return 'view';
            case 'POST':
                return 'create';
            case 'PUT':
            case 'PATCH':
                return 'update';
            case 'DELETE':
                return 'delete';
            default:
                return strtolower($method);
        }
    }

    /**
     * Get model type from request.
     */
    private function getModelType(Request $request): ?string
    {
        $route = $request->route()?->getName();

        if (str_contains($route, 'transaction')) {
            return 'Transaction';
        }

        if (str_contains($route, 'wallet')) {
            return 'Wallet';
        }

        if (str_contains($route, 'user')) {
            return 'User';
        }

        if (str_contains($route, 'currency')) {
            return 'Currency';
        }

        if (str_contains($route, 'branch')) {
            return 'Branch';
        }

        return null;
    }

    /**
     * Get model ID from request.
     */
    private function getModelId(Request $request): ?int
    {
        $route = $request->route();
        
        if (!$route) {
            return null;
        }

        // Try to get ID from route parameters
        $parameters = $route->parameters();
        
        foreach (['id', 'user', 'transaction', 'wallet'] as $param) {
            if (isset($parameters[$param])) {
                $value = $parameters[$param];
                return is_numeric($value) ? (int) $value : null;
            }
        }

        return null;
    }

    /**
     * Get description for the audit log.
     */
    private function getDescription(Request $request, Response $response): string
    {
        $route = $request->route()?->getName();
        $method = $request->method();
        $status = $response->getStatusCode();

        $description = "{$method} {$route}";

        if ($status >= 400) {
            $description .= " (Failed - {$status})";
        } else {
            $description .= " (Success - {$status})";
        }

        return $description;
    }

    /**
     * Get metadata for the audit log.
     */
    private function getMetadata(Request $request, Response $response): array
    {
        $metadata = [
            'request_size' => strlen($request->getContent()),
            'response_size' => strlen($response->getContent()),
            'session_id' => $request->session()?->getId(),
        ];

        // Add request parameters (filtered)
        $parameters = $request->all();
        $metadata['request_parameters'] = $this->filterSensitiveParameters($parameters);

        // Add response data for certain operations
        if ($this->shouldIncludeResponseData($request, $response)) {
            $responseData = json_decode($response->getContent(), true);
            if ($responseData) {
                $metadata['response_data'] = $this->filterSensitiveResponseData($responseData);
            }
        }

        return $metadata;
    }

    /**
     * Filter sensitive data from audit log.
     */
    private function filterSensitiveData(array $auditData, Request $request): array
    {
        // Remove sensitive headers
        $sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
        
        if (isset($auditData['metadata']['headers'])) {
            foreach ($sensitiveHeaders as $header) {
                unset($auditData['metadata']['headers'][$header]);
            }
        }

        return $auditData;
    }

    /**
     * Filter sensitive parameters.
     */
    private function filterSensitiveParameters(array $parameters): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'current_password',
            'pin',
            'otp',
            'token',
            'api_key',
            'secret',
            'private_key',
            'credit_card_number',
            'cvv',
            'ssn',
            'national_id',
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($parameters[$field])) {
                $parameters[$field] = '[FILTERED]';
            }
        }

        return $parameters;
    }

    /**
     * Filter sensitive response data.
     */
    private function filterSensitiveResponseData(array $responseData): array
    {
        $sensitiveFields = [
            'password',
            'token',
            'api_key',
            'secret',
            'private_key',
            'balance',
            'account_number',
            'routing_number',
        ];

        return $this->recursiveFilter($responseData, $sensitiveFields);
    }

    /**
     * Recursively filter sensitive data.
     */
    private function recursiveFilter(array $data, array $sensitiveFields): array
    {
        foreach ($data as $key => $value) {
            if (in_array($key, $sensitiveFields)) {
                $data[$key] = '[FILTERED]';
            } elseif (is_array($value)) {
                $data[$key] = $this->recursiveFilter($value, $sensitiveFields);
            }
        }

        return $data;
    }

    /**
     * Check if request is a financial operation.
     */
    private function isFinancialOperation(Request $request): bool
    {
        $route = $request->route()?->getName();
        
        $financialRoutes = [
            'transactions.*',
            'wallets.*',
            'transfers.*',
            'payments.*',
            'exchange.*',
        ];

        foreach ($financialRoutes as $pattern) {
            if (fnmatch($pattern, $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if request is an admin operation.
     */
    private function isAdminOperation(Request $request): bool
    {
        $route = $request->route()?->getName();
        return str_starts_with($route, 'admin.') || 
               str_contains($request->path(), '/admin/');
    }

    /**
     * Check if request is an authentication operation.
     */
    private function isAuthOperation(Request $request): bool
    {
        $route = $request->route()?->getName();
        return str_starts_with($route, 'auth.');
    }

    /**
     * Check if request accesses sensitive data.
     */
    private function isSensitiveDataAccess(Request $request): bool
    {
        $route = $request->route()?->getName();
        
        $sensitiveRoutes = [
            'users.show',
            'users.profile',
            'wallets.balance',
            'reports.*',
            'fraud.*',
        ];

        foreach ($sensitiveRoutes as $pattern) {
            if (fnmatch($pattern, $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if response data should be included in audit log.
     */
    private function shouldIncludeResponseData(Request $request, Response $response): bool
    {
        // Include response data for failed requests
        if ($response->getStatusCode() >= 400) {
            return true;
        }

        // Include response data for certain operations
        $route = $request->route()?->getName();
        $includeRoutes = [
            'auth.login',
            'auth.register',
            'transactions.create',
            'wallets.create',
        ];

        return in_array($route, $includeRoutes);
    }
}
