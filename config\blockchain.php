<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Blockchain Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for blockchain integrations and cryptocurrency support
    |
    */

    'enabled' => env('BLOCKCHAIN_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Supported Networks
    |--------------------------------------------------------------------------
    */

    'networks' => [
        'bitcoin' => [
            'enabled' => env('BITCOIN_ENABLED', true),
            'network' => env('BITCOIN_NETWORK', 'testnet'), // mainnet, testnet
            'rpc_url' => env('BITCOIN_RPC_URL', 'http://localhost:8332'),
            'rpc_username' => env('BITCOIN_RPC_USERNAME'),
            'rpc_password' => env('BITCOIN_RPC_PASSWORD'),
            'confirmations_required' => 3,
            'fee_per_byte' => 10, // satoshis
            'dust_threshold' => 546, // satoshis
        ],

        'ethereum' => [
            'enabled' => env('ETHEREUM_ENABLED', true),
            'network' => env('ETHEREUM_NETWORK', 'goerli'), // mainnet, goerli, sepolia
            'rpc_url' => env('ETHEREUM_RPC_URL', 'https://goerli.infura.io/v3/YOUR_PROJECT_ID'),
            'private_key' => env('ETHEREUM_PRIVATE_KEY'),
            'contract_address' => env('ETHEREUM_CONTRACT_ADDRESS'),
            'gas_limit' => 21000,
            'gas_price' => 20000000000, // 20 gwei
            'confirmations_required' => 12,
        ],

        'binance_smart_chain' => [
            'enabled' => env('BSC_ENABLED', false),
            'network' => env('BSC_NETWORK', 'testnet'), // mainnet, testnet
            'rpc_url' => env('BSC_RPC_URL', 'https://data-seed-prebsc-1-s1.binance.org:8545'),
            'private_key' => env('BSC_PRIVATE_KEY'),
            'gas_limit' => 21000,
            'gas_price' => 5000000000, // 5 gwei
            'confirmations_required' => 15,
        ],

        'polygon' => [
            'enabled' => env('POLYGON_ENABLED', false),
            'network' => env('POLYGON_NETWORK', 'mumbai'), // mainnet, mumbai
            'rpc_url' => env('POLYGON_RPC_URL', 'https://rpc-mumbai.maticvigil.com'),
            'private_key' => env('POLYGON_PRIVATE_KEY'),
            'gas_limit' => 21000,
            'gas_price' => 1000000000, // 1 gwei
            'confirmations_required' => 20,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cryptocurrency Configuration
    |--------------------------------------------------------------------------
    */

    'cryptocurrencies' => [
        'BTC' => [
            'name' => 'Bitcoin',
            'symbol' => 'BTC',
            'network' => 'bitcoin',
            'decimals' => 8,
            'min_amount' => 0.00001, // 1000 satoshis
            'max_amount' => 21000000,
            'withdrawal_fee' => 0.0005,
            'deposit_fee' => 0,
        ],

        'ETH' => [
            'name' => 'Ethereum',
            'symbol' => 'ETH',
            'network' => 'ethereum',
            'decimals' => 18,
            'min_amount' => 0.001,
            'max_amount' => 1000000,
            'withdrawal_fee' => 0.005,
            'deposit_fee' => 0,
        ],

        'USDT' => [
            'name' => 'Tether USD',
            'symbol' => 'USDT',
            'network' => 'ethereum',
            'contract_address' => '******************************************',
            'decimals' => 6,
            'min_amount' => 1,
            'max_amount' => 1000000,
            'withdrawal_fee' => 5,
            'deposit_fee' => 0,
        ],

        'USDC' => [
            'name' => 'USD Coin',
            'symbol' => 'USDC',
            'network' => 'ethereum',
            'contract_address' => '******************************************',
            'decimals' => 6,
            'min_amount' => 1,
            'max_amount' => 1000000,
            'withdrawal_fee' => 3,
            'deposit_fee' => 0,
        ],

        'BNB' => [
            'name' => 'Binance Coin',
            'symbol' => 'BNB',
            'network' => 'binance_smart_chain',
            'decimals' => 18,
            'min_amount' => 0.01,
            'max_amount' => 100000,
            'withdrawal_fee' => 0.005,
            'deposit_fee' => 0,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Wallet Configuration
    |--------------------------------------------------------------------------
    */

    'wallets' => [
        'hot_wallet_threshold' => 100000, // USD equivalent
        'cold_storage_percentage' => 0.8, // 80% in cold storage
        'auto_sweep_enabled' => true,
        'auto_sweep_threshold' => 10000, // USD equivalent
        
        'generation' => [
            'algorithm' => 'secp256k1',
            'entropy_bits' => 256,
            'mnemonic_words' => 24,
        ],

        'security' => [
            'multi_signature' => [
                'enabled' => true,
                'required_signatures' => 2,
                'total_signers' => 3,
            ],
            'hardware_security_module' => env('HSM_ENABLED', false),
            'key_encryption' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Monitoring
    |--------------------------------------------------------------------------
    */

    'monitoring' => [
        'block_confirmations' => [
            'bitcoin' => 3,
            'ethereum' => 12,
            'binance_smart_chain' => 15,
            'polygon' => 20,
        ],

        'polling_interval' => 30, // seconds
        'max_polling_attempts' => 120, // 1 hour with 30s intervals
        
        'alerts' => [
            'large_transaction_threshold' => 50000, // USD
            'unusual_activity_threshold' => 100000, // USD per hour
            'failed_transaction_threshold' => 5, // consecutive failures
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Integrations
    |--------------------------------------------------------------------------
    */

    'apis' => [
        'blockchain_info' => [
            'enabled' => true,
            'base_url' => 'https://blockchain.info/api',
            'rate_limit' => 300, // requests per hour
        ],

        'etherscan' => [
            'enabled' => true,
            'api_key' => env('ETHERSCAN_API_KEY'),
            'base_url' => 'https://api.etherscan.io/api',
            'rate_limit' => 5, // requests per second
        ],

        'bscscan' => [
            'enabled' => false,
            'api_key' => env('BSCSCAN_API_KEY'),
            'base_url' => 'https://api.bscscan.com/api',
            'rate_limit' => 5, // requests per second
        ],

        'polygonscan' => [
            'enabled' => false,
            'api_key' => env('POLYGONSCAN_API_KEY'),
            'base_url' => 'https://api.polygonscan.com/api',
            'rate_limit' => 5, // requests per second
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Price Feeds
    |--------------------------------------------------------------------------
    */

    'price_feeds' => [
        'primary' => 'coingecko',
        'fallback' => 'coinmarketcap',
        'update_interval' => 60, // seconds
        'cache_ttl' => 300, // 5 minutes
        
        'providers' => [
            'coingecko' => [
                'api_key' => env('COINGECKO_API_KEY'),
                'base_url' => 'https://api.coingecko.com/api/v3',
                'rate_limit' => 50, // requests per minute
            ],

            'coinmarketcap' => [
                'api_key' => env('COINMARKETCAP_API_KEY'),
                'base_url' => 'https://pro-api.coinmarketcap.com/v1',
                'rate_limit' => 333, // requests per day for basic plan
            ],

            'binance' => [
                'base_url' => 'https://api.binance.com/api/v3',
                'rate_limit' => 1200, // requests per minute
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance & Reporting
    |--------------------------------------------------------------------------
    */

    'compliance' => [
        'aml_monitoring' => true,
        'transaction_reporting' => true,
        'suspicious_activity_threshold' => 10000, // USD
        
        'reporting' => [
            'daily_volume_report' => true,
            'large_transaction_report' => true,
            'failed_transaction_report' => true,
            'wallet_balance_report' => true,
        ],

        'retention' => [
            'transaction_logs' => 2555, // 7 years in days
            'wallet_logs' => 2555,
            'api_logs' => 365, // 1 year
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Development & Testing
    |--------------------------------------------------------------------------
    */

    'development' => [
        'testnet_enabled' => env('BLOCKCHAIN_TESTNET', true),
        'mock_transactions' => env('BLOCKCHAIN_MOCK', false),
        'debug_logging' => env('BLOCKCHAIN_DEBUG', false),
        
        'faucets' => [
            'bitcoin_testnet' => 'https://testnet-faucet.mempool.co',
            'ethereum_goerli' => 'https://goerlifaucet.com',
            'bsc_testnet' => 'https://testnet.binance.org/faucet-smart',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    */

    'error_handling' => [
        'retry_attempts' => 3,
        'retry_delay' => 5, // seconds
        'timeout' => 30, // seconds
        
        'fallback_enabled' => true,
        'circuit_breaker' => [
            'failure_threshold' => 5,
            'recovery_timeout' => 300, // 5 minutes
        ],
    ],

];
