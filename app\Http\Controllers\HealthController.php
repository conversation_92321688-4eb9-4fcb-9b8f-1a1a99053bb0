<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Transaction;
use App\Models\Currency;

class HealthController extends Controller
{
    /**
     * Get system health status.
     */
    public function index(): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'queue' => $this->checkQueue(),
            'storage' => $this->checkStorage(),
            'external_apis' => $this->checkExternalAPIs(),
            'system_resources' => $this->checkSystemResources(),
        ];

        $overallStatus = $this->determineOverallStatus($checks);

        return response()->json([
            'status' => $overallStatus,
            'timestamp' => now()->toISOString(),
            'version' => config('financial.system.version', '1.0.0'),
            'environment' => app()->environment(),
            'checks' => $checks,
            'uptime' => $this->getUptime(),
            'system_info' => $this->getSystemInfo(),
        ]);
    }

    /**
     * Get detailed health check.
     */
    public function detailed(): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabaseDetailed(),
            'cache' => $this->checkCacheDetailed(),
            'queue' => $this->checkQueueDetailed(),
            'storage' => $this->checkStorageDetailed(),
            'external_apis' => $this->checkExternalAPIsDetailed(),
            'financial_system' => $this->checkFinancialSystem(),
            'security' => $this->checkSecurity(),
            'performance' => $this->checkPerformance(),
        ];

        $overallStatus = $this->determineOverallStatus($checks);

        return response()->json([
            'status' => $overallStatus,
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
            'metrics' => $this->getSystemMetrics(),
            'recommendations' => $this->getRecommendations($checks),
        ]);
    }

    /**
     * Check database connectivity and performance.
     */
    private function checkDatabase(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'message' => 'Database connection successful',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check database with detailed information.
     */
    private function checkDatabaseDetailed(): array
    {
        try {
            $start = microtime(true);
            
            // Basic connectivity
            DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            // Get database stats
            $userCount = User::count();
            $transactionCount = Transaction::count();
            $todayTransactions = Transaction::whereDate('created_at', today())->count();

            // Check for slow queries
            $slowQueries = $this->getSlowQueries();

            // Check connection pool
            $connections = $this->getDatabaseConnections();

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'statistics' => [
                    'total_users' => $userCount,
                    'total_transactions' => $transactionCount,
                    'today_transactions' => $todayTransactions,
                ],
                'connections' => $connections,
                'slow_queries' => count($slowQueries),
                'message' => 'Database is healthy',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Database check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache system.
     */
    private function checkCache(): array
    {
        try {
            $key = 'health_check_' . time();
            $value = 'test_value';

            Cache::put($key, $value, 60);
            $retrieved = Cache::get($key);
            Cache::forget($key);

            if ($retrieved === $value) {
                return [
                    'status' => 'healthy',
                    'message' => 'Cache is working properly',
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Cache read/write test failed',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Cache check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache with detailed information.
     */
    private function checkCacheDetailed(): array
    {
        try {
            $start = microtime(true);
            
            $key = 'health_check_' . time();
            $value = 'test_value';

            Cache::put($key, $value, 60);
            $retrieved = Cache::get($key);
            Cache::forget($key);
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            if ($retrieved === $value) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $responseTime,
                    'driver' => config('cache.default'),
                    'hit_ratio' => $this->getCacheHitRatio(),
                    'memory_usage' => $this->getCacheMemoryUsage(),
                    'message' => 'Cache is working properly',
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Cache read/write test failed',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Cache check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue system.
     */
    private function checkQueue(): array
    {
        try {
            $queueSize = Queue::size();
            $failedJobs = DB::table('failed_jobs')->count();

            $status = 'healthy';
            $message = 'Queue is working properly';

            if ($queueSize > 1000) {
                $status = 'warning';
                $message = 'Queue size is high: ' . $queueSize;
            }

            if ($failedJobs > 100) {
                $status = 'unhealthy';
                $message = 'Too many failed jobs: ' . $failedJobs;
            }

            return [
                'status' => $status,
                'queue_size' => $queueSize,
                'failed_jobs' => $failedJobs,
                'message' => $message,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Queue check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue with detailed information.
     */
    private function checkQueueDetailed(): array
    {
        try {
            $queueStats = [
                'default' => Queue::size('default'),
                'high-priority' => Queue::size('high-priority'),
                'emails' => Queue::size('emails'),
                'sms' => Queue::size('sms'),
            ];

            $failedJobs = DB::table('failed_jobs')->count();
            $recentFailures = DB::table('failed_jobs')
                ->where('failed_at', '>', now()->subHour())
                ->count();

            $totalQueued = array_sum($queueStats);
            
            $status = 'healthy';
            $message = 'All queues are healthy';

            if ($totalQueued > 1000) {
                $status = 'warning';
                $message = 'High queue volume detected';
            }

            if ($failedJobs > 100 || $recentFailures > 10) {
                $status = 'unhealthy';
                $message = 'High failure rate detected';
            }

            return [
                'status' => $status,
                'queues' => $queueStats,
                'total_queued' => $totalQueued,
                'failed_jobs' => $failedJobs,
                'recent_failures' => $recentFailures,
                'message' => $message,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Queue detailed check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage system.
     */
    private function checkStorage(): array
    {
        try {
            $testFile = 'health_check_' . time() . '.txt';
            $testContent = 'Health check test';

            Storage::put($testFile, $testContent);
            $retrieved = Storage::get($testFile);
            Storage::delete($testFile);

            if ($retrieved === $testContent) {
                $freeSpace = disk_free_space(storage_path());
                $totalSpace = disk_total_space(storage_path());
                $usedPercentage = round((($totalSpace - $freeSpace) / $totalSpace) * 100, 2);

                $status = 'healthy';
                $message = 'Storage is working properly';

                if ($usedPercentage > 90) {
                    $status = 'warning';
                    $message = 'Storage usage is high: ' . $usedPercentage . '%';
                }

                return [
                    'status' => $status,
                    'used_percentage' => $usedPercentage,
                    'free_space_gb' => round($freeSpace / 1024 / 1024 / 1024, 2),
                    'message' => $message,
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Storage read/write test failed',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Storage check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage with detailed information.
     */
    private function checkStorageDetailed(): array
    {
        try {
            $storageInfo = [];
            $paths = [
                'logs' => storage_path('logs'),
                'app' => storage_path('app'),
                'cache' => storage_path('framework/cache'),
                'sessions' => storage_path('framework/sessions'),
            ];

            foreach ($paths as $name => $path) {
                if (is_dir($path)) {
                    $size = $this->getDirectorySize($path);
                    $storageInfo[$name] = [
                        'size_mb' => round($size / 1024 / 1024, 2),
                        'writable' => is_writable($path),
                    ];
                }
            }

            $freeSpace = disk_free_space(storage_path());
            $totalSpace = disk_total_space(storage_path());
            $usedPercentage = round((($totalSpace - $freeSpace) / $totalSpace) * 100, 2);

            $status = $usedPercentage > 90 ? 'warning' : 'healthy';

            return [
                'status' => $status,
                'disk_usage' => [
                    'used_percentage' => $usedPercentage,
                    'free_space_gb' => round($freeSpace / 1024 / 1024 / 1024, 2),
                    'total_space_gb' => round($totalSpace / 1024 / 1024 / 1024, 2),
                ],
                'directories' => $storageInfo,
                'message' => 'Storage check completed',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Storage detailed check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check external APIs.
     */
    private function checkExternalAPIs(): array
    {
        $apis = [
            'exchange_rates' => $this->checkExchangeRateAPI(),
            'fraud_detection' => $this->checkFraudDetectionAPI(),
            'payment_gateways' => $this->checkPaymentGateways(),
        ];

        $healthyCount = count(array_filter($apis, fn($api) => $api['status'] === 'healthy'));
        $totalCount = count($apis);

        $overallStatus = $healthyCount === $totalCount ? 'healthy' : 
                        ($healthyCount > $totalCount / 2 ? 'warning' : 'unhealthy');

        return [
            'status' => $overallStatus,
            'healthy_apis' => $healthyCount,
            'total_apis' => $totalCount,
            'apis' => $apis,
        ];
    }

    /**
     * Check external APIs with detailed information.
     */
    private function checkExternalAPIsDetailed(): array
    {
        return $this->checkExternalAPIs();
    }

    /**
     * Check financial system specific components.
     */
    private function checkFinancialSystem(): array
    {
        try {
            $checks = [
                'active_currencies' => Currency::where('is_active', true)->count(),
                'pending_transactions' => Transaction::where('status', 'pending')->count(),
                'failed_transactions_today' => Transaction::whereDate('created_at', today())
                    ->where('status', 'failed')->count(),
                'fraud_alerts_today' => \App\Models\FraudDetection::whereDate('detected_at', today())
                    ->where('status', 'open')->count(),
            ];

            $status = 'healthy';
            $issues = [];

            if ($checks['pending_transactions'] > 1000) {
                $status = 'warning';
                $issues[] = 'High number of pending transactions';
            }

            if ($checks['failed_transactions_today'] > 100) {
                $status = 'unhealthy';
                $issues[] = 'High failure rate today';
            }

            if ($checks['fraud_alerts_today'] > 50) {
                $status = 'warning';
                $issues[] = 'High fraud alert volume';
            }

            return [
                'status' => $status,
                'metrics' => $checks,
                'issues' => $issues,
                'message' => empty($issues) ? 'Financial system is healthy' : implode(', ', $issues),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Financial system check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Helper methods for detailed checks.
     */
    private function getSlowQueries(): array
    {
        // This would typically query the slow query log
        // For now, return empty array
        return [];
    }

    private function getDatabaseConnections(): array
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            return [
                'active' => $result[0]->Value ?? 0,
                'max' => ini_get('mysql.max_connections') ?: 'unknown',
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    private function getCacheHitRatio(): float
    {
        // This would typically get actual cache statistics
        // For now, return a mock value
        return 85.5;
    }

    private function getCacheMemoryUsage(): array
    {
        // This would typically get actual memory usage
        // For now, return mock values
        return [
            'used_mb' => 128,
            'max_mb' => 512,
            'percentage' => 25,
        ];
    }

    private function checkExchangeRateAPI(): array
    {
        try {
            // Mock check - in real implementation, make actual API call
            return [
                'status' => 'healthy',
                'response_time_ms' => 150,
                'last_update' => now()->subMinutes(5)->toISOString(),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    private function checkFraudDetectionAPI(): array
    {
        try {
            // Mock check
            return [
                'status' => 'healthy',
                'response_time_ms' => 200,
                'accuracy' => 94.5,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    private function checkPaymentGateways(): array
    {
        $gateways = ['paypal', 'stripe', 'wise'];
        $results = [];

        foreach ($gateways as $gateway) {
            try {
                // Mock check
                $results[$gateway] = [
                    'status' => 'healthy',
                    'response_time_ms' => rand(100, 300),
                ];
            } catch (\Exception $e) {
                $results[$gateway] = [
                    'status' => 'unhealthy',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    private function checkSecurity(): array
    {
        return [
            'status' => 'healthy',
            'ssl_certificate' => [
                'valid' => true,
                'expires_in_days' => 90,
            ],
            'security_headers' => [
                'hsts' => true,
                'csp' => true,
                'x_frame_options' => true,
            ],
            'failed_login_attempts_today' => rand(5, 20),
        ];
    }

    private function checkPerformance(): array
    {
        return [
            'status' => 'healthy',
            'average_response_time_ms' => 250,
            'memory_usage_percentage' => 65,
            'cpu_usage_percentage' => 45,
            'active_sessions' => rand(100, 500),
        ];
    }

    private function getDirectorySize(string $path): int
    {
        $size = 0;
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($files as $file) {
            $size += $file->getSize();
        }

        return $size;
    }

    private function determineOverallStatus(array $checks): string
    {
        $statuses = array_column($checks, 'status');
        
        if (in_array('unhealthy', $statuses)) {
            return 'unhealthy';
        }
        
        if (in_array('warning', $statuses)) {
            return 'warning';
        }
        
        return 'healthy';
    }

    private function getUptime(): string
    {
        $uptime = time() - filectime(base_path());
        return gmdate('H:i:s', $uptime);
    }

    private function getSystemInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
        ];
    }

    private function getSystemMetrics(): array
    {
        return [
            'memory_usage' => [
                'current_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
                'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'limit' => ini_get('memory_limit'),
            ],
            'request_count_today' => rand(1000, 5000),
            'error_rate_today' => rand(1, 5) . '%',
            'average_response_time_ms' => rand(200, 400),
        ];
    }

    private function getRecommendations(array $checks): array
    {
        $recommendations = [];

        foreach ($checks as $component => $check) {
            if ($check['status'] === 'warning' || $check['status'] === 'unhealthy') {
                switch ($component) {
                    case 'database':
                        $recommendations[] = 'Consider optimizing database queries or scaling database resources';
                        break;
                    case 'cache':
                        $recommendations[] = 'Check cache configuration and consider increasing cache memory';
                        break;
                    case 'queue':
                        $recommendations[] = 'Scale queue workers or investigate failed jobs';
                        break;
                    case 'storage':
                        $recommendations[] = 'Clean up old files or increase storage capacity';
                        break;
                    case 'external_apis':
                        $recommendations[] = 'Check external API status and implement fallback mechanisms';
                        break;
                }
            }
        }

        return $recommendations;
    }
}
