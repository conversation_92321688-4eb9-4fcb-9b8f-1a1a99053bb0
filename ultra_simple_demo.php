<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\DB;

try {
    echo "⚡ Ultra Simple Demo Data\n";
    echo "========================\n\n";
    
    // Get admin user
    $user = User::where('email', '<EMAIL>')->first();
    if (!$user) {
        echo "❌ Admin user not found!\n";
        exit(1);
    }
    
    echo "✅ User: {$user->email}\n";
    
    // Clear existing demo transactions
    DB::table('transactions')->where('sender_name', 'LIKE', '%تجريبي%')->delete();
    echo "✅ Cleared old demo data\n";
    
    // Get first currency
    $currency = DB::table('currencies')->first();
    if (!$currency) {
        // Create a simple currency
        $currencyId = DB::table('currencies')->insertGetId([
            'code' => 'SAR',
            'name' => 'Saudi Riyal',
            'symbol' => 'ر.س',
            'exchange_rate' => 1.0,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    } else {
        $currencyId = $currency->id;
    }
    
    echo "✅ Currency ready\n";
    
    // Create simple transactions using raw SQL
    $transactions = [
        ['أحمد محمد', 500, 'completed'],
        ['فاطمة علي', 750, 'completed'],
        ['محمد عبدالله', 300, 'pending'],
        ['سارة أحمد', 1200, 'completed'],
        ['علي حسن', 450, 'processing'],
        ['نورا سالم', 800, 'completed'],
        ['خالد عمر', 600, 'pending'],
        ['مريم يوسف', 950, 'completed'],
    ];
    
    foreach ($transactions as $index => $transaction) {
        $indexPlusOne = $index + 1;
        $transactionNumber = 'DEMO' . str_pad($indexPlusOne, 6, '0', STR_PAD_LEFT);
        $referenceNumber = 'REF' . str_pad($indexPlusOne, 8, '0', STR_PAD_LEFT);
        $amount = $transaction[1];
        $fee = round($amount * 0.02, 2);
        $netAmount = $amount - $fee;
        $createdAt = now()->subDays(rand(0, 7));
        
        DB::table('transactions')->insert([
            'transaction_number' => $transactionNumber,
            'reference_number' => $referenceNumber,
            'type' => 'transfer',
            'category' => 'international',
            'sender_id' => $user->id,
            'sender_name' => $user->first_name . ' ' . $user->last_name . ' (تجريبي)',
            'sender_phone' => '+966501234567',
            'receiver_name' => $transaction[0],
            'receiver_phone' => '+1555000' . str_pad($index, 3, '0', STR_PAD_LEFT),
            'currency_id' => $currencyId,
            'amount' => $amount,
            'exchange_rate' => 3.75,
            'target_currency_id' => $currencyId,
            'target_amount' => round($amount / 3.75, 2),
            'commission_amount' => $fee,
            'commission_rate' => 0.02,
            'total_fees' => $fee,
            'net_amount' => $netAmount,
            'status' => $transaction[2],
            'payment_method' => 'bank_transfer',
            'purpose' => 'family_support',
            'notes' => 'تحويل تجريبي للاختبار',
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
            'processed_at' => $transaction[2] === 'completed' ? $createdAt->addMinutes(30) : null,
        ]);
        
        $transactionNum = $index + 1;
        echo "✅ Transaction {$transactionNum}: {$transaction[0]} - {$amount} SAR ({$transaction[2]})\n";
    }
    
    // Calculate stats
    $totalTransactions = DB::table('transactions')->where('sender_id', $user->id)->count();
    $completedTransactions = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'completed')->count();
    $pendingTransactions = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'pending')->count();
    $processingTransactions = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'processing')->count();
    $totalAmount = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'completed')->sum('amount');
    
    echo "\n📊 Dashboard Statistics:\n";
    echo "========================\n";
    echo "📈 Total Transactions: {$totalTransactions}\n";
    echo "✅ Completed: {$completedTransactions}\n";
    echo "⏳ Pending: {$pendingTransactions}\n";
    echo "🔄 Processing: {$processingTransactions}\n";
    echo "💰 Total Amount: " . number_format($totalAmount, 2) . " SAR\n";
    
    $successRate = $totalTransactions > 0 ? round(($completedTransactions / $totalTransactions) * 100, 1) : 0;
    echo "📊 Success Rate: {$successRate}%\n";
    
    echo "\n🎯 Test Dashboard Now:\n";
    echo "======================\n";
    echo "🔗 URL: http://localhost:8000/dashboard\n";
    echo "👤 Login: <EMAIL> / password123\n";
    echo "⚡ Should load SUPER FAST!\n";
    
    echo "\n✅ Ultra simple demo data created!\n";
    echo "🚀 Dashboard is now optimized and ready!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
