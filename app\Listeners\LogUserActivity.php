<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Events\TransactionCreated;
use App\Events\TransactionStatusUpdated;
use App\Events\FraudAlertTriggered;
use App\Events\LoginAttempt;
use App\Models\AuditLog;
use App\Models\UserActivity;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class LogUserActivity implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        try {
            match(get_class($event)) {
                UserRegistered::class => $this->handleUserRegistered($event),
                TransactionCreated::class => $this->handleTransactionCreated($event),
                TransactionStatusUpdated::class => $this->handleTransactionStatusUpdated($event),
                FraudAlertTriggered::class => $this->handleFraudAlert($event),
                LoginAttempt::class => $this->handleLoginAttempt($event),
                default => $this->handleGenericEvent($event)
            };

        } catch (\Exception $e) {
            Log::error('Failed to log user activity', [
                'event_class' => get_class($event),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle user registration event.
     */
    private function handleUserRegistered(UserRegistered $event): void
    {
        $user = $event->user;

        $this->logActivity([
            'user_id' => $user->id,
            'activity_type' => 'user_registration',
            'description' => 'User registered successfully',
            'metadata' => [
                'email' => $user->email,
                'country_id' => $user->country_id,
                'registration_method' => 'web', // Could be 'web', 'mobile', 'api'
                'user_agent' => request()->userAgent(),
                'ip_address' => request()->ip(),
            ],
            'severity' => 'info',
        ]);

        // Update user statistics
        $this->updateUserStatistics($user->id, 'registration');

        Log::info('User registration activity logged', [
            'user_id' => $user->id,
            'email' => $user->email,
        ]);
    }

    /**
     * Handle transaction creation event.
     */
    private function handleTransactionCreated(TransactionCreated $event): void
    {
        $transaction = $event->transaction;
        $user = $transaction->sender;

        $this->logActivity([
            'user_id' => $user->id,
            'activity_type' => 'transaction_created',
            'description' => "Transaction created: {$transaction->type}",
            'metadata' => [
                'transaction_id' => $transaction->id,
                'reference_number' => $transaction->reference_number,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'currency_code' => $transaction->currency->code,
                'receiver_country_id' => $transaction->receiver_country_id,
                'payment_method' => $transaction->payment_method,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ],
            'severity' => 'info',
        ]);

        // Update user statistics
        $this->updateUserStatistics($user->id, 'transaction_created');
        $this->updateTransactionStatistics($user->id, $transaction);

        Log::info('Transaction creation activity logged', [
            'user_id' => $user->id,
            'transaction_id' => $transaction->id,
            'reference_number' => $transaction->reference_number,
        ]);
    }

    /**
     * Handle transaction status update event.
     */
    private function handleTransactionStatusUpdated(TransactionStatusUpdated $event): void
    {
        $transaction = $event->transaction;
        $oldStatus = $event->oldStatus;
        $newStatus = $event->newStatus;
        $user = $transaction->sender;

        $this->logActivity([
            'user_id' => $user->id,
            'activity_type' => 'transaction_status_updated',
            'description' => "Transaction status changed from {$oldStatus} to {$newStatus}",
            'metadata' => [
                'transaction_id' => $transaction->id,
                'reference_number' => $transaction->reference_number,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'amount' => $transaction->amount,
                'currency_code' => $transaction->currency->code,
                'updated_by' => auth()->id(),
                'ip_address' => request()->ip(),
            ],
            'severity' => $this->getStatusUpdateSeverity($newStatus),
        ]);

        // Update user statistics for completed transactions
        if ($newStatus === 'completed') {
            $this->updateUserStatistics($user->id, 'transaction_completed');
        } elseif ($newStatus === 'failed') {
            $this->updateUserStatistics($user->id, 'transaction_failed');
        }

        Log::info('Transaction status update activity logged', [
            'user_id' => $user->id,
            'transaction_id' => $transaction->id,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
        ]);
    }

    /**
     * Handle fraud alert event.
     */
    private function handleFraudAlert(FraudAlertTriggered $event): void
    {
        $fraudDetection = $event->fraudDetection;
        $user = $fraudDetection->user;
        $transaction = $fraudDetection->transaction;

        $this->logActivity([
            'user_id' => $user->id,
            'activity_type' => 'fraud_alert',
            'description' => "Fraud alert triggered: {$fraudDetection->risk_level} risk",
            'metadata' => [
                'fraud_detection_id' => $fraudDetection->id,
                'transaction_id' => $transaction->id ?? null,
                'risk_level' => $fraudDetection->risk_level,
                'risk_score' => $fraudDetection->risk_score,
                'fraud_indicators' => $fraudDetection->fraud_indicators,
                'detection_method' => $fraudDetection->detection_method,
                'ip_address' => request()->ip(),
            ],
            'severity' => $this->getFraudAlertSeverity($fraudDetection->risk_level),
        ]);

        // Update user risk statistics
        $this->updateUserStatistics($user->id, 'fraud_alert');
        $this->updateRiskStatistics($user->id, $fraudDetection->risk_level);

        Log::warning('Fraud alert activity logged', [
            'user_id' => $user->id,
            'fraud_detection_id' => $fraudDetection->id,
            'risk_level' => $fraudDetection->risk_level,
        ]);
    }

    /**
     * Handle login attempt event.
     */
    private function handleLoginAttempt(LoginAttempt $event): void
    {
        $user = $event->user;
        $successful = $event->successful;
        $ipAddress = $event->ipAddress;

        $this->logActivity([
            'user_id' => $user->id,
            'activity_type' => 'login_attempt',
            'description' => $successful ? 'Successful login' : 'Failed login attempt',
            'metadata' => [
                'successful' => $successful,
                'ip_address' => $ipAddress,
                'user_agent' => request()->userAgent(),
                'location' => $this->getLocationFromIP($ipAddress),
                'device_fingerprint' => $this->getDeviceFingerprint(),
            ],
            'severity' => $successful ? 'info' : 'warning',
        ]);

        // Update login statistics
        $this->updateUserStatistics($user->id, $successful ? 'successful_login' : 'failed_login');

        // Track suspicious login patterns
        if (!$successful) {
            $this->trackFailedLoginAttempts($user->id, $ipAddress);
        }

        Log::info('Login attempt activity logged', [
            'user_id' => $user->id,
            'successful' => $successful,
            'ip_address' => $ipAddress,
        ]);
    }

    /**
     * Handle generic events.
     */
    private function handleGenericEvent($event): void
    {
        $eventClass = get_class($event);
        
        Log::info('Generic event activity logged', [
            'event_class' => $eventClass,
            'event_data' => method_exists($event, 'toArray') ? $event->toArray() : 'N/A',
        ]);
    }

    /**
     * Log activity to database.
     */
    private function logActivity(array $data): void
    {
        UserActivity::create([
            'user_id' => $data['user_id'],
            'activity_type' => $data['activity_type'],
            'description' => $data['description'],
            'metadata' => $data['metadata'],
            'severity' => $data['severity'] ?? 'info',
            'ip_address' => $data['metadata']['ip_address'] ?? request()->ip(),
            'user_agent' => $data['metadata']['user_agent'] ?? request()->userAgent(),
            'created_at' => now(),
        ]);
    }

    /**
     * Update user statistics.
     */
    private function updateUserStatistics(int $userId, string $activityType): void
    {
        $key = "user_stats:{$userId}:{$activityType}";
        $dailyKey = $key . ':' . now()->format('Y-m-d');
        $monthlyKey = $key . ':' . now()->format('Y-m');

        // Increment daily counter
        Cache::increment($dailyKey, 1);
        Cache::expire($dailyKey, 86400); // 24 hours

        // Increment monthly counter
        Cache::increment($monthlyKey, 1);
        Cache::expire($monthlyKey, 2592000); // 30 days

        // Update total counter
        Cache::increment($key, 1);
    }

    /**
     * Update transaction statistics.
     */
    private function updateTransactionStatistics(int $userId, $transaction): void
    {
        $amountKey = "user_transaction_amount:{$userId}:" . now()->format('Y-m-d');
        $currentAmount = Cache::get($amountKey, 0);
        Cache::put($amountKey, $currentAmount + $transaction->amount, 86400);

        // Track transaction types
        $typeKey = "user_transaction_type:{$userId}:{$transaction->type}:" . now()->format('Y-m-d');
        Cache::increment($typeKey, 1);
        Cache::expire($typeKey, 86400);
    }

    /**
     * Update risk statistics.
     */
    private function updateRiskStatistics(int $userId, string $riskLevel): void
    {
        $key = "user_risk:{$userId}:{$riskLevel}:" . now()->format('Y-m-d');
        Cache::increment($key, 1);
        Cache::expire($key, 86400);
    }

    /**
     * Track failed login attempts.
     */
    private function trackFailedLoginAttempts(int $userId, string $ipAddress): void
    {
        $userKey = "failed_logins:user:{$userId}";
        $ipKey = "failed_logins:ip:{$ipAddress}";

        $userAttempts = Cache::increment($userKey, 1);
        $ipAttempts = Cache::increment($ipKey, 1);

        // Set expiry for 1 hour
        Cache::expire($userKey, 3600);
        Cache::expire($ipKey, 3600);

        // Log if attempts exceed threshold
        if ($userAttempts >= 5) {
            Log::warning('Multiple failed login attempts detected', [
                'user_id' => $userId,
                'attempts' => $userAttempts,
                'ip_address' => $ipAddress,
            ]);
        }
    }

    /**
     * Get severity level for status updates.
     */
    private function getStatusUpdateSeverity(string $status): string
    {
        return match($status) {
            'completed' => 'info',
            'failed', 'cancelled', 'blocked' => 'warning',
            'processing' => 'info',
            default => 'info'
        };
    }

    /**
     * Get severity level for fraud alerts.
     */
    private function getFraudAlertSeverity(string $riskLevel): string
    {
        return match($riskLevel) {
            'critical' => 'critical',
            'high' => 'error',
            'medium' => 'warning',
            'low' => 'info',
            default => 'warning'
        };
    }

    /**
     * Get location from IP address (mock implementation).
     */
    private function getLocationFromIP(string $ipAddress): ?array
    {
        // This would integrate with a geolocation service
        return [
            'country' => 'Saudi Arabia',
            'city' => 'Riyadh',
            'ip' => $ipAddress,
        ];
    }

    /**
     * Get device fingerprint (mock implementation).
     */
    private function getDeviceFingerprint(): ?string
    {
        // This would generate a device fingerprint based on various factors
        return md5(request()->userAgent() . request()->ip());
    }

    /**
     * Handle a job failure.
     */
    public function failed($event, $exception): void
    {
        Log::error('User activity logging job failed', [
            'event_class' => get_class($event),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
