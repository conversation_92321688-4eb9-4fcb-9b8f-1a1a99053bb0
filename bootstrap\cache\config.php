<?php return array (
  'concurrency' => 
  array (
    'default' => 'process',
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => '12',
      'verify' => true,
      'limit' => NULL,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
      'verify' => true,
    ),
    'rehash_on_login' => true,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\resources\\views',
    ),
    'compiled' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework\\views',
  ),
  'app' => 
  array (
    'name' => 'Mony Transfer - Global Financial System',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost:8000',
    'frontend_url' => 'http://localhost:3000',
    'asset_url' => NULL,
    'timezone' => 'UTC',
    'locale' => 'ar',
    'fallback_locale' => 'en',
    'faker_locale' => 'ar_SA',
    'cipher' => 'AES-256-CBC',
    'key' => 'base64:tsM5ORaeYVEQ6bDHdTRalXaHrtMqky2pvSt97GovyNI=',
    'previous_keys' => 
    array (
    ),
    'maintenance' => 
    array (
      'driver' => 'file',
      'store' => 'database',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
      6 => 'Illuminate\\Cookie\\CookieServiceProvider',
      7 => 'Illuminate\\Database\\DatabaseServiceProvider',
      8 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      9 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      10 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      11 => 'Illuminate\\Hashing\\HashServiceProvider',
      12 => 'Illuminate\\Mail\\MailServiceProvider',
      13 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      14 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      15 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      16 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      17 => 'Illuminate\\Queue\\QueueServiceProvider',
      18 => 'Illuminate\\Redis\\RedisServiceProvider',
      19 => 'Illuminate\\Session\\SessionServiceProvider',
      20 => 'Illuminate\\Translation\\TranslationServiceProvider',
      21 => 'Illuminate\\Validation\\ValidationServiceProvider',
      22 => 'Illuminate\\View\\ViewServiceProvider',
      23 => 'App\\Providers\\AppServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Concurrency' => 'Illuminate\\Support\\Facades\\Concurrency',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Context' => 'Illuminate\\Support\\Facades\\Context',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schedule' => 'Illuminate\\Support\\Facades\\Schedule',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Uri' => 'Illuminate\\Support\\Uri',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'sanctum',
        'provider' => 'users',
        'hash' => false,
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'backup' => 
  array (
    'enabled' => true,
    'schedule' => 
    array (
      'database' => 
      array (
        'frequency' => 'daily',
        'time' => '02:00',
        'retention_days' => 30,
        'compress' => true,
        'encrypt' => true,
      ),
      'files' => 
      array (
        'frequency' => 'daily',
        'time' => '03:00',
        'retention_days' => 7,
        'compress' => true,
        'encrypt' => true,
      ),
      'full_system' => 
      array (
        'frequency' => 'weekly',
        'day' => 'sunday',
        'time' => '01:00',
        'retention_weeks' => 4,
        'compress' => true,
        'encrypt' => true,
      ),
      'incremental' => 
      array (
        'frequency' => 'hourly',
        'retention_hours' => 72,
        'compress' => true,
        'encrypt' => false,
      ),
    ),
    'sources' => 
    array (
      'database' => 
      array (
        'enabled' => true,
        'connections' => 
        array (
          0 => 'mysql',
        ),
        'tables' => 
        array (
          'include' => 
          array (
          ),
          'exclude' => 
          array (
            0 => 'sessions',
            1 => 'cache',
            2 => 'telescope_entries',
          ),
        ),
        'options' => 
        array (
          'single_transaction' => true,
          'lock_tables' => false,
          'add_drop_table' => true,
          'extended_insert' => true,
        ),
      ),
      'files' => 
      array (
        'enabled' => true,
        'paths' => 
        array (
          0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app',
          1 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs',
          2 => 'C:\\xampp\\htdocs\\Mony_Transfir\\config',
          3 => 'C:\\xampp\\htdocs\\Mony_Transfir\\.env',
        ),
        'exclude' => 
        array (
          0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework/cache',
          1 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework/sessions',
          2 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework/views',
          3 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/*.log',
        ),
      ),
      'user_uploads' => 
      array (
        'enabled' => true,
        'paths' => 
        array (
          0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/documents',
          1 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/receipts',
          2 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/kyc',
        ),
        'priority' => 'high',
      ),
      'system_config' => 
      array (
        'enabled' => true,
        'paths' => 
        array (
          0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\config',
          1 => 'C:\\xampp\\htdocs\\Mony_Transfir\\routes',
          2 => 'C:\\xampp\\htdocs\\Mony_Transfir\\database/migrations',
          3 => 'C:\\xampp\\htdocs\\Mony_Transfir\\database/seeders',
        ),
      ),
    ),
    'destinations' => 
    array (
      'local' => 
      array (
        'enabled' => true,
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\backups',
        'priority' => 1,
      ),
      's3' => 
      array (
        'enabled' => false,
        'disk' => 's3',
        'bucket' => NULL,
        'path' => 'backups',
        'priority' => 2,
        'storage_class' => 'STANDARD_IA',
      ),
      'google_drive' => 
      array (
        'enabled' => false,
        'disk' => 'google',
        'folder_id' => NULL,
        'priority' => 3,
      ),
      'ftp' => 
      array (
        'enabled' => false,
        'host' => NULL,
        'username' => NULL,
        'password' => NULL,
        'path' => '/backups',
        'priority' => 4,
      ),
      'sftp' => 
      array (
        'enabled' => false,
        'host' => NULL,
        'username' => NULL,
        'private_key' => NULL,
        'path' => '/backups',
        'priority' => 5,
      ),
    ),
    'security' => 
    array (
      'encryption' => 
      array (
        'enabled' => true,
        'algorithm' => 'AES-256-CBC',
        'key' => NULL,
        'compress_before_encrypt' => true,
      ),
      'integrity' => 
      array (
        'enabled' => true,
        'algorithm' => 'sha256',
        'verify_after_backup' => true,
        'store_checksums' => true,
      ),
      'access_control' => 
      array (
        'restrict_access' => true,
        'allowed_ips' => 
        array (
          0 => '127.0.0.1',
        ),
        'require_authentication' => true,
      ),
    ),
    'monitoring' => 
    array (
      'enabled' => true,
      'check_interval' => 3600,
      'max_backup_age_hours' => 25,
      'min_backup_size_mb' => 1,
      'notifications' => 
      array (
        'success' => false,
        'failure' => true,
        'missing' => true,
        'corrupted' => true,
      ),
      'channels' => 
      array (
        'email' => 
        array (
          'enabled' => true,
          'recipients' => 
          array (
            0 => '<EMAIL>',
          ),
        ),
        'slack' => 
        array (
          'enabled' => false,
          'webhook_url' => NULL,
          'channel' => '#backups',
        ),
        'discord' => 
        array (
          'enabled' => false,
          'webhook_url' => NULL,
        ),
      ),
      'health_checks' => 
      array (
        'verify_backup_integrity' => true,
        'test_restore_process' => 'weekly',
        'check_storage_space' => true,
        'validate_encryption' => true,
      ),
    ),
    'cleanup' => 
    array (
      'enabled' => true,
      'strategy' => 'grandfather_father_son',
      'retention_policy' => 
      array (
        'daily' => 7,
        'weekly' => 4,
        'monthly' => 12,
        'yearly' => 5,
      ),
      'cleanup_schedule' => 
      array (
        'frequency' => 'daily',
        'time' => '04:00',
      ),
      'emergency_cleanup' => 
      array (
        'enabled' => true,
        'trigger_when_disk_usage' => 90,
        'keep_minimum_backups' => 3,
      ),
    ),
    'restore' => 
    array (
      'enabled' => true,
      'require_confirmation' => true,
      'create_restore_point' => true,
      'verify_before_restore' => true,
      'test_mode' => false,
      'database_restore' => 
      array (
        'drop_existing_tables' => false,
        'disable_foreign_keys' => true,
        'timeout' => 3600,
      ),
      'file_restore' => 
      array (
        'overwrite_existing' => false,
        'preserve_permissions' => true,
        'timeout' => 7200,
      ),
      'notifications' => 
      array (
        'before_restore' => true,
        'after_restore' => true,
        'on_failure' => true,
      ),
    ),
    'performance' => 
    array (
      'compression' => 
      array (
        'enabled' => true,
        'algorithm' => 'gzip',
        'level' => 6,
      ),
      'parallel_processing' => 
      array (
        'enabled' => true,
        'max_processes' => 4,
        'chunk_size' => 1048576,
      ),
      'memory_limit' => '512M',
      'timeout' => 7200,
      'nice_level' => 10,
    ),
    'disaster_recovery' => 
    array (
      'enabled' => true,
      'offsite_backup_required' => true,
      'minimum_backup_locations' => 2,
      'recovery_time_objective' => 4,
      'recovery_point_objective' => 1,
      'emergency_contacts' => 
      array (
        'primary' => 
        array (
          'name' => 'IT Manager',
          'email' => '<EMAIL>',
          'phone' => '+************',
        ),
        'secondary' => 
        array (
          'name' => 'CTO',
          'email' => '<EMAIL>',
          'phone' => '+966501234568',
        ),
      ),
      'recovery_procedures' => 
      array (
        'documented' => true,
        'tested_frequency' => 'quarterly',
        'last_test_date' => NULL,
      ),
    ),
    'compliance' => 
    array (
      'audit_logging' => 
      array (
        'enabled' => true,
        'log_backup_operations' => true,
        'log_restore_operations' => true,
        'log_access_attempts' => true,
        'retention_years' => 7,
      ),
      'regulatory_requirements' => 
      array (
        'financial_data_retention' => 7,
        'audit_trail_retention' => 10,
        'encryption_required' => true,
        'offsite_storage_required' => true,
      ),
      'reporting' => 
      array (
        'generate_backup_reports' => true,
        'report_frequency' => 'monthly',
        'include_metrics' => true,
        'include_compliance_status' => true,
      ),
    ),
  ),
  'blockchain' => 
  array (
    'enabled' => true,
    'networks' => 
    array (
      'bitcoin' => 
      array (
        'enabled' => true,
        'network' => 'testnet',
        'rpc_url' => 'http://localhost:8332',
        'rpc_username' => NULL,
        'rpc_password' => NULL,
        'confirmations_required' => 3,
        'fee_per_byte' => 10,
        'dust_threshold' => 546,
      ),
      'ethereum' => 
      array (
        'enabled' => true,
        'network' => 'goerli',
        'rpc_url' => 'https://goerli.infura.io/v3/YOUR_PROJECT_ID',
        'private_key' => NULL,
        'contract_address' => NULL,
        'gas_limit' => 21000,
        'gas_price' => 20000000000,
        'confirmations_required' => 12,
      ),
      'binance_smart_chain' => 
      array (
        'enabled' => false,
        'network' => 'testnet',
        'rpc_url' => 'https://data-seed-prebsc-1-s1.binance.org:8545',
        'private_key' => NULL,
        'gas_limit' => 21000,
        'gas_price' => 5000000000,
        'confirmations_required' => 15,
      ),
      'polygon' => 
      array (
        'enabled' => false,
        'network' => 'mumbai',
        'rpc_url' => 'https://rpc-mumbai.maticvigil.com',
        'private_key' => NULL,
        'gas_limit' => 21000,
        'gas_price' => 1000000000,
        'confirmations_required' => 20,
      ),
    ),
    'cryptocurrencies' => 
    array (
      'BTC' => 
      array (
        'name' => 'Bitcoin',
        'symbol' => 'BTC',
        'network' => 'bitcoin',
        'decimals' => 8,
        'min_amount' => 1.0E-5,
        'max_amount' => 21000000,
        'withdrawal_fee' => 0.0005,
        'deposit_fee' => 0,
      ),
      'ETH' => 
      array (
        'name' => 'Ethereum',
        'symbol' => 'ETH',
        'network' => 'ethereum',
        'decimals' => 18,
        'min_amount' => 0.001,
        'max_amount' => 1000000,
        'withdrawal_fee' => 0.005,
        'deposit_fee' => 0,
      ),
      'USDT' => 
      array (
        'name' => 'Tether USD',
        'symbol' => 'USDT',
        'network' => 'ethereum',
        'contract_address' => '******************************************',
        'decimals' => 6,
        'min_amount' => 1,
        'max_amount' => 1000000,
        'withdrawal_fee' => 5,
        'deposit_fee' => 0,
      ),
      'USDC' => 
      array (
        'name' => 'USD Coin',
        'symbol' => 'USDC',
        'network' => 'ethereum',
        'contract_address' => '******************************************',
        'decimals' => 6,
        'min_amount' => 1,
        'max_amount' => 1000000,
        'withdrawal_fee' => 3,
        'deposit_fee' => 0,
      ),
      'BNB' => 
      array (
        'name' => 'Binance Coin',
        'symbol' => 'BNB',
        'network' => 'binance_smart_chain',
        'decimals' => 18,
        'min_amount' => 0.01,
        'max_amount' => 100000,
        'withdrawal_fee' => 0.005,
        'deposit_fee' => 0,
      ),
    ),
    'wallets' => 
    array (
      'hot_wallet_threshold' => 100000,
      'cold_storage_percentage' => 0.8,
      'auto_sweep_enabled' => true,
      'auto_sweep_threshold' => 10000,
      'generation' => 
      array (
        'algorithm' => 'secp256k1',
        'entropy_bits' => 256,
        'mnemonic_words' => 24,
      ),
      'security' => 
      array (
        'multi_signature' => 
        array (
          'enabled' => true,
          'required_signatures' => 2,
          'total_signers' => 3,
        ),
        'hardware_security_module' => false,
        'key_encryption' => true,
      ),
    ),
    'monitoring' => 
    array (
      'block_confirmations' => 
      array (
        'bitcoin' => 3,
        'ethereum' => 12,
        'binance_smart_chain' => 15,
        'polygon' => 20,
      ),
      'polling_interval' => 30,
      'max_polling_attempts' => 120,
      'alerts' => 
      array (
        'large_transaction_threshold' => 50000,
        'unusual_activity_threshold' => 100000,
        'failed_transaction_threshold' => 5,
      ),
    ),
    'apis' => 
    array (
      'blockchain_info' => 
      array (
        'enabled' => true,
        'base_url' => 'https://blockchain.info/api',
        'rate_limit' => 300,
      ),
      'etherscan' => 
      array (
        'enabled' => true,
        'api_key' => NULL,
        'base_url' => 'https://api.etherscan.io/api',
        'rate_limit' => 5,
      ),
      'bscscan' => 
      array (
        'enabled' => false,
        'api_key' => NULL,
        'base_url' => 'https://api.bscscan.com/api',
        'rate_limit' => 5,
      ),
      'polygonscan' => 
      array (
        'enabled' => false,
        'api_key' => NULL,
        'base_url' => 'https://api.polygonscan.com/api',
        'rate_limit' => 5,
      ),
    ),
    'price_feeds' => 
    array (
      'primary' => 'coingecko',
      'fallback' => 'coinmarketcap',
      'update_interval' => 60,
      'cache_ttl' => 300,
      'providers' => 
      array (
        'coingecko' => 
        array (
          'api_key' => NULL,
          'base_url' => 'https://api.coingecko.com/api/v3',
          'rate_limit' => 50,
        ),
        'coinmarketcap' => 
        array (
          'api_key' => NULL,
          'base_url' => 'https://pro-api.coinmarketcap.com/v1',
          'rate_limit' => 333,
        ),
        'binance' => 
        array (
          'base_url' => 'https://api.binance.com/api/v3',
          'rate_limit' => 1200,
        ),
      ),
    ),
    'compliance' => 
    array (
      'aml_monitoring' => true,
      'transaction_reporting' => true,
      'suspicious_activity_threshold' => 10000,
      'reporting' => 
      array (
        'daily_volume_report' => true,
        'large_transaction_report' => true,
        'failed_transaction_report' => true,
        'wallet_balance_report' => true,
      ),
      'retention' => 
      array (
        'transaction_logs' => 2555,
        'wallet_logs' => 2555,
        'api_logs' => 365,
      ),
    ),
    'development' => 
    array (
      'testnet_enabled' => true,
      'mock_transactions' => false,
      'debug_logging' => false,
      'faucets' => 
      array (
        'bitcoin_testnet' => 'https://testnet-faucet.mempool.co',
        'ethereum_goerli' => 'https://goerlifaucet.com',
        'bsc_testnet' => 'https://testnet.binance.org/faucet-smart',
      ),
    ),
    'error_handling' => 
    array (
      'retry_attempts' => 3,
      'retry_delay' => 5,
      'timeout' => 30,
      'fallback_enabled' => true,
      'circuit_breaker' => 
      array (
        'failure_threshold' => 5,
        'recovery_timeout' => 300,
      ),
    ),
  ),
  'broadcasting' => 
  array (
    'default' => 'null',
    'connections' => 
    array (
      'reverb' => 
      array (
        'driver' => 'reverb',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'host' => NULL,
          'port' => 443,
          'scheme' => 'https',
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => '',
        'app_id' => '',
        'options' => 
        array (
          'cluster' => 'mt1',
          'host' => 'api-mt1.pusherapp.com',
          'port' => '443',
          'scheme' => 'https',
          'encrypted' => true,
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'financial-notifications' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => '',
        'app_id' => '',
        'options' => 
        array (
          'cluster' => 'mt1',
          'encrypted' => true,
          'useTLS' => true,
        ),
      ),
      'transaction-updates' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => '',
        'app_id' => '',
        'options' => 
        array (
          'cluster' => 'mt1',
          'encrypted' => true,
          'useTLS' => true,
        ),
      ),
      'fraud-alerts' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => '',
        'app_id' => '',
        'options' => 
        array (
          'cluster' => 'mt1',
          'encrypted' => true,
          'useTLS' => true,
        ),
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'cache',
        'lock_connection' => NULL,
        'lock_table' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework/cache/data',
        'lock_path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'mony-transfer-global-financial-system-cache-',
    'tags' => 
    array (
      'exchange_rates' => 'financial_exchange_rates',
      'currencies' => 'financial_currencies',
      'countries' => 'financial_countries',
      'branches' => 'financial_branches',
      'users' => 'financial_users',
      'transactions' => 'financial_transactions',
      'wallets' => 'financial_wallets',
      'fraud_detection' => 'financial_fraud',
      'payment_gateways' => 'financial_gateways',
    ),
    'ttl' => 
    array (
      'exchange_rates' => 300,
      'currencies' => 3600,
      'countries' => 86400,
      'branches' => 3600,
      'user_profile' => 1800,
      'transaction_stats' => 600,
      'fraud_rules' => 1800,
      'system_config' => 3600,
    ),
  ),
  'compliance' => 
  array (
    'kyc' => 
    array (
      'enabled' => true,
      'required_for_registration' => false,
      'required_for_transactions' => true,
      'verification_levels' => 
      array (
        'basic' => 
        array (
          'required_documents' => 
          array (
            0 => 'national_id',
            1 => 'selfie',
          ),
          'transaction_limit_daily' => 10000,
          'transaction_limit_monthly' => 50000,
          'verification_time_hours' => 24,
        ),
        'enhanced' => 
        array (
          'required_documents' => 
          array (
            0 => 'national_id',
            1 => 'passport',
            2 => 'proof_of_address',
            3 => 'selfie',
          ),
          'transaction_limit_daily' => 100000,
          'transaction_limit_monthly' => 1000000,
          'verification_time_hours' => 72,
        ),
        'premium' => 
        array (
          'required_documents' => 
          array (
            0 => 'national_id',
            1 => 'passport',
            2 => 'proof_of_address',
            3 => 'proof_of_income',
            4 => 'selfie',
          ),
          'transaction_limit_daily' => 1000000,
          'transaction_limit_monthly' => 10000000,
          'verification_time_hours' => 168,
        ),
      ),
      'document_types' => 
      array (
        'national_id' => 
        array (
          'required' => true,
          'formats' => 
          array (
            0 => 'jpg',
            1 => 'jpeg',
            2 => 'png',
            3 => 'pdf',
          ),
          'max_size_mb' => 5,
          'expiry_check' => true,
        ),
        'passport' => 
        array (
          'required' => false,
          'formats' => 
          array (
            0 => 'jpg',
            1 => 'jpeg',
            2 => 'png',
            3 => 'pdf',
          ),
          'max_size_mb' => 5,
          'expiry_check' => true,
        ),
        'proof_of_address' => 
        array (
          'required' => true,
          'formats' => 
          array (
            0 => 'jpg',
            1 => 'jpeg',
            2 => 'png',
            3 => 'pdf',
          ),
          'max_size_mb' => 5,
          'max_age_months' => 3,
        ),
        'proof_of_income' => 
        array (
          'required' => false,
          'formats' => 
          array (
            0 => 'jpg',
            1 => 'jpeg',
            2 => 'png',
            3 => 'pdf',
          ),
          'max_size_mb' => 10,
          'max_age_months' => 6,
        ),
        'selfie' => 
        array (
          'required' => true,
          'formats' => 
          array (
            0 => 'jpg',
            1 => 'jpeg',
            2 => 'png',
          ),
          'max_size_mb' => 3,
          'face_detection' => true,
        ),
      ),
      'auto_verification' => 
      array (
        'enabled' => true,
        'ai_document_verification' => true,
        'face_matching' => true,
        'address_verification' => true,
        'confidence_threshold' => 0.85,
      ),
      'manual_review' => 
      array (
        'required_for_high_risk' => true,
        'required_for_failed_auto' => true,
        'sla_hours' => 48,
      ),
    ),
    'aml' => 
    array (
      'enabled' => true,
      'screening' => 
      array (
        'sanctions_lists' => 
        array (
          'ofac' => true,
          'un' => true,
          'eu' => true,
          'uk' => true,
          'local' => true,
        ),
        'pep_screening' => true,
        'adverse_media_screening' => true,
        'real_time_screening' => true,
        'batch_screening_frequency' => 'daily',
      ),
      'transaction_monitoring' => 
      array (
        'enabled' => true,
        'rules' => 
        array (
          'large_cash_transactions' => 
          array (
            'threshold' => 10000,
            'currency' => 'USD',
            'enabled' => true,
          ),
          'structured_transactions' => 
          array (
            'enabled' => true,
            'threshold' => 9000,
            'time_window_hours' => 24,
          ),
          'unusual_patterns' => 
          array (
            'enabled' => true,
            'velocity_threshold' => 5,
            'amount_threshold' => 50000,
          ),
          'cross_border_transactions' => 
          array (
            'enabled' => true,
            'high_risk_countries' => true,
            'threshold' => 5000,
          ),
        ),
      ),
      'suspicious_activity_reporting' => 
      array (
        'enabled' => true,
        'auto_filing' => false,
        'manual_review_required' => true,
        'filing_deadline_days' => 30,
        'retention_years' => 5,
      ),
      'record_keeping' => 
      array (
        'transaction_records_years' => 5,
        'customer_records_years' => 5,
        'correspondence_years' => 5,
        'training_records_years' => 3,
      ),
    ),
    'ctf' => 
    array (
      'enabled' => true,
      'screening_lists' => 
      array (
        'terrorist_organizations' => true,
        'designated_individuals' => true,
        'blocked_entities' => true,
      ),
      'enhanced_due_diligence' => 
      array (
        'high_risk_countries' => true,
        'high_risk_customers' => true,
        'high_risk_transactions' => true,
      ),
      'reporting' => 
      array (
        'suspicious_transactions' => true,
        'blocked_transactions' => true,
        'regulatory_reporting' => true,
      ),
    ),
    'data_protection' => 
    array (
      'gdpr' => 
      array (
        'enabled' => true,
        'data_retention_years' => 7,
        'right_to_be_forgotten' => true,
        'data_portability' => true,
        'consent_management' => true,
        'privacy_by_design' => true,
      ),
      'ccpa' => 
      array (
        'enabled' => false,
        'data_deletion_requests' => true,
        'data_access_requests' => true,
        'opt_out_rights' => true,
      ),
      'local_privacy_laws' => 
      array (
        'enabled' => true,
        'saudi_pdpl' => true,
        'uae_dpl' => true,
      ),
      'data_minimization' => 
      array (
        'enabled' => true,
        'collect_only_necessary' => true,
        'regular_data_audits' => true,
        'automated_deletion' => true,
      ),
    ),
    'financial_regulations' => 
    array (
      'pci_dss' => 
      array (
        'enabled' => true,
        'compliance_level' => 'level_1',
        'card_data_encryption' => true,
        'network_security' => true,
        'access_controls' => true,
        'monitoring' => true,
      ),
      'basel_iii' => 
      array (
        'enabled' => false,
        'capital_requirements' => true,
        'liquidity_requirements' => true,
        'leverage_ratio' => true,
      ),
      'local_regulations' => 
      array (
        'sama_regulations' => 
        array (
          'enabled' => true,
          'payment_services' => true,
          'money_transfer' => true,
          'cybersecurity' => true,
        ),
        'cbuae_regulations' => 
        array (
          'enabled' => true,
          'stored_value_facilities' => true,
          'payment_services' => true,
        ),
      ),
    ),
    'audit' => 
    array (
      'enabled' => true,
      'audit_trail' => 
      array (
        'all_transactions' => true,
        'user_activities' => true,
        'admin_activities' => true,
        'system_changes' => true,
        'data_access' => true,
      ),
      'retention_periods' => 
      array (
        'audit_logs_years' => 10,
        'transaction_logs_years' => 7,
        'access_logs_years' => 3,
        'error_logs_years' => 2,
      ),
      'integrity_protection' => 
      array (
        'digital_signatures' => true,
        'hash_verification' => true,
        'tamper_detection' => true,
        'backup_verification' => true,
      ),
    ),
    'reporting' => 
    array (
      'regulatory_reports' => 
      array (
        'enabled' => true,
        'automated_generation' => true,
        'manual_review_required' => true,
        'submission_deadlines' => 
        array (
          'monthly' => 15,
          'quarterly' => 30,
          'annually' => 90,
        ),
      ),
      'internal_reports' => 
      array (
        'compliance_dashboard' => true,
        'risk_reports' => true,
        'audit_reports' => true,
        'performance_reports' => true,
      ),
      'external_reports' => 
      array (
        'regulatory_filings' => true,
        'law_enforcement_requests' => true,
        'court_orders' => true,
        'auditor_reports' => true,
      ),
    ),
    'risk_management' => 
    array (
      'risk_assessment' => 
      array (
        'customer_risk_rating' => true,
        'transaction_risk_scoring' => true,
        'country_risk_assessment' => true,
        'product_risk_assessment' => true,
      ),
      'risk_categories' => 
      array (
        'low' => 
        array (
          'score_range' => 
          array (
            0 => 0,
            1 => 30,
          ),
          'monitoring_level' => 'standard',
          'review_frequency' => 'annual',
        ),
        'medium' => 
        array (
          'score_range' => 
          array (
            0 => 31,
            1 => 70,
          ),
          'monitoring_level' => 'enhanced',
          'review_frequency' => 'semi_annual',
        ),
        'high' => 
        array (
          'score_range' => 
          array (
            0 => 71,
            1 => 100,
          ),
          'monitoring_level' => 'intensive',
          'review_frequency' => 'quarterly',
        ),
      ),
      'risk_mitigation' => 
      array (
        'enhanced_due_diligence' => true,
        'transaction_limits' => true,
        'additional_verification' => true,
        'ongoing_monitoring' => true,
      ),
    ),
    'training' => 
    array (
      'enabled' => true,
      'mandatory_training' => 
      array (
        'aml_basics' => 
        array (
          'frequency' => 'annual',
          'duration_hours' => 4,
          'passing_score' => 80,
        ),
        'fraud_prevention' => 
        array (
          'frequency' => 'annual',
          'duration_hours' => 2,
          'passing_score' => 85,
        ),
        'data_protection' => 
        array (
          'frequency' => 'annual',
          'duration_hours' => 3,
          'passing_score' => 80,
        ),
        'cybersecurity' => 
        array (
          'frequency' => 'semi_annual',
          'duration_hours' => 2,
          'passing_score' => 85,
        ),
      ),
      'role_specific_training' => 
      array (
        'compliance_officers' => 
        array (
          'advanced_aml' => true,
          'regulatory_updates' => true,
          'investigation_techniques' => true,
        ),
        'customer_service' => 
        array (
          'customer_identification' => true,
          'suspicious_activity_detection' => true,
          'escalation_procedures' => true,
        ),
        'it_staff' => 
        array (
          'data_security' => true,
          'system_monitoring' => true,
          'incident_response' => true,
        ),
      ),
      'tracking' => 
      array (
        'completion_rates' => true,
        'test_scores' => true,
        'certification_status' => true,
        'renewal_reminders' => true,
      ),
    ),
  ),
  'database' => 
  array (
    'default' => 'sqlite',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'C:\\xampp\\htdocs\\Mony_Transfir\\database\\database.sqlite',
        'prefix' => '',
        'foreign_key_constraints' => true,
        'busy_timeout' => NULL,
        'journal_mode' => NULL,
        'synchronous' => NULL,
        'options' => 
        array (
          2 => 60,
          3 => 2,
        ),
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'laravel',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'mariadb' => 
      array (
        'driver' => 'mariadb',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'laravel',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '5432',
        'database' => 'laravel',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => 'localhost',
        'port' => '1433',
        'database' => 'laravel',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 
    array (
      'table' => 'migrations',
      'update_date_on_publish' => true,
    ),
    'redis' => 
    array (
      'client' => 'predis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'mony-transfer-global-financial-system-database-',
        'persistent' => false,
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/private',
        'serve' => true,
        'throw' => false,
        'report' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/public',
        'url' => 'http://localhost:8000/storage',
        'visibility' => 'public',
        'throw' => false,
        'report' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => 'mony-transfer-documents',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
        'report' => false,
      ),
      'documents' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/documents',
        'throw' => false,
      ),
      'receipts' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/receipts',
        'throw' => false,
      ),
      'reports' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/reports',
        'throw' => false,
      ),
      'backups' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/backups',
        'throw' => false,
      ),
      'kyc_documents' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/kyc',
        'throw' => false,
      ),
    ),
    'links' => 
    array (
      'C:\\xampp\\htdocs\\Mony_Transfir\\public\\storage' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app/public',
    ),
  ),
  'financial' => 
  array (
    'system' => 
    array (
      'name' => 'Mony Transfer Global',
      'version' => '1.0.0',
      'license' => 'Enterprise',
    ),
    'security' => 
    array (
      'encryption_key' => '',
      'jwt_secret' => '',
      'biometric_enabled' => true,
      'webauthn_enabled' => true,
      'two_factor_enabled' => true,
    ),
    'blockchain' => 
    array (
      'enabled' => true,
      'network' => 'ethereum',
      'private_key' => '',
      'contract_address' => '',
    ),
    'crypto' => 
    array (
      'enabled' => true,
      'wallet_enabled' => true,
      'supported_cryptos' => 
      array (
        0 => 'BTC',
        1 => 'ETH',
        2 => 'USDT',
        3 => 'BNB',
      ),
    ),
    'exchange_rates' => 
    array (
      'api_key' => '',
      'provider' => 'fixer.io',
      'update_interval' => '300',
      'default_spread' => 0.005,
    ),
    'payment_gateways' => 
    array (
      'paypal' => 
      array (
        'client_id' => '',
        'client_secret' => '',
        'mode' => 'sandbox',
      ),
      'stripe' => 
      array (
        'key' => '',
        'secret' => '',
      ),
      'wise' => 
      array (
        'api_key' => '',
        'environment' => 'sandbox',
      ),
    ),
    'banking' => 
    array (
      'swift_enabled' => true,
      'sepa_enabled' => true,
      'openbanking_enabled' => true,
    ),
    'ai' => 
    array (
      'fraud_detection_enabled' => true,
      'risk_analysis_enabled' => true,
      'model_endpoint' => '',
      'api_key' => '',
    ),
    'compliance' => 
    array (
      'aml_enabled' => true,
      'kyc_enabled' => true,
      'regulatory_reporting_enabled' => true,
    ),
    'mobile' => 
    array (
      'app_enabled' => true,
      'push_notifications' => true,
    ),
    'limits' => 
    array (
      'max_transfer_amount' => '1000000',
      'min_transfer_amount' => '1',
      'daily_transfer_limit' => '50000',
      'monthly_transfer_limit' => '500000',
    ),
    'fees' => 
    array (
      'default_commission_rate' => '0.02',
      'min_commission_amount' => '1',
      'max_commission_amount' => '100',
    ),
    'localization' => 
    array (
      'supported_languages' => 
      array (
        0 => 'ar',
        1 => 'en',
        2 => 'fr',
        3 => 'es',
      ),
      'default_currency' => 'USD',
      'supported_currencies' => 
      array (
        0 => 'USD',
        1 => 'EUR',
        2 => 'GBP',
        3 => 'SAR',
        4 => 'AED',
        5 => 'EGP',
      ),
    ),
    'risk_scoring' => 
    array (
      'amount_thresholds' => 
      array (
        'low' => 1000,
        'medium' => 5000,
        'high' => 10000,
      ),
      'frequency_thresholds' => 
      array (
        'daily' => 5,
        'weekly' => 20,
        'monthly' => 50,
      ),
      'country_risk_levels' => 
      array (
        'low' => 
        array (
          0 => 'USA',
          1 => 'GBR',
          2 => 'SAU',
          3 => 'ARE',
        ),
        'medium' => 
        array (
          0 => 'EGY',
          1 => 'JOR',
          2 => 'LBN',
        ),
        'high' => 
        array (
        ),
      ),
    ),
    'notifications' => 
    array (
      'channels' => 
      array (
        0 => 'database',
        1 => 'email',
        2 => 'sms',
        3 => 'push',
      ),
      'transaction_notifications' => true,
      'security_notifications' => true,
      'marketing_notifications' => false,
    ),
    'audit' => 
    array (
      'enabled' => true,
      'log_all_requests' => false,
      'log_sensitive_data' => false,
      'retention_days' => 365,
    ),
    'api' => 
    array (
      'rate_limit' => 1000,
      'version' => 'v1',
      'documentation_url' => '/api/documentation',
    ),
    'uploads' => 
    array (
      'max_file_size' => 10240,
      'allowed_types' => 
      array (
        0 => 'jpg',
        1 => 'jpeg',
        2 => 'png',
        3 => 'pdf',
        4 => 'doc',
        5 => 'docx',
      ),
      'kyc_documents_path' => 'kyc_documents',
      'receipts_path' => 'receipts',
      'qr_codes_path' => 'qr_codes',
    ),
  ),
  'l5-swagger' => 
  array (
    'default' => 'default',
    'documentations' => 
    array (
      'default' => 
      array (
        'api' => 
        array (
          'title' => 'L5 Swagger UI',
        ),
        'routes' => 
        array (
          'api' => 'api/documentation',
        ),
        'paths' => 
        array (
          'use_absolute_path' => true,
          'swagger_ui_assets_path' => 'vendor/swagger-api/swagger-ui/dist/',
          'docs_json' => 'api-docs.json',
          'docs_yaml' => 'api-docs.yaml',
          'format_to_use_for_docs' => 'json',
          'annotations' => 
          array (
            0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\app',
          ),
        ),
      ),
    ),
    'defaults' => 
    array (
      'routes' => 
      array (
        'docs' => 'docs',
        'oauth2_callback' => 'api/oauth2-callback',
        'middleware' => 
        array (
          'api' => 
          array (
          ),
          'asset' => 
          array (
          ),
          'docs' => 
          array (
          ),
          'oauth2_callback' => 
          array (
          ),
        ),
        'group_options' => 
        array (
        ),
      ),
      'paths' => 
      array (
        'docs' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\api-docs',
        'views' => 'C:\\xampp\\htdocs\\Mony_Transfir\\resources/views/vendor/l5-swagger',
        'base' => NULL,
        'excludes' => 
        array (
        ),
      ),
      'scanOptions' => 
      array (
        'default_processors_configuration' => 
        array (
        ),
        'analyser' => NULL,
        'analysis' => NULL,
        'processors' => 
        array (
        ),
        'pattern' => NULL,
        'exclude' => 
        array (
        ),
        'open_api_spec_version' => '3.0.0',
      ),
      'securityDefinitions' => 
      array (
        'securitySchemes' => 
        array (
        ),
        'security' => 
        array (
          0 => 
          array (
          ),
        ),
      ),
      'generate_always' => false,
      'generate_yaml_copy' => false,
      'proxy' => false,
      'additional_config_url' => NULL,
      'operations_sort' => NULL,
      'validator_url' => NULL,
      'ui' => 
      array (
        'display' => 
        array (
          'dark_mode' => false,
          'doc_expansion' => 'none',
          'filter' => true,
        ),
        'authorization' => 
        array (
          'persist_authorization' => false,
          'oauth2' => 
          array (
            'use_pkce_with_authorization_code_grant' => false,
          ),
        ),
      ),
      'constants' => 
      array (
        'L5_SWAGGER_CONST_HOST' => 'http://my-default-host.com',
      ),
    ),
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'deprecations' => 
    array (
      'channel' => NULL,
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/laravel.log',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
        'replace_placeholders' => true,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'handler_with' => 
        array (
          'stream' => 'php://stderr',
        ),
        'formatter' => NULL,
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
        'facility' => 8,
        'replace_placeholders' => true,
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/laravel.log',
      ),
      'financial' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/financial.log',
        'level' => 'debug',
        'days' => 30,
        'replace_placeholders' => true,
      ),
      'transactions' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/transactions.log',
        'level' => 'info',
        'days' => 90,
        'replace_placeholders' => true,
      ),
      'security' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/security.log',
        'level' => 'warning',
        'days' => 365,
        'replace_placeholders' => true,
      ),
      'fraud' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/fraud.log',
        'level' => 'warning',
        'days' => 365,
        'replace_placeholders' => true,
      ),
      'audit' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/audit.log',
        'level' => 'info',
        'days' => 365,
        'replace_placeholders' => true,
      ),
      'exchange_rates' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs/exchange_rates.log',
        'level' => 'info',
        'days' => 30,
        'replace_placeholders' => true,
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'scheme' => NULL,
        'url' => NULL,
        'host' => 'smtp.gmail.com',
        'port' => '587',
        'username' => '',
        'password' => '',
        'timeout' => NULL,
        'local_domain' => 'localhost',
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'resend' => 
      array (
        'transport' => 'resend',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
        'retry_after' => 60,
      ),
      'roundrobin' => 
      array (
        'transport' => 'roundrobin',
        'mailers' => 
        array (
          0 => 'ses',
          1 => 'postmark',
        ),
        'retry_after' => 60,
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Mony Transfer - Global Financial System',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'monitoring' => 
  array (
    'enabled' => true,
    'metrics' => 
    array (
      'collection_interval' => 60,
      'retention_days' => 30,
      'batch_size' => 1000,
      'system' => 
      array (
        'cpu_usage' => true,
        'memory_usage' => true,
        'disk_usage' => true,
        'network_io' => true,
        'load_average' => true,
      ),
      'application' => 
      array (
        'response_time' => true,
        'request_count' => true,
        'error_rate' => true,
        'queue_size' => true,
        'cache_hit_ratio' => true,
      ),
      'database' => 
      array (
        'query_time' => true,
        'connection_count' => true,
        'slow_queries' => true,
        'deadlocks' => true,
      ),
      'financial' => 
      array (
        'transaction_volume' => true,
        'transaction_count' => true,
        'fraud_detection_rate' => true,
        'payment_gateway_response_time' => true,
        'exchange_rate_updates' => true,
      ),
    ),
    'thresholds' => 
    array (
      'response_time' => 
      array (
        'warning' => 1000,
        'critical' => 3000,
      ),
      'cpu_usage' => 
      array (
        'warning' => 70,
        'critical' => 90,
      ),
      'memory_usage' => 
      array (
        'warning' => 80,
        'critical' => 95,
      ),
      'disk_usage' => 
      array (
        'warning' => 80,
        'critical' => 95,
      ),
      'error_rate' => 
      array (
        'warning' => 5,
        'critical' => 10,
      ),
      'queue_size' => 
      array (
        'warning' => 1000,
        'critical' => 5000,
      ),
      'database_connections' => 
      array (
        'warning' => 80,
        'critical' => 95,
      ),
      'fraud_detection_time' => 
      array (
        'warning' => 500,
        'critical' => 1000,
      ),
    ),
    'health_checks' => 
    array (
      'enabled' => true,
      'interval' => 30,
      'timeout' => 10,
      'checks' => 
      array (
        'database' => 
        array (
          'enabled' => true,
          'query' => 'SELECT 1',
          'timeout' => 5,
        ),
        'redis' => 
        array (
          'enabled' => true,
          'command' => 'ping',
          'timeout' => 3,
        ),
        'queue' => 
        array (
          'enabled' => true,
          'max_failed_jobs' => 100,
          'max_queue_size' => 10000,
        ),
        'storage' => 
        array (
          'enabled' => true,
          'paths' => 
          array (
            0 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\logs',
            1 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\app',
            2 => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework/cache',
          ),
          'min_free_space' => 1024,
        ),
        'external_apis' => 
        array (
          'enabled' => true,
          'endpoints' => 
          array (
            'exchange_rates' => 'https://api.fixer.io/latest',
            'fraud_detection' => NULL,
          ),
          'timeout' => 10,
        ),
      ),
    ),
    'alerts' => 
    array (
      'enabled' => true,
      'channels' => 
      array (
        0 => 'email',
        1 => 'slack',
        2 => 'sms',
      ),
      'escalation_delay' => 300,
      'max_alerts_per_hour' => 10,
      'recipients' => 
      array (
        'email' => 
        array (
          0 => '<EMAIL>',
          1 => '<EMAIL>',
        ),
        'slack' => 
        array (
          'webhook_url' => NULL,
          'channel' => '#alerts',
        ),
        'sms' => 
        array (
          0 => '+************',
        ),
      ),
      'rules' => 
      array (
        'critical_system_failure' => 
        array (
          'conditions' => 
          array (
            0 => 'cpu_usage > 90',
            1 => 'memory_usage > 95',
            2 => 'error_rate > 10',
          ),
          'channels' => 
          array (
            0 => 'email',
            1 => 'slack',
            2 => 'sms',
          ),
          'escalate_after' => 60,
        ),
        'high_transaction_volume' => 
        array (
          'conditions' => 
          array (
            0 => 'transaction_count > 10000 per hour',
            1 => 'transaction_volume > 1000000 per hour',
          ),
          'channels' => 
          array (
            0 => 'email',
            1 => 'slack',
          ),
          'escalate_after' => 300,
        ),
        'fraud_detection_issues' => 
        array (
          'conditions' => 
          array (
            0 => 'fraud_detection_time > 1000',
            1 => 'fraud_detection_errors > 5 per minute',
          ),
          'channels' => 
          array (
            0 => 'email',
            1 => 'slack',
            2 => 'sms',
          ),
          'escalate_after' => 30,
        ),
        'payment_gateway_issues' => 
        array (
          'conditions' => 
          array (
            0 => 'payment_gateway_response_time > 5000',
            1 => 'payment_gateway_error_rate > 5',
          ),
          'channels' => 
          array (
            0 => 'email',
            1 => 'slack',
          ),
          'escalate_after' => 120,
        ),
      ),
    ),
    'logging' => 
    array (
      'enabled' => true,
      'level' => 'info',
      'channel' => 'monitoring',
      'retention_days' => 90,
      'log_slow_requests' => true,
      'slow_request_threshold' => 2000,
      'log_large_responses' => true,
      'large_response_threshold' => 1048576,
      'log_failed_jobs' => true,
      'log_queue_delays' => true,
    ),
    'profiling' => 
    array (
      'enabled' => false,
      'sample_rate' => 0.1,
      'memory_tracking' => true,
      'query_tracking' => true,
      'cache_tracking' => true,
      'exclude_paths' => 
      array (
        0 => '/health',
        1 => '/metrics',
        2 => '/api/documentation',
      ),
      'storage' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\profiling',
        'retention_hours' => 24,
      ),
    ),
    'dashboard' => 
    array (
      'enabled' => true,
      'route' => '/monitoring',
      'middleware' => 
      array (
        0 => 'auth',
        1 => 'admin',
      ),
      'refresh_interval' => 30,
      'widgets' => 
      array (
        'system_overview' => true,
        'transaction_metrics' => true,
        'fraud_detection_stats' => true,
        'payment_gateway_status' => true,
        'queue_status' => true,
        'error_logs' => true,
      ),
      'charts' => 
      array (
        'response_time_trend' => true,
        'transaction_volume_trend' => true,
        'error_rate_trend' => true,
        'fraud_detection_trend' => true,
      ),
    ),
    'integrations' => 
    array (
      'prometheus' => 
      array (
        'enabled' => false,
        'endpoint' => '/metrics',
        'namespace' => 'mony_transfer',
      ),
      'grafana' => 
      array (
        'enabled' => false,
        'url' => NULL,
        'api_key' => NULL,
      ),
      'datadog' => 
      array (
        'enabled' => false,
        'api_key' => NULL,
        'app_key' => NULL,
      ),
      'new_relic' => 
      array (
        'enabled' => false,
        'license_key' => NULL,
        'app_name' => 'Mony Transfer',
      ),
    ),
    'maintenance' => 
    array (
      'auto_enable' => 
      array (
        'enabled' => false,
        'conditions' => 
        array (
          0 => 'error_rate > 50',
          1 => 'response_time > 10000',
          2 => 'database_connections > 95',
        ),
        'duration' => 300,
      ),
      'scheduled_maintenance' => 
      array (
        'enabled' => true,
        'schedule' => 'weekly sunday 02:00',
        'duration' => 3600,
        'notify_users' => true,
        'notification_advance' => 86400,
      ),
    ),
    'backup_monitoring' => 
    array (
      'enabled' => true,
      'check_interval' => 3600,
      'max_age_hours' => 24,
      'min_size_mb' => 10,
      'locations' => 
      array (
        'database' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\backups/database',
        'files' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\backups/files',
        'logs' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\backups/logs',
      ),
      'alerts' => 
      array (
        'missing_backup' => true,
        'old_backup' => true,
        'small_backup' => true,
        'failed_backup' => true,
      ),
    ),
  ),
  'notifications' => 
  array (
    'enabled' => true,
    'default_channels' => 
    array (
      0 => 'database',
      1 => 'mail',
    ),
    'channels' => 
    array (
      'database' => 
      array (
        'enabled' => true,
        'table' => 'notifications',
        'cleanup_after_days' => 90,
      ),
      'mail' => 
      array (
        'enabled' => true,
        'queue' => 'emails',
        'from' => 
        array (
          'address' => '<EMAIL>',
          'name' => 'Mony Transfer - Global Financial System',
        ),
        'templates_path' => 'emails.notifications',
      ),
      'sms' => 
      array (
        'enabled' => true,
        'provider' => 'twilio',
        'queue' => 'sms',
        'from' => NULL,
        'providers' => 
        array (
          'twilio' => 
          array (
            'sid' => NULL,
            'token' => NULL,
            'from' => NULL,
          ),
          'nexmo' => 
          array (
            'key' => NULL,
            'secret' => NULL,
            'from' => NULL,
          ),
        ),
      ),
      'push' => 
      array (
        'enabled' => true,
        'provider' => 'firebase',
        'queue' => 'push',
        'providers' => 
        array (
          'firebase' => 
          array (
            'server_key' => NULL,
            'sender_id' => NULL,
            'project_id' => NULL,
          ),
          'pusher' => 
          array (
            'app_id' => '',
            'key' => '',
            'secret' => '',
            'cluster' => 'mt1',
          ),
        ),
      ),
      'slack' => 
      array (
        'enabled' => false,
        'webhook_url' => NULL,
        'channel' => '#general',
        'username' => 'Mony Transfer Bot',
        'icon' => ':money_with_wings:',
      ),
      'discord' => 
      array (
        'enabled' => false,
        'webhook_url' => NULL,
        'username' => 'Mony Transfer Bot',
        'avatar_url' => NULL,
      ),
      'telegram' => 
      array (
        'enabled' => false,
        'bot_token' => NULL,
        'chat_id' => NULL,
      ),
    ),
    'types' => 
    array (
      'transaction_created' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
          3 => 'push',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => true,
      ),
      'transaction_completed' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
          3 => 'push',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => true,
      ),
      'transaction_failed' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
        ),
        'priority' => 'high',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'transaction_cancelled' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => true,
      ),
      'transaction_blocked' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
        ),
        'priority' => 'high',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'fraud_alert' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
          3 => 'slack',
        ),
        'priority' => 'critical',
        'delay' => 0,
        'user_controllable' => false,
        'admin_only' => true,
      ),
      'security_alert' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
          3 => 'slack',
        ),
        'priority' => 'critical',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'kyc_required' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'kyc_approved' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'push',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => true,
      ),
      'kyc_rejected' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'limit_exceeded' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'wallet_low_balance' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'push',
        ),
        'priority' => 'normal',
        'delay' => 0,
        'user_controllable' => true,
      ),
      'login_from_new_device' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
        ),
        'priority' => 'high',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'password_changed' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'sms',
        ),
        'priority' => 'high',
        'delay' => 0,
        'user_controllable' => false,
      ),
      'system_maintenance' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'push',
        ),
        'priority' => 'normal',
        'delay' => 3600,
        'user_controllable' => false,
      ),
      'promotional' => 
      array (
        'enabled' => true,
        'channels' => 
        array (
          0 => 'database',
          1 => 'mail',
          2 => 'push',
        ),
        'priority' => 'low',
        'delay' => 0,
        'user_controllable' => true,
      ),
    ),
    'user_preferences' => 
    array (
      'enabled' => true,
      'default_preferences' => 
      array (
        'email_notifications' => true,
        'sms_notifications' => true,
        'push_notifications' => true,
        'marketing_emails' => false,
        'security_alerts' => true,
      ),
      'preference_categories' => 
      array (
        'transactions' => 
        array (
          'label' => 'Transaction Notifications',
          'types' => 
          array (
            0 => 'transaction_created',
            1 => 'transaction_completed',
            2 => 'transaction_cancelled',
          ),
        ),
        'security' => 
        array (
          'label' => 'Security Notifications',
          'types' => 
          array (
            0 => 'login_from_new_device',
            1 => 'password_changed',
            2 => 'security_alert',
          ),
          'force_enabled' => true,
        ),
        'account' => 
        array (
          'label' => 'Account Notifications',
          'types' => 
          array (
            0 => 'kyc_approved',
            1 => 'kyc_rejected',
            2 => 'limit_exceeded',
            3 => 'wallet_low_balance',
          ),
        ),
        'marketing' => 
        array (
          'label' => 'Marketing & Promotions',
          'types' => 
          array (
            0 => 'promotional',
          ),
        ),
      ),
    ),
    'rate_limiting' => 
    array (
      'enabled' => true,
      'limits' => 
      array (
        'sms' => 
        array (
          'per_user_per_hour' => 5,
          'per_user_per_day' => 20,
          'global_per_minute' => 100,
        ),
        'email' => 
        array (
          'per_user_per_hour' => 10,
          'per_user_per_day' => 50,
          'global_per_minute' => 500,
        ),
        'push' => 
        array (
          'per_user_per_hour' => 20,
          'per_user_per_day' => 100,
          'global_per_minute' => 1000,
        ),
      ),
      'cooldown_periods' => 
      array (
        'duplicate_notification' => 300,
        'same_type_notification' => 60,
      ),
    ),
    'templates' => 
    array (
      'email' => 
      array (
        'layout' => 'emails.layout',
        'default_locale' => 'ar',
        'fallback_locale' => 'en',
        'variables' => 
        array (
          'app_name' => 'Mony Transfer - Global Financial System',
          'app_url' => 'http://localhost:8000',
          'support_email' => '<EMAIL>',
          'support_phone' => '+966-11-1234567',
        ),
      ),
      'sms' => 
      array (
        'max_length' => 160,
        'include_app_name' => true,
        'include_unsubscribe' => false,
      ),
      'push' => 
      array (
        'max_title_length' => 50,
        'max_body_length' => 200,
        'default_icon' => 'notification_icon',
        'default_sound' => 'default',
      ),
    ),
    'queue' => 
    array (
      'enabled' => true,
      'default_queue' => 'notifications',
      'priority_queues' => 
      array (
        'critical' => 'critical-notifications',
        'high' => 'high-notifications',
        'normal' => 'notifications',
        'low' => 'low-notifications',
      ),
      'retry_attempts' => 3,
      'retry_delay' => 
      array (
        0 => 60,
        1 => 300,
        2 => 900,
      ),
    ),
    'localization' => 
    array (
      'enabled' => true,
      'default_locale' => 'ar',
      'supported_locales' => 
      array (
        0 => 'ar',
        1 => 'en',
      ),
      'auto_detect_locale' => true,
      'fallback_to_default' => true,
    ),
    'analytics' => 
    array (
      'enabled' => true,
      'track_delivery' => true,
      'track_opens' => true,
      'track_clicks' => true,
      'retention_days' => 90,
      'anonymize_after_days' => 365,
    ),
    'debug' => 
    array (
      'enabled' => false,
      'log_all_notifications' => true,
      'test_mode' => false,
      'test_recipients' => 
      array (
        'email' => NULL,
        'sms' => NULL,
      ),
    ),
  ),
  'performance' => 
  array (
    'caching' => 
    array (
      'enabled' => true,
      'strategies' => 
      array (
        'query_cache' => 
        array (
          'enabled' => true,
          'ttl' => 3600,
          'tags' => 
          array (
            0 => 'database',
            1 => 'queries',
          ),
        ),
        'api_response_cache' => 
        array (
          'enabled' => true,
          'ttl' => 300,
          'exclude_routes' => 
          array (
            0 => 'api/v1/auth/*',
            1 => 'api/v1/transactions/create',
          ),
        ),
        'view_cache' => 
        array (
          'enabled' => true,
          'ttl' => 1800,
        ),
        'model_cache' => 
        array (
          'enabled' => true,
          'ttl' => 900,
          'models' => 
          array (
            'Currency' => 3600,
            'Country' => 7200,
            'ExchangeRate' => 300,
          ),
        ),
      ),
      'cache_warming' => 
      array (
        'enabled' => true,
        'schedule' => 'hourly',
        'routes' => 
        array (
          0 => 'api/v1/currencies',
          1 => 'api/v1/countries',
          2 => 'api/v1/exchange-rates',
        ),
      ),
    ),
    'database' => 
    array (
      'query_optimization' => 
      array (
        'enabled' => true,
        'log_slow_queries' => true,
        'slow_query_threshold' => 1000,
        'explain_slow_queries' => true,
      ),
      'connection_pooling' => 
      array (
        'enabled' => true,
        'max_connections' => 100,
        'min_connections' => 10,
        'idle_timeout' => 300,
      ),
      'read_write_splitting' => 
      array (
        'enabled' => false,
        'read_connections' => 
        array (
          'read1' => NULL,
          'read2' => NULL,
        ),
        'write_connection' => 'mysql',
      ),
      'indexing' => 
      array (
        'auto_suggest' => true,
        'monitor_missing_indexes' => true,
        'analyze_query_patterns' => true,
      ),
    ),
    'memory' => 
    array (
      'optimization' => 
      array (
        'enabled' => true,
        'memory_limit' => '512M',
        'gc_probability' => 1,
        'gc_divisor' => 100,
      ),
      'object_pooling' => 
      array (
        'enabled' => true,
        'pool_size' => 100,
        'objects' => 
        array (
          0 => 'transaction_processor',
          1 => 'fraud_detector',
          2 => 'exchange_rate_calculator',
        ),
      ),
      'memory_monitoring' => 
      array (
        'enabled' => true,
        'alert_threshold' => 80,
        'log_usage' => true,
      ),
    ),
    'assets' => 
    array (
      'compression' => 
      array (
        'enabled' => true,
        'gzip' => true,
        'brotli' => true,
        'compression_level' => 6,
      ),
      'minification' => 
      array (
        'enabled' => true,
        'css' => true,
        'js' => true,
        'html' => true,
      ),
      'cdn' => 
      array (
        'enabled' => false,
        'url' => NULL,
        'assets' => 
        array (
          0 => 'css',
          1 => 'js',
          2 => 'images',
        ),
      ),
      'image_optimization' => 
      array (
        'enabled' => true,
        'formats' => 
        array (
          0 => 'webp',
          1 => 'avif',
        ),
        'quality' => 85,
        'lazy_loading' => true,
      ),
    ),
    'api' => 
    array (
      'response_compression' => 
      array (
        'enabled' => true,
        'algorithms' => 
        array (
          0 => 'gzip',
          1 => 'deflate',
        ),
        'min_size' => 1024,
      ),
      'pagination' => 
      array (
        'default_per_page' => 20,
        'max_per_page' => 100,
        'optimize_count_queries' => true,
      ),
      'field_selection' => 
      array (
        'enabled' => true,
        'allow_sparse_fieldsets' => true,
        'default_fields' => 
        array (
          0 => 'id',
          1 => 'name',
          2 => 'created_at',
        ),
      ),
      'etag_caching' => 
      array (
        'enabled' => true,
        'strong_etags' => true,
        'include_routes' => 
        array (
          0 => 'api/v1/currencies',
          1 => 'api/v1/countries',
        ),
      ),
    ),
    'queue' => 
    array (
      'optimization' => 
      array (
        'enabled' => true,
        'batch_processing' => true,
        'batch_size' => 100,
        'parallel_workers' => 4,
      ),
      'priority_queues' => 
      array (
        'enabled' => true,
        'queues' => 
        array (
          'critical' => 1,
          'high' => 2,
          'normal' => 3,
          'low' => 4,
        ),
      ),
      'job_batching' => 
      array (
        'enabled' => true,
        'batch_size' => 50,
        'timeout' => 300,
      ),
    ),
    'session' => 
    array (
      'optimization' => 
      array (
        'enabled' => true,
        'driver' => 'redis',
        'gc_probability' => 1,
        'gc_divisor' => 1000,
      ),
      'compression' => 
      array (
        'enabled' => true,
        'algorithm' => 'gzip',
        'level' => 6,
      ),
    ),
    'load_balancing' => 
    array (
      'enabled' => false,
      'algorithm' => 'round_robin',
      'health_checks' => 
      array (
        'enabled' => true,
        'interval' => 30,
        'timeout' => 5,
        'unhealthy_threshold' => 3,
        'healthy_threshold' => 2,
      ),
      'sticky_sessions' => false,
    ),
    'cdn' => 
    array (
      'enabled' => false,
      'provider' => 'cloudflare',
      'url' => NULL,
      'zones' => 
      array (
        'static' => NULL,
        'images' => NULL,
        'api' => NULL,
      ),
      'cache_control' => 
      array (
        'static_assets' => 'public, max-age=31536000',
        'images' => 'public, max-age=2592000',
        'api_responses' => 'public, max-age=300',
      ),
    ),
    'monitoring' => 
    array (
      'enabled' => true,
      'profiling' => 
      array (
        'enabled' => false,
        'sample_rate' => 0.1,
        'memory_tracking' => true,
        'query_tracking' => true,
      ),
      'metrics' => 
      array (
        'response_time' => true,
        'memory_usage' => true,
        'query_count' => true,
        'cache_hit_ratio' => true,
        'queue_size' => true,
      ),
      'alerts' => 
      array (
        'slow_response' => 3000,
        'high_memory' => 80,
        'many_queries' => 100,
        'low_cache_hit' => 70,
      ),
    ),
    'optimization' => 
    array (
      'eager_loading' => 
      array (
        'enabled' => true,
        'auto_detect' => true,
        'relationships' => 
        array (
          'Transaction' => 
          array (
            0 => 'currency',
            1 => 'sender',
            2 => 'receiver',
          ),
          'User' => 
          array (
            0 => 'country',
            1 => 'wallets',
          ),
          'Wallet' => 
          array (
            0 => 'currency',
            1 => 'user',
          ),
        ),
      ),
      'lazy_loading' => 
      array (
        'enabled' => true,
        'chunk_size' => 1000,
        'models' => 
        array (
          0 => 'AuditLog',
          1 => 'Notification',
        ),
      ),
      'data_compression' => 
      array (
        'enabled' => true,
        'fields' => 
        array (
          0 => 'notes',
          1 => 'metadata',
          2 => 'description',
        ),
        'algorithm' => 'gzip',
      ),
      'background_processing' => 
      array (
        'enabled' => true,
        'operations' => 
        array (
          0 => 'email_sending',
          1 => 'report_generation',
          2 => 'data_export',
          3 => 'backup_creation',
        ),
      ),
    ),
    'auto_scaling' => 
    array (
      'enabled' => false,
      'metrics' => 
      array (
        'cpu_threshold' => 70,
        'memory_threshold' => 80,
        'response_time_threshold' => 2000,
        'queue_size_threshold' => 1000,
      ),
      'scaling_policies' => 
      array (
        'scale_up_cooldown' => 300,
        'scale_down_cooldown' => 600,
        'min_instances' => 2,
        'max_instances' => 10,
      ),
    ),
    'testing' => 
    array (
      'load_testing' => 
      array (
        'enabled' => false,
        'scenarios' => 
        array (
          'normal_load' => 100,
          'peak_load' => 500,
          'stress_test' => 1000,
        ),
        'duration' => 300,
      ),
      'benchmarking' => 
      array (
        'enabled' => false,
        'endpoints' => 
        array (
          0 => 'api/v1/transactions',
          1 => 'api/v1/wallets',
          2 => 'api/v1/exchange-rates',
        ),
        'iterations' => 1000,
      ),
    ),
  ),
  'queue' => 
  array (
    'default' => 'database',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
      'high-priority' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'high-priority',
        'retry_after' => 60,
        'after_commit' => false,
      ),
      'medium-priority' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'medium-priority',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'low-priority' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'low-priority',
        'retry_after' => 120,
        'after_commit' => false,
      ),
      'emails' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'emails',
        'retry_after' => 300,
        'after_commit' => false,
      ),
      'exchange-rates' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'exchange-rates',
        'retry_after' => 600,
        'after_commit' => false,
      ),
      'fraud-detection' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'fraud-detection',
        'retry_after' => 30,
        'after_commit' => false,
      ),
      'blockchain' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'blockchain',
        'retry_after' => 180,
        'after_commit' => false,
      ),
      'reports' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'reports',
        'retry_after' => 1800,
        'after_commit' => false,
      ),
    ),
    'batching' => 
    array (
      'database' => 'sqlite',
      'table' => 'job_batches',
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'sqlite',
      'table' => 'failed_jobs',
    ),
  ),
  'security' => 
  array (
    'authentication' => 
    array (
      'max_login_attempts' => 5,
      'lockout_duration' => 900,
      'password_reset_timeout' => 3600,
      'session_timeout' => 1800,
      'password_requirements' => 
      array (
        'min_length' => 8,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => true,
        'prevent_common_passwords' => true,
        'prevent_personal_info' => true,
      ),
      'two_factor' => 
      array (
        'enabled' => true,
        'required_for_admin' => true,
        'required_for_high_value' => true,
        'backup_codes_count' => 8,
        'totp_window' => 30,
      ),
      'device_tracking' => 
      array (
        'enabled' => true,
        'require_verification_new_device' => true,
        'remember_device_days' => 30,
        'max_devices_per_user' => 5,
      ),
    ),
    'api' => 
    array (
      'rate_limiting' => 
      array (
        'enabled' => true,
        'requests_per_minute' => 60,
        'requests_per_hour' => 1000,
        'burst_limit' => 10,
      ),
      'token_security' => 
      array (
        'expiry_hours' => 24,
        'refresh_token_expiry_days' => 30,
        'rotate_refresh_tokens' => true,
        'revoke_on_password_change' => true,
      ),
      'request_validation' => 
      array (
        'max_request_size' => 10485760,
        'allowed_content_types' => 
        array (
          0 => 'application/json',
          1 => 'application/x-www-form-urlencoded',
          2 => 'multipart/form-data',
        ),
        'validate_user_agent' => true,
        'validate_referer' => false,
      ),
    ),
    'data_protection' => 
    array (
      'encryption' => 
      array (
        'algorithm' => 'AES-256-GCM',
        'key_rotation_days' => 90,
        'encrypt_sensitive_fields' => true,
        'encrypt_pii' => true,
      ),
      'sensitive_fields' => 
      array (
        0 => 'national_id',
        1 => 'passport_number',
        2 => 'bank_account_number',
        3 => 'credit_card_number',
        4 => 'phone',
        5 => 'address',
        6 => 'date_of_birth',
      ),
      'data_masking' => 
      array (
        'enabled' => true,
        'mask_in_logs' => true,
        'mask_in_responses' => true,
        'mask_character' => '*',
      ),
      'data_retention' => 
      array (
        'user_data_years' => 7,
        'transaction_data_years' => 10,
        'log_data_years' => 3,
        'audit_data_years' => 10,
      ),
    ),
    'network' => 
    array (
      'ip_whitelist' => 
      array (
        'enabled' => false,
        'admin_routes' => 
        array (
          0 => '127.0.0.1',
          1 => '::1',
        ),
        'api_routes' => 
        array (
        ),
      ),
      'ip_blacklist' => 
      array (
        'enabled' => true,
        'auto_block_suspicious' => true,
        'block_duration_hours' => 24,
        'max_violations_per_hour' => 10,
      ),
      'ddos_protection' => 
      array (
        'enabled' => true,
        'requests_per_second' => 100,
        'burst_capacity' => 200,
        'block_duration_minutes' => 60,
      ),
      'ssl_tls' => 
      array (
        'force_https' => true,
        'hsts_enabled' => true,
        'hsts_max_age' => 31536000,
        'certificate_pinning' => false,
      ),
    ),
    'input_validation' => 
    array (
      'xss_protection' => 
      array (
        'enabled' => true,
        'strip_tags' => true,
        'encode_entities' => true,
        'allowed_tags' => 
        array (
        ),
      ),
      'sql_injection_protection' => 
      array (
        'enabled' => true,
        'detect_patterns' => true,
        'block_suspicious_queries' => true,
        'log_attempts' => true,
      ),
      'file_upload' => 
      array (
        'max_size' => 5242880,
        'allowed_extensions' => 
        array (
          0 => 'jpg',
          1 => 'jpeg',
          2 => 'png',
          3 => 'pdf',
          4 => 'doc',
          5 => 'docx',
        ),
        'scan_for_malware' => true,
        'quarantine_suspicious' => true,
      ),
      'content_security' => 
      array (
        'csp_enabled' => true,
        'csp_policy' => 'default-src \'self\'; script-src \'self\' \'unsafe-inline\'; style-src \'self\' \'unsafe-inline\';',
        'x_frame_options' => 'DENY',
        'x_content_type_options' => 'nosniff',
      ),
    ),
    'fraud_prevention' => 
    array (
      'device_fingerprinting' => 
      array (
        'enabled' => true,
        'track_browser' => true,
        'track_screen_resolution' => true,
        'track_timezone' => true,
        'track_plugins' => true,
      ),
      'behavioral_analysis' => 
      array (
        'enabled' => true,
        'track_typing_patterns' => false,
        'track_mouse_movements' => false,
        'track_session_duration' => true,
        'track_navigation_patterns' => true,
      ),
      'geolocation' => 
      array (
        'enabled' => true,
        'block_vpn_proxy' => true,
        'block_tor' => true,
        'unusual_location_threshold' => 1000,
        'require_verification_new_location' => true,
      ),
      'velocity_checks' => 
      array (
        'enabled' => true,
        'max_transactions_per_hour' => 10,
        'max_amount_per_hour' => 50000,
        'max_failed_attempts_per_hour' => 5,
      ),
    ),
    'monitoring' => 
    array (
      'security_events' => 
      array (
        'log_all_events' => true,
        'real_time_alerts' => true,
        'alert_channels' => 
        array (
          0 => 'email',
          1 => 'slack',
          2 => 'sms',
        ),
      ),
      'suspicious_activities' => 
      array (
        'multiple_failed_logins' => 3,
        'unusual_transaction_patterns' => true,
        'admin_access_outside_hours' => true,
        'bulk_data_access' => true,
        'privilege_escalation_attempts' => true,
      ),
      'compliance_monitoring' => 
      array (
        'gdpr_compliance' => true,
        'pci_dss_compliance' => true,
        'aml_compliance' => true,
        'kyc_compliance' => true,
      ),
    ),
    'incident_response' => 
    array (
      'auto_response' => 
      array (
        'enabled' => true,
        'block_suspicious_ips' => true,
        'disable_compromised_accounts' => true,
        'escalate_critical_incidents' => true,
      ),
      'notification_channels' => 
      array (
        'security_team' => 
        array (
          'email' => '<EMAIL>',
          'slack' => '#security-alerts',
          'phone' => '+************',
        ),
        'management' => 
        array (
          'email' => '<EMAIL>',
          'escalation_delay' => 300,
        ),
      ),
      'incident_classification' => 
      array (
        'low' => 
        array (
          'response_time' => 3600,
          'escalation_time' => 14400,
        ),
        'medium' => 
        array (
          'response_time' => 1800,
          'escalation_time' => 3600,
        ),
        'high' => 
        array (
          'response_time' => 900,
          'escalation_time' => 1800,
        ),
        'critical' => 
        array (
          'response_time' => 300,
          'escalation_time' => 600,
        ),
      ),
    ),
    'backup_security' => 
    array (
      'encrypt_backups' => true,
      'backup_retention_days' => 90,
      'offsite_backup' => true,
      'backup_verification' => true,
      'disaster_recovery_plan' => true,
    ),
    'headers' => 
    array (
      'x_frame_options' => 'DENY',
      'x_content_type_options' => 'nosniff',
      'x_xss_protection' => '1; mode=block',
      'referrer_policy' => 'strict-origin-when-cross-origin',
      'permissions_policy' => 'geolocation=(), microphone=(), camera=()',
      'strict_transport_security' => 'max-age=31536000; includeSubDomains',
    ),
    'testing' => 
    array (
      'penetration_testing' => 
      array (
        'frequency' => 'quarterly',
        'external_vendor' => true,
        'scope' => 
        array (
          0 => 'web_application',
          1 => 'api',
          2 => 'infrastructure',
        ),
      ),
      'vulnerability_scanning' => 
      array (
        'frequency' => 'weekly',
        'automated' => true,
        'manual_review' => 'monthly',
      ),
      'security_audits' => 
      array (
        'frequency' => 'annually',
        'compliance_audits' => true,
        'code_security_review' => 'quarterly',
      ),
    ),
  ),
  'services' => 
  array (
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'resend' => 
    array (
      'key' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'slack' => 
    array (
      'notifications' => 
      array (
        'bot_user_oauth_token' => NULL,
        'channel' => NULL,
      ),
    ),
    'paypal' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'mode' => 'sandbox',
      'webhook_id' => NULL,
    ),
    'stripe' => 
    array (
      'key' => '',
      'secret' => '',
      'webhook_secret' => NULL,
    ),
    'wise' => 
    array (
      'api_key' => '',
      'environment' => 'sandbox',
      'webhook_secret' => NULL,
    ),
    'fixer_io' => 
    array (
      'api_key' => NULL,
      'base_url' => 'http://data.fixer.io/api',
    ),
    'currency_api' => 
    array (
      'api_key' => NULL,
      'base_url' => 'https://api.currencyapi.com/v3',
    ),
    'coingecko' => 
    array (
      'api_key' => NULL,
      'base_url' => 'https://api.coingecko.com/api/v3',
    ),
    'twilio' => 
    array (
      'sid' => NULL,
      'token' => NULL,
      'from' => NULL,
    ),
    'firebase' => 
    array (
      'server_key' => NULL,
      'sender_id' => NULL,
    ),
    'blockchain' => 
    array (
      'ethereum' => 
      array (
        'rpc_url' => NULL,
        'private_key' => NULL,
        'contract_address' => NULL,
      ),
      'bitcoin' => 
      array (
        'rpc_url' => NULL,
        'username' => NULL,
        'password' => NULL,
      ),
    ),
    'ai_services' => 
    array (
      'fraud_detection' => 
      array (
        'api_key' => NULL,
        'endpoint' => NULL,
      ),
      'risk_analysis' => 
      array (
        'api_key' => NULL,
        'endpoint' => NULL,
      ),
    ),
  ),
  'session' => 
  array (
    'driver' => 'database',
    'lifetime' => 120,
    'expire_on_close' => true,
    'encrypt' => true,
    'files' => 'C:\\xampp\\htdocs\\Mony_Transfir\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'mony_transfer-_global_financial_system_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
    'partitioned' => false,
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => 'localhost:8000',
    ),
    'guard' => 
    array (
      0 => 'web',
    ),
    'expiration' => NULL,
    'token_prefix' => '',
    'middleware' => 
    array (
      'authenticate_session' => 'Laravel\\Sanctum\\Http\\Middleware\\AuthenticateSession',
      'encrypt_cookies' => 'Illuminate\\Cookie\\Middleware\\EncryptCookies',
      'validate_csrf_token' => 'Illuminate\\Foundation\\Http\\Middleware\\ValidateCsrfToken',
    ),
  ),
  'permission' => 
  array (
    'models' => 
    array (
      'permission' => 'Spatie\\Permission\\Models\\Permission',
      'role' => 'Spatie\\Permission\\Models\\Role',
    ),
    'table_names' => 
    array (
      'roles' => 'roles',
      'permissions' => 'permissions',
      'model_has_permissions' => 'model_has_permissions',
      'model_has_roles' => 'model_has_roles',
      'role_has_permissions' => 'role_has_permissions',
    ),
    'column_names' => 
    array (
      'role_pivot_key' => NULL,
      'permission_pivot_key' => NULL,
      'model_morph_key' => 'model_id',
      'team_foreign_key' => 'team_id',
    ),
    'register_permission_check_method' => true,
    'register_octane_reset_listener' => false,
    'events_enabled' => false,
    'teams' => false,
    'team_resolver' => 'Spatie\\Permission\\DefaultTeamResolver',
    'use_passport_client_credentials' => false,
    'display_permission_in_exception' => false,
    'display_role_in_exception' => false,
    'enable_wildcard_permission' => false,
    'cache' => 
    array (
      'expiration_time' => 
      \DateInterval::__set_state(array(
         'from_string' => true,
         'date_string' => '24 hours',
      )),
      'key' => 'spatie.permission.cache',
      'store' => 'default',
    ),
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
