<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Services\NotificationService;
use App\Models\AuditLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendWelcomeNotification implements ShouldQueue
{
    use InteractsWithQueue;

    protected NotificationService $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event): void
    {
        try {
            $user = $event->user;
            
            Log::info('Sending welcome notification to new user', [
                'user_id' => $user->id,
                'email' => $user->email,
            ]);

            // Send welcome notification
            $this->notificationService->sendNotification($user, [
                'type' => 'welcome',
                'title' => 'مرحباً بك في موني ترانسفير - Welcome to Mony Transfer',
                'message' => $this->buildWelcomeMessage($user),
                'data' => [
                    'user_id' => $user->id,
                    'registration_date' => $user->created_at->toISOString(),
                    'next_steps' => $this->getNextSteps($user),
                ],
                'channels' => ['database', 'email'],
                'priority' => 'normal',
            ]);

            // Send KYC reminder if required
            if (config('compliance.kyc.required', true)) {
                $this->sendKYCReminder($user);
            }

            // Log user registration activity
            AuditLog::logUserActivity($user, 'user_registration', 'registered', [
                'registration_ip' => $event->registrationData['ip_address'] ?? request()->ip(),
                'registration_source' => $event->registrationData['source'] ?? 'web',
                'user_agent' => $event->registrationData['user_agent'] ?? request()->userAgent(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send welcome notification', [
                'user_id' => $event->user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Build welcome message.
     */
    protected function buildWelcomeMessage($user): string
    {
        $name = $user->first_name;
        
        return "مرحباً {$name}! نرحب بك في موني ترانسفير، منصتك الموثوقة لتحويل الأموال. " .
               "يمكنك الآن إرسال واستقبال الأموال بأمان وسرعة إلى أكثر من 200 دولة حول العالم. " .
               "لبدء استخدام خدماتنا، يرجى إكمال التحقق من الهوية.";
    }

    /**
     * Get next steps for the user.
     */
    protected function getNextSteps($user): array
    {
        $steps = [
            [
                'title' => 'تحقق من بريدك الإلكتروني',
                'description' => 'تأكد من تفعيل حسابك عبر الرابط المرسل إلى بريدك الإلكتروني',
                'completed' => $user->hasVerifiedEmail(),
            ],
            [
                'title' => 'أكمل التحقق من الهوية (KYC)',
                'description' => 'ارفع المستندات المطلوبة للتحقق من هويتك',
                'completed' => $user->kyc_verified_at !== null,
            ],
            [
                'title' => 'أضف طريقة دفع',
                'description' => 'أضف بطاقة ائتمان أو حساب بنكي لتمويل معاملاتك',
                'completed' => false, // This would check if user has payment methods
            ],
            [
                'title' => 'أرسل أول تحويل',
                'description' => 'جرب إرسال أول تحويل مالي باستخدام منصتنا',
                'completed' => $user->sentTransactions()->exists(),
            ],
        ];

        return $steps;
    }

    /**
     * Send KYC reminder notification.
     */
    protected function sendKYCReminder($user): void
    {
        $this->notificationService->sendNotification($user, [
            'type' => 'kyc_required',
            'title' => 'إكمال التحقق من الهوية مطلوب - KYC Verification Required',
            'message' => 'لضمان أمان حسابك والامتثال للقوانين المالية، يرجى إكمال عملية التحقق من الهوية (KYC) في أقرب وقت ممكن.',
            'data' => [
                'user_id' => $user->id,
                'kyc_deadline' => now()->addDays(7)->toISOString(), // 7 days to complete KYC
                'required_documents' => [
                    'national_id' => 'صورة الهوية الوطنية أو جواز السفر',
                    'address_proof' => 'إثبات العنوان (فاتورة خدمات)',
                    'selfie' => 'صورة شخصية مع الهوية',
                ],
            ],
            'channels' => ['database', 'email'],
            'priority' => 'high',
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(UserRegistered $event, \Throwable $exception): void
    {
        Log::error('SendWelcomeNotification listener failed', [
            'user_id' => $event->user->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
