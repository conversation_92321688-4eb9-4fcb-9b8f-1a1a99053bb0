<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('Welcome to ELite Transfer') }}</title>
    <style>
        body {
            font-family: {{ app()->getLocale() === 'ar' ? "'Tajawal', 'Arial', sans-serif" : "'Helvetica Neue', 'Arial', sans-serif" }};
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
            direction: {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }};
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
        }
        .header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 18px;
        }
        .welcome-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            text-align: center;
            margin-bottom: 30px;
        }
        .greeting h2 {
            color: #1f2937;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .greeting p {
            color: #6b7280;
            font-size: 16px;
            margin: 0;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        .feature-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
        }
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .feature-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
        .cta-section {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 30px 0;
        }
        .cta-section h3 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        .cta-section p {
            margin: 0 0 25px 0;
            opacity: 0.9;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 0 10px 10px 0;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: white;
            color: #059669;
        }
        .btn-primary:hover {
            background-color: #f0f9ff;
            transform: translateY(-1px);
        }
        .btn-secondary {
            background-color: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid white;
        }
        .btn-secondary:hover {
            background-color: white;
            color: #059669;
        }
        .verification-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .verification-notice .icon {
            color: #d97706;
            font-size: 24px;
            margin-bottom: 10px;
        }
        .verification-notice h4 {
            color: #92400e;
            margin: 0 0 10px 0;
        }
        .verification-notice p {
            color: #92400e;
            margin: 0;
            font-size: 14px;
        }
        .support-section {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
        }
        .support-section h4 {
            color: #374151;
            margin: 0 0 15px 0;
        }
        .support-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .support-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .support-item .icon {
            font-size: 20px;
        }
        .support-item .text {
            font-size: 14px;
            color: #6b7280;
        }
        .footer {
            background-color: #1f2937;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .footer h4 {
            margin: 0 0 15px 0;
            color: white;
        }
        .footer p {
            margin: 5px 0;
            opacity: 0.8;
            font-size: 14px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: white;
            text-decoration: none;
            font-size: 24px;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }
        .social-links a:hover {
            opacity: 1;
        }
        @media (max-width: 600px) {
            .container { margin: 0; }
            .content { padding: 20px 15px; }
            .features-grid { grid-template-columns: 1fr; }
            .header { padding: 30px 20px; }
            .header h1 { font-size: 24px; }
            .greeting h2 { font-size: 22px; }
            .btn { display: block; margin: 10px 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="welcome-icon">🎉</div>
            <h1>{{ __('Welcome to Mony Transfer') }}</h1>
            <p>{{ __('Your journey to seamless money transfers begins here') }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Greeting -->
            <div class="greeting">
                <h2>{{ __('Hello') }} {{ $userName }}!</h2>
                <p>{{ __('Thank you for joining Mony Transfer. We\'re excited to help you send money safely and quickly around the world.') }}</p>
            </div>

            <!-- Email Verification Notice -->
            <div class="verification-notice">
                <div class="icon">📧</div>
                <h4>{{ __('Verify Your Email Address') }}</h4>
                <p>{{ __('To get started, please verify your email address by clicking the button below. This helps us keep your account secure.') }}</p>
                <div style="margin-top: 15px;">
                    <a href="{{ $verificationUrl }}" class="btn btn-primary">
                        {{ __('Verify Email Address') }}
                    </a>
                </div>
            </div>

            <!-- Features Grid -->
            <div class="features-grid">
                @foreach($features as $feature)
                <div class="feature-card">
                    <span class="feature-icon">{{ $feature['icon'] }}</span>
                    <div class="feature-title">{{ $feature['title_' . app()->getLocale()] }}</div>
                    <div class="feature-description">{{ $feature['description_' . app()->getLocale()] }}</div>
                </div>
                @endforeach
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <h3>{{ __('Ready to Send Your First Transfer?') }}</h3>
                <p>{{ __('Start sending money to your loved ones in just a few clicks. It\'s fast, secure, and affordable.') }}</p>
                <a href="{{ $appUrl }}/transactions/create" class="btn btn-primary">
                    {{ __('Send Money Now') }}
                </a>
                <a href="{{ $appUrl }}/dashboard" class="btn btn-secondary">
                    {{ __('Explore Dashboard') }}
                </a>
            </div>

            <!-- Support Section -->
            <div class="support-section">
                <h4>{{ __('Need Help? We\'re Here for You') }}</h4>
                <div class="support-grid">
                    <div class="support-item">
                        <span class="icon">📧</span>
                        <div class="text">
                            <strong>{{ __('Email Support') }}</strong><br>
                            {{ $supportEmail }}
                        </div>
                    </div>
                    <div class="support-item">
                        <span class="icon">📞</span>
                        <div class="text">
                            <strong>{{ __('Phone Support') }}</strong><br>
                            {{ $supportPhone }}
                        </div>
                    </div>
                    <div class="support-item">
                        <span class="icon">💬</span>
                        <div class="text">
                            <strong>{{ __('Live Chat') }}</strong><br>
                            {{ __('Available 24/7') }}
                        </div>
                    </div>
                    <div class="support-item">
                        <span class="icon">📚</span>
                        <div class="text">
                            <strong>{{ __('Help Center') }}</strong><br>
                            {{ __('FAQs & Guides') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Notice -->
            <div style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 20px; margin: 25px 0;">
                <div style="color: #1e40af; font-size: 20px; margin-bottom: 10px;">🔒</div>
                <h4 style="color: #1e40af; margin: 0 0 10px 0;">{{ __('Your Security is Our Priority') }}</h4>
                <p style="color: #1e40af; margin: 0; font-size: 14px;">
                    {{ __('We use bank-level encryption and advanced fraud detection to keep your money and personal information safe. Your account is protected by multiple layers of security.') }}
                </p>
            </div>

            <!-- Getting Started Tips -->
            <div style="margin: 30px 0;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">{{ __('Getting Started Tips') }}</h3>
                <ul style="color: #6b7280; padding-left: 20px;">
                    <li style="margin-bottom: 10px;">{{ __('Complete your profile to increase your transfer limits') }}</li>
                    <li style="margin-bottom: 10px;">{{ __('Add your preferred payment methods for faster transactions') }}</li>
                    <li style="margin-bottom: 10px;">{{ __('Save frequent recipients for quick transfers') }}</li>
                    <li style="margin-bottom: 10px;">{{ __('Enable notifications to stay updated on your transfers') }}</li>
                    <li style="margin-bottom: 10px;">{{ __('Verify your identity (KYC) for higher limits and better rates') }}</li>
                </ul>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <h4>Mony Transfer</h4>
            <p>{{ __('Making money transfers simple, fast, and secure') }}</p>
            
            <div class="social-links">
                <a href="#" title="Facebook">📘</a>
                <a href="#" title="Twitter">🐦</a>
                <a href="#" title="Instagram">📷</a>
                <a href="#" title="LinkedIn">💼</a>
            </div>
            
            <p>{{ __('Visit our website') }}: <a href="{{ $appUrl }}" style="color: #60a5fa;">{{ $appUrl }}</a></p>
            <p>{{ __('Download our mobile app for iOS and Android') }}</p>
            
            <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #374151;">
                <p style="font-size: 12px; opacity: 0.7;">
                    {{ __('This email was sent to') }} {{ $user->email }}. 
                    {{ __('If you did not create an account, please ignore this email.') }}
                </p>
                <p style="font-size: 12px; opacity: 0.7;">
                    © {{ date('Y') }} Mony Transfer. {{ __('All rights reserved.') }}
                </p>
            </div>
        </div>
    </div>
</body>
</html>
