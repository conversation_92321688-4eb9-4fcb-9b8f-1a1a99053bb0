<?php

namespace App\Exceptions;

class AuthenticationException extends BaseException
{
    protected int $statusCode = 401;

    /**
     * Create a new authentication exception for invalid credentials.
     */
    public static function invalidCredentials(): self
    {
        return new self(
            'بيانات تسجيل الدخول غير صحيحة',
            ['action_required' => 'check_credentials']
        );
    }

    /**
     * Create a new authentication exception for account locked.
     */
    public static function accountLocked(int $lockoutMinutes): self
    {
        return new self(
            "الحساب مقفل مؤقتاً لمدة {$lockoutMinutes} دقيقة بسبب محاولات دخول خاطئة متكررة",
            [
                'lockout_minutes' => $lockoutMinutes,
                'action_required' => 'wait_or_contact_support',
            ]
        );
    }

    /**
     * Create a new authentication exception for account suspended.
     */
    public static function accountSuspended(string $reason = null): self
    {
        $message = 'الحساب معلق';
        $context = ['action_required' => 'contact_support'];

        if ($reason) {
            $message .= ". السبب: {$reason}";
            $context['suspension_reason'] = $reason;
        }

        return new self($message, $context);
    }

    /**
     * Create a new authentication exception for account not verified.
     */
    public static function accountNotVerified(string $verificationType = 'email'): self
    {
        $messages = [
            'email' => 'يجب تأكيد البريد الإلكتروني أولاً',
            'phone' => 'يجب تأكيد رقم الهاتف أولاً',
            'kyc' => 'يجب إكمال التحقق من الهوية أولاً',
        ];

        return new self(
            $messages[$verificationType] ?? 'يجب تأكيد الحساب أولاً',
            [
                'verification_type' => $verificationType,
                'action_required' => 'complete_verification',
            ]
        );
    }

    /**
     * Create a new authentication exception for two-factor authentication required.
     */
    public static function twoFactorRequired(): self
    {
        return new self(
            'يتطلب رمز التحقق المرسل إلى هاتفك',
            [
                'action_required' => 'provide_2fa_code',
                'next_step' => 'enter_verification_code',
            ]
        );
    }

    /**
     * Create a new authentication exception for invalid two-factor code.
     */
    public static function invalidTwoFactorCode(): self
    {
        return new self(
            'رمز التحقق غير صحيح',
            [
                'action_required' => 'retry_2fa_code',
                'next_step' => 'request_new_code',
            ]
        );
    }

    /**
     * Create a new authentication exception for expired token.
     */
    public static function tokenExpired(): self
    {
        return new self(
            'انتهت صلاحية جلسة تسجيل الدخول',
            [
                'action_required' => 'login_again',
                'next_step' => 'redirect_to_login',
            ]
        );
    }

    /**
     * Create a new authentication exception for invalid token.
     */
    public static function invalidToken(): self
    {
        return new self(
            'رمز المصادقة غير صحيح',
            [
                'action_required' => 'login_again',
                'next_step' => 'redirect_to_login',
            ]
        );
    }

    /**
     * Create a new authentication exception for missing token.
     */
    public static function missingToken(): self
    {
        return new self(
            'رمز المصادقة مطلوب',
            [
                'action_required' => 'provide_auth_token',
                'next_step' => 'login_required',
            ]
        );
    }

    /**
     * Create a new authentication exception for session expired.
     */
    public static function sessionExpired(): self
    {
        return new self(
            'انتهت صلاحية الجلسة',
            [
                'action_required' => 'login_again',
                'next_step' => 'redirect_to_login',
            ]
        );
    }

    /**
     * Create a new authentication exception for too many login attempts.
     */
    public static function tooManyAttempts(int $retryAfterSeconds): self
    {
        $minutes = ceil($retryAfterSeconds / 60);
        
        return new self(
            "محاولات دخول كثيرة جداً. حاول مرة أخرى بعد {$minutes} دقيقة",
            [
                'retry_after_seconds' => $retryAfterSeconds,
                'retry_after_minutes' => $minutes,
                'action_required' => 'wait_and_retry',
            ]
        );
    }

    /**
     * Create a new authentication exception for device not recognized.
     */
    public static function deviceNotRecognized(): self
    {
        return new self(
            'جهاز غير معروف. يتطلب تأكيد إضافي',
            [
                'action_required' => 'verify_device',
                'next_step' => 'device_verification',
            ]
        );
    }

    /**
     * Create a new authentication exception for location not allowed.
     */
    public static function locationNotAllowed(string $location): self
    {
        return new self(
            "تسجيل الدخول من {$location} غير مسموح",
            [
                'blocked_location' => $location,
                'action_required' => 'contact_support',
            ]
        );
    }

    /**
     * Get the error code for this exception.
     */
    public function getErrorCode(): string
    {
        return 'AUTHENTICATION_ERROR';
    }
}
