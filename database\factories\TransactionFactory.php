<?php

namespace Database\Factories;

use App\Models\Transaction;
use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Transaction>
 */
class TransactionFactory extends Factory
{
    protected $model = Transaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = $this->faker->randomFloat(2, 10, 10000);
        $commissionRate = $this->faker->randomFloat(3, 0.01, 0.05);
        $commission = $amount * $commissionRate;
        $totalAmount = $amount + $commission;

        return [
            'transaction_id' => 'TXN' . strtoupper($this->faker->bothify('##??####')),
            'user_id' => User::factory(),
            'sender_name' => $this->faker->name(),
            'sender_phone' => $this->faker->phoneNumber(),
            'sender_email' => $this->faker->email(),
            'sender_country_id' => Country::factory(),
            'sender_address' => $this->faker->address(),
            'sender_id_number' => $this->faker->numerify('##########'),
            'sender_id_type' => $this->faker->randomElement(['passport', 'national_id', 'driving_license']),

            'recipient_name' => $this->faker->name(),
            'recipient_phone' => $this->faker->phoneNumber(),
            'recipient_email' => $this->faker->email(),
            'recipient_country_id' => Country::factory(),
            'recipient_address' => $this->faker->address(),
            'recipient_bank_name' => $this->faker->company() . ' Bank',
            'recipient_bank_account' => $this->faker->bankAccountNumber(),
            'recipient_bank_code' => $this->faker->numerify('####'),

            'amount' => $amount,
            'currency_from' => Currency::factory(),
            'currency_to' => Currency::factory(),
            'exchange_rate' => $this->faker->randomFloat(6, 0.5, 4.0),
            'amount_received' => $amount * $this->faker->randomFloat(6, 0.5, 4.0),
            'commission' => $commission,
            'commission_rate' => $commissionRate,
            'total_amount' => $totalAmount,

            'payment_method' => $this->faker->randomElement(['cash', 'bank_transfer', 'card', 'wallet']),
            'delivery_method' => $this->faker->randomElement(['cash_pickup', 'bank_deposit', 'mobile_wallet']),
            'purpose' => $this->faker->randomElement(['family_support', 'business', 'education', 'medical', 'other']),
            'notes' => $this->faker->optional()->sentence(),

            'status' => $this->faker->randomElement(['pending', 'processing', 'completed', 'cancelled', 'failed']),
            'priority' => $this->faker->randomElement(['normal', 'high', 'urgent']),
            'risk_level' => $this->faker->randomElement(['low', 'medium', 'high']),

            'agent_id' => null,
            'branch_id' => null,
            'reference_number' => 'REF' . $this->faker->numerify('############'),
            'tracking_number' => 'TRK' . $this->faker->numerify('############'),

            'processed_at' => $this->faker->optional(0.7)->dateTimeBetween('-30 days', 'now'),
            'completed_at' => $this->faker->optional(0.6)->dateTimeBetween('-30 days', 'now'),
            'cancelled_at' => null,
            'expires_at' => $this->faker->dateTimeBetween('now', '+7 days'),

            'metadata' => [
                'ip_address' => $this->faker->ipv4(),
                'user_agent' => $this->faker->userAgent(),
                'device_type' => $this->faker->randomElement(['web', 'mobile', 'tablet']),
            ],
        ];
    }

    /**
     * Indicate that the transaction is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'processed_at' => null,
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the transaction is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'processed_at' => $this->faker->dateTimeBetween('-7 days', '-1 day'),
            'completed_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Indicate that the transaction is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'cancelled_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Indicate that the transaction has high risk.
     */
    public function highRisk(): static
    {
        return $this->state(fn (array $attributes) => [
            'risk_level' => 'high',
            'amount' => $this->faker->randomFloat(2, 10000, 50000),
        ]);
    }

    /**
     * Indicate that the transaction is urgent.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'urgent',
        ]);
    }
}
