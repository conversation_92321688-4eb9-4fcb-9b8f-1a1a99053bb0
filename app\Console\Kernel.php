<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Financial System Scheduled Tasks
        
        // Update exchange rates every 5 minutes
        $schedule->command('exchange-rates:update')
                 ->everyFiveMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Process pending transactions every minute
        $schedule->command('transactions:process-pending')
                 ->everyMinute()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Update cryptocurrency prices every 2 minutes
        $schedule->command('crypto:update-prices')
                 ->everyTwoMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Collect system metrics every minute
        $schedule->call(function () {
            $metricsService = app(\App\Services\MetricsService::class);
            $metrics = $metricsService->collectAllMetrics();
            $metricsService->storeMetrics($metrics);
        })->everyMinute()
          ->name('collect-metrics')
          ->withoutOverlapping();

        // Daily maintenance tasks
        $schedule->command('system:maintenance', ['--type=cleanup'])
                 ->daily()
                 ->at('02:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Weekly full backup
        $schedule->command('system:maintenance', ['--type=backup'])
                 ->weekly()
                 ->sundays()
                 ->at('01:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Monthly optimization
        $schedule->command('system:maintenance', ['--type=optimize'])
                 ->monthly()
                 ->at('03:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Security maintenance every 6 hours
        $schedule->command('system:maintenance', ['--type=security'])
                 ->everySixHours()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Monitor blockchain transactions every 5 minutes
        $schedule->call(function () {
            $blockchainService = app(\App\Services\BlockchainService::class);
            // Monitor incoming transactions for all active crypto wallets
            // This would be implemented based on your specific requirements
        })->everyFiveMinutes()
          ->name('monitor-blockchain')
          ->withoutOverlapping();

        // Clean up old audit logs weekly
        $schedule->call(function () {
            \App\Models\AuditLog::where('created_at', '<', now()->subDays(90))->delete();
        })->weekly()
          ->sundays()
          ->at('04:00')
          ->name('cleanup-audit-logs');

        // Clean up old notifications monthly
        $schedule->call(function () {
            \App\Models\Notification::where('created_at', '<', now()->subDays(90))->delete();
        })->monthly()
          ->at('04:30')
          ->name('cleanup-notifications');

        // Generate daily reports
        $schedule->call(function () {
            // Generate and send daily summary reports
            // $reportService = app(\App\Services\ReportService::class);
            // $reportService->generateDailySummary();
        })->daily()
          ->at('06:00')
          ->name('daily-reports');

        // Check system health every 5 minutes
        $schedule->call(function () {
            $healthController = app(\App\Http\Controllers\HealthController::class);
            $health = $healthController->index();
            
            // Log critical issues
            $healthData = $health->getData(true);
            if ($healthData['status'] === 'unhealthy') {
                \Log::critical('System health check failed', $healthData);
            }
        })->everyFiveMinutes()
          ->name('health-check');

        // Auto-resolve low-risk fraud alerts after 7 days
        $schedule->call(function () {
            \App\Models\FraudDetection::where('risk_level', 'low')
                ->where('status', 'open')
                ->where('detected_at', '<', now()->subDays(7))
                ->update([
                    'status' => 'auto_resolved',
                    'resolved_at' => now(),
                    'resolution_notes' => 'Auto-resolved after 7 days (low risk)',
                ]);
        })->daily()
          ->at('05:00')
          ->name('auto-resolve-fraud-alerts');

        // Update user risk scores daily
        $schedule->call(function () {
            // Recalculate user risk scores based on recent activity
            $users = \App\Models\User::where('status', 'active')->get();
            
            foreach ($users as $user) {
                $riskScore = $this->calculateUserRiskScore($user);
                $user->update(['risk_score' => $riskScore]);
            }
        })->daily()
          ->at('07:00')
          ->name('update-risk-scores');

        // Send weekly compliance reports
        $schedule->call(function () {
            // Generate and send compliance reports to management
            // This would integrate with your compliance reporting system
        })->weekly()
          ->mondays()
          ->at('08:00')
          ->name('weekly-compliance-reports');

        // Backup verification - verify last backup integrity
        $schedule->call(function () {
            $backupService = app(\App\Services\BackupService::class);
            // Get latest backup and verify its integrity
            // $backupService->verifyLatestBackup();
        })->daily()
          ->at('09:00')
          ->name('verify-backup');

        // Clear expired cache entries
        $schedule->call(function () {
            \Cache::flush();
        })->daily()
          ->at('03:30')
          ->name('clear-cache');

        // Optimize database tables weekly
        $schedule->call(function () {
            $tables = \DB::select('SHOW TABLES');
            foreach ($tables as $table) {
                $tableName = array_values((array) $table)[0];
                try {
                    \DB::statement("OPTIMIZE TABLE `{$tableName}`");
                } catch (\Exception $e) {
                    \Log::warning("Failed to optimize table {$tableName}: " . $e->getMessage());
                }
            }
        })->weekly()
          ->saturdays()
          ->at('02:00')
          ->name('optimize-database');

        // Monitor queue health
        $schedule->call(function () {
            $queueSize = \DB::table('jobs')->count();
            $failedJobs = \DB::table('failed_jobs')->count();
            
            if ($queueSize > 1000) {
                \Log::warning("High queue size detected: {$queueSize} jobs");
            }
            
            if ($failedJobs > 100) {
                \Log::error("High failed job count: {$failedJobs} failed jobs");
            }
        })->everyTenMinutes()
          ->name('monitor-queue');

        // Send system status notifications
        $schedule->call(function () {
            // Send daily system status to administrators
            $metrics = app(\App\Services\MetricsService::class)->getDashboardMetrics();
            
            // Check for any critical issues and send notifications
            if ($metrics['alerts']['critical_alerts'] > 0) {
                // Send critical alert notification
                \Log::critical('Critical system alerts detected', $metrics['alerts']);
            }
        })->daily()
          ->at('10:00')
          ->name('system-status-notifications');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }

    /**
     * Calculate user risk score based on various factors.
     */
    private function calculateUserRiskScore(\App\Models\User $user): float
    {
        $score = 0;
        
        // Base score
        $score += 10;
        
        // Account age factor (newer accounts are riskier)
        $accountAgeDays = $user->created_at->diffInDays(now());
        if ($accountAgeDays < 30) {
            $score += 20;
        } elseif ($accountAgeDays < 90) {
            $score += 10;
        }
        
        // KYC verification status
        if (!$user->kyc_verified_at) {
            $score += 30;
        }
        
        // Transaction patterns
        $recentTransactions = $user->sentTransactions()
            ->where('created_at', '>', now()->subDays(30))
            ->count();
            
        if ($recentTransactions > 50) {
            $score += 15;
        }
        
        // Failed transaction rate
        $failedTransactions = $user->sentTransactions()
            ->where('created_at', '>', now()->subDays(30))
            ->where('status', 'failed')
            ->count();
            
        if ($recentTransactions > 0) {
            $failureRate = ($failedTransactions / $recentTransactions) * 100;
            if ($failureRate > 20) {
                $score += 25;
            }
        }
        
        // Fraud alerts
        $fraudAlerts = \App\Models\FraudDetection::where('user_id', $user->id)
            ->where('detected_at', '>', now()->subDays(90))
            ->count();
            
        $score += $fraudAlerts * 10;
        
        // Country risk (this would be based on your risk assessment)
        $highRiskCountries = ['XX', 'YY']; // Replace with actual country codes
        if (in_array($user->country->code ?? '', $highRiskCountries)) {
            $score += 20;
        }
        
        // Cap the score at 100
        return min($score, 100);
    }
}
