<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for performance optimization and monitoring
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Caching Configuration
    |--------------------------------------------------------------------------
    */

    'caching' => [
        'enabled' => env('PERFORMANCE_CACHING_ENABLED', true),
        
        'strategies' => [
            'query_cache' => [
                'enabled' => true,
                'ttl' => 3600, // 1 hour
                'tags' => ['database', 'queries'],
            ],
            
            'api_response_cache' => [
                'enabled' => true,
                'ttl' => 300, // 5 minutes
                'exclude_routes' => [
                    'api/v1/auth/*',
                    'api/v1/transactions/create',
                ],
            ],
            
            'view_cache' => [
                'enabled' => true,
                'ttl' => 1800, // 30 minutes
            ],
            
            'model_cache' => [
                'enabled' => true,
                'ttl' => 900, // 15 minutes
                'models' => [
                    'Currency' => 3600,
                    'Country' => 7200,
                    'ExchangeRate' => 300,
                ],
            ],
        ],

        'cache_warming' => [
            'enabled' => true,
            'schedule' => 'hourly',
            'routes' => [
                'api/v1/currencies',
                'api/v1/countries',
                'api/v1/exchange-rates',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Optimization
    |--------------------------------------------------------------------------
    */

    'database' => [
        'query_optimization' => [
            'enabled' => true,
            'log_slow_queries' => true,
            'slow_query_threshold' => 1000, // milliseconds
            'explain_slow_queries' => env('APP_DEBUG', false),
        ],

        'connection_pooling' => [
            'enabled' => true,
            'max_connections' => 100,
            'min_connections' => 10,
            'idle_timeout' => 300, // 5 minutes
        ],

        'read_write_splitting' => [
            'enabled' => env('DB_READ_WRITE_SPLIT', false),
            'read_connections' => [
                'read1' => env('DB_READ_HOST_1'),
                'read2' => env('DB_READ_HOST_2'),
            ],
            'write_connection' => 'mysql',
        ],

        'indexing' => [
            'auto_suggest' => true,
            'monitor_missing_indexes' => true,
            'analyze_query_patterns' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Memory Management
    |--------------------------------------------------------------------------
    */

    'memory' => [
        'optimization' => [
            'enabled' => true,
            'memory_limit' => '512M',
            'gc_probability' => 1,
            'gc_divisor' => 100,
        ],

        'object_pooling' => [
            'enabled' => true,
            'pool_size' => 100,
            'objects' => [
                'transaction_processor',
                'fraud_detector',
                'exchange_rate_calculator',
            ],
        ],

        'memory_monitoring' => [
            'enabled' => true,
            'alert_threshold' => 80, // percentage
            'log_usage' => env('APP_DEBUG', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Asset Optimization
    |--------------------------------------------------------------------------
    */

    'assets' => [
        'compression' => [
            'enabled' => true,
            'gzip' => true,
            'brotli' => true,
            'compression_level' => 6,
        ],

        'minification' => [
            'enabled' => true,
            'css' => true,
            'js' => true,
            'html' => true,
        ],

        'cdn' => [
            'enabled' => env('CDN_ENABLED', false),
            'url' => env('CDN_URL'),
            'assets' => ['css', 'js', 'images'],
        ],

        'image_optimization' => [
            'enabled' => true,
            'formats' => ['webp', 'avif'],
            'quality' => 85,
            'lazy_loading' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Performance
    |--------------------------------------------------------------------------
    */

    'api' => [
        'response_compression' => [
            'enabled' => true,
            'algorithms' => ['gzip', 'deflate'],
            'min_size' => 1024, // bytes
        ],

        'pagination' => [
            'default_per_page' => 20,
            'max_per_page' => 100,
            'optimize_count_queries' => true,
        ],

        'field_selection' => [
            'enabled' => true,
            'allow_sparse_fieldsets' => true,
            'default_fields' => ['id', 'name', 'created_at'],
        ],

        'etag_caching' => [
            'enabled' => true,
            'strong_etags' => true,
            'include_routes' => [
                'api/v1/currencies',
                'api/v1/countries',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Performance
    |--------------------------------------------------------------------------
    */

    'queue' => [
        'optimization' => [
            'enabled' => true,
            'batch_processing' => true,
            'batch_size' => 100,
            'parallel_workers' => 4,
        ],

        'priority_queues' => [
            'enabled' => true,
            'queues' => [
                'critical' => 1,
                'high' => 2,
                'normal' => 3,
                'low' => 4,
            ],
        ],

        'job_batching' => [
            'enabled' => true,
            'batch_size' => 50,
            'timeout' => 300, // 5 minutes
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Performance
    |--------------------------------------------------------------------------
    */

    'session' => [
        'optimization' => [
            'enabled' => true,
            'driver' => 'redis',
            'gc_probability' => 1,
            'gc_divisor' => 1000,
        ],

        'compression' => [
            'enabled' => true,
            'algorithm' => 'gzip',
            'level' => 6,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Load Balancing
    |--------------------------------------------------------------------------
    */

    'load_balancing' => [
        'enabled' => env('LOAD_BALANCING_ENABLED', false),
        'algorithm' => 'round_robin', // round_robin, least_connections, ip_hash
        'health_checks' => [
            'enabled' => true,
            'interval' => 30, // seconds
            'timeout' => 5, // seconds
            'unhealthy_threshold' => 3,
            'healthy_threshold' => 2,
        ],
        'sticky_sessions' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Content Delivery Network
    |--------------------------------------------------------------------------
    */

    'cdn' => [
        'enabled' => env('CDN_ENABLED', false),
        'provider' => env('CDN_PROVIDER', 'cloudflare'),
        'url' => env('CDN_URL'),
        'zones' => [
            'static' => env('CDN_STATIC_ZONE'),
            'images' => env('CDN_IMAGES_ZONE'),
            'api' => env('CDN_API_ZONE'),
        ],
        'cache_control' => [
            'static_assets' => 'public, max-age=31536000', // 1 year
            'images' => 'public, max-age=2592000', // 30 days
            'api_responses' => 'public, max-age=300', // 5 minutes
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Profiling
    |--------------------------------------------------------------------------
    */

    'monitoring' => [
        'enabled' => true,
        'profiling' => [
            'enabled' => env('PROFILING_ENABLED', false),
            'sample_rate' => 0.1, // 10%
            'memory_tracking' => true,
            'query_tracking' => true,
        ],

        'metrics' => [
            'response_time' => true,
            'memory_usage' => true,
            'query_count' => true,
            'cache_hit_ratio' => true,
            'queue_size' => true,
        ],

        'alerts' => [
            'slow_response' => 3000, // milliseconds
            'high_memory' => 80, // percentage
            'many_queries' => 100, // per request
            'low_cache_hit' => 70, // percentage
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Optimization Strategies
    |--------------------------------------------------------------------------
    */

    'optimization' => [
        'eager_loading' => [
            'enabled' => true,
            'auto_detect' => true,
            'relationships' => [
                'Transaction' => ['currency', 'sender', 'receiver'],
                'User' => ['country', 'wallets'],
                'Wallet' => ['currency', 'user'],
            ],
        ],

        'lazy_loading' => [
            'enabled' => true,
            'chunk_size' => 1000,
            'models' => ['AuditLog', 'Notification'],
        ],

        'data_compression' => [
            'enabled' => true,
            'fields' => ['notes', 'metadata', 'description'],
            'algorithm' => 'gzip',
        ],

        'background_processing' => [
            'enabled' => true,
            'operations' => [
                'email_sending',
                'report_generation',
                'data_export',
                'backup_creation',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto-scaling
    |--------------------------------------------------------------------------
    */

    'auto_scaling' => [
        'enabled' => env('AUTO_SCALING_ENABLED', false),
        'metrics' => [
            'cpu_threshold' => 70, // percentage
            'memory_threshold' => 80, // percentage
            'response_time_threshold' => 2000, // milliseconds
            'queue_size_threshold' => 1000, // jobs
        ],
        'scaling_policies' => [
            'scale_up_cooldown' => 300, // 5 minutes
            'scale_down_cooldown' => 600, // 10 minutes
            'min_instances' => 2,
            'max_instances' => 10,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Testing
    |--------------------------------------------------------------------------
    */

    'testing' => [
        'load_testing' => [
            'enabled' => env('LOAD_TESTING_ENABLED', false),
            'scenarios' => [
                'normal_load' => 100, // concurrent users
                'peak_load' => 500,
                'stress_test' => 1000,
            ],
            'duration' => 300, // 5 minutes
        ],

        'benchmarking' => [
            'enabled' => env('BENCHMARKING_ENABLED', false),
            'endpoints' => [
                'api/v1/transactions',
                'api/v1/wallets',
                'api/v1/exchange-rates',
            ],
            'iterations' => 1000,
        ],
    ],

];
