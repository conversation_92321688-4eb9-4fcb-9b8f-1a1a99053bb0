<?php

namespace Tests\Unit\PaymentGateways;

use Tests\TestCase;
use App\Services\PaymentGateways\StripePaymentGateway;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Mockery;

class StripePaymentGatewayTest extends TestCase
{
    use RefreshDatabase;

    private StripePaymentGateway $gateway;
    private User $user;
    private Transaction $transaction;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->gateway = new StripePaymentGateway();
        
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'kyc_status' => 'verified',
        ]);

        $this->transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'amount' => 100.00,
            'currency' => 'USD',
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function it_can_initialize_payment_successfully()
    {
        Http::fake([
            'api.stripe.com/v1/payment_intents' => Http::response([
                'id' => 'pi_test_123456',
                'client_secret' => 'pi_test_123456_secret_test',
                'status' => 'requires_payment_method',
                'amount' => 10000,
                'currency' => 'usd',
            ], 200)
        ]);

        $paymentData = [
            'amount' => 100.00,
            'currency' => 'USD',
            'transaction_id' => $this->transaction->id,
            'user_id' => $this->user->id,
            'email' => $this->user->email,
        ];

        $result = $this->gateway->initializePayment($paymentData);

        $this->assertTrue($result['success']);
        $this->assertEquals('pi_test_123456', $result['payment_id']);
        $this->assertEquals('pi_test_123456_secret_test', $result['client_secret']);
        $this->assertEquals(100.00, $result['amount']);
        $this->assertEquals('USD', $result['currency']);
    }

    /** @test */
    public function it_handles_payment_initialization_failure()
    {
        Http::fake([
            'api.stripe.com/v1/payment_intents' => Http::response([
                'error' => [
                    'type' => 'invalid_request_error',
                    'message' => 'Invalid currency',
                ]
            ], 400)
        ]);

        $paymentData = [
            'amount' => 100.00,
            'currency' => 'INVALID',
            'transaction_id' => $this->transaction->id,
        ];

        $result = $this->gateway->initializePayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('error', $result);
    }

    /** @test */
    public function it_can_process_payment_successfully()
    {
        Http::fake([
            'api.stripe.com/v1/payment_intents/pi_test_123456/confirm' => Http::response([
                'id' => 'pi_test_123456',
                'status' => 'succeeded',
                'amount' => 10000,
                'currency' => 'usd',
            ], 200)
        ]);

        $paymentData = [
            'payment_method' => 'pm_card_visa',
            'return_url' => 'https://example.com/return',
        ];

        $result = $this->gateway->processPayment('pi_test_123456', $paymentData);

        $this->assertTrue($result['success']);
        $this->assertEquals('pi_test_123456', $result['payment_id']);
        $this->assertEquals('succeeded', $result['status']);
        $this->assertEquals(100.00, $result['amount']);
    }

    /** @test */
    public function it_can_verify_payment_successfully()
    {
        Http::fake([
            'api.stripe.com/v1/payment_intents/pi_test_123456' => Http::response([
                'id' => 'pi_test_123456',
                'status' => 'succeeded',
                'amount' => 10000,
                'currency' => 'usd',
            ], 200)
        ]);

        $result = $this->gateway->verifyPayment('pi_test_123456');

        $this->assertTrue($result['success']);
        $this->assertEquals('pi_test_123456', $result['payment_id']);
        $this->assertEquals('succeeded', $result['status']);
        $this->assertTrue($result['verified']);
    }

    /** @test */
    public function it_can_refund_payment_successfully()
    {
        Http::fake([
            'api.stripe.com/v1/refunds' => Http::response([
                'id' => 're_test_123456',
                'status' => 'succeeded',
                'amount' => 5000,
                'currency' => 'usd',
            ], 200)
        ]);

        $result = $this->gateway->refundPayment('pi_test_123456', 50.00, 'USD');

        $this->assertTrue($result['success']);
        $this->assertEquals('re_test_123456', $result['refund_id']);
        $this->assertEquals('succeeded', $result['status']);
        $this->assertEquals(50.00, $result['amount']);
    }

    /** @test */
    public function it_validates_webhook_signature_correctly()
    {
        $payload = json_encode(['type' => 'payment_intent.succeeded']);
        $timestamp = time();
        $secret = 'whsec_test_secret';
        
        config(['payment_gateways.stripe.webhook_secret' => $secret]);
        
        $signedPayload = $timestamp . '.' . $payload;
        $signature = hash_hmac('sha256', $signedPayload, $secret);
        
        $headers = [
            'stripe-signature' => "t={$timestamp},v1={$signature}",
        ];

        $isValid = $this->gateway->validateWebhook($headers, $payload);

        $this->assertTrue($isValid);
    }

    /** @test */
    public function it_rejects_invalid_webhook_signature()
    {
        $payload = json_encode(['type' => 'payment_intent.succeeded']);
        $headers = [
            'stripe-signature' => 't=' . time() . ',v1=invalid_signature',
        ];

        $isValid = $this->gateway->validateWebhook($headers, $payload);

        $this->assertFalse($isValid);
    }

    /** @test */
    public function it_handles_webhook_events_correctly()
    {
        $this->transaction->update([
            'gateway_transaction_id' => 'pi_test_123456',
            'status' => 'processing',
        ]);

        $webhookData = [
            'type' => 'payment_intent.succeeded',
            'data' => [
                'object' => [
                    'id' => 'pi_test_123456',
                    'status' => 'succeeded',
                ]
            ]
        ];

        $result = $this->gateway->handleWebhook($webhookData);

        $this->assertTrue($result['success']);
        
        $this->transaction->refresh();
        $this->assertEquals('completed', $this->transaction->status);
    }

    /** @test */
    public function it_calculates_fees_correctly()
    {
        $fees = $this->gateway->calculateFees(100.00, 'USD', 'card');

        $this->assertEquals(2.9, $fees['percentage_fee']);
        $this->assertEquals(0.30, $fees['fixed_fee']);
        $this->assertEquals(3.20, $fees['total_fee']);
        $this->assertEquals(96.80, $fees['net_amount']);
    }

    /** @test */
    public function it_validates_payment_data_correctly()
    {
        $validData = [
            'amount' => 100.00,
            'currency' => 'USD',
        ];

        $result = $this->gateway->validatePaymentData($validData);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
    }

    /** @test */
    public function it_rejects_invalid_payment_data()
    {
        $invalidData = [
            'amount' => -10.00,
            'currency' => 'INVALID',
        ];

        $result = $this->gateway->validatePaymentData($invalidData);

        $this->assertFalse($result['valid']);
        $this->assertNotEmpty($result['errors']);
        $this->assertContains('Amount must be greater than 0', $result['errors']);
        $this->assertContains('Invalid or unsupported currency', $result['errors']);
    }

    /** @test */
    public function it_converts_amounts_to_stripe_format_correctly()
    {
        $reflection = new \ReflectionClass($this->gateway);
        $method = $reflection->getMethod('convertToStripeAmount');
        $method->setAccessible(true);

        // Test regular currency (USD)
        $result = $method->invokeArgs($this->gateway, [100.50, 'USD']);
        $this->assertEquals(10050, $result);

        // Test zero-decimal currency (JPY)
        $result = $method->invokeArgs($this->gateway, [100, 'JPY']);
        $this->assertEquals(100, $result);
    }

    /** @test */
    public function it_converts_amounts_from_stripe_format_correctly()
    {
        $reflection = new \ReflectionClass($this->gateway);
        $method = $reflection->getMethod('convertFromStripeAmount');
        $method->setAccessible(true);

        // Test regular currency (USD)
        $result = $method->invokeArgs($this->gateway, [10050, 'USD']);
        $this->assertEquals(100.50, $result);

        // Test zero-decimal currency (JPY)
        $result = $method->invokeArgs($this->gateway, [100, 'JPY']);
        $this->assertEquals(100.0, $result);
    }

    /** @test */
    public function it_returns_supported_currencies()
    {
        $currencies = $this->gateway->getSupportedCurrencies();

        $this->assertIsArray($currencies);
        $this->assertContains('USD', $currencies);
        $this->assertContains('EUR', $currencies);
        $this->assertContains('GBP', $currencies);
        $this->assertGreaterThan(20, count($currencies));
    }

    /** @test */
    public function it_returns_supported_countries()
    {
        $countries = $this->gateway->getSupportedCountries();

        $this->assertIsArray($countries);
        $this->assertContains('US', $countries);
        $this->assertContains('GB', $countries);
        $this->assertContains('CA', $countries);
        $this->assertGreaterThan(40, count($countries));
    }

    /** @test */
    public function it_returns_payment_methods()
    {
        $methods = $this->gateway->getPaymentMethods();

        $this->assertIsArray($methods);
        $this->assertArrayHasKey('card', $methods);
        $this->assertArrayHasKey('bank_transfer', $methods);
        $this->assertArrayHasKey('wallet', $methods);
    }

    /** @test */
    public function it_returns_transaction_limits()
    {
        $limits = $this->gateway->getTransactionLimits();

        $this->assertIsArray($limits);
        $this->assertArrayHasKey('min_amount', $limits);
        $this->assertArrayHasKey('max_amount', $limits);
        $this->assertArrayHasKey('daily_limit', $limits);
        $this->assertArrayHasKey('monthly_limit', $limits);
    }

    /** @test */
    public function it_tests_connection_successfully()
    {
        Http::fake([
            'api.stripe.com/v1/account' => Http::response([
                'id' => 'acct_test_123456',
                'business_profile' => [
                    'name' => 'Test Business',
                ]
            ], 200)
        ]);

        $result = $this->gateway->testConnection();

        $this->assertTrue($result['success']);
        $this->assertEquals('Connection successful', $result['message']);
    }

    /** @test */
    public function it_handles_connection_test_failure()
    {
        Http::fake([
            'api.stripe.com/v1/account' => Http::response([
                'error' => [
                    'type' => 'authentication_error',
                    'message' => 'Invalid API key',
                ]
            ], 401)
        ]);

        $result = $this->gateway->testConnection();

        $this->assertFalse($result['success']);
        $this->assertEquals('Connection failed', $result['message']);
    }

    /** @test */
    public function it_checks_availability_correctly()
    {
        config([
            'payment_gateways.stripe.secret_key' => 'sk_test_123456',
            'payment_gateways.stripe.webhook_secret' => 'whsec_test_123456',
        ]);

        $this->assertTrue($this->gateway->isAvailable());
    }

    /** @test */
    public function it_returns_gateway_name_and_version()
    {
        $this->assertEquals('Stripe', $this->gateway->getName());
        $this->assertEquals('2023-10-16', $this->gateway->getVersion());
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
