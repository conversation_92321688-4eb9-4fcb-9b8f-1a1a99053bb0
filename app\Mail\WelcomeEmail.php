<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class WelcomeEmail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'مرحباً بك في موني ترانسفر - Welcome to Mony Transfer',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.welcome',
            with: [
                'user' => $this->user,
                'userName' => $this->user->full_name,
                'verificationUrl' => $this->getVerificationUrl(),
                'supportEmail' => config('mail.support_email', '<EMAIL>'),
                'supportPhone' => config('app.support_phone', '+966-11-1234567'),
                'appUrl' => config('app.url'),
                'features' => $this->getKeyFeatures(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get email verification URL.
     */
    private function getVerificationUrl(): string
    {
        return url('/email/verify/' . $this->user->id . '/' . sha1($this->user->email));
    }

    /**
     * Get key features to highlight in welcome email.
     */
    private function getKeyFeatures(): array
    {
        return [
            [
                'title_ar' => 'تحويلات سريعة وآمنة',
                'title_en' => 'Fast & Secure Transfers',
                'description_ar' => 'حول أموالك بسرعة وأمان إلى أي مكان في العالم',
                'description_en' => 'Transfer money quickly and securely anywhere in the world',
                'icon' => '🚀'
            ],
            [
                'title_ar' => 'دعم العملات الرقمية',
                'title_en' => 'Cryptocurrency Support',
                'description_ar' => 'تداول وحول العملات الرقمية مثل البيتكوين والإيثيريوم',
                'description_en' => 'Trade and transfer cryptocurrencies like Bitcoin and Ethereum',
                'icon' => '₿'
            ],
            [
                'title_ar' => 'أسعار صرف تنافسية',
                'title_en' => 'Competitive Exchange Rates',
                'description_ar' => 'احصل على أفضل أسعار الصرف في السوق',
                'description_en' => 'Get the best exchange rates in the market',
                'icon' => '💱'
            ],
            [
                'title_ar' => 'أمان متقدم',
                'title_en' => 'Advanced Security',
                'description_ar' => 'حماية متعددة الطبقات وكشف الاحتيال بالذكاء الاصطناعي',
                'description_en' => 'Multi-layer protection and AI-powered fraud detection',
                'icon' => '🛡️'
            ],
            [
                'title_ar' => 'دعم عملاء 24/7',
                'title_en' => '24/7 Customer Support',
                'description_ar' => 'فريق دعم متاح على مدار الساعة لمساعدتك',
                'description_en' => 'Support team available around the clock to help you',
                'icon' => '🎧'
            ],
            [
                'title_ar' => 'تطبيق محمول',
                'title_en' => 'Mobile App',
                'description_ar' => 'أدر حساباتك ومعاملاتك من هاتفك المحمول',
                'description_en' => 'Manage your accounts and transactions from your mobile phone',
                'icon' => '📱'
            ]
        ];
    }
}
