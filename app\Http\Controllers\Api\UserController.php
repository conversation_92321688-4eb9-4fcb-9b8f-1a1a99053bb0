<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\KYCService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    protected KYCService $kycService;
    protected NotificationService $notificationService;

    public function __construct(KYCService $kycService, NotificationService $notificationService)
    {
        $this->kycService = $kycService;
        $this->notificationService = $notificationService;
    }

    /**
     * Get user profile.
     */
    public function profile(Request $request): JsonResponse
    {
        $user = $request->user()->load(['country']);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'phone' => $user->phone,
                'user_type' => $user->user_type,
                'status' => $user->status,
                'country' => $user->country ? [
                    'id' => $user->country->id,
                    'name_ar' => $user->country->name_ar,
                    'name_en' => $user->country->name_en,
                    'code' => $user->country->code,
                ] : null,
                'preferred_language' => $user->preferred_language,
                'preferred_currency' => $user->preferred_currency,
                'email_verified_at' => $user->email_verified_at,
                'phone_verified_at' => $user->phone_verified_at,
                'created_at' => $user->created_at,
            ],
        ]);
    }

    /**
     * Update user profile.
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'phone' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('users')->ignore($user->id),
            ],
            'date_of_birth' => 'sometimes|date|before:today',
            'gender' => 'sometimes|in:male,female',
            'address' => 'sometimes|string|max:500',
            'city' => 'sometimes|string|max:255',
            'postal_code' => 'sometimes|string|max:20',
            'preferred_language' => 'sometimes|in:ar,en',
            'preferred_currency' => 'sometimes|string|size:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث الملف الشخصي بنجاح',
            'data' => $this->formatUserData($user->fresh()),
        ]);
    }

    /**
     * Change password.
     */
    public function changePassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'كلمة المرور الحالية غير صحيحة',
            ], 422);
        }

        $user->update([
            'password' => Hash::make($request->new_password),
        ]);

        // Send security notification
        $this->notificationService->sendSecurityAlert($user, 'password_changed', [
            'changed_at' => now()->toISOString(),
            'ip_address' => $request->ip(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تغيير كلمة المرور بنجاح',
        ]);
    }

    /**
     * Upload avatar.
     */
    public function uploadAvatar(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'ملف الصورة غير صحيح',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $file = $request->file('avatar');

        // Store avatar
        $path = $file->store('avatars', 'public');

        // Delete old avatar if exists
        if ($user->avatar) {
            \Storage::disk('public')->delete($user->avatar);
        }

        $user->update(['avatar' => $path]);

        return response()->json([
            'success' => true,
            'message' => 'تم رفع الصورة الشخصية بنجاح',
            'data' => [
                'avatar_url' => asset('storage/' . $path),
            ],
        ]);
    }

    /**
     * Get user notifications.
     */
    public function notifications(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $notifications = $user->notifications()
                             ->orderBy('created_at', 'desc')
                             ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $notifications,
            'unread_count' => $this->notificationService->getUnreadCount($user->id),
        ]);
    }

    /**
     * Mark notification as read.
     */
    public function markNotificationRead(Request $request, int $notificationId): JsonResponse
    {
        $user = $request->user();
        
        $success = $this->notificationService->markAsRead($notificationId, $user->id);

        if (!$success) {
            return response()->json([
                'success' => false,
                'message' => 'الإشعار غير موجود',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد الإشعار كمقروء',
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllNotificationsRead(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $this->notificationService->markAllAsRead($user->id);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد جميع الإشعارات كمقروءة',
        ]);
    }

    /**
     * Get user activity log.
     */
    public function activityLog(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $activities = $user->auditLogs()
                          ->orderBy('created_at', 'desc')
                          ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $activities,
        ]);
    }

    /**
     * Delete user account.
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
            'confirmation' => 'required|string|in:DELETE_MY_ACCOUNT',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات التأكيد غير صحيحة',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'كلمة المرور غير صحيحة',
            ], 422);
        }

        // Check for pending transactions
        $pendingTransactions = $user->sentTransactions()
                                  ->whereIn('status', ['pending', 'processing'])
                                  ->count();

        if ($pendingTransactions > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الحساب. يوجد معاملات معلقة',
            ], 422);
        }

        // Soft delete user
        $user->update([
            'status' => 'deleted',
            'email' => $user->email . '_deleted_' . time(),
        ]);

        // Revoke all tokens
        $user->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الحساب بنجاح',
        ]);
    }

    /**
     * Format user data for response.
     */
    protected function formatUserData(User $user): array
    {
        return [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'user_type' => $user->user_type,
            'status' => $user->status,
            'date_of_birth' => $user->date_of_birth,
            'gender' => $user->gender,
            'address' => $user->address,
            'city' => $user->city,
            'postal_code' => $user->postal_code,
            'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null,
            'preferred_language' => $user->preferred_language,
            'preferred_currency' => $user->preferred_currency,
            'two_factor_enabled' => $user->two_factor_enabled,
            'biometric_enabled' => $user->biometric_enabled,
            'daily_limit' => $user->daily_limit,
            'monthly_limit' => $user->monthly_limit,
            'risk_level' => $user->risk_level,
            'kyc_verified_at' => $user->kyc_verified_at?->toISOString(),
            'aml_verified' => $user->aml_verified,
            'last_login_at' => $user->last_login_at?->toISOString(),
            'created_at' => $user->created_at->toISOString(),
            'country' => $user->country ? [
                'id' => $user->country->id,
                'name_ar' => $user->country->name_ar,
                'name_en' => $user->country->name_en,
                'code' => $user->country->code,
                'flag_url' => $user->country->flag_url,
            ] : null,
            'branch' => $user->branch ? [
                'id' => $user->branch->id,
                'name_ar' => $user->branch->name_ar,
                'name_en' => $user->branch->name_en,
                'code' => $user->branch->code,
            ] : null,
        ];
    }

    /**
     * Get user statistics.
     */
    protected function getUserStatistics(User $user): array
    {
        return [
            'total_sent_transactions' => $user->sentTransactions()->count(),
            'total_received_transactions' => $user->receivedTransactions()->count(),
            'total_sent_amount' => $user->sentTransactions()->where('status', 'completed')->sum('amount'),
            'total_received_amount' => $user->receivedTransactions()->where('status', 'completed')->sum('amount'),
            'pending_transactions' => $user->sentTransactions()->whereIn('status', ['pending', 'processing'])->count(),
            'failed_transactions' => $user->sentTransactions()->where('status', 'failed')->count(),
            'wallets_count' => $user->wallets()->count(),
            'documents_count' => $user->documents()->count(),
            'verified_documents' => $user->documents()->where('verification_status', 'verified')->count(),
        ];
    }
}
