<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Transaction;
use App\Models\Wallet;
use App\Models\PaymentGateway;
use App\Models\ExchangeRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FactoryTest extends TestCase
{
    use RefreshDatabase;

    public function test_country_factory_works()
    {
        $country = Country::factory()->create();
        
        $this->assertDatabaseHas('countries', [
            'id' => $country->id,
            'is_active' => $country->is_active,
        ]);
    }

    public function test_currency_factory_works()
    {
        $currency = Currency::factory()->create();
        
        $this->assertDatabaseHas('currencies', [
            'id' => $currency->id,
            'code' => $currency->code,
        ]);
    }

    public function test_user_factory_works()
    {
        $user = User::factory()->create();
        
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'email' => $user->email,
        ]);
    }

    public function test_transaction_factory_works()
    {
        $transaction = Transaction::factory()->create();
        
        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'transaction_id' => $transaction->transaction_id,
        ]);
    }

    public function test_wallet_factory_works()
    {
        $wallet = Wallet::factory()->create();
        
        $this->assertDatabaseHas('wallets', [
            'id' => $wallet->id,
            'wallet_number' => $wallet->wallet_number,
        ]);
    }

    public function test_payment_gateway_factory_works()
    {
        $gateway = PaymentGateway::factory()->create();
        
        $this->assertDatabaseHas('payment_gateways', [
            'id' => $gateway->id,
            'code' => $gateway->code,
        ]);
    }

    public function test_exchange_rate_factory_works()
    {
        $rate = ExchangeRate::factory()->create();
        
        $this->assertDatabaseHas('exchange_rates', [
            'id' => $rate->id,
            'rate' => $rate->rate,
        ]);
    }

    public function test_user_factory_with_states()
    {
        $admin = User::factory()->admin()->create();
        $this->assertEquals('admin', $admin->user_type);
        $this->assertEquals('active', $admin->status);

        $agent = User::factory()->agent()->create();
        $this->assertEquals('agent', $agent->user_type);

        $customer = User::factory()->customer()->create();
        $this->assertEquals('customer', $customer->user_type);

        $verified = User::factory()->verified()->create();
        $this->assertNotNull($verified->email_verified_at);
        $this->assertNotNull($verified->phone_verified_at);
    }

    public function test_country_factory_with_states()
    {
        $activeCountry = Country::factory()->active()->create();
        $this->assertTrue($activeCountry->is_active);

        $transferCountry = Country::factory()->supportsTransfers()->create();
        $this->assertTrue($transferCountry->supports_transfers);

        $cryptoCountry = Country::factory()->supportsCrypto()->create();
        $this->assertTrue($cryptoCountry->supports_crypto);
    }

    public function test_currency_factory_with_states()
    {
        $activeCurrency = Currency::factory()->active()->create();
        $this->assertTrue($activeCurrency->is_active);

        $baseCurrency = Currency::factory()->base()->create();
        $this->assertTrue($baseCurrency->is_base_currency);
        $this->assertEquals(1.0, $baseCurrency->rate_to_usd);

        $cryptoCurrency = Currency::factory()->crypto()->create();
        $this->assertTrue($cryptoCurrency->is_crypto);
        $this->assertNotNull($cryptoCurrency->crypto_network);
    }

    public function test_transaction_factory_with_states()
    {
        $pendingTransaction = Transaction::factory()->pending()->create();
        $this->assertEquals('pending', $pendingTransaction->status);
        $this->assertNull($pendingTransaction->processed_at);

        $completedTransaction = Transaction::factory()->completed()->create();
        $this->assertEquals('completed', $completedTransaction->status);
        $this->assertNotNull($completedTransaction->completed_at);

        $cancelledTransaction = Transaction::factory()->cancelled()->create();
        $this->assertEquals('cancelled', $cancelledTransaction->status);
        $this->assertNotNull($cancelledTransaction->cancelled_at);

        $highRiskTransaction = Transaction::factory()->highRisk()->create();
        $this->assertEquals('high', $highRiskTransaction->risk_level);

        $urgentTransaction = Transaction::factory()->urgent()->create();
        $this->assertEquals('urgent', $urgentTransaction->priority);
    }

    public function test_wallet_factory_with_states()
    {
        $activeWallet = Wallet::factory()->active()->create();
        $this->assertEquals('active', $activeWallet->status);

        $primaryWallet = Wallet::factory()->primary()->create();
        $this->assertTrue($primaryWallet->is_primary);

        $frozenWallet = Wallet::factory()->frozen()->create();
        $this->assertEquals('frozen', $frozenWallet->status);
        $this->assertEquals(0, $frozenWallet->available_balance);

        $highBalanceWallet = Wallet::factory()->highBalance()->create();
        $this->assertGreaterThanOrEqual(50000, $highBalanceWallet->balance);

        $emptyWallet = Wallet::factory()->empty()->create();
        $this->assertEquals(0, $emptyWallet->balance);
    }

    public function test_payment_gateway_factory_with_states()
    {
        $activeGateway = PaymentGateway::factory()->active()->create();
        $this->assertTrue($activeGateway->is_active);

        $sandboxGateway = PaymentGateway::factory()->sandbox()->create();
        $this->assertTrue($sandboxGateway->is_sandbox);

        $productionGateway = PaymentGateway::factory()->production()->create();
        $this->assertFalse($productionGateway->is_sandbox);

        $highAmountGateway = PaymentGateway::factory()->highAmount()->create();
        $this->assertGreaterThanOrEqual(500000, $highAmountGateway->max_amount);
    }

    public function test_exchange_rate_factory_with_states()
    {
        $activeRate = ExchangeRate::factory()->active()->create();
        $this->assertTrue($activeRate->is_active);

        $manualRate = ExchangeRate::factory()->manual()->create();
        $this->assertEquals('manual', $manualRate->source);

        $apiRate = ExchangeRate::factory()->api()->create();
        $this->assertEquals('api', $apiRate->source);

        $expiredRate = ExchangeRate::factory()->expired()->create();
        $this->assertFalse($expiredRate->is_active);
    }

    public function test_factories_work_together()
    {
        // Create related models
        $country = Country::factory()->active()->create();
        $currency = Currency::factory()->active()->create();
        
        $user = User::factory()->create([
            'country_id' => $country->id,
            'preferred_currency' => $currency->code,
        ]);

        $wallet = Wallet::factory()->create([
            'user_id' => $user->id,
            'currency_id' => $currency->id,
        ]);

        $transaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'sender_country_id' => $country->id,
            'recipient_country_id' => $country->id,
        ]);

        // Verify relationships
        $this->assertEquals($country->id, $user->country_id);
        $this->assertEquals($currency->code, $user->preferred_currency);
        $this->assertEquals($user->id, $wallet->user_id);
        $this->assertEquals($currency->id, $wallet->currency_id);
        $this->assertEquals($user->id, $transaction->user_id);
    }
}
