@extends('layouts.app')

@section('content')
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="h3 mb-0">تحويل جديد</h1>
            <p class="text-muted mb-0">إرسال أموال إلى أي مكان في العالم بسرعة وأمان</p>
        </div>
        <div class="col-auto">
            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-1"></i>العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <form id="transferForm" method="POST" action="{{ route('transactions.store') }}">
            @csrf
            
            <!-- Step 1: Transfer Details -->
            <div class="card mb-4" id="step1">
                <div class="card-header">
                    <h5 class="mb-0">
                        <span class="badge bg-primary me-2">1</span>
                        تفاصيل التحويل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="currency_from" class="form-label">العملة المرسلة</label>
                            <select class="form-select" id="currency_from" name="currency_from" required>
                                <option value="">اختر العملة</option>
                                <option value="SAR" selected>ريال سعودي (SAR)</option>
                                <option value="USD">دولار أمريكي (USD)</option>
                                <option value="EUR">يورو (EUR)</option>
                                <option value="GBP">جنيه إسترليني (GBP)</option>
                                <option value="AED">درهم إماراتي (AED)</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="currency_to" class="form-label">العملة المستلمة</label>
                            <select class="form-select" id="currency_to" name="currency_to" required>
                                <option value="">اختر العملة</option>
                                <option value="USD" selected>دولار أمريكي (USD)</option>
                                <option value="EUR">يورو (EUR)</option>
                                <option value="GBP">جنيه إسترليني (GBP)</option>
                                <option value="SAR">ريال سعودي (SAR)</option>
                                <option value="AED">درهم إماراتي (AED)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">المبلغ المرسل</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       min="1" max="100000" step="0.01" required>
                                <span class="input-group-text" id="fromCurrencySymbol">SAR</span>
                            </div>
                            <div class="form-text">الحد الأدنى: 1، الحد الأقصى: 100,000</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="recipient_amount" class="form-label">المبلغ المستلم</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="recipient_amount" readonly>
                                <span class="input-group-text" id="toCurrencySymbol">USD</span>
                            </div>
                            <div class="form-text">سعر الصرف: <span id="exchangeRate">-</span></div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="purpose" class="form-label">الغرض من التحويل</label>
                            <select class="form-select" id="purpose" name="purpose" required>
                                <option value="">اختر الغرض</option>
                                <option value="family_support">دعم الأسرة</option>
                                <option value="business">أعمال تجارية</option>
                                <option value="education">تعليم</option>
                                <option value="medical">طبي</option>
                                <option value="investment">استثمار</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="delivery_method" class="form-label">طريقة الاستلام</label>
                            <select class="form-select" id="delivery_method" name="delivery_method" required>
                                <option value="">اختر طريقة الاستلام</option>
                                <option value="cash_pickup">استلام نقدي</option>
                                <option value="bank_deposit">إيداع بنكي</option>
                                <option value="mobile_wallet">محفظة موبايل</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                            التالي <i class="bi bi-arrow-left ms-1"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Step 2: Recipient Information -->
            <div class="card mb-4 d-none" id="step2">
                <div class="card-header">
                    <h5 class="mb-0">
                        <span class="badge bg-primary me-2">2</span>
                        معلومات المستفيد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="recipient_name" class="form-label">اسم المستفيد</label>
                            <input type="text" class="form-control" id="recipient_name" name="recipient_name" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="recipient_phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="recipient_phone" name="recipient_phone" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="recipient_country_id" class="form-label">البلد</label>
                            <select class="form-select" id="recipient_country_id" name="recipient_country_id" required>
                                <option value="">اختر البلد</option>
                                @foreach($countries ?? [] as $country)
                                <option value="{{ $country->id }}">{{ $country->name_ar }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="recipient_email" class="form-label">البريد الإلكتروني (اختياري)</label>
                            <input type="email" class="form-control" id="recipient_email" name="recipient_email">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="recipient_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="recipient_address" name="recipient_address" rows="3" required></textarea>
                    </div>
                    
                    <!-- Bank Details (shown when bank_deposit is selected) -->
                    <div id="bankDetails" class="d-none">
                        <h6 class="mb-3">تفاصيل البنك</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="recipient_bank_name" class="form-label">اسم البنك</label>
                                <input type="text" class="form-control" id="recipient_bank_name" name="recipient_bank_name">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="recipient_bank_account" class="form-label">رقم الحساب</label>
                                <input type="text" class="form-control" id="recipient_bank_account" name="recipient_bank_account">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="recipient_bank_code" class="form-label">رمز البنك/SWIFT</label>
                            <input type="text" class="form-control" id="recipient_bank_code" name="recipient_bank_code">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                            <i class="bi bi-arrow-right me-1"></i>السابق
                        </button>
                        <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                            التالي <i class="bi bi-arrow-left ms-1"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Step 3: Payment Method -->
            <div class="card mb-4 d-none" id="step3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <span class="badge bg-primary me-2">3</span>
                        طريقة الدفع
                    </h5>
                </div>
                <div class="card-body">
                    <div id="paymentMethods" class="row">
                        <!-- Payment methods will be loaded here -->
                    </div>
                    
                    <!-- Payment Form -->
                    <div id="paymentForm" class="d-none mt-4">
                        <!-- Payment form fields will be loaded here based on selected method -->
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                            <i class="bi bi-arrow-right me-1"></i>السابق
                        </button>
                        <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                            التالي <i class="bi bi-arrow-left ms-1"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Step 4: Review and Confirm -->
            <div class="card mb-4 d-none" id="step4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <span class="badge bg-primary me-2">4</span>
                        مراجعة وتأكيد
                    </h5>
                </div>
                <div class="card-body">
                    <div id="transferSummary">
                        <!-- Transfer summary will be displayed here -->
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                        <label class="form-check-label" for="agreeTerms">
                            أوافق على <a href="#" target="_blank">الشروط والأحكام</a> و <a href="#" target="_blank">سياسة الخصوصية</a>
                        </label>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="prevStep(3)">
                            <i class="bi bi-arrow-right me-1"></i>السابق
                        </button>
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="bi bi-check-lg me-1"></i>تأكيد التحويل
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Summary Sidebar -->
    <div class="col-lg-4">
        <div class="card sticky-top" style="top: 20px;">
            <div class="card-header">
                <h6 class="mb-0">ملخص التحويل</h6>
            </div>
            <div class="card-body">
                <div id="transferSummaryCard">
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-calculator fs-1 d-block mb-2"></i>
                        أدخل تفاصيل التحويل لعرض الملخص
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentStep = 1;
let exchangeRates = {};
let selectedPaymentMethod = null;

// Step navigation
function nextStep(step) {
    if (validateCurrentStep()) {
        $(`#step${currentStep}`).addClass('d-none');
        $(`#step${step}`).removeClass('d-none');
        currentStep = step;
        
        if (step === 3) {
            loadPaymentMethods();
        } else if (step === 4) {
            generateSummary();
        }
        
        updateSummaryCard();
    }
}

function prevStep(step) {
    $(`#step${currentStep}`).addClass('d-none');
    $(`#step${step}`).removeClass('d-none');
    currentStep = step;
}

// Validate current step
function validateCurrentStep() {
    const step = $(`#step${currentStep}`);
    const requiredFields = step.find('[required]');
    let isValid = true;
    
    requiredFields.each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    if (!isValid) {
        showToast('يرجى ملء جميع الحقول المطلوبة', 'error');
    }
    
    return isValid;
}

// Load exchange rates
function loadExchangeRates() {
    const fromCurrency = $('#currency_from').val();
    const toCurrency = $('#currency_to').val();
    
    if (fromCurrency && toCurrency && fromCurrency !== toCurrency) {
        $.get('/api/v1/exchange-rates/quote', {
            from: fromCurrency,
            to: toCurrency,
            amount: $('#amount').val() || 1
        }, function(data) {
            if (data.success) {
                exchangeRates[`${fromCurrency}_${toCurrency}`] = data.data;
                updateExchangeRate();
                calculateRecipientAmount();
            }
        });
    }
}

// Update exchange rate display
function updateExchangeRate() {
    const fromCurrency = $('#currency_from').val();
    const toCurrency = $('#currency_to').val();
    const rateKey = `${fromCurrency}_${toCurrency}`;
    
    if (exchangeRates[rateKey]) {
        const rate = exchangeRates[rateKey].rate;
        $('#exchangeRate').text(`1 ${fromCurrency} = ${rate} ${toCurrency}`);
    }
}

// Calculate recipient amount
function calculateRecipientAmount() {
    const amount = parseFloat($('#amount').val()) || 0;
    const fromCurrency = $('#currency_from').val();
    const toCurrency = $('#currency_to').val();
    const rateKey = `${fromCurrency}_${toCurrency}`;
    
    if (amount > 0 && exchangeRates[rateKey]) {
        const rate = exchangeRates[rateKey].rate;
        const recipientAmount = amount * rate;
        $('#recipient_amount').val(formatNumber(recipientAmount));
    } else {
        $('#recipient_amount').val('');
    }
    
    updateSummaryCard();
}

// Load payment methods
function loadPaymentMethods() {
    const countryCode = 'SA'; // Default to Saudi Arabia
    const currencyCode = $('#currency_from').val();
    const amount = $('#amount').val();
    
    $.get('/api/v1/payments/methods', {
        country_code: countryCode,
        currency_code: currencyCode,
        amount: amount
    }, function(data) {
        if (data.success) {
            displayPaymentMethods(data.data.payment_methods);
        }
    });
}

// Display payment methods
function displayPaymentMethods(methods) {
    const container = $('#paymentMethods');
    container.empty();
    
    methods.forEach(function(method) {
        const methodCard = `
            <div class="col-md-6 mb-3">
                <div class="card payment-method-card" data-method="${method.type}" onclick="selectPaymentMethod('${method.type}')">
                    <div class="card-body text-center">
                        <i class="bi bi-credit-card fs-1 text-primary mb-2"></i>
                        <h6>${method.name}</h6>
                        <small class="text-muted">رسوم: ${method.fee_percentage}% + ${method.fee_fixed}</small>
                    </div>
                </div>
            </div>
        `;
        container.append(methodCard);
    });
}

// Select payment method
function selectPaymentMethod(method) {
    $('.payment-method-card').removeClass('border-primary');
    $(`.payment-method-card[data-method="${method}"]`).addClass('border-primary');
    selectedPaymentMethod = method;
    
    loadPaymentForm(method);
}

// Load payment form
function loadPaymentForm(method) {
    const formContainer = $('#paymentForm');
    let formHtml = '';
    
    switch(method) {
        case 'stripe':
        case 'visa':
        case 'mastercard':
            formHtml = `
                <h6 class="mb-3">تفاصيل البطاقة</h6>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم البطاقة</label>
                        <input type="text" class="form-control" name="card_number" placeholder="1234 5678 9012 3456" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم حامل البطاقة</label>
                        <input type="text" class="form-control" name="cardholder_name" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">شهر الانتهاء</label>
                        <select class="form-select" name="expiry_month" required>
                            <option value="">الشهر</option>
                            ${Array.from({length: 12}, (_, i) => `<option value="${String(i+1).padStart(2, '0')}">${String(i+1).padStart(2, '0')}</option>`).join('')}
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">سنة الانتهاء</label>
                        <select class="form-select" name="expiry_year" required>
                            <option value="">السنة</option>
                            ${Array.from({length: 10}, (_, i) => {
                                const year = new Date().getFullYear() + i;
                                return `<option value="${year}">${year}</option>`;
                            }).join('')}
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">CVV</label>
                        <input type="text" class="form-control" name="cvv" placeholder="123" maxlength="4" required>
                    </div>
                </div>
            `;
            break;
            
        case 'paypal':
            formHtml = `
                <h6 class="mb-3">PayPal</h6>
                <div class="mb-3">
                    <label class="form-label">البريد الإلكتروني لـ PayPal</label>
                    <input type="email" class="form-control" name="paypal_email" required>
                </div>
            `;
            break;
            
        case 'bank_transfer':
            formHtml = `
                <h6 class="mb-3">التحويل البنكي</h6>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    سيتم توجيهك لإكمال التحويل البنكي بعد تأكيد الطلب
                </div>
            `;
            break;
    }
    
    formContainer.html(formHtml).removeClass('d-none');
}

// Generate summary
function generateSummary() {
    const summary = {
        amount: $('#amount').val(),
        currency_from: $('#currency_from').val(),
        currency_to: $('#currency_to').val(),
        recipient_amount: $('#recipient_amount').val(),
        recipient_name: $('#recipient_name').val(),
        purpose: $('#purpose option:selected').text(),
        delivery_method: $('#delivery_method option:selected').text(),
        payment_method: selectedPaymentMethod
    };
    
    const summaryHtml = `
        <div class="row mb-3">
            <div class="col-6"><strong>المبلغ المرسل:</strong></div>
            <div class="col-6">${formatNumber(summary.amount)} ${summary.currency_from}</div>
        </div>
        <div class="row mb-3">
            <div class="col-6"><strong>المبلغ المستلم:</strong></div>
            <div class="col-6">${summary.recipient_amount} ${summary.currency_to}</div>
        </div>
        <div class="row mb-3">
            <div class="col-6"><strong>المستفيد:</strong></div>
            <div class="col-6">${summary.recipient_name}</div>
        </div>
        <div class="row mb-3">
            <div class="col-6"><strong>الغرض:</strong></div>
            <div class="col-6">${summary.purpose}</div>
        </div>
        <div class="row mb-3">
            <div class="col-6"><strong>طريقة الاستلام:</strong></div>
            <div class="col-6">${summary.delivery_method}</div>
        </div>
        <div class="row mb-3">
            <div class="col-6"><strong>طريقة الدفع:</strong></div>
            <div class="col-6">${summary.payment_method}</div>
        </div>
    `;
    
    $('#transferSummary').html(summaryHtml);
}

// Update summary card
function updateSummaryCard() {
    const amount = $('#amount').val();
    const currencyFrom = $('#currency_from').val();
    const recipientAmount = $('#recipient_amount').val();
    const currencyTo = $('#currency_to').val();
    
    if (amount && currencyFrom) {
        const summaryHtml = `
            <div class="text-center">
                <h4 class="text-primary">${formatNumber(amount)} ${currencyFrom}</h4>
                ${recipientAmount ? `<p class="text-muted">≈ ${recipientAmount} ${currencyTo}</p>` : ''}
                <hr>
                <div class="d-flex justify-content-between">
                    <span>المبلغ:</span>
                    <span>${formatNumber(amount)} ${currencyFrom}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>الرسوم:</span>
                    <span>-</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between fw-bold">
                    <span>الإجمالي:</span>
                    <span>${formatNumber(amount)} ${currencyFrom}</span>
                </div>
            </div>
        `;
        $('#transferSummaryCard').html(summaryHtml);
    }
}

// Event listeners
$(document).ready(function() {
    // Currency change events
    $('#currency_from, #currency_to').change(function() {
        $('#fromCurrencySymbol').text($('#currency_from').val());
        $('#toCurrencySymbol').text($('#currency_to').val());
        loadExchangeRates();
    });
    
    // Amount change event
    $('#amount').on('input', function() {
        calculateRecipientAmount();
    });
    
    // Delivery method change
    $('#delivery_method').change(function() {
        if ($(this).val() === 'bank_deposit') {
            $('#bankDetails').removeClass('d-none');
            $('#bankDetails input').attr('required', true);
        } else {
            $('#bankDetails').addClass('d-none');
            $('#bankDetails input').removeAttr('required');
        }
    });
    
    // Form submission
    $('#transferForm').submit(function(e) {
        e.preventDefault();
        
        if (!validateCurrentStep() || !$('#agreeTerms').is(':checked')) {
            showToast('يرجى مراجعة جميع البيانات والموافقة على الشروط', 'error');
            return;
        }
        
        // Submit form
        showLoading();
        
        const formData = new FormData(this);
        
        // Add payment data
        if (selectedPaymentMethod) {
            const paymentData = {};
            $('#paymentForm input, #paymentForm select').each(function() {
                if ($(this).attr('name')) {
                    paymentData[$(this).attr('name')] = $(this).val();
                }
            });
            formData.append('payment_data', JSON.stringify(paymentData));
        }
        
        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data.success) {
                    window.location.href = `/transactions/${data.data.transaction.transaction_id}`;
                } else {
                    showToast(data.message || 'حدث خطأ أثناء إنشاء التحويل', 'error');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showToast(response?.message || 'حدث خطأ أثناء إنشاء التحويل', 'error');
            },
            complete: function() {
                hideLoading();
            }
        });
    });
    
    // Initialize
    loadExchangeRates();
});
</script>
@endpush
