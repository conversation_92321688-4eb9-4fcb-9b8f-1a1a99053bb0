<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;

class CacheServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush(); // Clear cache before each test
    }

    /**
     * Test caching and retrieving exchange rates
     */
    public function test_cache_and_get_exchange_rates(): void
    {
        $rates = [
            'SAR_USD' => 0.2667,
            'USD_SAR' => 3.7500,
            'SAR_EUR' => 0.2400,
        ];

        // Cache the rates
        CacheService::cacheExchangeRates($rates);

        // Retrieve the rates
        $cachedRates = CacheService::getCachedExchangeRates();

        $this->assertEquals($rates, $cachedRates);
        $this->assertTrue(Cache::has('exchange_rates'));
        $this->assertTrue(Cache::has('exchange_rates_updated_at'));
    }

    /**
     * Test caching and retrieving user dashboard data
     */
    public function test_cache_and_get_user_dashboard(): void
    {
        $userId = 1;
        $dashboardData = [
            'total_transactions' => 10,
            'completed_transactions' => 8,
            'pending_transactions' => 2,
            'total_amount' => 5000.00,
        ];

        // Cache the dashboard data
        CacheService::cacheUserDashboard($userId, $dashboardData);

        // Retrieve the dashboard data
        $cachedData = CacheService::getCachedUserDashboard($userId);

        $this->assertEquals($dashboardData, $cachedData);
        $this->assertTrue(Cache::has("user_dashboard_{$userId}"));
    }

    /**
     * Test caching and retrieving transaction statistics
     */
    public function test_cache_and_get_transaction_stats(): void
    {
        $stats = [
            'daily_transactions' => 50,
            'weekly_transactions' => 300,
            'monthly_transactions' => 1200,
            'success_rate' => 95.5,
        ];

        // Cache the stats
        CacheService::cacheTransactionStats($stats);

        // Retrieve the stats
        $cachedStats = CacheService::getCachedTransactionStats();

        $this->assertEquals($stats, $cachedStats);
        $this->assertTrue(Cache::has('transaction_stats'));
        $this->assertTrue(Cache::has('transaction_stats_updated_at'));
    }

    /**
     * Test caching and retrieving system settings
     */
    public function test_cache_and_get_system_settings(): void
    {
        $settings = [
            'max_transaction_amount' => 50000,
            'min_transaction_amount' => 10,
            'default_fee_rate' => 2.5,
            'maintenance_mode' => false,
        ];

        // Cache the settings
        CacheService::cacheSystemSettings($settings);

        // Retrieve the settings
        $cachedSettings = CacheService::getCachedSystemSettings();

        $this->assertEquals($settings, $cachedSettings);
        $this->assertTrue(Cache::has('system_settings'));
    }

    /**
     * Test caching countries and currencies
     */
    public function test_cache_countries_and_currencies(): void
    {
        $countries = [
            ['code' => 'SAU', 'name' => 'Saudi Arabia'],
            ['code' => 'USA', 'name' => 'United States'],
        ];

        $currencies = [
            ['code' => 'SAR', 'name' => 'Saudi Riyal'],
            ['code' => 'USD', 'name' => 'US Dollar'],
        ];

        // Cache countries and currencies
        CacheService::cacheCountriesAndCurrencies($countries, $currencies);

        // Retrieve cached data
        $cachedCountries = CacheService::getCachedCountries();
        $cachedCurrencies = CacheService::getCachedCurrencies();

        $this->assertEquals($countries, $cachedCountries);
        $this->assertEquals($currencies, $cachedCurrencies);
        $this->assertTrue(Cache::has('countries'));
        $this->assertTrue(Cache::has('currencies'));
    }

    /**
     * Test clearing all cache
     */
    public function test_clear_all_cache(): void
    {
        // Add some cache data
        Cache::put('test_key_1', 'test_value_1', 60);
        Cache::put('test_key_2', 'test_value_2', 60);

        $this->assertTrue(Cache::has('test_key_1'));
        $this->assertTrue(Cache::has('test_key_2'));

        // Clear all cache
        CacheService::clearAllCache();

        $this->assertFalse(Cache::has('test_key_1'));
        $this->assertFalse(Cache::has('test_key_2'));
    }

    /**
     * Test getting cache statistics
     */
    public function test_get_cache_stats(): void
    {
        // Add some cache data
        CacheService::cacheExchangeRates(['SAR_USD' => 0.2667]);
        CacheService::cacheTransactionStats(['total' => 100]);

        $stats = CacheService::getCacheStats();

        $this->assertIsArray($stats);
        $this->assertTrue($stats['exchange_rates_cached']);
        $this->assertTrue($stats['transaction_stats_cached']);
        $this->assertNotNull($stats['exchange_rates_updated_at']);
        $this->assertNotNull($stats['transaction_stats_updated_at']);
    }

    /**
     * Test cache with null data
     */
    public function test_cache_with_null_data(): void
    {
        $cachedRates = CacheService::getCachedExchangeRates();
        $this->assertNull($cachedRates);

        $cachedDashboard = CacheService::getCachedUserDashboard(999);
        $this->assertNull($cachedDashboard);
    }

    /**
     * Test cache duration constants
     */
    public function test_cache_duration_constants(): void
    {
        $this->assertEquals(300, CacheService::CACHE_DURATION_SHORT);
        $this->assertEquals(1800, CacheService::CACHE_DURATION_MEDIUM);
        $this->assertEquals(3600, CacheService::CACHE_DURATION_LONG);
        $this->assertEquals(86400, CacheService::CACHE_DURATION_DAILY);
    }
}
