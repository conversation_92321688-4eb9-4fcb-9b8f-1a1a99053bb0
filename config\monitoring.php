<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Performance Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for system performance monitoring and alerting
    |
    */

    'enabled' => env('MONITORING_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Metrics Collection
    |--------------------------------------------------------------------------
    */

    'metrics' => [
        'collection_interval' => 60, // seconds
        'retention_days' => 30,
        'batch_size' => 1000,

        'system' => [
            'cpu_usage' => true,
            'memory_usage' => true,
            'disk_usage' => true,
            'network_io' => true,
            'load_average' => true,
        ],

        'application' => [
            'response_time' => true,
            'request_count' => true,
            'error_rate' => true,
            'queue_size' => true,
            'cache_hit_ratio' => true,
        ],

        'database' => [
            'query_time' => true,
            'connection_count' => true,
            'slow_queries' => true,
            'deadlocks' => true,
        ],

        'financial' => [
            'transaction_volume' => true,
            'transaction_count' => true,
            'fraud_detection_rate' => true,
            'payment_gateway_response_time' => true,
            'exchange_rate_updates' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Thresholds
    |--------------------------------------------------------------------------
    */

    'thresholds' => [
        'response_time' => [
            'warning' => 1000, // milliseconds
            'critical' => 3000,
        ],

        'cpu_usage' => [
            'warning' => 70, // percentage
            'critical' => 90,
        ],

        'memory_usage' => [
            'warning' => 80, // percentage
            'critical' => 95,
        ],

        'disk_usage' => [
            'warning' => 80, // percentage
            'critical' => 95,
        ],

        'error_rate' => [
            'warning' => 5, // percentage
            'critical' => 10,
        ],

        'queue_size' => [
            'warning' => 1000, // jobs
            'critical' => 5000,
        ],

        'database_connections' => [
            'warning' => 80, // percentage of max
            'critical' => 95,
        ],

        'fraud_detection_time' => [
            'warning' => 500, // milliseconds
            'critical' => 1000,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Health Checks
    |--------------------------------------------------------------------------
    */

    'health_checks' => [
        'enabled' => true,
        'interval' => 30, // seconds
        'timeout' => 10, // seconds

        'checks' => [
            'database' => [
                'enabled' => true,
                'query' => 'SELECT 1',
                'timeout' => 5,
            ],

            'redis' => [
                'enabled' => true,
                'command' => 'ping',
                'timeout' => 3,
            ],

            'queue' => [
                'enabled' => true,
                'max_failed_jobs' => 100,
                'max_queue_size' => 10000,
            ],

            'storage' => [
                'enabled' => true,
                'paths' => [
                    storage_path('logs'),
                    storage_path('app'),
                    storage_path('framework/cache'),
                ],
                'min_free_space' => 1024, // MB
            ],

            'external_apis' => [
                'enabled' => true,
                'endpoints' => [
                    'exchange_rates' => 'https://api.fixer.io/latest',
                    'fraud_detection' => env('AI_FRAUD_ENDPOINT'),
                ],
                'timeout' => 10,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Alerting Configuration
    |--------------------------------------------------------------------------
    */

    'alerts' => [
        'enabled' => true,
        'channels' => ['email', 'slack', 'sms'],
        'escalation_delay' => 300, // 5 minutes
        'max_alerts_per_hour' => 10,

        'recipients' => [
            'email' => [
                '<EMAIL>',
                '<EMAIL>',
            ],
            'slack' => [
                'webhook_url' => env('SLACK_WEBHOOK_URL'),
                'channel' => '#alerts',
            ],
            'sms' => [
                '+966501234567', // Operations team
            ],
        ],

        'rules' => [
            'critical_system_failure' => [
                'conditions' => [
                    'cpu_usage > 90',
                    'memory_usage > 95',
                    'error_rate > 10',
                ],
                'channels' => ['email', 'slack', 'sms'],
                'escalate_after' => 60, // seconds
            ],

            'high_transaction_volume' => [
                'conditions' => [
                    'transaction_count > 10000 per hour',
                    'transaction_volume > 1000000 per hour',
                ],
                'channels' => ['email', 'slack'],
                'escalate_after' => 300,
            ],

            'fraud_detection_issues' => [
                'conditions' => [
                    'fraud_detection_time > 1000',
                    'fraud_detection_errors > 5 per minute',
                ],
                'channels' => ['email', 'slack', 'sms'],
                'escalate_after' => 30,
            ],

            'payment_gateway_issues' => [
                'conditions' => [
                    'payment_gateway_response_time > 5000',
                    'payment_gateway_error_rate > 5',
                ],
                'channels' => ['email', 'slack'],
                'escalate_after' => 120,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */

    'logging' => [
        'enabled' => true,
        'level' => env('MONITORING_LOG_LEVEL', 'info'),
        'channel' => 'monitoring',
        'retention_days' => 90,

        'log_slow_requests' => true,
        'slow_request_threshold' => 2000, // milliseconds

        'log_large_responses' => true,
        'large_response_threshold' => 1048576, // 1MB

        'log_failed_jobs' => true,
        'log_queue_delays' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Profiling Configuration
    |--------------------------------------------------------------------------
    */

    'profiling' => [
        'enabled' => env('PROFILING_ENABLED', false),
        'sample_rate' => 0.1, // 10% of requests
        'memory_tracking' => true,
        'query_tracking' => true,
        'cache_tracking' => true,

        'exclude_paths' => [
            '/health',
            '/metrics',
            '/api/documentation',
        ],

        'storage' => [
            'driver' => 'file',
            'path' => storage_path('profiling'),
            'retention_hours' => 24,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Dashboard Configuration
    |--------------------------------------------------------------------------
    */

    'dashboard' => [
        'enabled' => true,
        'route' => '/monitoring',
        'middleware' => ['auth', 'admin'],
        'refresh_interval' => 30, // seconds

        'widgets' => [
            'system_overview' => true,
            'transaction_metrics' => true,
            'fraud_detection_stats' => true,
            'payment_gateway_status' => true,
            'queue_status' => true,
            'error_logs' => true,
        ],

        'charts' => [
            'response_time_trend' => true,
            'transaction_volume_trend' => true,
            'error_rate_trend' => true,
            'fraud_detection_trend' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Configuration
    |--------------------------------------------------------------------------
    */

    'integrations' => [
        'prometheus' => [
            'enabled' => env('PROMETHEUS_ENABLED', false),
            'endpoint' => '/metrics',
            'namespace' => 'mony_transfer',
        ],

        'grafana' => [
            'enabled' => env('GRAFANA_ENABLED', false),
            'url' => env('GRAFANA_URL'),
            'api_key' => env('GRAFANA_API_KEY'),
        ],

        'datadog' => [
            'enabled' => env('DATADOG_ENABLED', false),
            'api_key' => env('DATADOG_API_KEY'),
            'app_key' => env('DATADOG_APP_KEY'),
        ],

        'new_relic' => [
            'enabled' => env('NEW_RELIC_ENABLED', false),
            'license_key' => env('NEW_RELIC_LICENSE_KEY'),
            'app_name' => env('NEW_RELIC_APP_NAME', 'Mony Transfer'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode
    |--------------------------------------------------------------------------
    */

    'maintenance' => [
        'auto_enable' => [
            'enabled' => false,
            'conditions' => [
                'error_rate > 50',
                'response_time > 10000',
                'database_connections > 95',
            ],
            'duration' => 300, // 5 minutes
        ],

        'scheduled_maintenance' => [
            'enabled' => true,
            'schedule' => 'weekly sunday 02:00',
            'duration' => 3600, // 1 hour
            'notify_users' => true,
            'notification_advance' => 86400, // 24 hours
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Monitoring
    |--------------------------------------------------------------------------
    */

    'backup_monitoring' => [
        'enabled' => true,
        'check_interval' => 3600, // 1 hour
        'max_age_hours' => 24,
        'min_size_mb' => 10,

        'locations' => [
            'database' => storage_path('backups/database'),
            'files' => storage_path('backups/files'),
            'logs' => storage_path('backups/logs'),
        ],

        'alerts' => [
            'missing_backup' => true,
            'old_backup' => true,
            'small_backup' => true,
            'failed_backup' => true,
        ],
    ],

];
