<?php

namespace App\Contracts;

use App\Models\Transaction;

interface PaymentGatewayInterface
{
    /**
     * Process payment through the gateway.
     *
     * @param Transaction $transaction
     * @param array $paymentData
     * @return array
     */
    public function processPayment(Transaction $transaction, array $paymentData): array;

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float $amount
     * @param string $currency
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount, string $currency = 'USD'): array;

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array;

    /**
     * Validate webhook signature.
     *
     * @param array $headers
     * @param string $payload
     * @return bool
     */
    public function validateWebhook(array $headers, string $payload): bool;

    /**
     * Get supported currencies.
     *
     * @return array
     */
    public function getSupportedCurrencies(): array;

    /**
     * Get gateway configuration.
     *
     * @return array
     */
    public function getConfig(): array;
}
