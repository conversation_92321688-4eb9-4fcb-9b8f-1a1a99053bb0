<?php

namespace App\Contracts;

use App\Models\Transaction;

interface PaymentGatewayInterface
{
    /**
     * Process payment through the gateway.
     *
     * @param Transaction $transaction
     * @param array $paymentData
     * @return array
     */
    public function processPayment(Transaction $transaction, array $paymentData): array;

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float $amount
     * @param string $currency
     * @return array
     */
    public function refundPayment(string $transactionId, float $amount, string $currency = 'USD'): array;

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array;

    /**
     * Validate webhook signature.
     *
     * @param array $headers
     * @param string $payload
     * @return bool
     */
    public function validateWebhook(array $headers, string $payload): bool;

    /**
     * Get supported currencies.
     *
     * @return array
     */
    public function getSupportedCurrencies(): array;

    /**
     * Get gateway configuration.
     *
     * @return array
     */
    public function getConfig(): array;

    /**
     * Initialize payment
     */
    public function initializePayment(array $paymentData): array;

    /**
     * Verify payment
     */
    public function verifyPayment(string $paymentId): array;

    /**
     * Get supported countries
     */
    public function getSupportedCountries(): array;

    /**
     * Get payment methods
     */
    public function getPaymentMethods(): array;

    /**
     * Calculate fees
     */
    public function calculateFees(float $amount, string $currency, string $method): array;

    /**
     * Validate payment data
     */
    public function validatePaymentData(array $paymentData): array;

    /**
     * Test connection
     */
    public function testConnection(): array;

    /**
     * Handle webhook
     */
    public function handleWebhook(array $webhookData): array;

    /**
     * Get transaction limits
     */
    public function getTransactionLimits(): array;

    /**
     * Check if gateway is available
     */
    public function isAvailable(): bool;

    /**
     * Get gateway name
     */
    public function getName(): string;

    /**
     * Get gateway version
     */
    public function getVersion(): string;
}
