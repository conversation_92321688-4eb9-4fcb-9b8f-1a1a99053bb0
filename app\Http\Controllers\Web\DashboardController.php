<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show the dashboard (ultra-fast loading)
     */
    public function index()
    {
        // Return view immediately with no database queries
        return view('dashboard');
    }

    /**
     * Get dashboard data via AJAX (demo data)
     */
    public function data()
    {
        // Return demo stats for fast loading
        $stats = [
            'total_transactions' => 12,
            'completed_transactions' => 8,
            'pending_transactions' => 3,
            'total_amount' => 5750.00,
            'success_rate' => 66.7,
        ];

        return response()->json([
            'success' => true,
            'stats' => $stats,
        ]);
    }

    /**
     * Get recent transactions via AJAX (demo data)
     */
    public function transactions()
    {
        // Return demo transactions for fast loading
        $transactions = [
            [
                'transaction_id' => 'TXN001',
                'recipient_name' => 'أحمد محمد',
                'amount' => 500.00,
                'currency_from' => 'SAR',
                'status' => 'completed',
                'created_at' => now()->subDays(1)->format('Y-m-d H:i:s'),
            ],
            [
                'transaction_id' => 'TXN002',
                'recipient_name' => 'فاطمة علي',
                'amount' => 750.00,
                'currency_from' => 'SAR',
                'status' => 'completed',
                'created_at' => now()->subDays(2)->format('Y-m-d H:i:s'),
            ],
            [
                'transaction_id' => 'TXN003',
                'recipient_name' => 'محمد عبدالله',
                'amount' => 300.00,
                'currency_from' => 'SAR',
                'status' => 'pending',
                'created_at' => now()->subDays(3)->format('Y-m-d H:i:s'),
            ],
            [
                'transaction_id' => 'TXN004',
                'recipient_name' => 'سارة أحمد',
                'amount' => 1200.00,
                'currency_from' => 'SAR',
                'status' => 'completed',
                'created_at' => now()->subDays(4)->format('Y-m-d H:i:s'),
            ],
            [
                'transaction_id' => 'TXN005',
                'recipient_name' => 'علي حسن',
                'amount' => 450.00,
                'currency_from' => 'SAR',
                'status' => 'processing',
                'created_at' => now()->subDays(5)->format('Y-m-d H:i:s'),
            ],
        ];

        return response()->json([
            'success' => true,
            'transactions' => $transactions,
        ]);
    }

    /**
     * Get chart data via AJAX (demo data)
     */
    public function chartData()
    {
        // Return demo chart data for fast loading
        $chartData = [
            'completed' => 8,
            'pending' => 3,
            'failed' => 1,
        ];

        return response()->json([
            'success' => true,
            'chartData' => $chartData,
        ]);
    }

    /**
     * Get notifications via AJAX
     */
    public function notifications()
    {
        $user = Auth::user();

        $notifications = cache()->remember("notifications_{$user->id}", 600, function() use ($user) {
            return $this->getUserNotifications($user);
        });

        return response()->json([
            'success' => true,
            'notifications' => $notifications,
        ]);
    }

    /**
     * Show user profile
     */
    public function profile()
    {
        $user = Auth::user();
        $countries = Country::where('is_active', true)->orderBy('name_en')->get();
        
        return view('profile', compact('user', 'countries'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'country_id' => 'required|exists:countries,id',
            'address' => 'nullable|string|max:500',
            'date_of_birth' => 'nullable|date|before:today',
        ]);

        $user = Auth::user();
        $user->update($request->only([
            'first_name',
            'last_name',
            'phone',
            'country_id',
            'address',
            'date_of_birth',
        ]));

        return redirect()->route('profile')->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    /**
     * Show settings page
     */
    public function settings()
    {
        $user = Auth::user();
        
        return view('settings', compact('user'));
    }

    /**
     * Update user settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'language' => 'required|in:ar,en',
            'timezone' => 'required|string',
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'two_factor_enabled' => 'boolean',
        ]);

        $user = Auth::user();
        $user->update([
            'language' => $request->language,
            'timezone' => $request->timezone,
            'email_notifications' => $request->boolean('email_notifications'),
            'sms_notifications' => $request->boolean('sms_notifications'),
            'two_factor_enabled' => $request->boolean('two_factor_enabled'),
        ]);

        return redirect()->route('settings')->with('success', 'تم تحديث الإعدادات بنجاح');
    }

    /**
     * Get user statistics
     */
    protected function getUserStatistics(User $user): array
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;

        $totalTransactions = Transaction::where('user_id', $user->id)->count();
        
        $completedTransactions = Transaction::where('user_id', $user->id)
            ->where('status', 'completed')
            ->whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->count();
        
        $pendingTransactions = Transaction::where('user_id', $user->id)
            ->where('status', 'pending')
            ->count();
        
        $failedTransactions = Transaction::where('user_id', $user->id)
            ->whereIn('status', ['failed', 'cancelled'])
            ->whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->count();
        
        $totalAmount = Transaction::where('user_id', $user->id)
            ->where('status', 'completed')
            ->whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->sum('amount');
        
        $successRate = $totalTransactions > 0 
            ? round(($completedTransactions / $totalTransactions) * 100, 1)
            : 0;

        return [
            'total_transactions' => $totalTransactions,
            'completed_transactions' => $completedTransactions,
            'pending_transactions' => $pendingTransactions,
            'failed_transactions' => $failedTransactions,
            'total_amount' => $totalAmount,
            'success_rate' => $successRate,
        ];
    }

    /**
     * Get user notifications
     */
    protected function getUserNotifications(User $user): array
    {
        $notifications = [];

        // Check for pending KYC verification
        if (!$user->kyc_verified_at) {
            $notifications[] = [
                'type' => 'warning',
                'icon' => 'exclamation-triangle',
                'message' => 'يرجى إكمال التحقق من الهوية لزيادة حدود التحويل',
            ];
        }

        // Check for unverified email
        if (!$user->email_verified_at) {
            $notifications[] = [
                'type' => 'info',
                'icon' => 'envelope',
                'message' => 'يرجى تأكيد البريد الإلكتروني لتفعيل جميع الميزات',
            ];
        }

        // Check for pending transactions
        $pendingCount = Transaction::where('user_id', $user->id)
            ->where('status', 'pending')
            ->count();
        
        if ($pendingCount > 0) {
            $notifications[] = [
                'type' => 'info',
                'icon' => 'clock',
                'message' => "لديك {$pendingCount} تحويل معلق",
            ];
        }

        // Check for failed transactions in last 24 hours
        $recentFailures = Transaction::where('user_id', $user->id)
            ->whereIn('status', ['failed', 'cancelled'])
            ->where('created_at', '>=', now()->subDay())
            ->count();
        
        if ($recentFailures > 0) {
            $notifications[] = [
                'type' => 'danger',
                'icon' => 'x-circle',
                'message' => "فشل {$recentFailures} تحويل في آخر 24 ساعة",
            ];
        }

        return $notifications;
    }

    /**
     * Get status text in Arabic
     */
    protected function getStatusText($status)
    {
        $statusMap = [
            'pending' => 'معلق',
            'processing' => 'قيد المعالجة',
            'completed' => 'مكتمل',
            'failed' => 'فاشل',
            'cancelled' => 'ملغي'
        ];

        return $statusMap[$status] ?? $status;
    }
}
