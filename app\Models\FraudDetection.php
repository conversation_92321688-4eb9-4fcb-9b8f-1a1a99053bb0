<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FraudDetection extends Model
{
    use HasFactory;

    protected $table = 'fraud_detection';

    protected $fillable = [
        'transaction_id',
        'user_id',
        'alert_type',
        'risk_level',
        'risk_score',
        'description',
        'detection_data',
        'status',
        'assigned_to',
        'resolution_notes',
        'resolved_at',
        'is_automated',
        'detection_model',
        'metadata',
    ];

    protected $casts = [
        'detection_data' => 'array',
        'metadata' => 'array',
        'risk_score' => 'decimal:2',
        'is_automated' => 'boolean',
        'resolved_at' => 'datetime',
    ];

    /**
     * Get the transaction that triggered the fraud detection.
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Get the user associated with the fraud detection.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user assigned to investigate this fraud alert.
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Scope for open fraud alerts.
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'open');
    }

    /**
     * Scope for resolved fraud alerts.
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope for fraud alerts by risk level.
     */
    public function scopeByRiskLevel($query, string $riskLevel)
    {
        return $query->where('risk_level', $riskLevel);
    }

    /**
     * Scope for high risk alerts.
     */
    public function scopeHighRisk($query)
    {
        return $query->whereIn('risk_level', ['high', 'critical']);
    }

    /**
     * Scope for automated detections.
     */
    public function scopeAutomated($query)
    {
        return $query->where('is_automated', true);
    }

    /**
     * Scope for manual detections.
     */
    public function scopeManual($query)
    {
        return $query->where('is_automated', false);
    }

    /**
     * Mark fraud alert as resolved.
     */
    public function markAsResolved(string $resolutionNotes = '', int $assignedTo = null): void
    {
        $this->update([
            'status' => 'resolved',
            'resolution_notes' => $resolutionNotes,
            'resolved_at' => now(),
            'assigned_to' => $assignedTo ?? auth()->id(),
        ]);
    }

    /**
     * Mark fraud alert as false positive.
     */
    public function markAsFalsePositive(string $resolutionNotes = ''): void
    {
        $this->update([
            'status' => 'false_positive',
            'resolution_notes' => $resolutionNotes,
            'resolved_at' => now(),
            'assigned_to' => auth()->id(),
        ]);
    }

    /**
     * Assign fraud alert to user.
     */
    public function assignTo(int $userId): void
    {
        $this->update([
            'assigned_to' => $userId,
            'status' => 'investigating',
        ]);
    }

    /**
     * Get risk level color.
     */
    public function getRiskLevelColorAttribute(): string
    {
        return match ($this->risk_level) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'critical' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get formatted fraud detection data.
     */
    public function getFormattedDataAttribute(): array
    {
        return [
            'id' => $this->id,
            'transaction_id' => $this->transaction_id,
            'alert_type' => $this->alert_type,
            'risk_level' => $this->risk_level,
            'risk_score' => $this->risk_score,
            'description' => $this->description,
            'status' => $this->status,
            'is_automated' => $this->is_automated,
            'detection_model' => $this->detection_model,
            'created_at' => $this->created_at->toISOString(),
            'resolved_at' => $this->resolved_at?->toISOString(),
            'time_ago' => $this->created_at->diffForHumans(),
            'risk_color' => $this->risk_level_color,
            'transaction' => $this->transaction ? [
                'id' => $this->transaction->id,
                'amount' => $this->transaction->amount,
                'currency' => $this->transaction->currency->code,
                'status' => $this->transaction->status,
            ] : null,
            'user' => $this->user ? [
                'id' => $this->user->id,
                'name' => $this->user->first_name . ' ' . $this->user->last_name,
                'email' => $this->user->email,
            ] : null,
        ];
    }

    /**
     * Create fraud detection alert.
     */
    public static function createAlert(array $data): self
    {
        return self::create($data);
    }
}
