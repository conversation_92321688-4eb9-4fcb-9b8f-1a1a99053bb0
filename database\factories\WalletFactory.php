<?php

namespace Database\Factories;

use App\Models\Wallet;
use App\Models\User;
use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Wallet>
 */
class WalletFactory extends Factory
{
    protected $model = Wallet::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'currency_id' => Currency::factory(),
            'wallet_number' => 'WLT' . $this->faker->numerify('############'),
            'balance' => $this->faker->randomFloat(2, 0, 10000),
            'available_balance' => function (array $attributes) {
                return $attributes['balance'] - $this->faker->randomFloat(2, 0, $attributes['balance'] * 0.1);
            },
            'pending_balance' => $this->faker->randomFloat(2, 0, 500),
            'frozen_balance' => $this->faker->randomFloat(2, 0, 100),
            'daily_limit' => $this->faker->randomFloat(2, 1000, 10000),
            'monthly_limit' => $this->faker->randomFloat(2, 10000, 100000),
            'status' => $this->faker->randomElement(['active', 'inactive', 'frozen', 'suspended']),
            'is_primary' => $this->faker->boolean(20), // 20% chance of being primary
            'last_transaction_at' => $this->faker->optional(0.8)->dateTimeBetween('-30 days', 'now'),
            'metadata' => [
                'created_via' => $this->faker->randomElement(['web', 'mobile', 'api']),
                'verification_level' => $this->faker->randomElement(['basic', 'verified', 'premium']),
            ],
        ];
    }

    /**
     * Indicate that the wallet is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the wallet is primary.
     */
    public function primary(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_primary' => true,
        ]);
    }

    /**
     * Indicate that the wallet is frozen.
     */
    public function frozen(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'frozen',
            'frozen_balance' => $attributes['balance'],
            'available_balance' => 0,
        ]);
    }

    /**
     * Indicate that the wallet has high balance.
     */
    public function highBalance(): static
    {
        return $this->state(fn (array $attributes) => [
            'balance' => $this->faker->randomFloat(2, 50000, 100000),
            'available_balance' => function (array $attributes) {
                return $attributes['balance'] - $this->faker->randomFloat(2, 0, $attributes['balance'] * 0.05);
            },
        ]);
    }

    /**
     * Indicate that the wallet is empty.
     */
    public function empty(): static
    {
        return $this->state(fn (array $attributes) => [
            'balance' => 0,
            'available_balance' => 0,
            'pending_balance' => 0,
            'frozen_balance' => 0,
        ]);
    }
}
