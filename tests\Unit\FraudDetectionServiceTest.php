<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Currency;
use App\Models\Country;
use App\Models\Transaction;
use App\Services\FraudDetectionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FraudDetectionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected FraudDetectionService $fraudService;
    protected User $user;
    protected Currency $currency;
    protected Country $country;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->fraudService = new FraudDetectionService();
        $this->country = Country::factory()->create();
        $this->currency = Currency::factory()->create();
        $this->user = User::factory()->create([
            'country_id' => $this->country->id,
            'created_at' => now()->subDays(30), // Not a new user
        ]);
    }

    public function test_low_amount_transaction_has_low_risk()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 100, // Low amount
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertLessThan(30, $analysis['risk_score']);
        $this->assertEquals('low', $analysis['risk_level']);
        $this->assertFalse($analysis['requires_review']);
        $this->assertFalse($analysis['auto_block']);
    }

    public function test_high_amount_transaction_increases_risk()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 75000, // High amount
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertGreaterThan(30, $analysis['risk_score']);
        $this->assertContains('Large transaction amount', $analysis['risk_factors']);
    }

    public function test_new_user_increases_risk_score()
    {
        $newUser = User::factory()->create([
            'country_id' => $this->country->id,
            'created_at' => now()->subDays(3), // New user
        ]);

        $transaction = Transaction::factory()->create([
            'sender_id' => $newUser->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('New user account (less than 7 days old)', $analysis['risk_factors']);
        $this->assertGreaterThan(20, $analysis['risk_score']);
    }

    public function test_unverified_user_increases_risk_score()
    {
        $this->user->update([
            'kyc_verified_at' => null,
            'aml_verified' => false,
        ]);

        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('Unverified user (KYC/AML not completed)', $analysis['risk_factors']);
        $this->assertGreaterThan(25, $analysis['risk_score']);
    }

    public function test_round_number_amount_increases_risk()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000, // Round number
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('Round number amount (potential structuring)', $analysis['risk_factors']);
    }

    public function test_off_hours_transaction_increases_risk()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'created_at' => now()->setHour(2), // 2 AM
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('Transaction during off-hours', $analysis['risk_factors']);
    }

    public function test_weekend_transaction_increases_risk()
    {
        $weekend = now()->next('Saturday');
        
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'created_at' => $weekend,
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('Weekend transaction', $analysis['risk_factors']);
    }

    public function test_high_frequency_transactions_increase_risk()
    {
        // Create multiple recent transactions
        Transaction::factory()->count(4)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'created_at' => now()->subMinutes(30),
        ]);

        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('High transaction frequency (5+ in last hour)', $analysis['risk_factors']);
    }

    public function test_amount_significantly_higher_than_average_increases_risk()
    {
        // Create historical transactions with low amounts
        Transaction::factory()->count(5)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 100,
            'status' => 'completed',
            'created_at' => now()->subDays(10),
        ]);

        // Create new transaction with much higher amount
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 5000, // 50x higher than average
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('Amount significantly higher than user average', $analysis['risk_factors']);
    }

    public function test_high_risk_user_increases_transaction_risk()
    {
        $this->user->update(['risk_level' => 'high']);

        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('User classified as high risk', $analysis['risk_factors']);
        $this->assertGreaterThan(35, $analysis['risk_score']);
    }

    public function test_multiple_failed_transactions_increase_risk()
    {
        // Create multiple failed transactions
        Transaction::factory()->count(4)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'failed',
            'created_at' => now()->subDays(3),
        ]);

        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('Multiple failed transactions in past week', $analysis['risk_factors']);
    }

    public function test_very_high_risk_score_triggers_auto_block()
    {
        // Create conditions for very high risk
        $newUser = User::factory()->create([
            'country_id' => $this->country->id,
            'created_at' => now()->subDays(1), // Very new user
            'kyc_verified_at' => null,
            'aml_verified' => false,
            'risk_level' => 'high',
        ]);

        $transaction = Transaction::factory()->create([
            'sender_id' => $newUser->id,
            'currency_id' => $this->currency->id,
            'amount' => 100000, // Very high amount
            'created_at' => now()->setHour(3), // Off hours
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertGreaterThanOrEqual(80, $analysis['risk_score']);
        $this->assertEquals('high', $analysis['risk_level']);
        $this->assertTrue($analysis['auto_block']);
    }

    public function test_medium_risk_score_requires_review()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 25000, // Medium-high amount
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertGreaterThanOrEqual(50, $analysis['risk_score']);
        $this->assertLessThan(80, $analysis['risk_score']);
        $this->assertEquals('medium', $analysis['risk_level']);
        $this->assertTrue($analysis['requires_review']);
        $this->assertFalse($analysis['auto_block']);
    }

    public function test_analyze_user_calculates_risk_correctly()
    {
        // Create high volume transactions
        Transaction::factory()->count(10)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 5000,
            'created_at' => now()->subDays(15),
        ]);

        $analysis = $this->fraudService->analyzeUser($this->user);

        $this->assertIsArray($analysis);
        $this->assertArrayHasKey('risk_score', $analysis);
        $this->assertArrayHasKey('risk_factors', $analysis);
        $this->assertArrayHasKey('recommendation', $analysis);
    }

    public function test_fraud_alert_is_created_for_risky_transaction()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 50000, // High amount to trigger alert
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertDatabaseHas('fraud_detections', [
            'transaction_id' => $transaction->id,
            'user_id' => $this->user->id,
            'status' => 'open',
        ]);
    }

    public function test_cross_border_transaction_increases_risk()
    {
        $otherCountry = Country::factory()->create();
        
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'sender_country_id' => $this->country->id,
            'receiver_country_id' => $otherCountry->id,
        ]);

        $analysis = $this->fraudService->analyzeTransaction($transaction);

        $this->assertContains('Cross-border transaction', $analysis['risk_factors']);
    }
}
