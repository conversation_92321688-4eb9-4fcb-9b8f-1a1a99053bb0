<?php

namespace Database\Factories;

use App\Models\PaymentGateway;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentGateway>
 */
class PaymentGatewayFactory extends Factory
{
    protected $model = PaymentGateway::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $gateways = [
            [
                'name' => 'PayPal',
                'code' => 'paypal',
                'type' => 'online',
                'countries' => ['USA', 'GBR', 'CAN', 'AUS'],
                'currencies' => ['USD', 'EUR', 'GBP', 'CAD'],
            ],
            [
                'name' => 'Stripe',
                'code' => 'stripe',
                'type' => 'online',
                'countries' => ['USA', 'GBR', 'CAN', 'AUS', 'DEU'],
                'currencies' => ['USD', 'EUR', 'GBP', 'CAD'],
            ],
            [
                'name' => 'Wise (TransferWise)',
                'code' => 'wise',
                'type' => 'bank_transfer',
                'countries' => ['USA', 'GBR', 'EUR', 'CAN', 'AUS'],
                'currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
            ],
            [
                'name' => 'Western Union',
                'code' => 'western_union',
                'type' => 'cash',
                'countries' => ['USA', 'SAU', 'ARE', 'EGY', 'JOR'],
                'currencies' => ['USD', 'SAR', 'AED', 'EGP', 'JOD'],
            ],
            [
                'name' => 'MoneyGram',
                'code' => 'moneygram',
                'type' => 'cash',
                'countries' => ['USA', 'SAU', 'ARE', 'EGY', 'JOR'],
                'currencies' => ['USD', 'SAR', 'AED', 'EGP', 'JOD'],
            ],
        ];

        $gateway = $this->faker->randomElement($gateways);

        return [
            'name' => $gateway['name'],
            'code' => $gateway['code'],
            'type' => $gateway['type'],
            'is_active' => $this->faker->boolean(85),
            'is_sandbox' => $this->faker->boolean(30),
            'supported_countries' => $gateway['countries'],
            'supported_currencies' => $gateway['currencies'],
            'min_amount' => $this->faker->randomFloat(2, 1, 10),
            'max_amount' => $this->faker->randomFloat(2, 10000, 100000),
            'commission_rate' => $this->faker->randomFloat(4, 0.01, 0.05),
            'fixed_fee' => $this->faker->randomFloat(2, 0, 5),
            'processing_time_minutes' => $this->faker->numberBetween(1, 1440), // 1 minute to 24 hours
            'api_endpoint' => $this->faker->url(),
            'webhook_url' => $this->faker->url(),
            'credentials' => [
                'api_key' => $this->faker->uuid(),
                'secret_key' => $this->faker->sha256(),
                'merchant_id' => $this->faker->numerify('MERCH_########'),
            ],
            'settings' => [
                'auto_capture' => $this->faker->boolean(),
                'require_cvv' => $this->faker->boolean(80),
                'require_3ds' => $this->faker->boolean(60),
                'timeout_seconds' => $this->faker->numberBetween(30, 300),
            ],
            'metadata' => [
                'integration_date' => $this->faker->dateTimeBetween('-2 years', 'now')->format('Y-m-d'),
                'last_health_check' => $this->faker->dateTimeBetween('-1 day', 'now')->format('Y-m-d H:i:s'),
                'success_rate' => $this->faker->randomFloat(2, 85, 99.9),
            ],
        ];
    }

    /**
     * Indicate that the gateway is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the gateway is in sandbox mode.
     */
    public function sandbox(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_sandbox' => true,
        ]);
    }

    /**
     * Indicate that the gateway is for production.
     */
    public function production(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_sandbox' => false,
        ]);
    }

    /**
     * Indicate that the gateway supports high amounts.
     */
    public function highAmount(): static
    {
        return $this->state(fn (array $attributes) => [
            'max_amount' => $this->faker->randomFloat(2, 500000, 1000000),
        ]);
    }
}
