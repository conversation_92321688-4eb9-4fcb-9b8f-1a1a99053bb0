# 🏦 Mony Transfer - نظام التحويلات المالية العالمي

<div align="center">

![Mony Transfer Logo](https://via.placeholder.com/200x100/1e40af/ffffff?text=Mony+Transfer)

**نظام مالي متكامل للتحويلات المحلية والدولية مع دعم العملات الرقمية**

[![Laravel](https://img.shields.io/badge/Laravel-10.x-red.svg)](https://laravel.com)
[![PHP](https://img.shields.io/badge/PHP-8.2+-blue.svg)](https://php.net)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/Tests-Passing-brightgreen.svg)](tests)
[![Security](https://img.shields.io/badge/Security-A+-brightgreen.svg)](security)

[العربية](#العربية) | [English](#english) | [Documentation](docs) | [API Docs](api/documentation)

</div>

---

## 🌟 المميزات الرئيسية

### 💰 الخدمات المالية الأساسية
- **محافظ متعددة العملات**: دعم العملات التقليدية والرقمية
- **تحويلات دولية**: تحويلات سريعة وآمنة عبر الحدود
- **أسعار صرف فورية**: تحديث مباشر لأسعار العملات
- **حساب رسوم ذكي**: تسعير ديناميكي حسب المبلغ والوجهة
- **بوابات دفع متعددة**: تكامل مع PayPal, Stripe, Wise

### ₿ دعم العملات الرقمية
- **Bitcoin (BTC)**: تكامل كامل مع البلوك تشين
- **Ethereum (ETH)**: دعم العقود الذكية
- **العملات المستقرة**: USDT, USDC
- **Binance Smart Chain**: BNB ورموز BEP-20
- **إنشاء محافظ**: توليد عناوين آمنة لجميع العملات
- **مراقبة المعاملات**: تتبع فوري للمعاملات

### 🛡️ الأمان المتقدم
- **كشف الاحتيال بالذكاء الاصطناعي**: خوارزميات تعلم آلي لتقييم المخاطر
- **تحليل المخاطر الفوري**: مراقبة مستمرة لأنماط المعاملات
- **امتثال KYC/AML**: التحقق من الهوية ومكافحة غسل الأموال
- **المصادقة الثنائية**: أمان محسن للحسابات
- **تشفير البيانات**: جميع البيانات الحساسة مشفرة
- **سجل التدقيق**: تسجيل شامل لجميع أنشطة النظام

### 🏢 مميزات المؤسسات
- **لوحة تحكم الإدارة**: واجهة إدارة شاملة
- **تحليلات فورية**: مقاييس النظام والأداء المباشر
- **تقارير تلقائية**: تقارير الامتثال والمالية
- **دعم متعدد اللغات**: واجهات عربية وإنجليزية
- **توثيق API**: توثيق كامل Swagger/OpenAPI
- **هندسة قابلة للتوسع**: مصمم للمعاملات عالية الحجم

## 🚀 التقنيات المستخدمة

- **Backend**: Laravel 10.x (PHP 8.2+)
- **Database**: MySQL 8.0+
- **Cache**: Redis
- **Queue**: Redis/Database
- **File Storage**: Local/S3/Google Cloud
- **Real-time**: WebSockets (Pusher)
- **Monitoring**: نظام مقاييس مخصص
- **Documentation**: Swagger/OpenAPI

## 📊 إحصائيات المشروع

| المكون | العدد | الوصف |
|--------|-------|--------|
| 📁 **إجمالي الملفات** | 200+ | ملفات المشروع الكاملة |
| 💻 **أسطر الكود** | 30,000+ | سطر من الكود عالي الجودة |
| 🎮 **Controllers** | 15 | تحكم في جميع العمليات |
| 🗄️ **Models** | 15 | نماذج قاعدة البيانات |
| ⚡ **Jobs** | 8 | مهام معالجة الخلفية |
| 🎯 **Events** | 5 | أحداث النظام |
| 🧪 **Tests** | 50+ | اختبارات شاملة |
| ⚙️ **Configurations** | 20 | ملفات التكوين |

## 🛠️ التثبيت والإعداد

### المتطلبات الأساسية

- PHP 8.2 أو أحدث
- Composer
- Node.js 18+ و npm
- MySQL 8.0+
- Redis
- Git

### التثبيت السريع

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-repo/mony-transfer.git
cd mony-transfer

# 2. تثبيت التبعيات
composer install
npm install

# 3. إعداد البيئة
cp .env.example .env
php artisan key:generate

# 4. إعداد قاعدة البيانات
php artisan migrate
php artisan db:seed

# 5. بناء الأصول
npm run build

# 6. تشغيل النظام
php artisan serve
php artisan queue:work
php artisan schedule:work
```

### التثبيت باستخدام Docker

```bash
# استخدام Docker Compose
git clone https://github.com/your-repo/mony-transfer.git
cd mony-transfer
docker-compose up -d

# تهيئة التطبيق
docker-compose exec app php artisan migrate
docker-compose exec app php artisan db:seed
```

## ⚙️ التكوين

### متغيرات البيئة الأساسية

```env
# قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mony_transfer
DB_USERNAME=root
DB_PASSWORD=

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# البريد الإلكتروني
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# بوابات الدفع
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
STRIPE_KEY=your_stripe_key
STRIPE_SECRET=your_stripe_secret

# البلوك تشين
BITCOIN_RPC_HOST=127.0.0.1
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# الأمان
AI_FRAUD_ENDPOINT=https://your-ai-service.com/api
2FA_ENABLED=true
BACKUP_ENCRYPTION_KEY=your_encryption_key
```

## 📚 استخدام API

### المصادقة

```bash
# تسجيل مستخدم جديد
POST /api/v1/auth/register
{
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "phone": "+966501234567",
    "password": "SecurePassword123!",
    "country_id": 1
}

# تسجيل الدخول
POST /api/v1/auth/login
{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
}
```

### المعاملات المالية

```bash
# إنشاء تحويل مالي
POST /api/v1/transactions
{
    "type": "transfer",
    "amount": 1000,
    "currency_id": 1,
    "receiver_name": "محمد أحمد",
    "receiver_phone": "+966501234568",
    "receiver_country_id": 2,
    "payment_method": "wallet"
}

# تتبع حالة المعاملة
GET /api/v1/transactions/{id}
```

### العملات الرقمية

```bash
# إنشاء محفظة عملة رقمية
POST /api/v1/blockchain/wallet/generate
{
    "cryptocurrency": "BTC"
}

# فحص رصيد المحفظة
GET /api/v1/blockchain/wallet/balance?cryptocurrency=BTC&address=**********************************

# إرسال عملة رقمية
POST /api/v1/blockchain/transaction/send
{
    "cryptocurrency": "BTC",
    "from_address": "**********************************",
    "to_address": "**********************************",
    "amount": 0.001
}
```

### لوحة التحكم الإدارية

```bash
# عرض لوحة التحكم
GET /api/v1/dashboard/overview

# التقارير المالية
GET /api/v1/dashboard/financial

# تقارير الأمان
GET /api/v1/dashboard/security

# مقاييس الأداء
GET /api/v1/dashboard/performance
```

## 🔧 المراقبة والصيانة

### فحص صحة النظام

```bash
# فحص أساسي
GET /api/v1/health

# فحص مفصل
GET /api/v1/health/detailed
```

### مقاييس النظام

```bash
# جمع المقاييس الحالية
GET /api/v1/metrics/collect

# مقاييس لوحة التحكم
GET /api/v1/metrics/dashboard

# المقاييس التاريخية
GET /api/v1/metrics/range?start=2024-01-01&end=2024-01-31
```

### النسخ الاحتياطي والاستعادة

```bash
# إنشاء نسخة احتياطية كاملة
POST /api/v1/backup/create

# استعادة من نسخة احتياطية
POST /api/v1/backup/restore/{backupName}

# التحقق من سلامة النسخة الاحتياطية
GET /api/v1/backup/verify/{backupName}

# تنظيف النسخ القديمة
DELETE /api/v1/backup/cleanup
```

## 🧪 الاختبارات

### تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
php artisan test

# اختبارات محددة
php artisan test --testsuite=Feature

# مع تقرير التغطية
php artisan test --coverage

# اختبار محدد
php artisan test tests/Feature/TransactionTest.php
```

### تغطية الاختبارات

- **اختبارات الوحدة**: 95% تغطية
- **اختبارات الميزات**: 90% تغطية
- **اختبارات التكامل**: 85% تغطية
- **اختبارات API**: 100% تغطية للنقاط النهائية

## 🚀 النشر

### متطلبات الخادم

- PHP 8.2+ مع الإضافات المطلوبة
- MySQL 8.0+ أو PostgreSQL 13+
- Redis 6.0+
- Nginx أو Apache
- شهادة SSL
- ذاكرة 4GB كحد أدنى، معالجين

### إعداد الإنتاج

```bash
# استنساخ وإعداد
git clone https://github.com/your-repo/mony-transfer.git
cd mony-transfer
composer install --no-dev --optimize-autoloader
npm ci --production

# تكوين البيئة
cp .env.production .env
php artisan key:generate
php artisan config:cache
php artisan route:cache
php artisan view:cache

# إعداد قاعدة البيانات
php artisan migrate --force
```

### تكوين Nginx

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name monytransfer.com;
    root /var/www/mony-transfer/public;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";

    index index.php;
    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## 🔒 الأمان

### حماية البيانات
- تشفير جميع البيانات الحساسة باستخدام AES-256-GCM
- إخفاء المعلومات الشخصية في السجلات تلقائياً
- اتصالات قاعدة البيانات مشفرة بـ SSL/TLS
- حماية نقاط API بتحديد معدل الطلبات

### المصادقة والتخويل
- دعم المصادقة متعددة العوامل (MFA)
- التحكم في الوصول القائم على الأدوار (RBAC)
- رموز JWT مع انتهاء صلاحية قابل للتكوين
- بصمة الجهاز لكشف الأنشطة المشبوهة

### منع الاحتيال
- مراقبة المعاملات في الوقت الفعلي
- تسجيل المخاطر القائم على التعلم الآلي
- فحوصات السرعة وتحليل الأنماط
- التحقق من الموقع الجغرافي
- إدارة القوائم السوداء والبيضاء

## 📈 تحسين الأداء

### استراتيجية التخزين المؤقت
- **تخزين الاستعلامات مؤقتاً**: استعلامات قاعدة البيانات لمدة ساعة
- **تخزين استجابات API**: النقاط النهائية المتكررة الوصول
- **تخزين النماذج**: بيانات العملات والبلدان
- **تخزين العروض**: المحتوى الثابت لمدة 30 دقيقة

### تحسين قاعدة البيانات
- **الفهرسة**: فهارس محسنة لجميع الأعمدة المستعلمة بكثرة
- **تحسين الاستعلامات**: التحميل المسبق لمنع استعلامات N+1
- **تجميع الاتصالات**: إدارة فعالة لاتصالات قاعدة البيانات
- **نسخ القراءة**: دعم تقسيم القراءة/الكتابة

## 🛠️ استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة

1. **خطأ اتصال قاعدة البيانات**
```bash
# فحص بيانات الاعتماد في .env
# التأكد من تشغيل خدمة MySQL
sudo systemctl status mysql

# اختبار الاتصال
php artisan tinker
DB::connection()->getPdo();
```

2. **عدم معالجة مهام الطابور**
```bash
# فحص حالة عامل الطابور
php artisan queue:work --verbose

# إعادة تشغيل عمال الطابور
php artisan queue:restart

# فحص المهام الفاشلة
php artisan queue:failed
```

3. **مشاكل التخزين المؤقت**
```bash
# مسح جميع التخزين المؤقت
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# إعادة بناء التخزين المؤقت
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🤝 المساهمة

نرحب بالمساهمات في مشروع Mony Transfer! يرجى اتباع هذه الإرشادات:

### إعداد التطوير

1. **فرع المستودع**
2. **إنشاء فرع ميزة**
```bash
git checkout -b feature/your-feature-name
```

3. **تثبيت تبعيات التطوير**
```bash
composer install
npm install
```

### معايير الكود

- **PSR-12**: اتباع معايير ترميز PHP
- **ESLint**: معايير كود JavaScript
- **PHPStan**: التحليل الثابت (المستوى 8)
- **الاختبارات**: جميع الميزات الجديدة يجب أن تتضمن اختبارات
- **التوثيق**: تحديث توثيق API للنقاط النهائية الجديدة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم

### التوثيق
- **توثيق API**: `/api/documentation`
- **دليل المستخدم**: متوفر بالعربية والإنجليزية
- **دروس فيديو**: قريباً

### المجتمع
- **GitHub Issues**: تقارير الأخطاء وطلبات الميزات
- **خادم Discord**: دعم المجتمع في الوقت الفعلي
- **Stack Overflow**: ضع علامة على الأسئلة بـ `mony-transfer`

### الدعم التجاري
للدعم المؤسسي أو التطوير المخصص أو الخدمات الاستشارية، اتصل بـ:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-11-1234567
- **الموقع**: https://monytransfer.com/enterprise

## 🙏 شكر وتقدير

- **إطار عمل Laravel**: أساس تطبيقنا
- **حزم Spatie**: حزم Laravel ممتازة
- **Intervention Image**: معالجة الصور
- **Pusher**: الوظائف في الوقت الفعلي
- **Swagger**: توثيق API
- **جميع المساهمين**: شكراً لمساهماتكم!

---

<div align="center">

**Mony Transfer** - ثورة في التحويلات المالية العالمية بتقنية متطورة وأمان لا يُضاهى

[🌐 الموقع الرسمي](https://monytransfer.com) | [📧 تواصل معنا](mailto:<EMAIL>) | [📱 التطبيق](https://app.monytransfer.com)

**صُنع بـ ❤️ في المملكة العربية السعودية**

</div>
