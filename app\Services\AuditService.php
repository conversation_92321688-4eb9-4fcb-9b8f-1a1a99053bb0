<?php

namespace App\Services;

use App\Models\AuditLog;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class AuditService
{
    /**
     * Log transaction created event
     */
    public function logTransactionCreated(Transaction $transaction, User $user): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'transaction_created',
            'user_id' => $user->id,
            'auditable_type' => Transaction::class,
            'auditable_id' => $transaction->id,
            'description' => "Transaction {$transaction->transaction_id} created",
            'old_values' => null,
            'new_values' => $transaction->toArray(),
            'metadata' => [
                'transaction_id' => $transaction->transaction_id,
                'amount' => $transaction->amount,
                'currency_from' => $transaction->currency_from,
                'currency_to' => $transaction->currency_to,
                'payment_method' => $transaction->payment_method,
                'delivery_method' => $transaction->delivery_method,
            ],
        ]);
    }

    /**
     * Log transaction processed event
     */
    public function logTransactionProcessed(Transaction $transaction): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'transaction_processed',
            'user_id' => $transaction->user_id,
            'auditable_type' => Transaction::class,
            'auditable_id' => $transaction->id,
            'description' => "Transaction {$transaction->transaction_id} processed",
            'old_values' => ['status' => 'pending'],
            'new_values' => ['status' => $transaction->status],
            'metadata' => [
                'transaction_id' => $transaction->transaction_id,
                'processed_by' => $transaction->processed_by,
                'processed_at' => $transaction->processed_at,
            ],
        ]);
    }

    /**
     * Log transaction cancelled event
     */
    public function logTransactionCancelled(Transaction $transaction, string $reason = null): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'transaction_cancelled',
            'user_id' => $transaction->user_id,
            'auditable_type' => Transaction::class,
            'auditable_id' => $transaction->id,
            'description' => "Transaction {$transaction->transaction_id} cancelled",
            'old_values' => ['status' => $transaction->getOriginal('status')],
            'new_values' => ['status' => 'cancelled'],
            'metadata' => [
                'transaction_id' => $transaction->transaction_id,
                'cancelled_by' => $transaction->cancelled_by,
                'cancelled_at' => $transaction->cancelled_at,
                'cancellation_reason' => $reason,
            ],
        ]);
    }

    /**
     * Log user login event
     */
    public function logUserLogin(User $user): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'user_login',
            'user_id' => $user->id,
            'auditable_type' => User::class,
            'auditable_id' => $user->id,
            'description' => "User {$user->email} logged in",
            'old_values' => null,
            'new_values' => null,
            'metadata' => [
                'email' => $user->email,
                'user_type' => $user->user_type,
                'login_method' => 'email_password',
            ],
        ]);
    }

    /**
     * Log user logout event
     */
    public function logUserLogout(User $user): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'user_logout',
            'user_id' => $user->id,
            'auditable_type' => User::class,
            'auditable_id' => $user->id,
            'description' => "User {$user->email} logged out",
            'old_values' => null,
            'new_values' => null,
            'metadata' => [
                'email' => $user->email,
                'session_duration' => $this->calculateSessionDuration($user),
            ],
        ]);
    }

    /**
     * Log user registration event
     */
    public function logUserRegistration(User $user): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'user_registered',
            'user_id' => $user->id,
            'auditable_type' => User::class,
            'auditable_id' => $user->id,
            'description' => "New user {$user->email} registered",
            'old_values' => null,
            'new_values' => $user->toArray(),
            'metadata' => [
                'email' => $user->email,
                'user_type' => $user->user_type,
                'country_id' => $user->country_id,
                'registration_method' => 'web',
            ],
        ]);
    }

    /**
     * Log profile update event
     */
    public function logProfileUpdate(User $user, array $oldValues, array $newValues): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'profile_updated',
            'user_id' => $user->id,
            'auditable_type' => User::class,
            'auditable_id' => $user->id,
            'description' => "User {$user->email} updated profile",
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'metadata' => [
                'fields_changed' => array_keys($newValues),
                'updated_by' => Auth::id(),
            ],
        ]);
    }

    /**
     * Log password change event
     */
    public function logPasswordChange(User $user): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'password_changed',
            'user_id' => $user->id,
            'auditable_type' => User::class,
            'auditable_id' => $user->id,
            'description' => "User {$user->email} changed password",
            'old_values' => null,
            'new_values' => null,
            'metadata' => [
                'email' => $user->email,
                'changed_by' => Auth::id(),
            ],
        ]);
    }

    /**
     * Log failed login attempt
     */
    public function logFailedLogin(string $email, string $reason): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'login_failed',
            'user_id' => null,
            'auditable_type' => null,
            'auditable_id' => null,
            'description' => "Failed login attempt for {$email}",
            'old_values' => null,
            'new_values' => null,
            'metadata' => [
                'email' => $email,
                'failure_reason' => $reason,
                'attempt_count' => $this->getRecentFailedAttempts($email),
            ],
        ]);
    }

    /**
     * Log suspicious activity
     */
    public function logSuspiciousActivity(string $activity, array $details = []): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'suspicious_activity',
            'user_id' => Auth::id(),
            'auditable_type' => null,
            'auditable_id' => null,
            'description' => "Suspicious activity detected: {$activity}",
            'old_values' => null,
            'new_values' => null,
            'metadata' => array_merge([
                'activity_type' => $activity,
                'detected_at' => now(),
            ], $details),
            'severity' => 'high',
        ]);
    }

    /**
     * Log admin action
     */
    public function logAdminAction(string $action, array $details = []): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'admin_action',
            'user_id' => Auth::id(),
            'auditable_type' => null,
            'auditable_id' => null,
            'description' => "Admin action: {$action}",
            'old_values' => $details['old_values'] ?? null,
            'new_values' => $details['new_values'] ?? null,
            'metadata' => array_merge([
                'action_type' => $action,
                'performed_at' => now(),
            ], $details),
            'severity' => 'medium',
        ]);
    }

    /**
     * Log system event
     */
    public function logSystemEvent(string $event, array $details = []): AuditLog
    {
        return $this->createAuditLog([
            'event_type' => 'system_event',
            'user_id' => null,
            'auditable_type' => null,
            'auditable_id' => null,
            'description' => "System event: {$event}",
            'old_values' => null,
            'new_values' => null,
            'metadata' => array_merge([
                'event_type' => $event,
                'occurred_at' => now(),
            ], $details),
            'severity' => 'low',
        ]);
    }

    /**
     * Get audit logs for user
     */
    public function getUserAuditLogs(int $userId, int $limit = 50): array
    {
        return AuditLog::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get audit logs for transaction
     */
    public function getTransactionAuditLogs(int $transactionId): array
    {
        return AuditLog::where('auditable_type', Transaction::class)
            ->where('auditable_id', $transactionId)
            ->orderBy('created_at', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * Get recent suspicious activities
     */
    public function getRecentSuspiciousActivities(int $hours = 24): array
    {
        return AuditLog::where('event_type', 'suspicious_activity')
            ->where('created_at', '>=', now()->subHours($hours))
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Create audit log entry
     */
    protected function createAuditLog(array $data): AuditLog
    {
        return AuditLog::create(array_merge($data, [
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'url' => Request::fullUrl(),
            'method' => Request::method(),
            'severity' => $data['severity'] ?? 'low',
            'created_at' => now(),
        ]));
    }

    /**
     * Calculate session duration
     */
    protected function calculateSessionDuration(User $user): ?int
    {
        if ($user->last_login_at) {
            return now()->diffInMinutes($user->last_login_at);
        }
        
        return null;
    }

    /**
     * Get recent failed login attempts
     */
    protected function getRecentFailedAttempts(string $email): int
    {
        return AuditLog::where('event_type', 'login_failed')
            ->where('metadata->email', $email)
            ->where('created_at', '>=', now()->subHour())
            ->count();
    }

    /**
     * Clean old audit logs
     */
    public function cleanOldLogs(int $daysToKeep = 90): int
    {
        return AuditLog::where('created_at', '<', now()->subDays($daysToKeep))
            ->where('severity', 'low')
            ->delete();
    }

    /**
     * Export audit logs
     */
    public function exportLogs(array $filters = []): array
    {
        $query = AuditLog::query();

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['event_type'])) {
            $query->where('event_type', $filters['event_type']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['severity'])) {
            $query->where('severity', $filters['severity']);
        }

        return $query->orderBy('created_at', 'desc')
            ->limit($filters['limit'] ?? 1000)
            ->get()
            ->toArray();
    }
}
