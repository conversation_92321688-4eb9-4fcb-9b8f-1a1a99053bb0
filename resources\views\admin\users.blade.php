@extends('layouts.app')

@section('content')
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="h3 mb-0">إدارة المستخدمين</h1>
            <p class="text-muted mb-0">عرض وإدارة جميع المستخدمين المسجلين في النظام</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshUsers()">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                </button>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="bi bi-plus-lg me-1"></i>إضافة مستخدم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="filterStatus" class="form-label">الحالة</label>
                <select class="form-select" id="filterStatus" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="suspended">موقوف</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="filterType" class="form-label">نوع المستخدم</label>
                <select class="form-select" id="filterType" name="user_type">
                    <option value="">جميع الأنواع</option>
                    <option value="customer">عميل</option>
                    <option value="admin">مدير</option>
                    <option value="agent">وكيل</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="filterKyc" class="form-label">حالة KYC</label>
                <select class="form-select" id="filterKyc" name="kyc_status">
                    <option value="">جميع الحالات</option>
                    <option value="pending">معلق</option>
                    <option value="verified">مؤكد</option>
                    <option value="rejected">مرفوض</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="filterSearch" class="form-label">البحث</label>
                <input type="text" class="form-control" id="filterSearch" name="search" placeholder="البحث بالاسم أو البريد">
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المستخدمين</h5>
        <span class="badge bg-primary" id="usersCount">0 مستخدم</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="usersTable">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" class="form-check-input" id="selectAll">
                        </th>
                        <th>المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>KYC</th>
                        <th>تاريخ التسجيل</th>
                        <th>آخر دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <!-- Users will be loaded here -->
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Users pagination">
            <ul class="pagination justify-content-center" id="usersPagination">
                <!-- Pagination will be loaded here -->
            </ul>
        </nav>
    </div>
</div>

<!-- Bulk Actions -->
<div class="card mt-4" id="bulkActionsCard" style="display: none;">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col">
                <span id="selectedCount">0</span> مستخدم محدد
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="bulkAction('activate')">
                        <i class="bi bi-check-circle me-1"></i>تفعيل
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="bulkAction('suspend')">
                        <i class="bi bi-pause-circle me-1"></i>إيقاف
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="bulkAction('delete')">
                        <i class="bi bi-trash me-1"></i>حذف
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentPage = 1;
let selectedUsers = [];

// Load users
function loadUsers(page = 1) {
    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    params.append('page', page);
    
    showLoading();
    
    fetch(`/api/v1/admin/users?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUsers(data.data.users);
                updatePagination(data.data.pagination);
                updateUsersCount(data.data.pagination.total);
            }
        })
        .catch(error => {
            console.error('Error loading users:', error);
            showToast('فشل في تحميل المستخدمين', 'error');
        })
        .finally(() => {
            hideLoading();
        });
}

// Display users in table
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input user-checkbox" value="${user.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar me-2">
                            <div class="avatar-initial bg-primary rounded-circle">
                                ${user.first_name.charAt(0)}${user.last_name.charAt(0)}
                            </div>
                        </div>
                        <div>
                            <div class="fw-bold">${user.first_name} ${user.last_name}</div>
                            <small class="text-muted">ID: ${user.id}</small>
                        </div>
                    </div>
                </td>
                <td>${user.email}</td>
                <td>${user.phone || '-'}</td>
                <td>
                    <span class="badge bg-${getUserTypeBadge(user.user_type)}">${getUserTypeText(user.user_type)}</span>
                </td>
                <td>
                    <span class="badge bg-${getStatusBadge(user.status)}">${getStatusText(user.status)}</span>
                </td>
                <td>
                    <span class="badge bg-${getKycBadge(user.kyc_status)}">${getKycText(user.kyc_status)}</span>
                </td>
                <td>
                    <small>${formatDate(user.created_at)}</small>
                </td>
                <td>
                    <small>${user.last_login_at ? formatDate(user.last_login_at) : 'لم يسجل دخول'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewUser(${user.id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editUser(${user.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.insertAdjacentHTML('beforeend', row);
    });
    
    // Add event listeners for checkboxes
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedUsers);
    });
}

// Helper functions for badges
function getUserTypeBadge(type) {
    const badges = {
        'customer': 'primary',
        'admin': 'danger',
        'agent': 'info'
    };
    return badges[type] || 'secondary';
}

function getUserTypeText(type) {
    const texts = {
        'customer': 'عميل',
        'admin': 'مدير',
        'agent': 'وكيل'
    };
    return texts[type] || type;
}

function getStatusBadge(status) {
    const badges = {
        'active': 'success',
        'inactive': 'secondary',
        'suspended': 'warning'
    };
    return badges[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'suspended': 'موقوف'
    };
    return texts[status] || status;
}

function getKycBadge(status) {
    const badges = {
        'pending': 'warning',
        'verified': 'success',
        'rejected': 'danger'
    };
    return badges[status] || 'secondary';
}

function getKycText(status) {
    const texts = {
        'pending': 'معلق',
        'verified': 'مؤكد',
        'rejected': 'مرفوض'
    };
    return texts[status] || status;
}

// Update pagination
function updatePagination(pagination) {
    const paginationEl = document.getElementById('usersPagination');
    paginationEl.innerHTML = '';
    
    // Previous button
    if (pagination.current_page > 1) {
        paginationEl.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadUsers(${pagination.current_page - 1})">السابق</a>
            </li>
        `;
    }
    
    // Page numbers
    for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.last_page, pagination.current_page + 2); i++) {
        paginationEl.innerHTML += `
            <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
            </li>
        `;
    }
    
    // Next button
    if (pagination.current_page < pagination.last_page) {
        paginationEl.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadUsers(${pagination.current_page + 1})">التالي</a>
            </li>
        `;
    }
}

// Update users count
function updateUsersCount(count) {
    document.getElementById('usersCount').textContent = `${count} مستخدم`;
}

// Update selected users
function updateSelectedUsers() {
    selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    document.getElementById('selectedCount').textContent = selectedUsers.length;
    
    if (selectedUsers.length > 0) {
        document.getElementById('bulkActionsCard').style.display = 'block';
    } else {
        document.getElementById('bulkActionsCard').style.display = 'none';
    }
}

// Select all users
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
    updateSelectedUsers();
});

// Filter form submission
document.getElementById('filterForm').addEventListener('input', function() {
    loadUsers(1);
});

// Refresh users
function refreshUsers() {
    loadUsers(currentPage);
}

// User actions
function viewUser(userId) {
    // Implement view user functionality
    showToast('عرض تفاصيل المستخدم - قيد التطوير', 'info');
}

function editUser(userId) {
    // Implement edit user functionality
    showToast('تعديل المستخدم - قيد التطوير', 'info');
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        // Implement delete user functionality
        showToast('حذف المستخدم - قيد التطوير', 'info');
    }
}

// Bulk actions
function bulkAction(action) {
    if (selectedUsers.length === 0) {
        showToast('يرجى تحديد مستخدم واحد على الأقل', 'warning');
        return;
    }
    
    const actionText = {
        'activate': 'تفعيل',
        'suspend': 'إيقاف',
        'delete': 'حذف'
    };
    
    if (confirm(`هل أنت متأكد من ${actionText[action]} ${selectedUsers.length} مستخدم؟`)) {
        // Implement bulk action functionality
        showToast(`${actionText[action]} المستخدمين - قيد التطوير`, 'info');
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
});
</script>
@endpush

@push('styles')
<style>
.avatar {
    width: 40px;
    height: 40px;
}

.avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
}
</style>
@endpush
