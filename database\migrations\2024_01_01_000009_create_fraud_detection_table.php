<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fraud_detection', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('alert_type', [
                'suspicious_amount', 'unusual_pattern', 'velocity_check',
                'geographic_anomaly', 'device_fingerprint', 'behavioral_analysis',
                'blacklist_match', 'ai_prediction', 'manual_review'
            ]);
            $table->enum('risk_level', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->decimal('risk_score', 5, 2)->default(0.00); // 0.00 to 100.00
            $table->text('description');
            $table->json('detection_data')->nullable(); // AI model output, patterns, etc.
            $table->enum('status', ['open', 'investigating', 'resolved', 'false_positive'])->default('open');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->text('resolution_notes')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->boolean('is_automated')->default(true);
            $table->string('detection_model')->nullable(); // AI model version
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['alert_type', 'risk_level']);
            $table->index(['status', 'created_at']);
            $table->index(['user_id', 'risk_score']);
            $table->index('is_automated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fraud_detection');
    }
};
