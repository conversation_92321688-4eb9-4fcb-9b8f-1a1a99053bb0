<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RegistrationSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a country for users
        $this->country = Country::create([
            'name_ar' => 'المملكة العربية السعودية',
            'name_en' => 'Saudi Arabia',
            'code' => 'SAU',
            'iso2' => 'SA',
            'phone_code' => '+966',
            'currency_code' => 'SAR',
            'is_active' => true,
            'supports_transfers' => true,
        ]);
    }

    public function test_user_can_register_successfully()
    {
        $userData = [
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+966501234567',
            'country_id' => $this->country->id,
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'first_name',
                            'last_name',
                            'email',
                            'phone',
                            'user_type',
                        ],
                        'token',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'user' => [
                            'first_name' => 'أحمد',
                            'last_name' => 'محمد',
                            'email' => '<EMAIL>',
                            'user_type' => 'customer',
                        ],
                    ],
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'first_name' => 'أحمد',
            'user_type' => 'customer',
            'status' => 'pending_verification',
        ]);
    }

    public function test_registration_fails_with_duplicate_email()
    {
        // Create existing user
        User::create([
            'first_name' => 'Existing',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => bcrypt('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
        ]);

        $userData = [
            'first_name' => 'New',
            'last_name' => 'User',
            'email' => '<EMAIL>', // Same email
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+966507654321',
            'country_id' => $this->country->id,
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_registration_fails_with_duplicate_phone()
    {
        // Create existing user
        User::create([
            'first_name' => 'Existing',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => bcrypt('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
        ]);

        $userData = [
            'first_name' => 'New',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+966501234567', // Same phone
            'country_id' => $this->country->id,
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone']);
    }

    public function test_registration_requires_all_mandatory_fields()
    {
        $response = $this->postJson('/api/v1/auth/register', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'first_name',
                    'last_name',
                    'email',
                    'password',
                    'phone',
                    'country_id',
                    'terms_accepted',
                    'privacy_accepted',
                ]);
    }

    public function test_registration_validates_password_strength()
    {
        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => 'weak', // Weak password
            'password_confirmation' => 'weak',
            'phone' => '+966501234567',
            'country_id' => $this->country->id,
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    public function test_registration_validates_phone_format()
    {
        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '123456789', // Invalid format
            'country_id' => $this->country->id,
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone']);
    }

    public function test_registration_validates_email_format()
    {
        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => 'invalid-email', // Invalid format
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+966501234567',
            'country_id' => $this->country->id,
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_registration_requires_terms_acceptance()
    {
        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+966501234567',
            'country_id' => $this->country->id,
            'terms_accepted' => false, // Not accepted
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['terms_accepted']);
    }

    public function test_registration_creates_user_with_correct_defaults()
    {
        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+966501234567',
            'country_id' => $this->country->id,
            'terms_accepted' => true,
            'privacy_accepted' => true,
        ];

        $response = $this->postJson('/api/v1/auth/register', $userData);

        $response->assertStatus(201);

        $user = User::where('email', '<EMAIL>')->first();

        $this->assertEquals('customer', $user->user_type);
        $this->assertEquals('pending_verification', $user->status);
        $this->assertEquals('ar', $user->preferred_language);
        $this->assertEquals('USD', $user->preferred_currency);
        $this->assertEquals(10000, $user->daily_limit);
        $this->assertEquals(100000, $user->monthly_limit);
        $this->assertEquals('medium', $user->risk_level);
    }
}
