<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->string('code')->unique();
            $table->foreignId('country_id')->constrained()->onDelete('cascade');
            $table->string('city_ar');
            $table->string('city_en');
            $table->text('address_ar');
            $table->text('address_en');
            $table->string('phone', 20);
            $table->string('email')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_main_branch')->default(false);
            $table->json('working_hours')->nullable();
            $table->json('services')->nullable(); // Available services
            $table->decimal('cash_limit', 15, 2)->default(100000);
            $table->json('supported_currencies')->nullable();
            $table->string('manager_name')->nullable();
            $table->string('manager_phone')->nullable();
            $table->timestamps();
            
            $table->index(['country_id', 'is_active']);
            $table->index('is_main_branch');
            $table->index(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branches');
    }
};
