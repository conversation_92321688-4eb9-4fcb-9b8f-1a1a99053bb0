<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Wallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'currency_id',
        'wallet_number',
        'balance',
        'pending_balance',
        'frozen_balance',
        'is_active',
        'is_crypto_wallet',
        'crypto_address',
        'private_key_encrypted',
        'public_key',
        'qr_code_path',
        'daily_limit',
        'monthly_limit',
        'total_received',
        'total_sent',
        'transaction_count',
        'last_transaction_at',
        'metadata',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'frozen_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'is_crypto_wallet' => 'boolean',
        'daily_limit' => 'decimal:2',
        'monthly_limit' => 'decimal:2',
        'total_received' => 'decimal:2',
        'total_sent' => 'decimal:2',
        'transaction_count' => 'integer',
        'last_transaction_at' => 'datetime',
        'metadata' => 'array',
    ];

    protected $hidden = [
        'private_key_encrypted',
    ];

    /**
     * Get the user that owns the wallet.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the currency of the wallet.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Get the sent transactions from this wallet.
     */
    public function sentTransactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'sender_wallet_id');
    }

    /**
     * Get the received transactions to this wallet.
     */
    public function receivedTransactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'receiver_wallet_id');
    }

    /**
     * Generate unique wallet number.
     */
    public static function generateWalletNumber(): string
    {
        do {
            $number = 'WLT' . date('Ymd') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('wallet_number', $number)->exists());

        return $number;
    }

    /**
     * Get available balance (balance - pending - frozen).
     */
    public function getAvailableBalanceAttribute(): float
    {
        return $this->balance - $this->pending_balance - $this->frozen_balance;
    }

    /**
     * Check if wallet has sufficient balance for amount.
     */
    public function hasSufficientBalance(float $amount): bool
    {
        return $this->available_balance >= $amount;
    }

    /**
     * Add funds to wallet.
     */
    public function addFunds(float $amount, string $type = 'received'): void
    {
        $this->increment('balance', $amount);
        
        if ($type === 'received') {
            $this->increment('total_received', $amount);
        }
        
        $this->increment('transaction_count');
        $this->update(['last_transaction_at' => now()]);
    }

    /**
     * Deduct funds from wallet.
     */
    public function deductFunds(float $amount): bool
    {
        if (!$this->hasSufficientBalance($amount)) {
            return false;
        }

        $this->decrement('balance', $amount);
        $this->increment('total_sent', $amount);
        $this->increment('transaction_count');
        $this->update(['last_transaction_at' => now()]);

        return true;
    }

    /**
     * Freeze funds in wallet.
     */
    public function freezeFunds(float $amount): bool
    {
        if ($this->balance - $this->frozen_balance < $amount) {
            return false;
        }

        $this->increment('frozen_balance', $amount);
        return true;
    }

    /**
     * Unfreeze funds in wallet.
     */
    public function unfreezeFunds(float $amount): void
    {
        $this->decrement('frozen_balance', min($amount, $this->frozen_balance));
    }

    /**
     * Add pending balance.
     */
    public function addPendingBalance(float $amount): void
    {
        $this->increment('pending_balance', $amount);
    }

    /**
     * Remove pending balance.
     */
    public function removePendingBalance(float $amount): void
    {
        $this->decrement('pending_balance', min($amount, $this->pending_balance));
    }

    /**
     * Check if wallet is crypto wallet.
     */
    public function isCryptoWallet(): bool
    {
        return $this->is_crypto_wallet;
    }

    /**
     * Check if wallet is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Scope for active wallets.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for crypto wallets.
     */
    public function scopeCrypto($query)
    {
        return $query->where('is_crypto_wallet', true);
    }

    /**
     * Scope for fiat wallets.
     */
    public function scopeFiat($query)
    {
        return $query->where('is_crypto_wallet', false);
    }

    /**
     * Get formatted balance with currency symbol.
     */
    public function getFormattedBalanceAttribute(): string
    {
        return $this->currency->symbol . ' ' . $this->currency->formatAmount($this->balance);
    }

    /**
     * Get wallet display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->currency->name . ' Wallet (' . $this->wallet_number . ')';
    }

    /**
     * Check daily spending limit.
     */
    public function checkDailyLimit(float $amount): bool
    {
        $todaySpent = $this->sentTransactions()
            ->whereDate('created_at', today())
            ->sum('amount');

        return ($todaySpent + $amount) <= $this->daily_limit;
    }

    /**
     * Check monthly spending limit.
     */
    public function checkMonthlyLimit(float $amount): bool
    {
        $monthlySpent = $this->sentTransactions()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        return ($monthlySpent + $amount) <= $this->monthly_limit;
    }
}
