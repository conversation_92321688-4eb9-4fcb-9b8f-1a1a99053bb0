<?php

// Test script to verify auth guard fix
echo "🔧 Testing Auth Guard Fix...\n\n";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    // Test 1: Check auth configuration
    echo "1. Testing Auth Configuration...\n";
    $authConfig = config('auth.guards');
    
    if (isset($authConfig['api'])) {
        echo "   ✅ API guard is configured\n";
        echo "   ✅ Driver: " . $authConfig['api']['driver'] . "\n";
        echo "   ✅ Provider: " . $authConfig['api']['provider'] . "\n";
    } else {
        echo "   ❌ API guard is not configured\n";
    }

    if (isset($authConfig['web'])) {
        echo "   ✅ Web guard is configured\n";
    } else {
        echo "   ❌ Web guard is not configured\n";
    }

    // Test 2: Check Sanctum configuration
    echo "\n2. Testing Sanctum Configuration...\n";
    if (class_exists('Laravel\Sanctum\Sanctum')) {
        echo "   ✅ Sanctum is installed\n";
    } else {
        echo "   ❌ Sanctum is not installed\n";
    }

    // Test 3: Check routes
    echo "\n3. Testing Routes...\n";
    $router = app('router');
    $routes = $router->getRoutes();
    
    $healthRouteFound = false;
    $apiRoutesFound = 0;
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (str_contains($uri, 'health')) {
            $healthRouteFound = true;
        }
        if (str_contains($uri, 'api/v1')) {
            $apiRoutesFound++;
        }
    }
    
    if ($healthRouteFound) {
        echo "   ✅ Health check route found\n";
    } else {
        echo "   ❌ Health check route not found\n";
    }
    
    echo "   ✅ Found $apiRoutesFound API routes\n";

    // Test 4: Test middleware
    echo "\n4. Testing Middleware...\n";
    $middleware = app('router')->getMiddleware();
    
    if (isset($middleware['auth'])) {
        echo "   ✅ Auth middleware is registered\n";
    }
    
    if (class_exists('App\Http\Middleware\KYCMiddleware')) {
        echo "   ✅ KYC middleware exists\n";
    }
    
    if (class_exists('App\Http\Middleware\ComplianceMiddleware')) {
        echo "   ✅ Compliance middleware exists\n";
    }

    echo "\n🎉 Auth Guard Fix Test Completed!\n";
    echo "✅ All authentication components are properly configured\n";
    echo "🌐 You can now access: http://localhost:8000\n";
    echo "🔍 Health check: http://localhost:8000/api/health\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
