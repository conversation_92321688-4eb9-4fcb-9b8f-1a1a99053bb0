<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Currency;
use App\Models\Country;

class CreateTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->status === 'active';
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $user = auth()->user();
        
        return [
            'type' => [
                'required',
                'string',
                Rule::in(['transfer', 'deposit', 'withdrawal'])
            ],
            'amount' => [
                'required',
                'numeric',
                'min:10',
                'max:' . ($user->daily_limit ?? 50000),
                function ($attribute, $value, $fail) {
                    if ($value <= 0) {
                        $fail('The amount must be greater than zero.');
                    }
                    
                    // Check if amount exceeds available balance for transfers/withdrawals
                    if (in_array($this->type, ['transfer', 'withdrawal'])) {
                        $wallet = $user->wallets()
                            ->where('currency_id', $this->currency_id)
                            ->first();
                        
                        if (!$wallet || $wallet->balance < $value) {
                            $fail('Insufficient balance for this transaction.');
                        }
                    }
                }
            ],
            'currency_id' => [
                'required',
                'integer',
                'exists:currencies,id',
                function ($attribute, $value, $fail) {
                    $currency = Currency::find($value);
                    if (!$currency || !$currency->is_active) {
                        $fail('The selected currency is not available.');
                    }
                }
            ],
            'receiver_name' => [
                'required_if:type,transfer',
                'string',
                'max:255',
                'regex:/^[\p{L}\p{M}\s\-\.\']+$/u' // Unicode letters, marks, spaces, hyphens, dots, apostrophes
            ],
            'receiver_phone' => [
                'required_if:type,transfer',
                'string',
                'regex:/^\+[1-9]\d{1,14}$/', // International phone format
                function ($attribute, $value, $fail) {
                    if ($this->type === 'transfer' && $value === auth()->user()->phone) {
                        $fail('You cannot send money to yourself.');
                    }
                }
            ],
            'receiver_email' => [
                'nullable',
                'email',
                'max:255'
            ],
            'receiver_country_id' => [
                'required_if:type,transfer',
                'integer',
                'exists:countries,id',
                function ($attribute, $value, $fail) {
                    $country = Country::find($value);
                    if (!$country || !$country->is_active) {
                        $fail('The selected country is not available for transfers.');
                    }
                }
            ],
            'payment_method' => [
                'required',
                'string',
                Rule::in(['wallet', 'bank_transfer', 'credit_card', 'debit_card', 'paypal', 'crypto'])
            ],
            'payment_gateway_id' => [
                'nullable',
                'integer',
                'exists:payment_gateways,id',
                'required_if:payment_method,credit_card,debit_card,paypal'
            ],
            'bank_account' => [
                'required_if:type,withdrawal',
                'string',
                'max:50',
                'regex:/^[A-Z0-9]+$/' // Alphanumeric bank account format
            ],
            'bank_name' => [
                'required_if:type,withdrawal',
                'string',
                'max:255'
            ],
            'bank_swift_code' => [
                'nullable',
                'string',
                'size:8,11',
                'regex:/^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/' // SWIFT code format
            ],
            'notes' => [
                'nullable',
                'string',
                'max:500'
            ],
            'purpose' => [
                'nullable',
                'string',
                Rule::in([
                    'family_support',
                    'business',
                    'education',
                    'medical',
                    'investment',
                    'personal',
                    'other'
                ])
            ],
            'reference' => [
                'nullable',
                'string',
                'max:100',
                'unique:transactions,external_reference'
            ],
            'scheduled_at' => [
                'nullable',
                'date',
                'after:now',
                'before:' . now()->addMonths(6)->toDateString()
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'نوع المعاملة مطلوب.',
            'type.in' => 'نوع المعاملة غير صحيح.',
            'amount.required' => 'المبلغ مطلوب.',
            'amount.numeric' => 'المبلغ يجب أن يكون رقماً.',
            'amount.min' => 'الحد الأدنى للمبلغ هو :min.',
            'amount.max' => 'المبلغ يتجاوز الحد اليومي المسموح.',
            'currency_id.required' => 'العملة مطلوبة.',
            'currency_id.exists' => 'العملة المحددة غير موجودة.',
            'receiver_name.required_if' => 'اسم المستلم مطلوب للتحويلات.',
            'receiver_name.regex' => 'اسم المستلم يحتوي على أحرف غير صحيحة.',
            'receiver_phone.required_if' => 'رقم هاتف المستلم مطلوب للتحويلات.',
            'receiver_phone.regex' => 'تنسيق رقم الهاتف غير صحيح. يجب أن يبدأ بـ + ورمز الدولة.',
            'receiver_email.email' => 'تنسيق البريد الإلكتروني غير صحيح.',
            'receiver_country_id.required_if' => 'دولة المستلم مطلوبة للتحويلات.',
            'receiver_country_id.exists' => 'الدولة المحددة غير موجودة.',
            'payment_method.required' => 'طريقة الدفع مطلوبة.',
            'payment_method.in' => 'طريقة الدفع غير صحيحة.',
            'payment_gateway_id.required_if' => 'بوابة الدفع مطلوبة لهذه الطريقة.',
            'bank_account.required_if' => 'رقم الحساب البنكي مطلوب للسحب.',
            'bank_account.regex' => 'تنسيق رقم الحساب البنكي غير صحيح.',
            'bank_name.required_if' => 'اسم البنك مطلوب للسحب.',
            'bank_swift_code.regex' => 'تنسيق رمز SWIFT غير صحيح.',
            'notes.max' => 'الملاحظات لا يجب أن تتجاوز :max حرف.',
            'purpose.in' => 'الغرض من التحويل غير صحيح.',
            'reference.unique' => 'رقم المرجع مستخدم من قبل.',
            'scheduled_at.after' => 'تاريخ الجدولة يجب أن يكون في المستقبل.',
            'scheduled_at.before' => 'لا يمكن جدولة المعاملة لأكثر من 6 أشهر.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'type' => 'نوع المعاملة',
            'amount' => 'المبلغ',
            'currency_id' => 'العملة',
            'receiver_name' => 'اسم المستلم',
            'receiver_phone' => 'رقم هاتف المستلم',
            'receiver_email' => 'بريد المستلم الإلكتروني',
            'receiver_country_id' => 'دولة المستلم',
            'payment_method' => 'طريقة الدفع',
            'payment_gateway_id' => 'بوابة الدفع',
            'bank_account' => 'رقم الحساب البنكي',
            'bank_name' => 'اسم البنك',
            'bank_swift_code' => 'رمز SWIFT',
            'notes' => 'الملاحظات',
            'purpose' => 'الغرض',
            'reference' => 'رقم المرجع',
            'scheduled_at' => 'تاريخ الجدولة',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check daily transaction limit
            $this->checkDailyLimit($validator);
            
            // Check monthly transaction limit
            $this->checkMonthlyLimit($validator);
            
            // Check KYC requirements for large amounts
            $this->checkKYCRequirements($validator);
            
            // Check if user can perform this type of transaction
            $this->checkTransactionPermissions($validator);
        });
    }

    /**
     * Check daily transaction limit.
     */
    private function checkDailyLimit($validator): void
    {
        $user = auth()->user();
        $today = now()->startOfDay();
        
        $dailyTotal = $user->sentTransactions()
            ->where('created_at', '>=', $today)
            ->where('status', '!=', 'failed')
            ->sum('amount');
        
        $dailyLimit = $user->daily_limit ?? 50000;
        
        if (($dailyTotal + $this->amount) > $dailyLimit) {
            $remaining = max(0, $dailyLimit - $dailyTotal);
            $validator->errors()->add('amount', 
                "المبلغ يتجاوز الحد اليومي. المتبقي: {$remaining}"
            );
        }
    }

    /**
     * Check monthly transaction limit.
     */
    private function checkMonthlyLimit($validator): void
    {
        $user = auth()->user();
        $monthStart = now()->startOfMonth();
        
        $monthlyTotal = $user->sentTransactions()
            ->where('created_at', '>=', $monthStart)
            ->where('status', '!=', 'failed')
            ->sum('amount');
        
        $monthlyLimit = $user->monthly_limit ?? 500000;
        
        if (($monthlyTotal + $this->amount) > $monthlyLimit) {
            $remaining = max(0, $monthlyLimit - $monthlyTotal);
            $validator->errors()->add('amount', 
                "المبلغ يتجاوز الحد الشهري. المتبقي: {$remaining}"
            );
        }
    }

    /**
     * Check KYC requirements for large amounts.
     */
    private function checkKYCRequirements($validator): void
    {
        $user = auth()->user();
        $kycLimit = config('financial.kyc_required_amount', 10000);
        
        if ($this->amount > $kycLimit && !$user->kyc_verified_at) {
            $validator->errors()->add('amount', 
                "المعاملات التي تزيد عن {$kycLimit} تتطلب التحقق من الهوية (KYC)"
            );
        }
    }

    /**
     * Check transaction permissions.
     */
    private function checkTransactionPermissions($validator): void
    {
        $user = auth()->user();
        
        // Check if user can perform withdrawals
        if ($this->type === 'withdrawal' && !$user->can_withdraw) {
            $validator->errors()->add('type', 'حسابك لا يسمح بعمليات السحب');
        }
        
        // Check if user can perform international transfers
        if ($this->type === 'transfer' && 
            $this->receiver_country_id !== $user->country_id && 
            !$user->can_international_transfer) {
            $validator->errors()->add('receiver_country_id', 
                'حسابك لا يسمح بالتحويلات الدولية'
            );
        }
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add calculated fee
        if (!$key) {
            $validated['fee'] = $this->calculateFee();
        }
        
        return $validated;
    }

    /**
     * Calculate transaction fee.
     */
    private function calculateFee(): float
    {
        $feeConfig = config('financial.fees.transaction', []);
        
        $baseFee = $feeConfig['base_fee'] ?? 5.0;
        $percentageFee = $feeConfig['percentage'] ?? 0.01;
        $maxFee = $feeConfig['max_fee'] ?? 100.0;
        $minFee = $feeConfig['min_fee'] ?? 1.0;

        $calculatedFee = $baseFee + ($this->amount * $percentageFee);
        
        // International transfer additional fee
        if ($this->type === 'transfer' && 
            $this->receiver_country_id !== auth()->user()->country_id) {
            $internationalFee = $feeConfig['international_fee'] ?? 10.0;
            $calculatedFee += $internationalFee;
        }

        return max($minFee, min($maxFee, $calculatedFee));
    }
}
