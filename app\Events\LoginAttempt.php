<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LoginAttempt
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ?User $user;
    public string $email;
    public bool $successful;
    public array $attemptData;

    /**
     * Create a new event instance.
     */
    public function __construct(?User $user, string $email, bool $successful, array $attemptData = [])
    {
        $this->user = $user;
        $this->email = $email;
        $this->successful = $successful;
        $this->attemptData = $attemptData;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel('admin.security'),
        ];

        // If login was successful and user exists, also broadcast to user's private channel
        if ($this->successful && $this->user) {
            $channels[] = new PrivateChannel('user.' . $this->user->id);
        }

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'user_id' => $this->user?->id,
            'email' => $this->email,
            'successful' => $this->successful,
            'ip_address' => $this->attemptData['ip_address'] ?? request()->ip(),
            'user_agent' => $this->attemptData['user_agent'] ?? request()->userAgent(),
            'timestamp' => now()->toISOString(),
            'country' => $this->attemptData['country'] ?? null,
            'city' => $this->attemptData['city'] ?? null,
            'device_type' => $this->attemptData['device_type'] ?? null,
            'is_suspicious' => $this->attemptData['is_suspicious'] ?? false,
            'failure_reason' => $this->attemptData['failure_reason'] ?? null,
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return $this->successful ? 'login.successful' : 'login.failed';
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        // Always broadcast failed attempts for security monitoring
        if (!$this->successful) {
            return true;
        }

        // Broadcast successful logins if they're from new devices or suspicious
        return $this->attemptData['is_new_device'] ?? false || 
               $this->attemptData['is_suspicious'] ?? false;
    }
}
