#!/bin/bash

# Money Transfer Application Deployment Script
# This script handles production deployment with zero-downtime

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
VERSION="${2:-latest}"
BACKUP_ENABLED="${BACKUP_ENABLED:-true}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
        log_warning "Initiating rollback..."
        rollback_deployment
    fi
    exit 1
}

# Trap errors
trap 'error_exit "Deployment failed at line $LINENO"' ERR

# Load environment configuration
load_environment() {
    local env_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    
    if [[ ! -f "$env_file" ]]; then
        error_exit "Environment file $env_file not found"
    fi
    
    log_info "Loading environment configuration for $ENVIRONMENT"
    set -a
    source "$env_file"
    set +a
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        error_exit "Docker is not running"
    fi
    
    # Check if required environment variables are set
    local required_vars=(
        "APP_KEY"
        "DB_PASSWORD"
        "REDIS_PASSWORD"
        "STRIPE_SECRET_KEY"
        "PAYPAL_CLIENT_SECRET"
        "JWT_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error_exit "Required environment variable $var is not set"
        fi
    done
    
    # Check disk space
    local available_space=$(df / | awk 'NR==2 {print $4}')
    local required_space=5000000 # 5GB in KB
    
    if [[ $available_space -lt $required_space ]]; then
        error_exit "Insufficient disk space. Required: 5GB, Available: $(($available_space/1024/1024))GB"
    fi
    
    # Check if ports are available
    local ports=(80 443 5432 6379 9090 3000)
    for port in "${ports[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            log_warning "Port $port is already in use"
        fi
    done
    
    log_success "Pre-deployment checks passed"
}

# Create backup
create_backup() {
    if [[ "$BACKUP_ENABLED" != "true" ]]; then
        log_info "Backup disabled, skipping..."
        return
    fi
    
    log_info "Creating backup..."
    
    local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Database backup
    log_info "Backing up database..."
    docker-compose exec -T postgres pg_dump -U "$DB_USERNAME" "$DB_DATABASE" > "$backup_dir/database.sql"
    
    # Application files backup
    log_info "Backing up application files..."
    tar -czf "$backup_dir/storage.tar.gz" -C "$PROJECT_ROOT" storage/
    
    # Configuration backup
    cp "$PROJECT_ROOT/.env.$ENVIRONMENT" "$backup_dir/"
    
    # Store backup path for potential rollback
    echo "$backup_dir" > "$PROJECT_ROOT/.last_backup"
    
    log_success "Backup created at $backup_dir"
}

# Pull latest images
pull_images() {
    log_info "Pulling latest Docker images..."
    
    # Pull base images
    docker pull postgres:15-alpine
    docker pull redis:7-alpine
    docker pull nginx:alpine
    
    # Build application image
    log_info "Building application image..."
    docker-compose build --no-cache app
    
    log_success "Images updated successfully"
}

# Database migration
run_migrations() {
    log_info "Running database migrations..."
    
    # Check if database is accessible
    docker-compose exec -T postgres pg_isready -U "$DB_USERNAME" -d "$DB_DATABASE" || error_exit "Database is not accessible"
    
    # Run migrations
    docker-compose exec -T app php artisan migrate --force
    
    # Seed database if needed
    if [[ "$ENVIRONMENT" != "production" ]]; then
        docker-compose exec -T app php artisan db:seed --force
    fi
    
    log_success "Database migrations completed"
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."
    
    # Stop existing containers gracefully
    log_info "Stopping existing containers..."
    docker-compose down --timeout 30
    
    # Start new containers
    log_info "Starting new containers..."
    docker-compose up -d
    
    # Wait for containers to be ready
    log_info "Waiting for containers to be ready..."
    sleep 30
    
    # Clear application caches
    log_info "Clearing application caches..."
    docker-compose exec -T app php artisan config:cache
    docker-compose exec -T app php artisan route:cache
    docker-compose exec -T app php artisan view:cache
    docker-compose exec -T app php artisan optimize
    
    log_success "Application deployed successfully"
}

# Health checks
run_health_checks() {
    log_info "Running health checks..."
    
    local start_time=$(date +%s)
    local timeout=$HEALTH_CHECK_TIMEOUT
    
    # Check application health
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $timeout ]]; then
            error_exit "Health check timeout after ${timeout}s"
        fi
        
        # Check application endpoint
        if curl -f -s http://localhost/health >/dev/null 2>&1; then
            log_success "Application health check passed"
            break
        fi
        
        log_info "Waiting for application to be ready... (${elapsed}s/${timeout}s)"
        sleep 10
    done
    
    # Check database connectivity
    docker-compose exec -T app php artisan tinker --execute="DB::connection()->getPdo();" || error_exit "Database connectivity check failed"
    
    # Check Redis connectivity
    docker-compose exec -T redis redis-cli ping | grep -q PONG || error_exit "Redis connectivity check failed"
    
    # Check payment gateways
    log_info "Checking payment gateway connectivity..."
    docker-compose exec -T app php artisan payment:test-gateways || log_warning "Payment gateway check failed"
    
    # Check queue workers
    docker-compose exec -T app php artisan queue:monitor || log_warning "Queue worker check failed"
    
    log_success "All health checks passed"
}

# Post-deployment tasks
post_deployment_tasks() {
    log_info "Running post-deployment tasks..."
    
    # Warm up application cache
    log_info "Warming up application cache..."
    curl -s http://localhost/ >/dev/null || true
    curl -s http://localhost/api/health >/dev/null || true
    
    # Send deployment notification
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        log_info "Sending deployment notification..."
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ Money Transfer App deployed successfully to $ENVIRONMENT (version: $VERSION)\"}" \
            "$SLACK_WEBHOOK_URL" || log_warning "Failed to send Slack notification"
    fi
    
    # Update monitoring
    if [[ -f "$PROJECT_ROOT/scripts/update-monitoring.sh" ]]; then
        log_info "Updating monitoring configuration..."
        bash "$PROJECT_ROOT/scripts/update-monitoring.sh" || log_warning "Failed to update monitoring"
    fi
    
    log_success "Post-deployment tasks completed"
}

# Rollback function
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    if [[ ! -f "$PROJECT_ROOT/.last_backup" ]]; then
        log_error "No backup found for rollback"
        return 1
    fi
    
    local backup_dir=$(cat "$PROJECT_ROOT/.last_backup")
    
    if [[ ! -d "$backup_dir" ]]; then
        log_error "Backup directory not found: $backup_dir"
        return 1
    fi
    
    # Stop current containers
    docker-compose down --timeout 30
    
    # Restore database
    log_info "Restoring database..."
    docker-compose up -d postgres
    sleep 10
    docker-compose exec -T postgres psql -U "$DB_USERNAME" -d "$DB_DATABASE" < "$backup_dir/database.sql"
    
    # Restore application files
    log_info "Restoring application files..."
    tar -xzf "$backup_dir/storage.tar.gz" -C "$PROJECT_ROOT"
    
    # Restore configuration
    cp "$backup_dir/.env.$ENVIRONMENT" "$PROJECT_ROOT/"
    
    # Start containers with previous configuration
    docker-compose up -d
    
    log_success "Rollback completed"
}

# Cleanup old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    local backup_retention_days="${BACKUP_RETENTION_DAYS:-7}"
    find "$PROJECT_ROOT/backups" -type d -mtime +$backup_retention_days -exec rm -rf {} + 2>/dev/null || true
    
    # Cleanup old Docker images
    docker image prune -f --filter "until=24h" || true
    
    log_success "Cleanup completed"
}

# Main deployment function
main() {
    log_info "Starting deployment of Money Transfer Application"
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    log_info "Timestamp: $(date)"
    
    cd "$PROJECT_ROOT"
    
    # Load environment
    load_environment
    
    # Run deployment steps
    pre_deployment_checks
    create_backup
    pull_images
    run_migrations
    deploy_application
    run_health_checks
    post_deployment_tasks
    cleanup_old_backups
    
    log_success "Deployment completed successfully!"
    log_info "Application is now running at: $APP_URL"
    log_info "Monitoring dashboard: http://localhost:3000"
    log_info "Logs: docker-compose logs -f"
}

# Script usage
usage() {
    echo "Usage: $0 [environment] [version]"
    echo ""
    echo "Arguments:"
    echo "  environment    Target environment (production, staging, development) [default: production]"
    echo "  version        Application version to deploy [default: latest]"
    echo ""
    echo "Environment variables:"
    echo "  BACKUP_ENABLED         Enable/disable backup creation [default: true]"
    echo "  HEALTH_CHECK_TIMEOUT   Health check timeout in seconds [default: 300]"
    echo "  ROLLBACK_ON_FAILURE    Enable automatic rollback on failure [default: true]"
    echo "  BACKUP_RETENTION_DAYS  Number of days to keep backups [default: 7]"
    echo ""
    echo "Examples:"
    echo "  $0                           # Deploy to production with latest version"
    echo "  $0 staging v1.2.3           # Deploy version v1.2.3 to staging"
    echo "  BACKUP_ENABLED=false $0     # Deploy without creating backup"
}

# Handle script arguments
case "${1:-}" in
    -h|--help)
        usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
