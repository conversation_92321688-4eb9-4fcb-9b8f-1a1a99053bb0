<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class KYCDocument extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'kyc_documents';

    protected $fillable = [
        'user_id',
        'document_type',
        'document_number',
        'file_path',
        'file_name',
        'file_size',
        'mime_type',
        'status',
        'verified_at',
        'verified_by',
        'rejection_reason',
        'expiry_date',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'verified_at' => 'datetime',
        'expiry_date' => 'date',
        'metadata' => 'array',
        'file_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'pending',
    ];

    /**
     * Get the user that owns the document.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who verified this document.
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope for documents by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Scope for documents by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for pending documents.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved documents.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected documents.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for expired documents.
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    /**
     * Scope for expiring soon documents.
     */
    public function scopeExpiringSoon($query, int $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>', now());
    }

    /**
     * Get file URL.
     */
    public function getFileUrlAttribute(): ?string
    {
        if (!$this->file_path) {
            return null;
        }

        return Storage::url($this->file_path);
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get document type label.
     */
    public function getDocumentTypeLabelAttribute(): array
    {
        return match($this->document_type) {
            'national_id' => ['ar' => 'الهوية الوطنية', 'en' => 'National ID'],
            'passport' => ['ar' => 'جواز السفر', 'en' => 'Passport'],
            'driving_license' => ['ar' => 'رخصة القيادة', 'en' => 'Driving License'],
            'utility_bill' => ['ar' => 'فاتورة خدمات', 'en' => 'Utility Bill'],
            'bank_statement' => ['ar' => 'كشف حساب بنكي', 'en' => 'Bank Statement'],
            'address_proof' => ['ar' => 'إثبات العنوان', 'en' => 'Address Proof'],
            'income_proof' => ['ar' => 'إثبات الدخل', 'en' => 'Income Proof'],
            'business_license' => ['ar' => 'رخصة تجارية', 'en' => 'Business License'],
            default => ['ar' => 'مستند آخر', 'en' => 'Other Document']
        };
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): array
    {
        return match($this->status) {
            'pending' => ['ar' => 'قيد المراجعة', 'en' => 'Pending Review'],
            'approved' => ['ar' => 'مقبول', 'en' => 'Approved'],
            'rejected' => ['ar' => 'مرفوض', 'en' => 'Rejected'],
            'expired' => ['ar' => 'منتهي الصلاحية', 'en' => 'Expired'],
            default => ['ar' => 'غير معروف', 'en' => 'Unknown']
        ];
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'yellow',
            'approved' => 'green',
            'rejected' => 'red',
            'expired' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Check if document is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if document is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if document is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if document is expired.
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * Check if document is expiring soon.
     */
    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->expiry_date && 
               $this->expiry_date->isBefore(now()->addDays($days)) && 
               $this->expiry_date->isFuture();
    }

    /**
     * Approve document.
     */
    public function approve(int $verifierId, string $notes = null): bool
    {
        return $this->update([
            'status' => 'approved',
            'verified_at' => now(),
            'verified_by' => $verifierId,
            'notes' => $notes,
            'rejection_reason' => null,
        ]);
    }

    /**
     * Reject document.
     */
    public function reject(int $verifierId, string $reason, string $notes = null): bool
    {
        return $this->update([
            'status' => 'rejected',
            'verified_at' => now(),
            'verified_by' => $verifierId,
            'rejection_reason' => $reason,
            'notes' => $notes,
        ]);
    }

    /**
     * Mark as expired.
     */
    public function markAsExpired(): bool
    {
        return $this->update(['status' => 'expired']);
    }

    /**
     * Get file extension.
     */
    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->file_name, PATHINFO_EXTENSION);
    }

    /**
     * Check if file is image.
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if file is PDF.
     */
    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    /**
     * Delete file from storage.
     */
    public function deleteFile(): bool
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            return Storage::delete($this->file_path);
        }

        return true;
    }

    /**
     * Get documents statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_documents' => static::count(),
            'pending_documents' => static::pending()->count(),
            'approved_documents' => static::approved()->count(),
            'rejected_documents' => static::rejected()->count(),
            'expired_documents' => static::expired()->count(),
            'expiring_soon' => static::expiringSoon()->count(),
        ];
    }

    /**
     * Clean up expired documents.
     */
    public static function cleanupExpired(): int
    {
        $expiredDocuments = static::expired()->get();
        $deletedCount = 0;

        foreach ($expiredDocuments as $document) {
            $document->deleteFile();
            $document->delete();
            $deletedCount++;
        }

        return $deletedCount;
    }
}
