<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected User $user;
    protected array $notificationData;
    protected ?int $notificationId;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, array $notificationData, ?int $notificationId = null)
    {
        $this->user = $user;
        $this->notificationData = $notificationData;
        $this->notificationId = $notificationId;
    }

    /**
     * Execute the job.
     */
    public function handle(NotificationService $notificationService): void
    {
        try {
            // Send notification through the service
            $success = $notificationService->sendNotification($this->user, $this->notificationData);

            if ($success) {
                Log::info('Notification sent successfully', [
                    'user_id' => $this->user->id,
                    'type' => $this->notificationData['type'] ?? 'unknown',
                    'channels' => $this->notificationData['channels'] ?? ['database'],
                ]);

                // Mark notification as sent if we have an ID
                if ($this->notificationId) {
                    $notification = Notification::find($this->notificationId);
                    if ($notification) {
                        $notification->markAsSent();
                    }
                }
            } else {
                Log::error('Failed to send notification', [
                    'user_id' => $this->user->id,
                    'notification_data' => $this->notificationData,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('SendNotificationJob failed', [
                'user_id' => $this->user->id,
                'notification_id' => $this->notificationId,
                'error' => $e->getMessage(),
                'data' => $this->notificationData,
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SendNotificationJob failed permanently', [
            'user_id' => $this->user->id,
            'notification_id' => $this->notificationId,
            'data' => $this->notificationData,
            'error' => $exception->getMessage(),
        ]);

        // Mark notification as failed if we have an ID
        if ($this->notificationId) {
            $notification = Notification::find($this->notificationId);
            if ($notification) {
                $notification->update([
                    'metadata' => array_merge($notification->metadata ?? [], [
                        'send_failed' => true,
                        'error_message' => $exception->getMessage(),
                        'failed_at' => now()->toISOString(),
                    ]),
                ]);
            }
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'notification',
            'user:' . $this->user->id,
            'type:' . ($this->notificationData['type'] ?? 'unknown'),
            'priority:' . ($this->notificationData['priority'] ?? 'normal'),
        ];
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        $priority = $this->notificationData['priority'] ?? 'normal';
        
        return match ($priority) {
            'critical' => now()->addMinutes(10),
            'high' => now()->addMinutes(15),
            'normal' => now()->addMinutes(30),
            'low' => now()->addHour(),
            default => now()->addMinutes(30),
        };
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        $priority = $this->notificationData['priority'] ?? 'normal';
        
        return match ($priority) {
            'critical' => [5, 15, 30],      // Retry quickly for critical notifications
            'high' => [10, 30, 60],         // Standard retry for high priority
            'normal' => [30, 60, 120],      // Normal retry intervals
            'low' => [60, 300, 600],        // Slower retry for low priority
            default => [30, 60, 120],
        };
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        $priority = $this->notificationData['priority'] ?? 'normal';
        
        // You can add rate limiting middleware here based on priority
        return [];
    }

    /**
     * Determine if the job should be retried.
     */
    public function shouldRetry(\Throwable $exception): bool
    {
        // Don't retry for certain types of exceptions
        $nonRetryableExceptions = [
            \InvalidArgumentException::class,
            \BadMethodCallException::class,
        ];

        foreach ($nonRetryableExceptions as $exceptionClass) {
            if ($exception instanceof $exceptionClass) {
                return false;
            }
        }

        return true;
    }
}
