<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Country;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransactionSystemTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Country $saudiArabia;
    protected Country $unitedStates;
    protected Currency $usd;
    protected Currency $sar;
    protected ExchangeRate $exchangeRate;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create countries
        $this->saudiArabia = Country::create([
            'name_ar' => 'المملكة العربية السعودية',
            'name_en' => 'Saudi Arabia',
            'code' => 'SAU',
            'iso2' => 'SA',
            'phone_code' => '+966',
            'currency_code' => 'SAR',
            'is_active' => true,
            'supports_transfers' => true,
        ]);

        $this->unitedStates = Country::create([
            'name_ar' => 'الولايات المتحدة الأمريكية',
            'name_en' => 'United States',
            'code' => 'USA',
            'iso2' => 'US',
            'phone_code' => '+1',
            'currency_code' => 'USD',
            'is_active' => true,
            'supports_transfers' => true,
        ]);

        // Create currencies
        $this->usd = Currency::create([
            'code' => 'USD',
            'name_ar' => 'دولار أمريكي',
            'name_en' => 'US Dollar',
            'symbol' => '$',
            'is_active' => true,
            'is_base_currency' => true,
            'rate_to_usd' => 1.0,
        ]);

        $this->sar = Currency::create([
            'code' => 'SAR',
            'name_ar' => 'ريال سعودي',
            'name_en' => 'Saudi Riyal',
            'symbol' => 'ر.س',
            'is_active' => true,
            'is_base_currency' => false,
            'rate_to_usd' => 0.27,
        ]);

        // Create exchange rate
        $this->exchangeRate = ExchangeRate::create([
            'from_currency_id' => $this->usd->id,
            'to_currency_id' => $this->sar->id,
            'rate' => 3.75,
            'buy_rate' => 3.77,
            'sell_rate' => 3.73,
            'spread' => 0.04,
            'source' => 'manual',
            'is_active' => true,
            'last_updated' => now(),
        ]);

        // Create user
        $this->user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => bcrypt('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->saudiArabia->id,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'daily_limit' => 50000,
            'monthly_limit' => 500000,
        ]);
    }

    public function test_user_can_get_exchange_rate_quote()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions/quote?' . http_build_query([
                'amount' => 1000,
                'currency_from' => 'USD',
                'currency_to' => 'SAR',
            ]));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'quote' => [
                            'original_amount',
                            'converted_amount',
                            'rate',
                            'buy_rate',
                            'sell_rate',
                            'fee',
                            'total_amount',
                        ],
                        'valid_until',
                        'quote_id',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $this->assertNotEmpty($response->json('data.quote.converted_amount'));
        $this->assertGreaterThan(0, $response->json('data.quote.fee'));
    }

    public function test_user_can_create_transaction()
    {
        $transactionData = [
            'sender_name' => 'أحمد محمد',
            'sender_phone' => '+966501234567',
            'sender_email' => '<EMAIL>',
            'sender_country_id' => $this->saudiArabia->id,
            'sender_address' => 'الرياض، المملكة العربية السعودية',
            'sender_id_number' => '1234567890',
            'sender_id_type' => 'national_id',
            
            'recipient_name' => 'John Smith',
            'recipient_phone' => '+12345678901',
            'recipient_email' => '<EMAIL>',
            'recipient_country_id' => $this->unitedStates->id,
            'recipient_address' => 'New York, USA',
            
            'amount' => 1000,
            'currency_from' => 'USD',
            'currency_to' => 'SAR',
            'payment_method' => 'cash',
            'delivery_method' => 'cash_pickup',
            'purpose' => 'family_support',
            'notes' => 'Monthly family support',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/transactions', $transactionData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'transaction' => [
                            'id',
                            'transaction_id',
                            'status',
                            'amount',
                            'fee',
                            'total_amount',
                            'recipient_amount',
                            'currency_from',
                            'currency_to',
                            'sender',
                            'recipient',
                            'payment_method',
                            'delivery_method',
                        ],
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'amount' => 1000,
            'currency_from' => 'USD',
            'currency_to' => 'SAR',
            'status' => 'pending',
        ]);
    }

    public function test_transaction_creation_validates_required_fields()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/transactions', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'sender_name',
                    'sender_phone',
                    'sender_country_id',
                    'sender_address',
                    'sender_id_number',
                    'sender_id_type',
                    'recipient_name',
                    'recipient_phone',
                    'recipient_country_id',
                    'recipient_address',
                    'amount',
                    'currency_from',
                    'currency_to',
                    'payment_method',
                    'delivery_method',
                    'purpose',
                ]);
    }

    public function test_user_can_view_their_transactions()
    {
        // Create some transactions
        Transaction::factory()->count(3)->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'transactions' => [
                            '*' => [
                                'id',
                                'transaction_id',
                                'status',
                                'amount',
                                'currency_from',
                                'currency_to',
                                'created_at',
                            ],
                        ],
                        'pagination',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $this->assertCount(3, $response->json('data.transactions'));
    }

    public function test_user_can_filter_transactions_by_status()
    {
        // Create transactions with different statuses
        Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions?status=pending');

        $response->assertStatus(200);
        
        $transactions = $response->json('data.transactions');
        $this->assertCount(1, $transactions);
        $this->assertEquals('pending', $transactions[0]['status']);
    }

    public function test_user_can_view_specific_transaction()
    {
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'transaction_id' => 'MT20241201ABC123',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions/MT20241201ABC123');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'transaction',
                        'tracking',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'transaction' => [
                            'transaction_id' => 'MT20241201ABC123',
                        ],
                    ],
                ]);
    }

    public function test_user_cannot_view_other_users_transactions()
    {
        $otherUser = User::factory()->create();
        $transaction = Transaction::factory()->create([
            'user_id' => $otherUser->id,
            'transaction_id' => 'MT20241201XYZ789',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions/MT20241201XYZ789');

        $response->assertStatus(404);
    }

    public function test_user_can_cancel_pending_transaction()
    {
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
            'transaction_id' => 'MT20241201CANCEL',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/transactions/MT20241201CANCEL/cancel', [
                'reason' => 'Changed my mind',
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ]);

        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'status' => 'cancelled',
            'cancellation_reason' => 'Changed my mind',
        ]);
    }

    public function test_user_can_get_transaction_statistics()
    {
        // Create some completed transactions
        Transaction::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
            'amount' => 1000,
            'created_at' => now()->subDays(5),
        ]);

        Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
            'amount' => 500,
            'created_at' => now()->subDays(2),
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions/statistics?period=month');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'period',
                        'start_date',
                        'end_date',
                        'statistics',
                        'top_currencies',
                        'top_countries',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $stats = $response->json('data.statistics');
        $this->assertEquals(3, $stats->total_transactions);
        $this->assertEquals(2, $stats->completed_transactions);
        $this->assertEquals(1, $stats->pending_transactions);
    }

    public function test_user_can_get_supported_options()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions/supported-options');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'countries',
                        'currencies',
                        'payment_methods',
                        'delivery_methods',
                        'purposes',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['countries']);
        $this->assertNotEmpty($data['currencies']);
        $this->assertNotEmpty($data['payment_methods']);
    }

    public function test_transaction_creation_fails_with_invalid_currency()
    {
        $transactionData = [
            'sender_name' => 'Test User',
            'sender_phone' => '+966501234567',
            'sender_country_id' => $this->saudiArabia->id,
            'sender_address' => 'Test Address',
            'sender_id_number' => '1234567890',
            'sender_id_type' => 'national_id',
            'recipient_name' => 'Recipient',
            'recipient_phone' => '+12345678901',
            'recipient_country_id' => $this->unitedStates->id,
            'recipient_address' => 'Recipient Address',
            'amount' => 1000,
            'currency_from' => 'INVALID',
            'currency_to' => 'SAR',
            'payment_method' => 'cash',
            'delivery_method' => 'cash_pickup',
            'purpose' => 'family_support',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/transactions', $transactionData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['currency_from']);
    }

    public function test_quote_validation_requires_valid_parameters()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/transactions/quote');

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['amount', 'currency_from', 'currency_to']);
    }
}
