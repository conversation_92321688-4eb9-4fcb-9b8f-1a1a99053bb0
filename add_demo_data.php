<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Transaction;
use App\Models\Recipient;
use App\Models\Country;
use Illuminate\Support\Facades\Hash;

try {
    echo "🚀 Adding Demo Data for Dashboard\n";
    echo "=================================\n\n";
    
    // Get current user
    $user = User::where('email', '<EMAIL>')->first();
    if (!$user) {
        echo "❌ Admin user not found!\n";
        exit(1);
    }
    
    // Get or create Saudi Arabia
    $saudiArabia = Country::firstOrCreate(
        ['code' => 'SA'],
        [
            'name_en' => 'Saudi Arabia',
            'name_ar' => 'السعودية',
            'iso2' => 'SA',
            'iso3' => 'SAU',
            'currency_code' => 'SAR',
            'phone_code' => '+966',
            'is_active' => true
        ]
    );
    
    // Get or create USA
    $usa = Country::firstOrCreate(
        ['code' => 'US'],
        [
            'name_en' => 'United States',
            'name_ar' => 'الولايات المتحدة',
            'iso2' => 'US',
            'iso3' => 'USA',
            'currency_code' => 'USD',
            'phone_code' => '+1',
            'is_active' => true
        ]
    );
    
    echo "✅ Countries created/found\n";
    
    // Create demo recipients
    $recipients = [
        ['أحمد محمد', '<EMAIL>', '+1234567890'],
        ['فاطمة علي', '<EMAIL>', '+1234567891'],
        ['محمد عبدالله', '<EMAIL>', '+1234567892'],
        ['سارة أحمد', '<EMAIL>', '+1234567893'],
        ['علي حسن', '<EMAIL>', '+1234567894'],
    ];
    
    foreach ($recipients as $recipientData) {
        $names = explode(' ', $recipientData[0]);
        Recipient::firstOrCreate([
            'user_id' => $user->id,
            'email' => $recipientData[1],
        ], [
            'first_name' => $names[0],
            'last_name' => $names[1] ?? '',
            'phone' => $recipientData[2],
            'country_id' => $usa->id,
            'relationship' => ['family', 'friend', 'business'][rand(0, 2)],
            'is_active' => true,
        ]);
    }
    
    echo "✅ Recipients created\n";
    
    // Create demo transactions
    $recipients = Recipient::where('user_id', $user->id)->get();
    $statuses = ['completed', 'pending', 'processing'];
    $paymentMethods = ['bank_transfer', 'credit_card', 'debit_card'];
    
    for ($i = 1; $i <= 15; $i++) {
        $recipient = $recipients->random();
        $amount = rand(100, 2000);
        $fee = $amount * 0.02;
        $status = $statuses[array_rand($statuses)];
        $createdAt = now()->subDays(rand(0, 30))->subHours(rand(0, 23));
        
        Transaction::firstOrCreate([
            'transaction_id' => 'TXN' . str_pad($i, 6, '0', STR_PAD_LEFT),
        ], [
            'user_id' => $user->id,
            'recipient_id' => $recipient->id,
            'sender_name' => $user->first_name . ' ' . $user->last_name,
            'sender_phone' => $user->phone,
            'sender_email' => $user->email,
            'sender_country_id' => $saudiArabia->id,
            'recipient_name' => $recipient->first_name . ' ' . $recipient->last_name,
            'recipient_phone' => $recipient->phone,
            'recipient_email' => $recipient->email,
            'recipient_country_id' => $recipient->country_id,
            'amount' => $amount,
            'currency_from' => 'SAR',
            'currency_to' => 'USD',
            'exchange_rate' => 3.75,
            'fee' => $fee,
            'total_amount' => $amount + $fee,
            'payment_method' => $paymentMethods[array_rand($paymentMethods)],
            'purpose' => ['family_support', 'education', 'business', 'personal'][rand(0, 3)],
            'status' => $status,
            'notes' => 'تحويل تجريبي للاختبار',
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
            'completed_at' => $status === 'completed' ? $createdAt->addMinutes(rand(5, 60)) : null,
        ]);
    }
    
    echo "✅ Transactions created\n";
    
    // Summary
    $stats = [
        'total_transactions' => Transaction::where('user_id', $user->id)->count(),
        'completed_transactions' => Transaction::where('user_id', $user->id)->where('status', 'completed')->count(),
        'pending_transactions' => Transaction::where('user_id', $user->id)->where('status', 'pending')->count(),
        'total_amount' => Transaction::where('user_id', $user->id)->where('status', 'completed')->sum('amount'),
        'recipients_count' => Recipient::where('user_id', $user->id)->count(),
    ];
    
    echo "\n📊 Demo Data Summary:\n";
    echo "====================\n";
    echo "👤 User: {$user->email}\n";
    echo "🏦 Countries: " . Country::count() . "\n";
    echo "👥 Recipients: {$stats['recipients_count']}\n";
    echo "💸 Total Transactions: {$stats['total_transactions']}\n";
    echo "✅ Completed: {$stats['completed_transactions']}\n";
    echo "⏳ Pending: {$stats['pending_transactions']}\n";
    echo "💰 Total Amount: " . number_format($stats['total_amount'], 2) . " SAR\n";
    
    echo "\n🌐 Test Dashboard:\n";
    echo "==================\n";
    echo "🔗 Dashboard: http://localhost:8000/dashboard\n";
    echo "👤 Login: <EMAIL> / password123\n";
    
    echo "\n✅ Demo data added successfully!\n";
    echo "🚀 Dashboard should now load fast with real data!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
