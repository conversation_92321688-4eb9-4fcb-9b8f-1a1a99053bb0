<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Sender Information
            'sender_name' => ['required', 'string', 'max:100', 'regex:/^[\p{L}\s\-\.\']+$/u'],
            'sender_phone' => ['required', 'string', 'regex:/^\+[1-9]\d{1,14}$/'],
            'sender_email' => ['nullable', 'email', 'max:255'],
            'sender_country_id' => ['required', 'integer', 'exists:countries,id'],
            'sender_address' => ['required', 'string', 'max:255'],
            'sender_id_number' => ['required', 'string', 'max:50'],
            'sender_id_type' => ['required', 'string', 'in:passport,national_id,driving_license'],

            // Recipient Information
            'recipient_name' => ['required', 'string', 'max:100', 'regex:/^[\p{L}\s\-\.\']+$/u'],
            'recipient_phone' => ['required', 'string', 'regex:/^\+[1-9]\d{1,14}$/'],
            'recipient_email' => ['nullable', 'email', 'max:255'],
            'recipient_country_id' => ['required', 'integer', 'exists:countries,id'],
            'recipient_address' => ['required', 'string', 'max:255'],
            'recipient_bank_name' => ['nullable', 'string', 'max:100'],
            'recipient_bank_account' => ['nullable', 'string', 'max:50'],
            'recipient_bank_code' => ['nullable', 'string', 'max:20'],

            // Transaction Details
            'amount' => ['required', 'numeric', 'min:1', 'max:1000000'],
            'currency_from' => ['required', 'string', 'size:3', 'exists:currencies,code'],
            'currency_to' => ['required', 'string', 'size:3', 'exists:currencies,code'],
            'payment_method' => ['required', 'string', 'in:cash,bank_transfer,card,wallet,crypto'],
            'delivery_method' => ['required', 'string', 'in:cash_pickup,bank_deposit,mobile_wallet,crypto_wallet'],
            'purpose' => ['required', 'string', 'in:family_support,business,education,medical,investment,other'],
            'notes' => ['nullable', 'string', 'max:500'],
            'priority' => ['nullable', 'string', 'in:normal,high,urgent'],

            // Additional Information
            'relationship_to_recipient' => ['nullable', 'string', 'max:50'],
            'source_of_funds' => ['nullable', 'string', 'in:salary,business,investment,savings,gift,other'],
            'expected_delivery_date' => ['nullable', 'date', 'after:today'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // Sender Messages
            'sender_name.required' => 'اسم المرسل مطلوب',
            'sender_name.regex' => 'اسم المرسل يجب أن يحتوي على أحرف فقط',
            'sender_phone.required' => 'رقم هاتف المرسل مطلوب',
            'sender_phone.regex' => 'رقم هاتف المرسل غير صحيح',
            'sender_country_id.required' => 'دولة المرسل مطلوبة',
            'sender_country_id.exists' => 'دولة المرسل غير صحيحة',
            'sender_address.required' => 'عنوان المرسل مطلوب',
            'sender_id_number.required' => 'رقم هوية المرسل مطلوب',
            'sender_id_type.required' => 'نوع هوية المرسل مطلوب',
            'sender_id_type.in' => 'نوع هوية المرسل غير صحيح',

            // Recipient Messages
            'recipient_name.required' => 'اسم المستلم مطلوب',
            'recipient_name.regex' => 'اسم المستلم يجب أن يحتوي على أحرف فقط',
            'recipient_phone.required' => 'رقم هاتف المستلم مطلوب',
            'recipient_phone.regex' => 'رقم هاتف المستلم غير صحيح',
            'recipient_country_id.required' => 'دولة المستلم مطلوبة',
            'recipient_country_id.exists' => 'دولة المستلم غير صحيحة',
            'recipient_address.required' => 'عنوان المستلم مطلوب',

            // Transaction Messages
            'amount.required' => 'مبلغ التحويل مطلوب',
            'amount.numeric' => 'مبلغ التحويل يجب أن يكون رقماً',
            'amount.min' => 'مبلغ التحويل يجب أن يكون أكبر من 1',
            'amount.max' => 'مبلغ التحويل يجب أن يكون أقل من 1,000,000',
            'currency_from.required' => 'عملة الإرسال مطلوبة',
            'currency_from.exists' => 'عملة الإرسال غير صحيحة',
            'currency_to.required' => 'عملة الاستلام مطلوبة',
            'currency_to.exists' => 'عملة الاستلام غير صحيحة',
            'payment_method.required' => 'طريقة الدفع مطلوبة',
            'payment_method.in' => 'طريقة الدفع غير صحيحة',
            'delivery_method.required' => 'طريقة الاستلام مطلوبة',
            'delivery_method.in' => 'طريقة الاستلام غير صحيحة',
            'purpose.required' => 'غرض التحويل مطلوب',
            'purpose.in' => 'غرض التحويل غير صحيح',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'sender_name' => 'اسم المرسل',
            'sender_phone' => 'رقم هاتف المرسل',
            'sender_email' => 'بريد المرسل الإلكتروني',
            'sender_country_id' => 'دولة المرسل',
            'sender_address' => 'عنوان المرسل',
            'sender_id_number' => 'رقم هوية المرسل',
            'sender_id_type' => 'نوع هوية المرسل',
            'recipient_name' => 'اسم المستلم',
            'recipient_phone' => 'رقم هاتف المستلم',
            'recipient_email' => 'بريد المستلم الإلكتروني',
            'recipient_country_id' => 'دولة المستلم',
            'recipient_address' => 'عنوان المستلم',
            'recipient_bank_name' => 'اسم البنك',
            'recipient_bank_account' => 'رقم الحساب البنكي',
            'recipient_bank_code' => 'رمز البنك',
            'amount' => 'مبلغ التحويل',
            'currency_from' => 'عملة الإرسال',
            'currency_to' => 'عملة الاستلام',
            'payment_method' => 'طريقة الدفع',
            'delivery_method' => 'طريقة الاستلام',
            'purpose' => 'غرض التحويل',
            'notes' => 'ملاحظات',
            'priority' => 'الأولوية',
            'relationship_to_recipient' => 'العلاقة بالمستلم',
            'source_of_funds' => 'مصدر الأموال',
            'expected_delivery_date' => 'تاريخ الاستلام المتوقع',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if currencies are different
            if ($this->currency_from === $this->currency_to) {
                $validator->errors()->add('currency_to', 'عملة الاستلام يجب أن تكون مختلفة عن عملة الإرسال');
            }

            // Check if sender and recipient are different
            if ($this->sender_phone === $this->recipient_phone) {
                $validator->errors()->add('recipient_phone', 'رقم هاتف المستلم يجب أن يكون مختلفاً عن رقم هاتف المرسل');
            }

            // Validate bank details if delivery method is bank deposit
            if ($this->delivery_method === 'bank_deposit') {
                if (empty($this->recipient_bank_name)) {
                    $validator->errors()->add('recipient_bank_name', 'اسم البنك مطلوب للتحويل البنكي');
                }
                if (empty($this->recipient_bank_account)) {
                    $validator->errors()->add('recipient_bank_account', 'رقم الحساب البنكي مطلوب للتحويل البنكي');
                }
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Validation\ValidationException($validator, response()->json([
            'success' => false,
            'message' => 'بيانات التحويل غير صحيحة',
            'errors' => $validator->errors(),
            'error_code' => 'TRANSACTION_VALIDATION_FAILED',
        ], 422));
    }
}
