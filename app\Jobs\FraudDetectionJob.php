<?php

namespace App\Jobs;

use App\Models\Transaction;
use App\Models\FraudDetection;
use App\Services\FraudDetectionService;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class FraudDetectionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $transactionId;
    protected array $analysisData;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 120; // 2 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(int $transactionId, array $analysisData = [])
    {
        $this->transactionId = $transactionId;
        $this->analysisData = $analysisData;
    }

    /**
     * Execute the job.
     */
    public function handle(FraudDetectionService $fraudService, NotificationService $notificationService): void
    {
        try {
            $transaction = Transaction::with(['sender', 'receiver', 'currency'])->findOrFail($this->transactionId);
            
            Log::info('Starting fraud detection analysis', [
                'transaction_id' => $this->transactionId,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency->code,
                'sender_id' => $transaction->sender_id,
            ]);

            // Perform fraud detection analysis
            $analysis = $fraudService->analyzeTransaction($transaction, $this->analysisData);

            Log::info('Fraud detection analysis completed', [
                'transaction_id' => $this->transactionId,
                'risk_level' => $analysis['risk_level'],
                'risk_score' => $analysis['risk_score'],
                'requires_review' => $analysis['requires_review'],
                'auto_block' => $analysis['auto_block'],
            ]);

            // Update transaction with fraud analysis results
            $transaction->update([
                'risk_score' => $analysis['risk_score'],
                'is_suspicious' => $analysis['requires_review'],
                'fraud_checked' => true,
                'fraud_checked_at' => now(),
            ]);

            // Handle high-risk transactions
            if ($analysis['auto_block']) {
                $this->handleAutoBlock($transaction, $analysis, $notificationService);
            } elseif ($analysis['requires_review']) {
                $this->handleRequiresReview($transaction, $analysis, $notificationService);
            }

            // Create fraud detection record if suspicious
            if ($analysis['requires_review'] || $analysis['auto_block']) {
                $this->createFraudDetectionRecord($transaction, $analysis);
            }

        } catch (\Exception $e) {
            Log::error('Fraud detection job failed', [
                'transaction_id' => $this->transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Mark transaction as fraud check failed
            $transaction = Transaction::find($this->transactionId);
            if ($transaction) {
                $transaction->update([
                    'fraud_checked' => false,
                    'fraud_check_error' => $e->getMessage(),
                ]);
            }

            throw $e;
        }
    }

    /**
     * Handle auto-blocked transactions.
     */
    protected function handleAutoBlock(Transaction $transaction, array $analysis, NotificationService $notificationService): void
    {
        // Block the transaction
        $transaction->update([
            'status' => 'blocked',
            'blocked_at' => now(),
            'block_reason' => 'Automatic fraud detection',
        ]);

        // Send fraud alert to user
        $notificationService->sendFraudAlert($transaction->sender, [
            'transaction_id' => $transaction->id,
            'risk_level' => $analysis['risk_level'],
            'risk_score' => $analysis['risk_score'],
            'reason' => 'Transaction automatically blocked due to high fraud risk',
        ]);

        // Send alert to admin/security team
        $this->sendAdminAlert($transaction, $analysis, 'auto_blocked');

        Log::warning('Transaction auto-blocked due to fraud risk', [
            'transaction_id' => $transaction->id,
            'risk_score' => $analysis['risk_score'],
            'risk_factors' => $analysis['risk_factors'],
        ]);
    }

    /**
     * Handle transactions requiring manual review.
     */
    protected function handleRequiresReview(Transaction $transaction, array $analysis, NotificationService $notificationService): void
    {
        // Mark transaction for review
        $transaction->update([
            'status' => 'under_review',
            'review_required' => true,
            'review_reason' => 'Fraud detection flagged for manual review',
        ]);

        // Send notification to security team
        $this->sendAdminAlert($transaction, $analysis, 'requires_review');

        Log::info('Transaction flagged for manual review', [
            'transaction_id' => $transaction->id,
            'risk_score' => $analysis['risk_score'],
            'risk_factors' => $analysis['risk_factors'],
        ]);
    }

    /**
     * Create fraud detection record.
     */
    protected function createFraudDetectionRecord(Transaction $transaction, array $analysis): FraudDetection
    {
        return FraudDetection::create([
            'transaction_id' => $transaction->id,
            'user_id' => $transaction->sender_id,
            'alert_type' => $this->determineAlertType($analysis['risk_factors']),
            'risk_level' => $analysis['risk_level'],
            'risk_score' => $analysis['risk_score'],
            'description' => $this->buildDescription($analysis),
            'detection_data' => [
                'risk_factors' => $analysis['risk_factors'],
                'analysis_details' => $analysis['details'] ?? [],
                'model_version' => $analysis['model_version'] ?? 'v1.0',
                'analysis_timestamp' => now()->toISOString(),
            ],
            'status' => $analysis['auto_block'] ? 'resolved' : 'open',
            'is_automated' => true,
            'detection_model' => $analysis['model_version'] ?? 'fraud_detection_v1.0',
            'metadata' => [
                'transaction_amount' => $transaction->amount,
                'transaction_currency' => $transaction->currency->code,
                'sender_country' => $transaction->senderCountry?->code,
                'receiver_country' => $transaction->receiverCountry?->code,
            ],
        ]);
    }

    /**
     * Determine alert type based on risk factors.
     */
    protected function determineAlertType(array $riskFactors): string
    {
        // Prioritize alert types based on risk factors
        if (in_array('blacklist_match', $riskFactors)) {
            return 'blacklist_match';
        }
        if (in_array('suspicious_amount', $riskFactors)) {
            return 'suspicious_amount';
        }
        if (in_array('velocity_check', $riskFactors)) {
            return 'velocity_check';
        }
        if (in_array('geographic_anomaly', $riskFactors)) {
            return 'geographic_anomaly';
        }
        if (in_array('unusual_pattern', $riskFactors)) {
            return 'unusual_pattern';
        }
        
        return 'ai_prediction';
    }

    /**
     * Build description for fraud detection record.
     */
    protected function buildDescription(array $analysis): string
    {
        $riskFactors = $analysis['risk_factors'];
        $riskLevel = $analysis['risk_level'];
        $riskScore = $analysis['risk_score'];

        $description = "Fraud detection analysis completed with {$riskLevel} risk level (score: {$riskScore}). ";
        $description .= "Risk factors identified: " . implode(', ', $riskFactors);

        return $description;
    }

    /**
     * Send alert to admin/security team.
     */
    protected function sendAdminAlert(Transaction $transaction, array $analysis, string $alertType): void
    {
        // This would send alerts to admin users or security team
        // For now, we'll just log it
        Log::alert('Fraud detection admin alert', [
            'alert_type' => $alertType,
            'transaction_id' => $transaction->id,
            'sender_id' => $transaction->sender_id,
            'amount' => $transaction->amount,
            'currency' => $transaction->currency->code,
            'risk_level' => $analysis['risk_level'],
            'risk_score' => $analysis['risk_score'],
            'risk_factors' => $analysis['risk_factors'],
        ]);
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('FraudDetectionJob failed permanently', [
            'transaction_id' => $this->transactionId,
            'analysis_data' => $this->analysisData,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Mark transaction as fraud check failed
        $transaction = Transaction::find($this->transactionId);
        if ($transaction) {
            $transaction->update([
                'fraud_checked' => false,
                'fraud_check_error' => $exception->getMessage(),
                'fraud_check_failed_at' => now(),
            ]);
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'fraud-detection',
            'transaction:' . $this->transactionId,
            'security',
            'critical',
        ];
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(10); // Allow retries for up to 10 minutes
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [10, 30, 60]; // Retry after 10s, 30s, then 60s
    }
}
