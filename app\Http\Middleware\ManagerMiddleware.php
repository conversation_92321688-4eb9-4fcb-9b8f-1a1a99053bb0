<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ManagerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required',
                'error_code' => 'AUTH_REQUIRED'
            ], 401);
        }

        $user = auth()->user();

        // Check if user has manager or admin role
        if (!$user->hasRole(['manager', 'admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Manager access required',
                'error_code' => 'MANAGER_ACCESS_REQUIRED'
            ], 403);
        }

        // Check if user account is active
        if ($user->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Account is not active',
                'error_code' => 'ACCOUNT_INACTIVE'
            ], 403);
        }

        // Check if user is verified
        if (!$user->kyc_verified_at) {
            return response()->json([
                'success' => false,
                'message' => 'Account verification required',
                'error_code' => 'VERIFICATION_REQUIRED'
            ], 403);
        }

        // Log manager access
        \Log::info('Manager access granted', [
            'user_id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'route' => $request->route()->getName(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return $next($request);
    }
}
