<?php

namespace App\Services;

use App\Models\User;
use App\Models\Transaction;
use App\Models\AuditLog;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ComplianceService
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Check AML compliance for transaction.
     */
    public function checkAMLCompliance(Transaction $transaction): array
    {
        $checks = [
            'sanctions_screening' => $this->checkSanctionsLists($transaction),
            'pep_screening' => $this->checkPEPLists($transaction),
            'transaction_limits' => $this->checkTransactionLimits($transaction),
            'velocity_checks' => $this->checkVelocityLimits($transaction),
            'geographic_restrictions' => $this->checkGeographicRestrictions($transaction),
            'suspicious_patterns' => $this->checkSuspiciousPatterns($transaction),
        ];

        $overallCompliance = $this->evaluateOverallCompliance($checks);

        // Log compliance check
        AuditLog::create([
            'user_id' => $transaction->sender_id,
            'event_type' => 'aml_compliance_check',
            'action' => 'checked',
            'model_type' => Transaction::class,
            'model_id' => $transaction->id,
            'description' => 'AML compliance check performed',
            'severity' => $overallCompliance['requires_review'] ? 'warning' : 'info',
            'metadata' => [
                'transaction_id' => $transaction->id,
                'compliance_score' => $overallCompliance['score'],
                'checks_performed' => array_keys($checks),
                'failed_checks' => array_keys(array_filter($checks, fn($check) => !$check['passed'])),
            ],
        ]);

        return [
            'passed' => $overallCompliance['passed'],
            'score' => $overallCompliance['score'],
            'requires_review' => $overallCompliance['requires_review'],
            'auto_block' => $overallCompliance['auto_block'],
            'checks' => $checks,
            'recommendations' => $this->getComplianceRecommendations($checks),
        ];
    }

    /**
     * Check sanctions lists.
     */
    protected function checkSanctionsLists(Transaction $transaction): array
    {
        // In a real implementation, this would check against OFAC, UN, EU sanctions lists
        $senderName = $transaction->sender->first_name . ' ' . $transaction->sender->last_name;
        $receiverName = $transaction->receiver_name;

        // Simplified check - in production, use proper sanctions screening API
        $sanctionedNames = [
            // Add sanctioned names/entities
        ];

        $senderMatch = $this->fuzzyNameMatch($senderName, $sanctionedNames);
        $receiverMatch = $this->fuzzyNameMatch($receiverName, $sanctionedNames);

        $passed = !$senderMatch && !$receiverMatch;

        return [
            'passed' => $passed,
            'score' => $passed ? 100 : 0,
            'details' => [
                'sender_match' => $senderMatch,
                'receiver_match' => $receiverMatch,
                'lists_checked' => ['OFAC', 'UN', 'EU'],
            ],
            'risk_level' => $passed ? 'low' : 'critical',
        ];
    }

    /**
     * Check PEP (Politically Exposed Persons) lists.
     */
    protected function checkPEPLists(Transaction $transaction): array
    {
        // In production, integrate with PEP screening services
        $senderName = $transaction->sender->first_name . ' ' . $transaction->sender->last_name;
        
        // Simplified PEP check
        $pepMatch = false; // Would check against PEP databases

        return [
            'passed' => !$pepMatch,
            'score' => $pepMatch ? 70 : 100, // PEP doesn't automatically fail, but requires enhanced due diligence
            'details' => [
                'pep_match' => $pepMatch,
                'enhanced_due_diligence_required' => $pepMatch,
            ],
            'risk_level' => $pepMatch ? 'high' : 'low',
        ];
    }

    /**
     * Check transaction limits.
     */
    protected function checkTransactionLimits(Transaction $transaction): array
    {
        $user = $transaction->sender;
        $amount = $transaction->amount;

        $dailyLimit = $user->daily_limit ?? config('financial.limits.daily_default', 10000);
        $monthlyLimit = $user->monthly_limit ?? config('financial.limits.monthly_default', 100000);

        // Calculate current usage
        $dailyUsage = $this->getDailyTransactionVolume($user);
        $monthlyUsage = $this->getMonthlyTransactionVolume($user);

        $dailyExceeded = ($dailyUsage + $amount) > $dailyLimit;
        $monthlyExceeded = ($monthlyUsage + $amount) > $monthlyLimit;

        $passed = !$dailyExceeded && !$monthlyExceeded;

        return [
            'passed' => $passed,
            'score' => $passed ? 100 : 60,
            'details' => [
                'daily_limit' => $dailyLimit,
                'daily_usage' => $dailyUsage,
                'daily_remaining' => max(0, $dailyLimit - $dailyUsage),
                'daily_exceeded' => $dailyExceeded,
                'monthly_limit' => $monthlyLimit,
                'monthly_usage' => $monthlyUsage,
                'monthly_remaining' => max(0, $monthlyLimit - $monthlyUsage),
                'monthly_exceeded' => $monthlyExceeded,
            ],
            'risk_level' => $passed ? 'low' : 'medium',
        ];
    }

    /**
     * Check velocity limits.
     */
    protected function checkVelocityLimits(Transaction $transaction): array
    {
        $user = $transaction->sender;
        
        // Check transaction frequency
        $recentTransactions = Transaction::where('sender_id', $user->id)
                                        ->where('created_at', '>=', now()->subHour())
                                        ->count();

        $maxTransactionsPerHour = config('compliance.velocity.max_transactions_per_hour', 5);
        $velocityExceeded = $recentTransactions >= $maxTransactionsPerHour;

        return [
            'passed' => !$velocityExceeded,
            'score' => $velocityExceeded ? 40 : 100,
            'details' => [
                'transactions_last_hour' => $recentTransactions,
                'max_allowed_per_hour' => $maxTransactionsPerHour,
                'velocity_exceeded' => $velocityExceeded,
            ],
            'risk_level' => $velocityExceeded ? 'high' : 'low',
        ];
    }

    /**
     * Check geographic restrictions.
     */
    protected function checkGeographicRestrictions(Transaction $transaction): array
    {
        $senderCountry = $transaction->senderCountry;
        $receiverCountry = $transaction->receiverCountry;

        // Check restricted countries
        $restrictedCountries = config('compliance.restricted_countries', []);
        
        $senderRestricted = $senderCountry && in_array($senderCountry->code, $restrictedCountries);
        $receiverRestricted = $receiverCountry && in_array($receiverCountry->code, $restrictedCountries);

        $passed = !$senderRestricted && !$receiverRestricted;

        return [
            'passed' => $passed,
            'score' => $passed ? 100 : 0,
            'details' => [
                'sender_country' => $senderCountry?->code,
                'receiver_country' => $receiverCountry?->code,
                'sender_restricted' => $senderRestricted,
                'receiver_restricted' => $receiverRestricted,
                'restricted_countries' => $restrictedCountries,
            ],
            'risk_level' => $passed ? 'low' : 'critical',
        ];
    }

    /**
     * Check for suspicious patterns.
     */
    protected function checkSuspiciousPatterns(Transaction $transaction): array
    {
        $suspiciousIndicators = [];
        $score = 100;

        // Check for round amounts (potential structuring)
        if ($transaction->amount % 1000 == 0 && $transaction->amount >= 5000) {
            $suspiciousIndicators[] = 'round_amount';
            $score -= 10;
        }

        // Check for frequent transactions to same receiver
        $sameReceiverCount = Transaction::where('sender_id', $transaction->sender_id)
                                       ->where('receiver_phone', $transaction->receiver_phone)
                                       ->where('created_at', '>=', now()->subDays(7))
                                       ->count();

        if ($sameReceiverCount > 5) {
            $suspiciousIndicators[] = 'frequent_same_receiver';
            $score -= 15;
        }

        // Check for unusual timing (late night transactions)
        $hour = $transaction->created_at->hour;
        if ($hour >= 23 || $hour <= 5) {
            $suspiciousIndicators[] = 'unusual_timing';
            $score -= 5;
        }

        $passed = empty($suspiciousIndicators);

        return [
            'passed' => $passed,
            'score' => max(0, $score),
            'details' => [
                'suspicious_indicators' => $suspiciousIndicators,
                'pattern_analysis' => [
                    'round_amount' => $transaction->amount % 1000 == 0,
                    'same_receiver_frequency' => $sameReceiverCount,
                    'transaction_hour' => $hour,
                ],
            ],
            'risk_level' => $score >= 80 ? 'low' : ($score >= 60 ? 'medium' : 'high'),
        ];
    }

    /**
     * Evaluate overall compliance.
     */
    protected function evaluateOverallCompliance(array $checks): array
    {
        $totalScore = 0;
        $maxScore = 0;
        $criticalFailures = 0;

        foreach ($checks as $check) {
            $totalScore += $check['score'];
            $maxScore += 100;
            
            if ($check['risk_level'] === 'critical' && !$check['passed']) {
                $criticalFailures++;
            }
        }

        $overallScore = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 0;

        return [
            'passed' => $criticalFailures === 0 && $overallScore >= 70,
            'score' => $overallScore,
            'requires_review' => $overallScore < 80 || $criticalFailures > 0,
            'auto_block' => $criticalFailures > 0 || $overallScore < 50,
        ];
    }

    /**
     * Get compliance recommendations.
     */
    protected function getComplianceRecommendations(array $checks): array
    {
        $recommendations = [];

        foreach ($checks as $checkName => $check) {
            if (!$check['passed']) {
                $recommendations[] = match ($checkName) {
                    'sanctions_screening' => 'Enhanced due diligence required - potential sanctions match',
                    'pep_screening' => 'Enhanced due diligence required - PEP identified',
                    'transaction_limits' => 'Transaction exceeds user limits - consider limit increase',
                    'velocity_checks' => 'High transaction frequency detected - monitor for unusual activity',
                    'geographic_restrictions' => 'Transaction involves restricted jurisdiction',
                    'suspicious_patterns' => 'Suspicious transaction patterns detected - manual review recommended',
                    default => 'Manual review recommended',
                };
            }
        }

        return $recommendations;
    }

    /**
     * Generate compliance report.
     */
    public function generateComplianceReport(Carbon $startDate, Carbon $endDate): array
    {
        $transactions = Transaction::whereBetween('created_at', [$startDate, $endDate])->get();
        
        $report = [
            'period' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
            'summary' => [
                'total_transactions' => $transactions->count(),
                'compliant_transactions' => 0,
                'flagged_transactions' => 0,
                'blocked_transactions' => 0,
                'manual_reviews' => 0,
            ],
            'compliance_checks' => [
                'sanctions_screening' => ['passed' => 0, 'failed' => 0],
                'pep_screening' => ['passed' => 0, 'failed' => 0],
                'transaction_limits' => ['passed' => 0, 'failed' => 0],
                'velocity_checks' => ['passed' => 0, 'failed' => 0],
                'geographic_restrictions' => ['passed' => 0, 'failed' => 0],
            ],
        ];

        // This would be populated with actual compliance check results
        // For now, returning the structure

        return $report;
    }

    /**
     * Helper methods
     */
    protected function fuzzyNameMatch(string $name, array $sanctionedNames): bool
    {
        // Simplified fuzzy matching - in production, use proper fuzzy matching algorithms
        $name = strtolower(trim($name));
        
        foreach ($sanctionedNames as $sanctionedName) {
            $sanctionedName = strtolower(trim($sanctionedName));
            
            if (similar_text($name, $sanctionedName, $percent) && $percent > 85) {
                return true;
            }
        }
        
        return false;
    }

    protected function getDailyTransactionVolume(User $user): float
    {
        return Transaction::where('sender_id', $user->id)
                         ->whereDate('created_at', today())
                         ->where('status', '!=', 'failed')
                         ->sum('amount');
    }

    protected function getMonthlyTransactionVolume(User $user): float
    {
        return Transaction::where('sender_id', $user->id)
                         ->whereMonth('created_at', now()->month)
                         ->whereYear('created_at', now()->year)
                         ->where('status', '!=', 'failed')
                         ->sum('amount');
    }
}
