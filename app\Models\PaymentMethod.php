<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentMethod extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'type',
        'gateway',
        'is_active',
        'fee_percentage',
        'fee_fixed',
        'min_amount',
        'max_amount',
        'processing_time',
        'requires_verification',
        'supported_countries',
        'supported_currencies',
        'configuration',
        'display_order',
        'description',
        'icon_url',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'requires_verification' => 'boolean',
        'fee_percentage' => 'decimal:4',
        'fee_fixed' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'supported_countries' => 'array',
        'supported_currencies' => 'array',
        'configuration' => 'array',
    ];

    /**
     * Get payment transactions for this method
     */
    public function paymentTransactions()
    {
        return $this->hasMany(PaymentTransaction::class, 'gateway', 'type');
    }

    /**
     * Check if payment method supports country
     */
    public function supportsCountry(string $countryCode): bool
    {
        if (empty($this->supported_countries)) {
            return true; // No restrictions
        }
        
        return in_array($countryCode, $this->supported_countries);
    }

    /**
     * Check if payment method supports currency
     */
    public function supportsCurrency(string $currencyCode): bool
    {
        if (empty($this->supported_currencies)) {
            return true; // No restrictions
        }
        
        return in_array($currencyCode, $this->supported_currencies);
    }

    /**
     * Calculate fees for amount
     */
    public function calculateFees(float $amount): array
    {
        $percentageFee = ($amount * $this->fee_percentage) / 100;
        $totalFee = $percentageFee + $this->fee_fixed;
        
        return [
            'percentage_fee' => $percentageFee,
            'fixed_fee' => $this->fee_fixed,
            'total_fee' => $totalFee,
            'total_amount' => $amount + $totalFee,
        ];
    }

    /**
     * Check if amount is within limits
     */
    public function isAmountValid(float $amount): bool
    {
        if ($this->min_amount && $amount < $this->min_amount) {
            return false;
        }
        
        if ($this->max_amount && $amount > $this->max_amount) {
            return false;
        }
        
        return true;
    }

    /**
     * Get active payment methods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get payment methods for country and currency
     */
    public function scopeForCountryAndCurrency($query, string $countryCode, string $currencyCode)
    {
        return $query->where(function($q) use ($countryCode) {
                $q->whereJsonContains('supported_countries', $countryCode)
                  ->orWhereNull('supported_countries');
            })
            ->where(function($q) use ($currencyCode) {
                $q->whereJsonContains('supported_currencies', $currencyCode)
                  ->orWhereNull('supported_currencies');
            });
    }

    /**
     * Get payment methods ordered by display order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }
}
