<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_number')->unique();
            $table->string('reference_number')->unique();
            $table->enum('type', ['transfer', 'deposit', 'withdrawal', 'exchange', 'fee', 'commission']);
            $table->enum('category', ['domestic', 'international', 'crypto', 'bank_transfer', 'cash']);
            
            // Sender Information
            $table->foreignId('sender_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('sender_wallet_id')->nullable()->constrained('wallets')->onDelete('set null');
            $table->string('sender_name')->nullable();
            $table->string('sender_phone')->nullable();
            $table->string('sender_national_id')->nullable();
            
            // Receiver Information
            $table->foreignId('receiver_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('receiver_wallet_id')->nullable()->constrained('wallets')->onDelete('set null');
            $table->string('receiver_name');
            $table->string('receiver_phone')->nullable();
            $table->string('receiver_national_id')->nullable();
            $table->string('receiver_bank_account')->nullable();
            $table->string('receiver_bank_name')->nullable();
            $table->string('receiver_bank_code')->nullable();
            
            // Amount and Currency
            $table->foreignId('currency_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 15, 2);
            $table->decimal('exchange_rate', 15, 8)->default(1.********);
            $table->foreignId('target_currency_id')->nullable()->constrained('currencies')->onDelete('set null');
            $table->decimal('target_amount', 15, 2)->nullable();
            
            // Fees and Commission
            $table->decimal('commission_amount', 15, 2)->default(0.00);
            $table->decimal('commission_rate', 5, 4)->default(0.0000);
            $table->decimal('additional_fees', 15, 2)->default(0.00);
            $table->decimal('total_fees', 15, 2)->default(0.00);
            $table->decimal('net_amount', 15, 2);
            
            // Status and Processing
            $table->enum('status', [
                'pending', 'processing', 'completed', 'failed', 
                'cancelled', 'refunded', 'on_hold', 'requires_verification'
            ])->default('pending');
            $table->string('payment_method')->nullable(); // cash, bank, card, crypto, etc.
            $table->string('payment_gateway')->nullable(); // paypal, stripe, wise, etc.
            $table->string('gateway_transaction_id')->nullable();
            $table->string('blockchain_hash')->nullable();
            $table->integer('blockchain_confirmations')->default(0);
            
            // Branch and Agent Information
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('agent_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Geographic Information
            $table->foreignId('sender_country_id')->nullable()->constrained('countries')->onDelete('set null');
            $table->foreignId('receiver_country_id')->nullable()->constrained('countries')->onDelete('set null');
            
            // Timing
            $table->timestamp('initiated_at');
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            
            // Additional Information
            $table->text('purpose')->nullable();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->string('receipt_url')->nullable();
            
            // Risk and Compliance
            $table->enum('risk_score', ['low', 'medium', 'high'])->default('low');
            $table->boolean('requires_kyc')->default(false);
            $table->boolean('aml_checked')->default(false);
            $table->boolean('is_suspicious')->default(false);
            $table->text('compliance_notes')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['sender_id', 'receiver_id']);
            $table->index(['transaction_number', 'reference_number']);
            $table->index(['type', 'category']);
            $table->index(['branch_id', 'agent_id']);
            $table->index(['currency_id', 'target_currency_id']);
            $table->index(['risk_score', 'is_suspicious']);
            $table->index(['sender_country_id', 'receiver_country_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
