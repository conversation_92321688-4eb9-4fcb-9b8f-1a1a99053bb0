<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Transaction;

class TransactionConfirmation extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public Transaction $transaction;

    /**
     * Create a new message instance.
     */
    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = match($this->transaction->type) {
            'transfer' => 'تأكيد التحويل - Transfer Confirmation',
            'deposit' => 'تأكيد الإيداع - Deposit Confirmation',
            'withdrawal' => 'تأكيد السحب - Withdrawal Confirmation',
            default => 'تأكيد المعاملة - Transaction Confirmation'
        };

        return new Envelope(
            subject: $subject . ' #' . $this->transaction->reference_number,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.transaction-confirmation',
            with: [
                'transaction' => $this->transaction,
                'user' => $this->transaction->sender,
                'currency' => $this->transaction->currency,
                'formattedAmount' => number_format($this->transaction->amount, 2),
                'statusText' => $this->getStatusText(),
                'statusColor' => $this->getStatusColor(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get status text in both languages.
     */
    private function getStatusText(): array
    {
        return match($this->transaction->status) {
            'pending' => ['ar' => 'قيد المعالجة', 'en' => 'Pending'],
            'processing' => ['ar' => 'جاري المعالجة', 'en' => 'Processing'],
            'completed' => ['ar' => 'مكتملة', 'en' => 'Completed'],
            'failed' => ['ar' => 'فاشلة', 'en' => 'Failed'],
            'cancelled' => ['ar' => 'ملغية', 'en' => 'Cancelled'],
            'blocked' => ['ar' => 'محجوبة', 'en' => 'Blocked'],
            default => ['ar' => 'غير معروف', 'en' => 'Unknown']
        };
    }

    /**
     * Get status color for styling.
     */
    private function getStatusColor(): string
    {
        return match($this->transaction->status) {
            'pending' => '#f59e0b',
            'processing' => '#3b82f6',
            'completed' => '#10b981',
            'failed' => '#ef4444',
            'cancelled' => '#6b7280',
            'blocked' => '#dc2626',
            default => '#6b7280'
        };
    }
}
