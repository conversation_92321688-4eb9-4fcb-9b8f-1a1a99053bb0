<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Transaction;
use App\Models\PaymentMethod;
use App\Models\PaymentTransaction;
use App\Models\Country;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Services\PaymentGatewayService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class PaymentSystemTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Transaction $transaction;
    protected PaymentMethod $paymentMethod;
    protected Country $country;
    protected Currency $currency;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create country
        $this->country = Country::create([
            'name_ar' => 'المملكة العربية السعودية',
            'name_en' => 'Saudi Arabia',
            'code' => 'SAU',
            'iso2' => 'SA',
            'phone_code' => '+966',
            'currency_code' => 'SAR',
            'is_active' => true,
            'supports_transfers' => true,
        ]);

        // Create currency
        $this->currency = Currency::create([
            'code' => 'SAR',
            'name_ar' => 'ريال سعودي',
            'name_en' => 'Saudi Riyal',
            'symbol' => 'ر.س',
            'is_active' => true,
            'is_base_currency' => false,
            'rate_to_usd' => 0.27,
        ]);

        // Create user
        $this->user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => bcrypt('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);

        // Create payment method
        $this->paymentMethod = PaymentMethod::create([
            'name' => 'Stripe Credit Card',
            'type' => 'stripe',
            'gateway' => 'stripe',
            'is_active' => true,
            'fee_percentage' => 2.9,
            'fee_fixed' => 0.30,
            'min_amount' => 1.00,
            'max_amount' => 10000.00,
            'processing_time' => 'Instant',
            'requires_verification' => false,
            'supported_countries' => ['SA', 'US', 'GB'],
            'supported_currencies' => ['SAR', 'USD', 'GBP'],
        ]);

        // Create transaction
        $this->transaction = Transaction::create([
            'transaction_id' => 'MT20241201TEST123',
            'transaction_number' => 'TXN-' . time(),
            'user_id' => $this->user->id,
            'type' => 'money_transfer',
            'status' => 'pending',
            'amount' => 1000.00,
            'fee' => 50.00,
            'total_amount' => 1050.00,
            'currency_from' => 'SAR',
            'currency_to' => 'USD',
            'payment_method' => 'stripe',
            'sender_name' => 'Test User',
            'sender_phone' => '+966501234567',
            'sender_country_id' => $this->country->id,
            'sender_address' => 'Test Address',
            'recipient_name' => 'Recipient User',
            'recipient_phone' => '+12345678901',
            'recipient_country_id' => $this->country->id,
            'recipient_address' => 'Recipient Address',
            'delivery_method' => 'cash_pickup',
            'purpose' => 'family_support',
        ]);
    }

    public function test_user_can_get_available_payment_methods()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/payments/methods?' . http_build_query([
                'country_code' => 'SA',
                'currency_code' => 'SAR',
                'amount' => 1000,
            ]));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'payment_methods' => [
                            '*' => [
                                'id',
                                'name',
                                'type',
                                'fee_percentage',
                                'fee_fixed',
                                'min_amount',
                                'max_amount',
                                'processing_time',
                                'requires_verification',
                            ],
                        ],
                        'country_code',
                        'currency_code',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'country_code' => 'SA',
                        'currency_code' => 'SAR',
                    ],
                ]);

        $this->assertNotEmpty($response->json('data.payment_methods'));
    }

    public function test_user_can_calculate_payment_fees()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/payments/calculate-fees', [
                'payment_method' => 'stripe',
                'amount' => 1000,
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'base_amount',
                        'percentage_fee',
                        'fixed_fee',
                        'total_fee',
                        'total_amount',
                        'fee_breakdown',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $data = $response->json('data');
        $this->assertEquals(1000, $data['base_amount']);
        $this->assertEquals(29, $data['percentage_fee']); // 2.9% of 1000
        $this->assertEquals(0.30, $data['fixed_fee']);
        $this->assertEquals(29.30, $data['total_fee']);
        $this->assertEquals(1029.30, $data['total_amount']);
    }

    public function test_payment_processing_with_mock_gateway()
    {
        // Mock the payment gateway service
        $mockPaymentService = Mockery::mock(PaymentGatewayService::class);
        $mockPaymentService->shouldReceive('processPayment')
            ->once()
            ->andReturn([
                'status' => 'success',
                'reference' => 'stripe_test_123',
                'amount' => 1050.00,
                'currency' => 'SAR',
                'processed_at' => now(),
            ]);

        $this->app->instance(PaymentGatewayService::class, $mockPaymentService);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/payments/process', [
                'transaction_id' => $this->transaction->transaction_id,
                'payment_method' => 'stripe',
                'payment_data' => [
                    'card_number' => '****************',
                    'expiry_month' => '12',
                    'expiry_year' => '2025',
                    'cvv' => '123',
                    'cardholder_name' => 'Test User',
                ],
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'payment_result',
                        'transaction_id',
                        'status',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'transaction_id' => $this->transaction->transaction_id,
                        'status' => 'success',
                    ],
                ]);
    }

    public function test_payment_verification()
    {
        // Mock the payment gateway service
        $mockPaymentService = Mockery::mock(PaymentGatewayService::class);
        $mockPaymentService->shouldReceive('verifyPayment')
            ->once()
            ->with('stripe_test_123', 'stripe')
            ->andReturn([
                'status' => 'completed',
                'amount' => 1050.00,
                'currency' => 'SAR',
                'gateway_response' => ['id' => 'stripe_test_123'],
            ]);

        $this->app->instance(PaymentGatewayService::class, $mockPaymentService);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/payments/verify', [
                'payment_reference' => 'stripe_test_123',
                'gateway' => 'stripe',
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'status',
                        'amount',
                        'currency',
                        'gateway_response',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'status' => 'completed',
                        'amount' => 1050.00,
                        'currency' => 'SAR',
                    ],
                ]);
    }

    public function test_payment_refund_request()
    {
        // Update transaction to completed status
        $this->transaction->update(['status' => 'completed']);

        // Mock the payment gateway service
        $mockPaymentService = Mockery::mock(PaymentGatewayService::class);
        $mockPaymentService->shouldReceive('refundPayment')
            ->once()
            ->andReturn([
                'status' => 'success',
                'reference' => 'refund_123',
                'amount' => 1050.00,
                'gateway_response' => ['id' => 'refund_123'],
            ]);

        $this->app->instance(PaymentGatewayService::class, $mockPaymentService);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/payments/refund', [
                'transaction_id' => $this->transaction->transaction_id,
                'reason' => 'Customer requested refund',
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'refund_result',
                        'transaction_id',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'transaction_id' => $this->transaction->transaction_id,
                    ],
                ]);

        // Check transaction status updated
        $this->transaction->refresh();
        $this->assertEquals('refund_requested', $this->transaction->status);
        $this->assertEquals('Customer requested refund', $this->transaction->refund_reason);
    }

    public function test_user_can_get_payment_history()
    {
        // Create some payment transactions
        PaymentTransaction::create([
            'transaction_id' => $this->transaction->id,
            'gateway' => 'stripe',
            'amount' => 1050.00,
            'currency' => 'SAR',
            'status' => 'completed',
            'type' => 'payment',
            'gateway_reference' => 'stripe_test_123',
        ]);

        PaymentTransaction::create([
            'transaction_id' => $this->transaction->id,
            'gateway' => 'stripe',
            'amount' => -500.00,
            'currency' => 'SAR',
            'status' => 'completed',
            'type' => 'refund',
            'gateway_reference' => 'refund_123',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/payments/history');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'payments' => [
                            '*' => [
                                'id',
                                'gateway',
                                'amount',
                                'currency',
                                'status',
                                'type',
                                'created_at',
                            ],
                        ],
                        'pagination',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $this->assertCount(2, $response->json('data.payments'));
    }

    public function test_user_can_get_payment_statistics()
    {
        // Create payment transactions for statistics
        PaymentTransaction::create([
            'transaction_id' => $this->transaction->id,
            'gateway' => 'stripe',
            'amount' => 1050.00,
            'currency' => 'SAR',
            'status' => 'completed',
            'type' => 'payment',
            'created_at' => now()->subDays(5),
        ]);

        PaymentTransaction::create([
            'transaction_id' => $this->transaction->id,
            'gateway' => 'paypal',
            'amount' => 500.00,
            'currency' => 'SAR',
            'status' => 'failed',
            'type' => 'payment',
            'created_at' => now()->subDays(3),
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/payments/statistics?period=month');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'period',
                        'start_date',
                        'end_date',
                        'statistics',
                        'payment_method_breakdown',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'period' => 'month',
                    ],
                ]);

        $stats = $response->json('data.statistics');
        $this->assertEquals(2, $stats->total_payments);
        $this->assertEquals(1, $stats->successful_payments);
        $this->assertEquals(1, $stats->failed_payments);
    }

    public function test_payment_webhook_handling()
    {
        // Mock the payment gateway service
        $mockPaymentService = Mockery::mock(PaymentGatewayService::class);
        $mockPaymentService->shouldReceive('handleWebhook')
            ->once()
            ->with('stripe', Mockery::any())
            ->andReturn([
                'status' => 'processed',
                'event_type' => 'payment.completed',
                'transaction_reference' => 'stripe_test_123',
            ]);

        $this->app->instance(PaymentGatewayService::class, $mockPaymentService);

        $webhookPayload = [
            'id' => 'evt_test_123',
            'type' => 'payment_intent.succeeded',
            'data' => [
                'object' => [
                    'id' => 'stripe_test_123',
                    'status' => 'succeeded',
                    'amount' => 105000, // Stripe uses cents
                ],
            ],
        ];

        $response = $this->postJson('/api/webhooks/payments/stripe', $webhookPayload);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Webhook processed successfully',
                ]);
    }

    public function test_payment_method_filtering_by_amount()
    {
        // Create payment method with specific limits
        PaymentMethod::create([
            'name' => 'High Value Card',
            'type' => 'visa',
            'gateway' => 'visa',
            'is_active' => true,
            'fee_percentage' => 1.5,
            'fee_fixed' => 0.00,
            'min_amount' => 5000.00,
            'max_amount' => 50000.00,
            'supported_countries' => ['SA'],
            'supported_currencies' => ['SAR'],
        ]);

        // Test with low amount (should not include high value card)
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/payments/methods?' . http_build_query([
                'country_code' => 'SA',
                'currency_code' => 'SAR',
                'amount' => 1000,
            ]));

        $response->assertStatus(200);
        $methods = $response->json('data.payment_methods');
        
        // Should only include the stripe method (min 1.00)
        $this->assertCount(1, $methods);
        $this->assertEquals('stripe', $methods[0]['type']);

        // Test with high amount (should include both methods)
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/payments/methods?' . http_build_query([
                'country_code' => 'SA',
                'currency_code' => 'SAR',
                'amount' => 10000,
            ]));

        $response->assertStatus(200);
        $methods = $response->json('data.payment_methods');
        
        // Should include both methods
        $this->assertCount(2, $methods);
    }

    public function test_payment_processing_validation()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/v1/payments/process', [
                // Missing required fields
            ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'transaction_id',
                    'payment_method',
                    'payment_data',
                ]);
    }

    public function test_unauthorized_user_cannot_access_payment_endpoints()
    {
        $response = $this->getJson('/api/v1/payments/methods?country_code=SA&currency_code=SAR');
        $response->assertStatus(401);

        $response = $this->postJson('/api/v1/payments/process', []);
        $response->assertStatus(401);

        $response = $this->getJson('/api/v1/payments/history');
        $response->assertStatus(401);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
