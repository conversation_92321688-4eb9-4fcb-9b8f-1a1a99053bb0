<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Transaction;
use App\Models\Recipient;
use App\Models\Country;
use App\Models\Currency;
use Illuminate\Support\Facades\Hash;

class DemoDataSeeder extends Seeder
{
    public function run()
    {
        // Create demo users
        $this->createDemoUsers();
        
        // Create demo recipients
        $this->createDemoRecipients();
        
        // Create demo transactions
        $this->createDemoTransactions();
    }

    private function createDemoUsers()
    {
        $countries = Country::all();
        $saudiArabia = $countries->where('code', 'SA')->first();
        
        if (!$saudiArabia) {
            $saudiArabia = Country::create([
                'name_en' => 'Saudi Arabia',
                'name_ar' => 'السعودية',
                'code' => 'SA',
                'iso2' => 'SA',
                'iso3' => 'SAU',
                'currency_code' => 'SAR',
                'phone_code' => '+966',
                'is_active' => true
            ]);
        }

        // Create demo customers
        for ($i = 1; $i <= 5; $i++) {
            User::firstOrCreate(
                ['email' => "customer{$i}@demo.com"],
                [
                    'first_name' => "عميل {$i}",
                    'last_name' => 'تجريبي',
                    'phone' => "+96650123456{$i}",
                    'password' => Hash::make('password123'),
                    'user_type' => 'customer',
                    'status' => 'active',
                    'country_id' => $saudiArabia->id,
                    'email_verified_at' => now(),
                    'phone_verified_at' => now(),
                    'kyc_status' => 'verified',
                    'created_at' => now()->subDays(rand(1, 30)),
                ]
            );
        }
    }

    private function createDemoRecipients()
    {
        $users = User::where('user_type', 'customer')->get();
        $countries = Country::all();
        
        $recipientNames = [
            ['أحمد محمد', 'Ahmed Mohammed'],
            ['فاطمة علي', 'Fatima Ali'],
            ['محمد عبدالله', 'Mohammed Abdullah'],
            ['سارة أحمد', 'Sara Ahmed'],
            ['علي حسن', 'Ali Hassan'],
            ['نورا سالم', 'Nora Salem'],
            ['خالد عمر', 'Khalid Omar'],
            ['مريم يوسف', 'Mariam Youssef']
        ];

        foreach ($users as $user) {
            for ($i = 0; $i < rand(2, 4); $i++) {
                $name = $recipientNames[array_rand($recipientNames)];
                $country = $countries->random();
                
                Recipient::firstOrCreate([
                    'user_id' => $user->id,
                    'first_name' => explode(' ', $name[0])[0],
                    'last_name' => explode(' ', $name[0])[1] ?? '',
                    'email' => strtolower(str_replace(' ', '.', $name[1])) . '@example.com',
                    'phone' => '+' . $country->phone_code . rand(100000000, 999999999),
                    'country_id' => $country->id,
                    'relationship' => ['family', 'friend', 'business'][rand(0, 2)],
                    'is_active' => true,
                ]);
            }
        }
    }

    private function createDemoTransactions()
    {
        $users = User::where('user_type', 'customer')->get();
        $recipients = Recipient::all();
        $countries = Country::all();
        
        $statuses = ['completed', 'pending', 'processing', 'failed'];
        $paymentMethods = ['bank_transfer', 'credit_card', 'debit_card', 'wallet'];
        
        foreach ($users as $user) {
            $userRecipients = $recipients->where('user_id', $user->id);
            
            for ($i = 0; $i < rand(3, 8); $i++) {
                $recipient = $userRecipients->random();
                $amount = rand(100, 5000);
                $fee = $amount * 0.02; // 2% fee
                $status = $statuses[array_rand($statuses)];
                $createdAt = now()->subDays(rand(0, 30))->subHours(rand(0, 23));
                
                Transaction::create([
                    'transaction_id' => 'TXN' . strtoupper(uniqid()),
                    'user_id' => $user->id,
                    'recipient_id' => $recipient->id,
                    'sender_name' => $user->first_name . ' ' . $user->last_name,
                    'sender_phone' => $user->phone,
                    'sender_email' => $user->email,
                    'sender_country_id' => $user->country_id,
                    'recipient_name' => $recipient->first_name . ' ' . $recipient->last_name,
                    'recipient_phone' => $recipient->phone,
                    'recipient_email' => $recipient->email,
                    'recipient_country_id' => $recipient->country_id,
                    'amount' => $amount,
                    'currency_from' => 'SAR',
                    'currency_to' => $countries->find($recipient->country_id)->currency_code ?? 'USD',
                    'exchange_rate' => rand(25, 40) / 10, // Random rate between 2.5 and 4.0
                    'fee' => $fee,
                    'total_amount' => $amount + $fee,
                    'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                    'purpose' => ['family_support', 'education', 'business', 'personal'][rand(0, 3)],
                    'status' => $status,
                    'notes' => 'تحويل تجريبي للاختبار',
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                    'completed_at' => $status === 'completed' ? $createdAt->addMinutes(rand(5, 60)) : null,
                ]);
            }
        }
    }
}
