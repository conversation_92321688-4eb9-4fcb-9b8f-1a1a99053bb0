version: '3.8'

services:
  # Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mony-transfer-app
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./storage:/var/www/html/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=mysql
      - DB_DATABASE=mony_transfer
      - DB_USERNAME=mony_user
      - DB_PASSWORD=secure_password
      - REDIS_HOST=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - mysql
      - redis
    networks:
      - mony-transfer-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: mony-transfer-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: mony_transfer
      MYSQL_USER: mony_user
      MYSQL_PASSWORD: secure_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - mony-transfer-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: mony-transfer-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis_password
    networks:
      - mony-transfer-network

  # Queue Worker
  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mony-transfer-queue
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    volumes:
      - ./storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - DB_DATABASE=mony_transfer
      - DB_USERNAME=mony_user
      - DB_PASSWORD=secure_password
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - mysql
      - redis
    networks:
      - mony-transfer-network

  # Scheduler
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mony-transfer-scheduler
    restart: unless-stopped
    command: php artisan schedule:work
    volumes:
      - ./storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - DB_DATABASE=mony_transfer
      - DB_USERNAME=mony_user
      - DB_PASSWORD=secure_password
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - mony-transfer-network

  # Nginx Load Balancer (Optional)
  nginx:
    image: nginx:alpine
    container_name: mony-transfer-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - mony-transfer-network

  # Monitoring (Optional)
  prometheus:
    image: prom/prometheus
    container_name: mony-transfer-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - mony-transfer-network

  grafana:
    image: grafana/grafana
    container_name: mony-transfer-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin_password
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - mony-transfer-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  mony-transfer-network:
    driver: bridge
