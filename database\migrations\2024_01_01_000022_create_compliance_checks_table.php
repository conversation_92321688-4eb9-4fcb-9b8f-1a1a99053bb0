<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('compliance_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('transaction_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('check_type'); // aml, sanctions, pep, adverse_media
            $table->string('provider'); // compliance_api_provider
            $table->string('reference_id')->nullable(); // Provider's reference
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'manual_review'])->default('pending');
            $table->enum('result', ['clear', 'match', 'potential_match', 'error'])->nullable();
            $table->decimal('risk_score', 5, 4)->nullable(); // 0.0000 to 1.0000
            $table->json('matches')->nullable(); // Array of matches found
            $table->json('request_data')->nullable(); // Data sent to provider
            $table->json('response_data')->nullable(); // Full response from provider
            $table->text('notes')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('requires_manual_review')->default(false);
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'check_type', 'status']);
            $table->index(['transaction_id', 'check_type']);
            $table->index(['status', 'created_at']);
            $table->index(['result', 'risk_score']);
            $table->index('expires_at');
            $table->index(['requires_manual_review', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('compliance_checks');
    }
};
