<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Country;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\PaymentGateway;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Support\Facades\Hash;

class FinancialSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed Countries
        $this->seedCountries();
        
        // Seed Currencies
        $this->seedCurrencies();
        
        // Seed Exchange Rates
        $this->seedExchangeRates();
        
        // Seed Payment Gateways
        $this->seedPaymentGateways();
        
        // Seed Admin User
        $this->seedAdminUser();
        
        // Seed Branches
        $this->seedBranches();
    }

    private function seedCountries()
    {
        $countries = [
            [
                'name_ar' => 'المملكة العربية السعودية',
                'name_en' => 'Saudi Arabia',
                'code' => 'SAU',
                'iso2' => 'SA',
                'phone_code' => '+966',
                'currency_code' => 'SAR',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => true,
                'max_transfer_amount' => 500000,
                'min_transfer_amount' => 1,
                'commission_rate' => 0.015,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card', 'crypto'],
                'required_documents' => ['national_id', 'proof_of_address'],
            ],
            [
                'name_ar' => 'الإمارات العربية المتحدة',
                'name_en' => 'United Arab Emirates',
                'code' => 'ARE',
                'iso2' => 'AE',
                'phone_code' => '+971',
                'currency_code' => 'AED',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => true,
                'max_transfer_amount' => 400000,
                'min_transfer_amount' => 1,
                'commission_rate' => 0.02,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card', 'crypto'],
                'required_documents' => ['emirates_id', 'proof_of_address'],
            ],
            [
                'name_ar' => 'مصر',
                'name_en' => 'Egypt',
                'code' => 'EGY',
                'iso2' => 'EG',
                'phone_code' => '+20',
                'currency_code' => 'EGP',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 200000,
                'min_transfer_amount' => 1,
                'commission_rate' => 0.025,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card'],
                'required_documents' => ['national_id', 'proof_of_address'],
            ],
            [
                'name_ar' => 'الولايات المتحدة الأمريكية',
                'name_en' => 'United States',
                'code' => 'USA',
                'iso2' => 'US',
                'phone_code' => '+1',
                'currency_code' => 'USD',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => true,
                'max_transfer_amount' => 1000000,
                'min_transfer_amount' => 1,
                'commission_rate' => 0.01,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card', 'crypto', 'paypal'],
                'required_documents' => ['ssn', 'drivers_license', 'proof_of_address'],
            ],
            [
                'name_ar' => 'المملكة المتحدة',
                'name_en' => 'United Kingdom',
                'code' => 'GBR',
                'iso2' => 'GB',
                'phone_code' => '+44',
                'currency_code' => 'GBP',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => true,
                'max_transfer_amount' => 800000,
                'min_transfer_amount' => 1,
                'commission_rate' => 0.012,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card', 'crypto'],
                'required_documents' => ['passport', 'proof_of_address'],
            ],
        ];

        foreach ($countries as $country) {
            Country::create($country);
        }
    }

    private function seedCurrencies()
    {
        $currencies = [
            [
                'name_ar' => 'الدولار الأمريكي',
                'name_en' => 'US Dollar',
                'code' => 'USD',
                'symbol' => '$',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => true,
                'rate_to_usd' => 1.********,
                'decimal_places' => 2,
                'supported_countries' => ['USA', 'SAU', 'ARE', 'EGY', 'GBR'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 1000000,
            ],
            [
                'name_ar' => 'الريال السعودي',
                'name_en' => 'Saudi Riyal',
                'code' => 'SAR',
                'symbol' => '﷼',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.26666667,
                'decimal_places' => 2,
                'supported_countries' => ['SAU'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 500000,
            ],
            [
                'name_ar' => 'الدرهم الإماراتي',
                'name_en' => 'UAE Dirham',
                'code' => 'AED',
                'symbol' => 'د.إ',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.27225000,
                'decimal_places' => 2,
                'supported_countries' => ['ARE'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 400000,
            ],
            [
                'name_ar' => 'الجنيه المصري',
                'name_en' => 'Egyptian Pound',
                'code' => 'EGP',
                'symbol' => 'ج.م',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.02040816,
                'decimal_places' => 2,
                'supported_countries' => ['EGY'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 200000,
            ],
            [
                'name_ar' => 'الجنيه الإسترليني',
                'name_en' => 'British Pound',
                'code' => 'GBP',
                'symbol' => '£',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 1.27000000,
                'decimal_places' => 2,
                'supported_countries' => ['GBR'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 800000,
            ],
            [
                'name_ar' => 'البيتكوين',
                'name_en' => 'Bitcoin',
                'code' => 'BTC',
                'symbol' => '₿',
                'is_crypto' => true,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 45000.********,
                'decimal_places' => 8,
                'crypto_network' => 'bitcoin',
                'supported_countries' => ['USA', 'SAU', 'ARE', 'GBR'],
                'min_transfer_amount' => 0.00001,
                'max_transfer_amount' => 100,
            ],
            [
                'name_ar' => 'الإيثيريوم',
                'name_en' => 'Ethereum',
                'code' => 'ETH',
                'symbol' => 'Ξ',
                'is_crypto' => true,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 3000.********,
                'decimal_places' => 8,
                'crypto_network' => 'ethereum',
                'supported_countries' => ['USA', 'SAU', 'ARE', 'GBR'],
                'min_transfer_amount' => 0.001,
                'max_transfer_amount' => 1000,
            ],
            [
                'name_ar' => 'تيثر',
                'name_en' => 'Tether USD',
                'code' => 'USDT',
                'symbol' => '₮',
                'is_crypto' => true,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 1.********,
                'decimal_places' => 6,
                'crypto_network' => 'ethereum',
                'contract_address' => '******************************************',
                'supported_countries' => ['USA', 'SAU', 'ARE', 'GBR'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 100000,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::create($currency);
        }
    }

    private function seedExchangeRates()
    {
        $usd = Currency::where('code', 'USD')->first();
        $sar = Currency::where('code', 'SAR')->first();
        $aed = Currency::where('code', 'AED')->first();
        $egp = Currency::where('code', 'EGP')->first();
        $gbp = Currency::where('code', 'GBP')->first();

        $rates = [
            ['from' => $usd->id, 'to' => $sar->id, 'rate' => 3.75000000],
            ['from' => $sar->id, 'to' => $usd->id, 'rate' => 0.26666667],
            ['from' => $usd->id, 'to' => $aed->id, 'rate' => 3.67300000],
            ['from' => $aed->id, 'to' => $usd->id, 'rate' => 0.27225000],
            ['from' => $usd->id, 'to' => $egp->id, 'rate' => 49.********],
            ['from' => $egp->id, 'to' => $usd->id, 'rate' => 0.02040816],
            ['from' => $usd->id, 'to' => $gbp->id, 'rate' => 0.78740000],
            ['from' => $gbp->id, 'to' => $usd->id, 'rate' => 1.27000000],
        ];

        foreach ($rates as $rate) {
            ExchangeRate::create([
                'from_currency_id' => $rate['from'],
                'to_currency_id' => $rate['to'],
                'rate' => $rate['rate'],
                'buy_rate' => $rate['rate'] * 1.005, // 0.5% markup
                'sell_rate' => $rate['rate'] * 0.995, // 0.5% markdown
                'spread' => 0.005,
                'source' => 'manual',
                'last_updated' => now(),
                'is_active' => true,
            ]);
        }
    }

    private function seedPaymentGateways()
    {
        $gateways = [
            [
                'name' => 'PayPal',
                'code' => 'paypal',
                'type' => 'digital_wallet',
                'is_active' => true,
                'supported_countries' => ['USA', 'GBR', 'SAU', 'ARE'],
                'supported_currencies' => ['USD', 'GBP', 'SAR', 'AED'],
                'min_amount' => 1.00,
                'max_amount' => 100000.00,
                'fee_percentage' => 0.029,
                'fixed_fee' => 0.30,
                'processing_time_minutes' => 0,
                'description' => 'PayPal payment gateway for international transfers',
            ],
            [
                'name' => 'Stripe',
                'code' => 'stripe',
                'type' => 'card',
                'is_active' => true,
                'supported_countries' => ['USA', 'GBR', 'SAU', 'ARE'],
                'supported_currencies' => ['USD', 'GBP', 'SAR', 'AED'],
                'min_amount' => 0.50,
                'max_amount' => 50000.00,
                'fee_percentage' => 0.029,
                'fixed_fee' => 0.30,
                'processing_time_minutes' => 0,
                'description' => 'Stripe payment processing for card payments',
            ],
            [
                'name' => 'Wise (TransferWise)',
                'code' => 'wise',
                'type' => 'bank',
                'is_active' => true,
                'supported_countries' => ['USA', 'GBR', 'SAU', 'ARE', 'EGY'],
                'supported_currencies' => ['USD', 'GBP', 'SAR', 'AED', 'EGP'],
                'min_amount' => 1.00,
                'max_amount' => 1000000.00,
                'fee_percentage' => 0.005,
                'fixed_fee' => 1.00,
                'processing_time_minutes' => 60,
                'description' => 'Wise international money transfer service',
            ],
        ];

        foreach ($gateways as $gateway) {
            PaymentGateway::create($gateway);
        }
    }

    private function seedAdminUser()
    {
        $saudiArabia = Country::where('code', 'SAU')->first();

        User::create([
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'email' => '<EMAIL>',
            'phone' => '+************',
            'password' => Hash::make('Admin@123456'),
            'user_type' => 'admin',
            'status' => 'active',
            'country_id' => $saudiArabia->id,
            'preferred_language' => 'ar',
            'preferred_currency' => 'USD',
            'daily_limit' => 1000000,
            'monthly_limit' => 10000000,
            'risk_level' => 'low',
            'kyc_verified_at' => now(),
            'aml_verified' => true,
            'aml_verified_at' => now(),
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);
    }

    private function seedBranches()
    {
        $saudiArabia = Country::where('code', 'SAU')->first();
        $uae = Country::where('code', 'ARE')->first();

        $branches = [
            [
                'name_ar' => 'الفرع الرئيسي - الرياض',
                'name_en' => 'Main Branch - Riyadh',
                'code' => 'RYD001',
                'country_id' => $saudiArabia->id,
                'city_ar' => 'الرياض',
                'city_en' => 'Riyadh',
                'address_ar' => 'شارع الملك فهد، حي العليا',
                'address_en' => 'King Fahd Road, Al Olaya District',
                'phone' => '+966112345678',
                'email' => '<EMAIL>',
                'is_active' => true,
                'is_main_branch' => true,
                'cash_limit' => 500000,
                'supported_currencies' => ['USD', 'SAR', 'AED', 'EGP'],
                'manager_name' => 'أحمد محمد السعودي',
                'manager_phone' => '+966501234567',
            ],
            [
                'name_ar' => 'فرع دبي',
                'name_en' => 'Dubai Branch',
                'code' => 'DXB001',
                'country_id' => $uae->id,
                'city_ar' => 'دبي',
                'city_en' => 'Dubai',
                'address_ar' => 'شارع الشيخ زايد، منطقة دبي المالية العالمية',
                'address_en' => 'Sheikh Zayed Road, DIFC',
                'phone' => '+971043456789',
                'email' => '<EMAIL>',
                'is_active' => true,
                'is_main_branch' => false,
                'cash_limit' => 400000,
                'supported_currencies' => ['USD', 'AED', 'SAR'],
                'manager_name' => 'محمد علي الإماراتي',
                'manager_phone' => '+971501234567',
            ],
        ];

        foreach ($branches as $branch) {
            Branch::create($branch);
        }
    }
}
