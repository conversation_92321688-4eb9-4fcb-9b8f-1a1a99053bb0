<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class RateLimit extends Model
{
    use HasFactory;

    protected $fillable = [
        'identifier',
        'type',
        'endpoint',
        'attempts',
        'max_attempts',
        'window_seconds',
        'window_start',
        'reset_at',
        'is_blocked',
        'blocked_until',
        'metadata',
    ];

    protected $casts = [
        'window_start' => 'datetime',
        'reset_at' => 'datetime',
        'is_blocked' => 'boolean',
        'blocked_until' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Scope to get active rate limits
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('reset_at', '>', now());
    }

    /**
     * Scope to get blocked rate limits
     */
    public function scopeBlocked(Builder $query): Builder
    {
        return $query->where('is_blocked', true)
            ->where(function ($q) {
                $q->whereNull('blocked_until')
                  ->orWhere('blocked_until', '>', now());
            });
    }

    /**
     * Scope to filter by identifier and type
     */
    public function scopeForIdentifier(Builder $query, string $identifier, string $type, ?string $endpoint = null): Builder
    {
        $query = $query->where('identifier', $identifier)
            ->where('type', $type);

        if ($endpoint) {
            $query->where('endpoint', $endpoint);
        }

        return $query;
    }

    /**
     * Check if rate limit is exceeded
     */
    public static function isExceeded(
        string $identifier,
        string $type,
        int $maxAttempts,
        int $windowSeconds,
        ?string $endpoint = null
    ): bool {
        $rateLimit = self::getCurrentRateLimit($identifier, $type, $endpoint);
        
        if (!$rateLimit) {
            return false;
        }

        return $rateLimit->attempts >= $maxAttempts;
    }

    /**
     * Record an attempt
     */
    public static function recordAttempt(
        string $identifier,
        string $type,
        int $maxAttempts,
        int $windowSeconds,
        ?string $endpoint = null,
        array $metadata = []
    ): self {
        $now = now();
        $windowStart = $now->copy()->subSeconds($windowSeconds);

        $rateLimit = self::forIdentifier($identifier, $type, $endpoint)
            ->where('window_start', '>=', $windowStart)
            ->first();

        if (!$rateLimit) {
            $rateLimit = self::create([
                'identifier' => $identifier,
                'type' => $type,
                'endpoint' => $endpoint,
                'attempts' => 1,
                'max_attempts' => $maxAttempts,
                'window_seconds' => $windowSeconds,
                'window_start' => $now,
                'reset_at' => $now->copy()->addSeconds($windowSeconds),
                'is_blocked' => false,
                'metadata' => $metadata,
            ]);
        } else {
            $rateLimit->increment('attempts');
            $rateLimit->update(['metadata' => array_merge($rateLimit->metadata ?? [], $metadata)]);
        }

        // Check if should be blocked
        if ($rateLimit->attempts >= $maxAttempts && !$rateLimit->is_blocked) {
            $rateLimit->block();
        }

        return $rateLimit;
    }

    /**
     * Get current rate limit for identifier
     */
    public static function getCurrentRateLimit(
        string $identifier,
        string $type,
        ?string $endpoint = null
    ): ?self {
        return self::forIdentifier($identifier, $type, $endpoint)
            ->active()
            ->first();
    }

    /**
     * Get remaining attempts
     */
    public static function getRemainingAttempts(
        string $identifier,
        string $type,
        int $maxAttempts,
        ?string $endpoint = null
    ): int {
        $rateLimit = self::getCurrentRateLimit($identifier, $type, $endpoint);
        
        if (!$rateLimit) {
            return $maxAttempts;
        }

        return max(0, $maxAttempts - $rateLimit->attempts);
    }

    /**
     * Get time until reset
     */
    public static function getTimeUntilReset(
        string $identifier,
        string $type,
        ?string $endpoint = null
    ): ?int {
        $rateLimit = self::getCurrentRateLimit($identifier, $type, $endpoint);
        
        if (!$rateLimit) {
            return null;
        }

        return max(0, $rateLimit->reset_at->diffInSeconds(now()));
    }

    /**
     * Block the rate limit
     */
    public function block(int $blockDurationSeconds = 3600): void
    {
        $this->update([
            'is_blocked' => true,
            'blocked_until' => now()->addSeconds($blockDurationSeconds),
        ]);
    }

    /**
     * Unblock the rate limit
     */
    public function unblock(): void
    {
        $this->update([
            'is_blocked' => false,
            'blocked_until' => null,
        ]);
    }

    /**
     * Check if currently blocked
     */
    public function isCurrentlyBlocked(): bool
    {
        if (!$this->is_blocked) {
            return false;
        }

        if ($this->blocked_until && $this->blocked_until->isPast()) {
            $this->unblock();
            return false;
        }

        return true;
    }

    /**
     * Reset rate limit
     */
    public function reset(): void
    {
        $this->update([
            'attempts' => 0,
            'window_start' => now(),
            'reset_at' => now()->addSeconds($this->window_seconds),
            'is_blocked' => false,
            'blocked_until' => null,
        ]);
    }

    /**
     * Clean expired rate limits
     */
    public static function cleanExpired(): int
    {
        return self::where('reset_at', '<', now()->subHour())->delete();
    }

    /**
     * Get rate limit status for identifier
     */
    public static function getStatus(
        string $identifier,
        string $type,
        int $maxAttempts,
        int $windowSeconds,
        ?string $endpoint = null
    ): array {
        $rateLimit = self::getCurrentRateLimit($identifier, $type, $endpoint);
        
        if (!$rateLimit) {
            return [
                'attempts' => 0,
                'max_attempts' => $maxAttempts,
                'remaining' => $maxAttempts,
                'reset_at' => now()->addSeconds($windowSeconds),
                'is_blocked' => false,
                'blocked_until' => null,
            ];
        }

        return [
            'attempts' => $rateLimit->attempts,
            'max_attempts' => $rateLimit->max_attempts,
            'remaining' => max(0, $rateLimit->max_attempts - $rateLimit->attempts),
            'reset_at' => $rateLimit->reset_at,
            'is_blocked' => $rateLimit->isCurrentlyBlocked(),
            'blocked_until' => $rateLimit->blocked_until,
        ];
    }

    /**
     * Apply rate limiting for different types
     */
    public static function applyLimit(
        string $identifier,
        string $type,
        ?string $endpoint = null,
        array $metadata = []
    ): array {
        $limits = config('rate_limits', [
            'ip' => ['max' => 100, 'window' => 3600], // 100 per hour
            'user' => ['max' => 1000, 'window' => 3600], // 1000 per hour
            'api_key' => ['max' => 5000, 'window' => 3600], // 5000 per hour
        ]);

        $limit = $limits[$type] ?? $limits['ip'];
        $maxAttempts = $limit['max'];
        $windowSeconds = $limit['window'];

        // Check if already exceeded
        if (self::isExceeded($identifier, $type, $maxAttempts, $windowSeconds, $endpoint)) {
            $rateLimit = self::getCurrentRateLimit($identifier, $type, $endpoint);
            
            return [
                'allowed' => false,
                'rate_limit' => $rateLimit,
                'status' => self::getStatus($identifier, $type, $maxAttempts, $windowSeconds, $endpoint),
            ];
        }

        // Record the attempt
        $rateLimit = self::recordAttempt($identifier, $type, $maxAttempts, $windowSeconds, $endpoint, $metadata);
        
        return [
            'allowed' => true,
            'rate_limit' => $rateLimit,
            'status' => self::getStatus($identifier, $type, $maxAttempts, $windowSeconds, $endpoint),
        ];
    }

    /**
     * Get statistics for monitoring
     */
    public static function getStatistics(): array
    {
        $now = now();
        
        return [
            'total_active' => self::active()->count(),
            'total_blocked' => self::blocked()->count(),
            'by_type' => self::active()
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'top_blocked_ips' => self::blocked()
                ->where('type', 'ip')
                ->selectRaw('identifier, COUNT(*) as count')
                ->groupBy('identifier')
                ->orderByDesc('count')
                ->limit(10)
                ->pluck('count', 'identifier')
                ->toArray(),
        ];
    }
}
