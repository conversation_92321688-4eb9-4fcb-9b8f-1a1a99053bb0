<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Country;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create a country
        $country = Country::first();
        if (!$country) {
            $country = Country::create([
                'name_ar' => 'المملكة العربية السعودية',
                'name_en' => 'Saudi Arabia',
                'code' => 'SAU',
                'iso2' => 'SA',
                'phone_code' => '+966',
                'currency_code' => 'SAR',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 500000.00,
                'min_transfer_amount' => 1.00,
                'commission_rate' => 0.015,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card'],
                'required_documents' => ['national_id', 'proof_of_address'],
            ]);
        }

        // Create admin user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Admin',
                'last_name' => 'User',
                'phone' => '+************',
                'password' => Hash::make('password'),
                'user_type' => 'admin',
                'status' => 'active',
                'country_id' => $country->id,
                'email_verified_at' => now(),
                'phone_verified_at' => now(),
            ]
        );

        // Create test user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Test',
                'last_name' => 'User',
                'phone' => '+************',
                'password' => Hash::make('password'),
                'user_type' => 'customer',
                'status' => 'active',
                'country_id' => $country->id,
                'email_verified_at' => now(),
                'phone_verified_at' => now(),
            ]
        );

        $this->command->info('Users created successfully!');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Test User: <EMAIL> / password');
    }
}
