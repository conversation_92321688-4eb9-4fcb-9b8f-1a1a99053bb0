<?php

namespace App\Http\Middleware;

use App\Services\ComplianceService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ComplianceMiddleware
{
    protected ComplianceService $complianceService;

    public function __construct(ComplianceService $complianceService)
    {
        $this->complianceService = $complianceService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $checkType = 'basic'): Response
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بالوصول',
                'error_code' => 'UNAUTHORIZED',
            ], 401);
        }

        // Perform compliance checks based on type
        switch ($checkType) {
            case 'sanctions':
                if (!$this->passesSanctionsCheck($user)) {
                    return $this->createComplianceResponse('SANCTIONS_CHECK_FAILED', 'فشل في فحص قوائم العقوبات');
                }
                break;

            case 'pep':
                if (!$this->passesPEPCheck($user)) {
                    return $this->createComplianceResponse('PEP_CHECK_FAILED', 'فشل في فحص الأشخاص المعرضين سياسياً');
                }
                break;

            case 'geographic':
                if (!$this->passesGeographicCheck($user)) {
                    return $this->createComplianceResponse('GEOGRAPHIC_RESTRICTION', 'قيود جغرافية تمنع الوصول');
                }
                break;

            case 'transaction_limits':
                if (!$this->passesTransactionLimitsCheck($user, $request)) {
                    return $this->createComplianceResponse('TRANSACTION_LIMITS_EXCEEDED', 'تم تجاوز حدود المعاملات');
                }
                break;

            case 'velocity':
                if (!$this->passesVelocityCheck($user)) {
                    return $this->createComplianceResponse('VELOCITY_LIMITS_EXCEEDED', 'تم تجاوز حدود سرعة المعاملات');
                }
                break;

            case 'full':
                $complianceResult = $this->performFullComplianceCheck($user, $request);
                if (!$complianceResult['passed']) {
                    return $this->createComplianceResponse(
                        'COMPLIANCE_CHECK_FAILED',
                        'فشل في فحص الامتثال',
                        $complianceResult
                    );
                }
                break;
        }

        return $next($request);
    }

    /**
     * Check sanctions lists.
     */
    protected function passesSanctionsCheck($user): bool
    {
        // Simplified sanctions check
        // In production, this would integrate with proper sanctions screening APIs
        $fullName = strtolower($user->first_name . ' ' . $user->last_name);
        
        // Check against basic sanctions list (this would be more comprehensive in production)
        $sanctionedNames = config('compliance.sanctions.names', []);
        
        foreach ($sanctionedNames as $sanctionedName) {
            if (str_contains($fullName, strtolower($sanctionedName))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check PEP lists.
     */
    protected function passesPEPCheck($user): bool
    {
        // Simplified PEP check
        // In production, this would integrate with PEP screening services
        return true; // PEP status doesn't automatically block, but requires enhanced due diligence
    }

    /**
     * Check geographic restrictions.
     */
    protected function passesGeographicCheck($user): bool
    {
        $userCountry = $user->country?->code;
        $restrictedCountries = config('compliance.restricted_countries', []);
        
        if ($userCountry && in_array($userCountry, $restrictedCountries)) {
            return false;
        }

        // Check IP-based location
        $ipCountry = $this->getCountryFromIP(request()->ip());
        if ($ipCountry && in_array($ipCountry, $restrictedCountries)) {
            return false;
        }

        return true;
    }

    /**
     * Check transaction limits.
     */
    protected function passesTransactionLimitsCheck($user, Request $request): bool
    {
        // Only check for transaction-related requests
        if (!$this->isTransactionRequest($request)) {
            return true;
        }

        $amount = $request->input('amount', 0);
        if ($amount <= 0) {
            return true;
        }

        $dailyLimit = $user->daily_limit ?? config('financial.limits.daily_default', 10000);
        $monthlyLimit = $user->monthly_limit ?? config('financial.limits.monthly_default', 100000);

        $dailyUsage = $this->getDailyTransactionVolume($user);
        $monthlyUsage = $this->getMonthlyTransactionVolume($user);

        return ($dailyUsage + $amount) <= $dailyLimit && 
               ($monthlyUsage + $amount) <= $monthlyLimit;
    }

    /**
     * Check velocity limits.
     */
    protected function passesVelocityCheck($user): bool
    {
        $recentTransactions = $user->sentTransactions()
                                  ->where('created_at', '>=', now()->subHour())
                                  ->count();

        $maxTransactionsPerHour = config('compliance.velocity.max_transactions_per_hour', 5);
        
        return $recentTransactions < $maxTransactionsPerHour;
    }

    /**
     * Perform full compliance check.
     */
    protected function performFullComplianceCheck($user, Request $request): array
    {
        $checks = [
            'sanctions' => $this->passesSanctionsCheck($user),
            'pep' => $this->passesPEPCheck($user),
            'geographic' => $this->passesGeographicCheck($user),
            'transaction_limits' => $this->passesTransactionLimitsCheck($user, $request),
            'velocity' => $this->passesVelocityCheck($user),
        ];

        $passed = !in_array(false, $checks);
        $failedChecks = array_keys(array_filter($checks, fn($check) => !$check));

        return [
            'passed' => $passed,
            'checks' => $checks,
            'failed_checks' => $failedChecks,
            'score' => $passed ? 100 : (count($checks) - count($failedChecks)) / count($checks) * 100,
        ];
    }

    /**
     * Create compliance response.
     */
    protected function createComplianceResponse(string $errorCode, string $message, array $details = []): Response
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'error_code' => $errorCode,
            'compliance_details' => $details,
            'contact_support' => true,
        ], 403);
    }

    /**
     * Check if request is transaction-related.
     */
    protected function isTransactionRequest(Request $request): bool
    {
        $transactionRoutes = [
            'transactions.store',
            'transactions.create',
            'api.transactions.store',
        ];

        return in_array($request->route()?->getName(), $transactionRoutes) ||
               str_contains($request->path(), 'transaction');
    }

    /**
     * Get country from IP address.
     */
    protected function getCountryFromIP(string $ip): ?string
    {
        // In production, use a proper IP geolocation service
        // For now, return null (no restriction)
        return null;
    }

    /**
     * Get daily transaction volume.
     */
    protected function getDailyTransactionVolume($user): float
    {
        return $user->sentTransactions()
                   ->whereDate('created_at', today())
                   ->where('status', '!=', 'failed')
                   ->sum('amount');
    }

    /**
     * Get monthly transaction volume.
     */
    protected function getMonthlyTransactionVolume($user): float
    {
        return $user->sentTransactions()
                   ->whereMonth('created_at', now()->month)
                   ->whereYear('created_at', now()->year)
                   ->where('status', '!=', 'failed')
                   ->sum('amount');
    }
}
