<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Advanced security settings for the financial system
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Authentication Security
    |--------------------------------------------------------------------------
    */

    'authentication' => [
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'password_reset_timeout' => 3600, // 1 hour
        'session_timeout' => 1800, // 30 minutes of inactivity
        
        'password_requirements' => [
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_symbols' => true,
            'prevent_common_passwords' => true,
            'prevent_personal_info' => true,
        ],

        'two_factor' => [
            'enabled' => env('2FA_ENABLED', true),
            'required_for_admin' => true,
            'required_for_high_value' => true,
            'backup_codes_count' => 8,
            'totp_window' => 30, // seconds
        ],

        'device_tracking' => [
            'enabled' => true,
            'require_verification_new_device' => true,
            'remember_device_days' => 30,
            'max_devices_per_user' => 5,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Security
    |--------------------------------------------------------------------------
    */

    'api' => [
        'rate_limiting' => [
            'enabled' => true,
            'requests_per_minute' => 60,
            'requests_per_hour' => 1000,
            'burst_limit' => 10,
        ],

        'token_security' => [
            'expiry_hours' => 24,
            'refresh_token_expiry_days' => 30,
            'rotate_refresh_tokens' => true,
            'revoke_on_password_change' => true,
        ],

        'request_validation' => [
            'max_request_size' => 10485760, // 10MB
            'allowed_content_types' => [
                'application/json',
                'application/x-www-form-urlencoded',
                'multipart/form-data',
            ],
            'validate_user_agent' => true,
            'validate_referer' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Protection
    |--------------------------------------------------------------------------
    */

    'data_protection' => [
        'encryption' => [
            'algorithm' => 'AES-256-GCM',
            'key_rotation_days' => 90,
            'encrypt_sensitive_fields' => true,
            'encrypt_pii' => true,
        ],

        'sensitive_fields' => [
            'national_id',
            'passport_number',
            'bank_account_number',
            'credit_card_number',
            'phone',
            'address',
            'date_of_birth',
        ],

        'data_masking' => [
            'enabled' => true,
            'mask_in_logs' => true,
            'mask_in_responses' => true,
            'mask_character' => '*',
        ],

        'data_retention' => [
            'user_data_years' => 7,
            'transaction_data_years' => 10,
            'log_data_years' => 3,
            'audit_data_years' => 10,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Network Security
    |--------------------------------------------------------------------------
    */

    'network' => [
        'ip_whitelist' => [
            'enabled' => false,
            'admin_routes' => explode(',', env('ADMIN_IP_WHITELIST', '127.0.0.1,::1')),
            'api_routes' => [],
        ],

        'ip_blacklist' => [
            'enabled' => true,
            'auto_block_suspicious' => true,
            'block_duration_hours' => 24,
            'max_violations_per_hour' => 10,
        ],

        'ddos_protection' => [
            'enabled' => true,
            'requests_per_second' => 100,
            'burst_capacity' => 200,
            'block_duration_minutes' => 60,
        ],

        'ssl_tls' => [
            'force_https' => env('FORCE_HTTPS', true),
            'hsts_enabled' => true,
            'hsts_max_age' => 31536000, // 1 year
            'certificate_pinning' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Input Validation & Sanitization
    |--------------------------------------------------------------------------
    */

    'input_validation' => [
        'xss_protection' => [
            'enabled' => true,
            'strip_tags' => true,
            'encode_entities' => true,
            'allowed_tags' => [],
        ],

        'sql_injection_protection' => [
            'enabled' => true,
            'detect_patterns' => true,
            'block_suspicious_queries' => true,
            'log_attempts' => true,
        ],

        'file_upload' => [
            'max_size' => 5242880, // 5MB
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
            'scan_for_malware' => true,
            'quarantine_suspicious' => true,
        ],

        'content_security' => [
            'csp_enabled' => true,
            'csp_policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
            'x_frame_options' => 'DENY',
            'x_content_type_options' => 'nosniff',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Fraud Prevention
    |--------------------------------------------------------------------------
    */

    'fraud_prevention' => [
        'device_fingerprinting' => [
            'enabled' => true,
            'track_browser' => true,
            'track_screen_resolution' => true,
            'track_timezone' => true,
            'track_plugins' => true,
        ],

        'behavioral_analysis' => [
            'enabled' => true,
            'track_typing_patterns' => false,
            'track_mouse_movements' => false,
            'track_session_duration' => true,
            'track_navigation_patterns' => true,
        ],

        'geolocation' => [
            'enabled' => true,
            'block_vpn_proxy' => true,
            'block_tor' => true,
            'unusual_location_threshold' => 1000, // km
            'require_verification_new_location' => true,
        ],

        'velocity_checks' => [
            'enabled' => true,
            'max_transactions_per_hour' => 10,
            'max_amount_per_hour' => 50000,
            'max_failed_attempts_per_hour' => 5,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Alerting
    |--------------------------------------------------------------------------
    */

    'monitoring' => [
        'security_events' => [
            'log_all_events' => true,
            'real_time_alerts' => true,
            'alert_channels' => ['email', 'slack', 'sms'],
        ],

        'suspicious_activities' => [
            'multiple_failed_logins' => 3,
            'unusual_transaction_patterns' => true,
            'admin_access_outside_hours' => true,
            'bulk_data_access' => true,
            'privilege_escalation_attempts' => true,
        ],

        'compliance_monitoring' => [
            'gdpr_compliance' => true,
            'pci_dss_compliance' => true,
            'aml_compliance' => true,
            'kyc_compliance' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Incident Response
    |--------------------------------------------------------------------------
    */

    'incident_response' => [
        'auto_response' => [
            'enabled' => true,
            'block_suspicious_ips' => true,
            'disable_compromised_accounts' => true,
            'escalate_critical_incidents' => true,
        ],

        'notification_channels' => [
            'security_team' => [
                'email' => '<EMAIL>',
                'slack' => '#security-alerts',
                'phone' => '+************',
            ],
            'management' => [
                'email' => '<EMAIL>',
                'escalation_delay' => 300, // 5 minutes
            ],
        ],

        'incident_classification' => [
            'low' => [
                'response_time' => 3600, // 1 hour
                'escalation_time' => 14400, // 4 hours
            ],
            'medium' => [
                'response_time' => 1800, // 30 minutes
                'escalation_time' => 3600, // 1 hour
            ],
            'high' => [
                'response_time' => 900, // 15 minutes
                'escalation_time' => 1800, // 30 minutes
            ],
            'critical' => [
                'response_time' => 300, // 5 minutes
                'escalation_time' => 600, // 10 minutes
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup & Recovery
    |--------------------------------------------------------------------------
    */

    'backup_security' => [
        'encrypt_backups' => true,
        'backup_retention_days' => 90,
        'offsite_backup' => true,
        'backup_verification' => true,
        'disaster_recovery_plan' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    */

    'headers' => [
        'x_frame_options' => 'DENY',
        'x_content_type_options' => 'nosniff',
        'x_xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'permissions_policy' => 'geolocation=(), microphone=(), camera=()',
        'strict_transport_security' => 'max-age=31536000; includeSubDomains',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Testing
    |--------------------------------------------------------------------------
    */

    'testing' => [
        'penetration_testing' => [
            'frequency' => 'quarterly',
            'external_vendor' => true,
            'scope' => ['web_application', 'api', 'infrastructure'],
        ],

        'vulnerability_scanning' => [
            'frequency' => 'weekly',
            'automated' => true,
            'manual_review' => 'monthly',
        ],

        'security_audits' => [
            'frequency' => 'annually',
            'compliance_audits' => true,
            'code_security_review' => 'quarterly',
        ],
    ],

];
