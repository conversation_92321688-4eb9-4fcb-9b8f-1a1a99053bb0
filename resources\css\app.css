@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

/* Base Styles */
@layer base {
  html {
    font-family: 'Inter', 'Tajawal', system-ui, sans-serif;
  }

  [dir="rtl"] {
    font-family: 'Tajawal', 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    font-feature-settings: 'cv03', 'cv04', 'cv11';
  }

  * {
    @apply border-gray-200;
  }
}

/* Component Styles */
@layer components {
  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-sm;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm;
  }

  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 shadow-sm;
  }

  .btn-outline {
    @apply bg-white border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  /* Form Elements */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  .form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  .form-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
  }

  .form-radio {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply mt-1 text-sm text-red-600;
  }

  .form-help {
    @apply mt-1 text-sm text-gray-500;
  }

  /* Cards */
  .card {
    @apply bg-white shadow rounded-lg;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-secondary {
    @apply bg-gray-100 text-gray-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800;
  }

  /* Alerts */
  .alert {
    @apply p-4 rounded-md;
  }

  .alert-success {
    @apply bg-green-50 border border-green-200 text-green-800;
  }

  .alert-error {
    @apply bg-red-50 border border-red-200 text-red-800;
  }

  .alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
  }

  .alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-800;
  }

  /* Navigation */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }

  .nav-link-active {
    @apply bg-blue-100 text-blue-700;
  }

  .nav-link-inactive {
    @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900;
  }

  /* Tables */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600;
  }

  .loading-dots {
    @apply flex space-x-1;
  }

  .loading-dot {
    @apply h-2 w-2 bg-blue-600 rounded-full animate-pulse;
  }
}

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}
