<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Country extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'code',
        'iso2',
        'phone_code',
        'currency_code',
        'flag_url',
        'is_active',
        'supports_transfers',
        'supports_crypto',
        'max_transfer_amount',
        'min_transfer_amount',
        'commission_rate',
        'supported_payment_methods',
        'required_documents',
        'regulations',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'supports_transfers' => 'boolean',
        'supports_crypto' => 'boolean',
        'max_transfer_amount' => 'decimal:2',
        'min_transfer_amount' => 'decimal:2',
        'commission_rate' => 'decimal:4',
        'supported_payment_methods' => 'array',
        'required_documents' => 'array',
    ];

    /**
     * Get the branches for the country.
     */
    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    /**
     * Get the users for the country.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the transactions sent from this country.
     */
    public function sentTransactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'sender_country_id');
    }

    /**
     * Get the transactions received in this country.
     */
    public function receivedTransactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'receiver_country_id');
    }

    /**
     * Scope a query to only include active countries.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include countries that support transfers.
     */
    public function scopeSupportsTransfers($query)
    {
        return $query->where('supports_transfers', true);
    }

    /**
     * Scope a query to only include countries that support crypto.
     */
    public function scopeSupportsCrypto($query)
    {
        return $query->where('supports_crypto', true);
    }

    /**
     * Get the country name based on current locale.
     */
    public function getNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Check if the country supports a specific payment method.
     */
    public function supportsPaymentMethod(string $method): bool
    {
        return in_array($method, $this->supported_payment_methods ?? []);
    }

    /**
     * Get required documents for this country.
     */
    public function getRequiredDocuments(): array
    {
        return $this->required_documents ?? [];
    }
}
