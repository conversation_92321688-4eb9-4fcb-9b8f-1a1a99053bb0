<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'password',
        'user_type',
        'status',
        'country_id',
        'branch_id',
        'national_id',
        'date_of_birth',
        'gender',
        'address',
        'city',
        'postal_code',
        'avatar',
        'preferred_language',
        'preferred_currency',
        'two_factor_enabled',
        'biometric_enabled',
        'daily_limit',
        'monthly_limit',
        'risk_level',
        'kyc_documents',
        'kyc_verified_at',
        'aml_verified',
        'aml_verified_at',
        'last_login_at',
        'last_login_ip',
        'login_history',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'biometric_data',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'two_factor_enabled' => 'boolean',
            'biometric_enabled' => 'boolean',
            'daily_limit' => 'decimal:2',
            'monthly_limit' => 'decimal:2',
            'kyc_documents' => 'array',
            'kyc_verified_at' => 'datetime',
            'aml_verified' => 'boolean',
            'aml_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'login_history' => 'array',
            'two_factor_recovery_codes' => 'array',
            'biometric_data' => 'array',
        ];
    }

    /**
     * Get the country that the user belongs to.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the branch that the user belongs to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the wallets for the user.
     */
    public function wallets(): HasMany
    {
        return $this->hasMany(Wallet::class);
    }

    /**
     * Get the sent transactions for the user.
     */
    public function sentTransactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'sender_id');
    }

    /**
     * Get the received transactions for the user.
     */
    public function receivedTransactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'receiver_id');
    }

    /**
     * Get the notifications for the user.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get the fraud detection alerts for the user.
     */
    public function fraudAlerts(): HasMany
    {
        return $this->hasMany(FraudDetection::class);
    }

    /**
     * Get the audit logs for the user.
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Check if user is verified (KYC and AML).
     */
    public function isVerified(): bool
    {
        return $this->kyc_verified_at !== null && $this->aml_verified;
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->user_type === 'admin';
    }

    /**
     * Check if user is an agent.
     */
    public function isAgent(): bool
    {
        return $this->user_type === 'agent';
    }

    /**
     * Check if user is a customer.
     */
    public function isCustomer(): bool
    {
        return $this->user_type === 'customer';
    }

    /**
     * Check if user is a branch manager.
     */
    public function isBranchManager(): bool
    {
        return $this->user_type === 'branch_manager';
    }

    /**
     * Get user's wallet for a specific currency.
     */
    public function getWalletForCurrency(int $currencyId): ?Wallet
    {
        return $this->wallets()->where('currency_id', $currencyId)->first();
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include verified users.
     */
    public function scopeVerified($query)
    {
        return $query->whereNotNull('kyc_verified_at')->where('aml_verified', true);
    }
}
