<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

abstract class BaseException extends Exception
{
    /**
     * The error code for this exception.
     */
    protected string $errorCode;

    /**
     * The HTTP status code for this exception.
     */
    protected int $statusCode = 500;

    /**
     * Additional context data for this exception.
     */
    protected array $context = [];

    /**
     * Whether this exception should be logged.
     */
    protected bool $shouldLog = true;

    /**
     * Whether this exception should be reported.
     */
    protected bool $shouldReport = true;

    /**
     * Create a new exception instance.
     */
    public function __construct(string $message = '', array $context = [], ?Exception $previous = null)
    {
        $this->context = $context;
        parent::__construct($message, 0, $previous);
    }

    /**
     * Get the error code for this exception.
     */
    public function getErrorCode(): string
    {
        return $this->errorCode ?? 'UNKNOWN_ERROR';
    }

    /**
     * Get the HTTP status code for this exception.
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the context data for this exception.
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set additional context data.
     */
    public function setContext(array $context): self
    {
        $this->context = array_merge($this->context, $context);
        return $this;
    }

    /**
     * Determine if this exception should be logged.
     */
    public function shouldLog(): bool
    {
        return $this->shouldLog;
    }

    /**
     * Determine if this exception should be reported.
     */
    public function shouldReport(): bool
    {
        return $this->shouldReport;
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(Request $request): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $this->getMessage(),
            'error_code' => $this->getErrorCode(),
            'timestamp' => now()->toISOString(),
        ];

        // Add context in development environment
        if (app()->environment('local', 'development')) {
            $response['context'] = $this->getContext();
            $response['trace'] = $this->getTraceAsString();
        }

        // Add additional data if available
        if (method_exists($this, 'getAdditionalData')) {
            $response = array_merge($response, $this->getAdditionalData());
        }

        return response()->json($response, $this->getStatusCode());
    }

    /**
     * Convert the exception to an array.
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'error_code' => $this->getErrorCode(),
            'status_code' => $this->getStatusCode(),
            'context' => $this->getContext(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
        ];
    }

    /**
     * Convert the exception to a string.
     */
    public function __toString(): string
    {
        return sprintf(
            '%s: [%s] %s in %s:%d',
            static::class,
            $this->getErrorCode(),
            $this->getMessage(),
            $this->getFile(),
            $this->getLine()
        );
    }
}
