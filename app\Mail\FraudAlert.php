<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\FraudDetection;

class FraudAlert extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public FraudDetection $fraudDetection;

    /**
     * Create a new message instance.
     */
    public function __construct(FraudDetection $fraudDetection)
    {
        $this->fraudDetection = $fraudDetection;
        
        // Set high priority for fraud alerts
        $this->priority = 1;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $urgencyText = match($this->fraudDetection->risk_level) {
            'critical' => '[عاجل جداً - URGENT]',
            'high' => '[عاجل - HIGH PRIORITY]',
            'medium' => '[متوسط - MEDIUM]',
            default => '[تنبيه - ALERT]'
        };

        return new Envelope(
            subject: $urgencyText . ' تنبيه احتيال - Fraud Alert #' . $this->fraudDetection->id,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.fraud-alert',
            with: [
                'fraudDetection' => $this->fraudDetection,
                'transaction' => $this->fraudDetection->transaction,
                'user' => $this->fraudDetection->user,
                'riskLevel' => $this->fraudDetection->risk_level,
                'riskScore' => $this->fraudDetection->risk_score,
                'indicators' => $this->fraudDetection->fraud_indicators,
                'urgencyColor' => $this->getUrgencyColor(),
                'urgencyText' => $this->getUrgencyText(),
                'recommendedActions' => $this->getRecommendedActions(),
                'dashboardUrl' => config('app.url') . '/admin/fraud-detection/' . $this->fraudDetection->id,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get urgency color based on risk level.
     */
    private function getUrgencyColor(): string
    {
        return match($this->fraudDetection->risk_level) {
            'critical' => '#dc2626', // Red
            'high' => '#ea580c',      // Orange-red
            'medium' => '#d97706',    // Orange
            'low' => '#65a30d',       // Green
            default => '#6b7280'      // Gray
        };
    }

    /**
     * Get urgency text in both languages.
     */
    private function getUrgencyText(): array
    {
        return match($this->fraudDetection->risk_level) {
            'critical' => [
                'ar' => 'خطر حرج - يتطلب تدخل فوري',
                'en' => 'Critical Risk - Immediate Action Required'
            ],
            'high' => [
                'ar' => 'خطر عالي - يتطلب مراجعة عاجلة',
                'en' => 'High Risk - Urgent Review Required'
            ],
            'medium' => [
                'ar' => 'خطر متوسط - يتطلب مراجعة',
                'en' => 'Medium Risk - Review Required'
            ],
            'low' => [
                'ar' => 'خطر منخفض - للمراجعة',
                'en' => 'Low Risk - For Review'
            ],
            default => [
                'ar' => 'تنبيه أمني',
                'en' => 'Security Alert'
            ]
        };
    }

    /**
     * Get recommended actions based on risk level.
     */
    private function getRecommendedActions(): array
    {
        return match($this->fraudDetection->risk_level) {
            'critical' => [
                [
                    'action_ar' => 'حجب المعاملة فوراً',
                    'action_en' => 'Block transaction immediately',
                    'priority' => 'immediate'
                ],
                [
                    'action_ar' => 'تجميد الحساب مؤقتاً',
                    'action_en' => 'Temporarily freeze account',
                    'priority' => 'immediate'
                ],
                [
                    'action_ar' => 'التواصل مع العميل للتحقق',
                    'action_en' => 'Contact customer for verification',
                    'priority' => 'urgent'
                ],
                [
                    'action_ar' => 'إبلاغ السلطات المختصة',
                    'action_en' => 'Report to relevant authorities',
                    'priority' => 'urgent'
                ]
            ],
            'high' => [
                [
                    'action_ar' => 'مراجعة المعاملة يدوياً',
                    'action_en' => 'Manual transaction review',
                    'priority' => 'urgent'
                ],
                [
                    'action_ar' => 'طلب وثائق إضافية',
                    'action_en' => 'Request additional documentation',
                    'priority' => 'high'
                ],
                [
                    'action_ar' => 'تحديث ملف المخاطر للعميل',
                    'action_en' => 'Update customer risk profile',
                    'priority' => 'medium'
                ]
            ],
            'medium' => [
                [
                    'action_ar' => 'مراجعة نشاط الحساب',
                    'action_en' => 'Review account activity',
                    'priority' => 'medium'
                ],
                [
                    'action_ar' => 'مراقبة المعاملات المستقبلية',
                    'action_en' => 'Monitor future transactions',
                    'priority' => 'low'
                ]
            ],
            default => [
                [
                    'action_ar' => 'توثيق الحادثة',
                    'action_en' => 'Document incident',
                    'priority' => 'low'
                ]
            ]
        ];
    }
}
