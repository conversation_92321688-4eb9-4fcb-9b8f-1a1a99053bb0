<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class CurrencyController extends Controller
{
    /**
     * Get all active currencies.
     */
    public function index(Request $request): JsonResponse
    {
        $currencies = Cache::remember('currencies_active', 3600, function () {
            return Currency::active()
                ->with(['exchangeRatesFrom', 'exchangeRatesTo'])
                ->orderBy('is_base_currency', 'desc')
                ->orderBy('name_en')
                ->get();
        });

        return response()->json([
            'success' => true,
            'data' => $currencies,
        ]);
    }

    /**
     * Get specific currency details.
     */
    public function show($id): JsonResponse
    {
        $currency = Currency::with([
            'exchangeRatesFrom.toCurrency',
            'exchangeRatesTo.fromCurrency',
            'wallets' => function ($query) {
                $query->where('is_active', true)->limit(10);
            }
        ])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $currency,
        ]);
    }

    /**
     * Get cryptocurrency prices.
     */
    public function cryptoPrices(): JsonResponse
    {
        $cryptoCurrencies = Currency::crypto()
            ->active()
            ->get()
            ->map(function ($currency) {
                return [
                    'id' => $currency->id,
                    'code' => $currency->code,
                    'name' => $currency->name,
                    'symbol' => $currency->symbol,
                    'rate_to_usd' => $currency->rate_to_usd,
                    'network' => $currency->crypto_network,
                    'contract_address' => $currency->contract_address,
                    'last_updated' => now(),
                    // In real implementation, fetch from external API
                    'price_change_24h' => rand(-10, 15) / 100, // Mock data
                    'volume_24h' => rand(1000000, 10000000),
                    'market_cap' => $currency->rate_to_usd * rand(1000000, 100000000),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $cryptoCurrencies,
            'timestamp' => now(),
        ]);
    }

    /**
     * Get supported currencies for a country.
     */
    public function supportedInCountry($countryCode): JsonResponse
    {
        $currencies = Currency::active()
            ->whereJsonContains('supported_countries', $countryCode)
            ->orderBy('is_base_currency', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $currencies,
            'country_code' => $countryCode,
        ]);
    }

    /**
     * Convert amount between currencies.
     */
    public function convert(Request $request): JsonResponse
    {
        $request->validate([
            'from_currency' => 'required|exists:currencies,code',
            'to_currency' => 'required|exists:currencies,code',
            'amount' => 'required|numeric|min:0.01',
        ]);

        $fromCurrency = Currency::where('code', $request->from_currency)->first();
        $toCurrency = Currency::where('code', $request->to_currency)->first();

        if ($fromCurrency->id === $toCurrency->id) {
            return response()->json([
                'success' => true,
                'data' => [
                    'from_currency' => $fromCurrency->code,
                    'to_currency' => $toCurrency->code,
                    'amount' => $request->amount,
                    'converted_amount' => $request->amount,
                    'exchange_rate' => 1.0,
                    'timestamp' => now(),
                ],
            ]);
        }

        $exchangeRate = ExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->where('is_active', true)
            ->first();

        if (!$exchangeRate) {
            // Try reverse rate
            $reverseRate = ExchangeRate::where('from_currency_id', $toCurrency->id)
                ->where('to_currency_id', $fromCurrency->id)
                ->where('is_active', true)
                ->first();

            if ($reverseRate) {
                $rate = 1 / $reverseRate->rate;
            } else {
                // Calculate via USD if no direct rate
                $fromToUsd = $fromCurrency->rate_to_usd;
                $toToUsd = $toCurrency->rate_to_usd;
                $rate = $toToUsd / $fromToUsd;
            }
        } else {
            $rate = $exchangeRate->rate;
        }

        $convertedAmount = $request->amount * $rate;

        return response()->json([
            'success' => true,
            'data' => [
                'from_currency' => $fromCurrency->code,
                'to_currency' => $toCurrency->code,
                'amount' => $request->amount,
                'converted_amount' => round($convertedAmount, $toCurrency->decimal_places),
                'exchange_rate' => $rate,
                'fee_percentage' => 0.5, // 0.5% conversion fee
                'fee_amount' => round($convertedAmount * 0.005, $toCurrency->decimal_places),
                'net_amount' => round($convertedAmount * 0.995, $toCurrency->decimal_places),
                'timestamp' => now(),
            ],
        ]);
    }

    /**
     * Get currency statistics.
     */
    public function statistics($code): JsonResponse
    {
        $currency = Currency::where('code', $code)->firstOrFail();

        $stats = [
            'total_wallets' => $currency->wallets()->count(),
            'active_wallets' => $currency->wallets()->where('is_active', true)->count(),
            'total_balance' => $currency->wallets()->sum('balance'),
            'total_transactions' => $currency->transactions()->count(),
            'transactions_today' => $currency->transactions()
                ->whereDate('created_at', today())
                ->count(),
            'volume_today' => $currency->transactions()
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->sum('amount'),
            'volume_this_month' => $currency->transactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', 'completed')
                ->sum('amount'),
            'average_transaction_amount' => $currency->transactions()
                ->where('status', 'completed')
                ->avg('amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'currency' => $currency,
                'statistics' => $stats,
            ],
        ]);
    }

    /**
     * Get trending currencies.
     */
    public function trending(): JsonResponse
    {
        $trending = Currency::active()
            ->withCount(['transactions' => function ($query) {
                $query->where('created_at', '>=', now()->subDays(7));
            }])
            ->orderBy('transactions_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($currency) {
                return [
                    'id' => $currency->id,
                    'code' => $currency->code,
                    'name' => $currency->name,
                    'symbol' => $currency->symbol,
                    'is_crypto' => $currency->is_crypto,
                    'transactions_count' => $currency->transactions_count,
                    'trend_percentage' => rand(-20, 50), // Mock trend data
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $trending,
            'period' => 'last_7_days',
        ]);
    }
}
