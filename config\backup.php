<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Backup Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for automated backup system
    |
    */

    'enabled' => env('BACKUP_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Backup Schedule
    |--------------------------------------------------------------------------
    */

    'schedule' => [
        'database' => [
            'frequency' => 'daily',
            'time' => '02:00',
            'retention_days' => 30,
            'compress' => true,
            'encrypt' => true,
        ],

        'files' => [
            'frequency' => 'daily',
            'time' => '03:00',
            'retention_days' => 7,
            'compress' => true,
            'encrypt' => true,
        ],

        'full_system' => [
            'frequency' => 'weekly',
            'day' => 'sunday',
            'time' => '01:00',
            'retention_weeks' => 4,
            'compress' => true,
            'encrypt' => true,
        ],

        'incremental' => [
            'frequency' => 'hourly',
            'retention_hours' => 72,
            'compress' => true,
            'encrypt' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Sources
    |--------------------------------------------------------------------------
    */

    'sources' => [
        'database' => [
            'enabled' => true,
            'connections' => ['mysql'],
            'tables' => [
                'include' => [], // Empty means all tables
                'exclude' => ['sessions', 'cache', 'telescope_entries'],
            ],
            'options' => [
                'single_transaction' => true,
                'lock_tables' => false,
                'add_drop_table' => true,
                'extended_insert' => true,
            ],
        ],

        'files' => [
            'enabled' => true,
            'paths' => [
                storage_path('app'),
                storage_path('logs'),
                base_path('config'),
                base_path('.env'),
            ],
            'exclude' => [
                storage_path('framework/cache'),
                storage_path('framework/sessions'),
                storage_path('framework/views'),
                storage_path('logs/*.log'),
            ],
        ],

        'user_uploads' => [
            'enabled' => true,
            'paths' => [
                storage_path('app/documents'),
                storage_path('app/receipts'),
                storage_path('app/kyc'),
            ],
            'priority' => 'high',
        ],

        'system_config' => [
            'enabled' => true,
            'paths' => [
                base_path('config'),
                base_path('routes'),
                base_path('database/migrations'),
                base_path('database/seeders'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Destinations
    |--------------------------------------------------------------------------
    */

    'destinations' => [
        'local' => [
            'enabled' => true,
            'path' => storage_path('backups'),
            'priority' => 1,
        ],

        's3' => [
            'enabled' => env('BACKUP_S3_ENABLED', false),
            'disk' => 's3',
            'bucket' => env('AWS_BACKUP_BUCKET'),
            'path' => 'backups',
            'priority' => 2,
            'storage_class' => 'STANDARD_IA',
        ],

        'google_drive' => [
            'enabled' => env('BACKUP_GOOGLE_DRIVE_ENABLED', false),
            'disk' => 'google',
            'folder_id' => env('GOOGLE_DRIVE_BACKUP_FOLDER_ID'),
            'priority' => 3,
        ],

        'ftp' => [
            'enabled' => env('BACKUP_FTP_ENABLED', false),
            'host' => env('BACKUP_FTP_HOST'),
            'username' => env('BACKUP_FTP_USERNAME'),
            'password' => env('BACKUP_FTP_PASSWORD'),
            'path' => '/backups',
            'priority' => 4,
        ],

        'sftp' => [
            'enabled' => env('BACKUP_SFTP_ENABLED', false),
            'host' => env('BACKUP_SFTP_HOST'),
            'username' => env('BACKUP_SFTP_USERNAME'),
            'private_key' => env('BACKUP_SFTP_PRIVATE_KEY'),
            'path' => '/backups',
            'priority' => 5,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Security
    |--------------------------------------------------------------------------
    */

    'security' => [
        'encryption' => [
            'enabled' => true,
            'algorithm' => 'AES-256-CBC',
            'key' => env('BACKUP_ENCRYPTION_KEY'),
            'compress_before_encrypt' => true,
        ],

        'integrity' => [
            'enabled' => true,
            'algorithm' => 'sha256',
            'verify_after_backup' => true,
            'store_checksums' => true,
        ],

        'access_control' => [
            'restrict_access' => true,
            'allowed_ips' => explode(',', env('BACKUP_ALLOWED_IPS', '127.0.0.1')),
            'require_authentication' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Monitoring
    |--------------------------------------------------------------------------
    */

    'monitoring' => [
        'enabled' => true,
        'check_interval' => 3600, // 1 hour
        'max_backup_age_hours' => 25, // Alert if no backup in 25 hours
        'min_backup_size_mb' => 1, // Alert if backup is too small

        'notifications' => [
            'success' => false,
            'failure' => true,
            'missing' => true,
            'corrupted' => true,
        ],

        'channels' => [
            'email' => [
                'enabled' => true,
                'recipients' => explode(',', env('BACKUP_NOTIFICATION_EMAILS', '<EMAIL>')),
            ],
            'slack' => [
                'enabled' => env('BACKUP_SLACK_ENABLED', false),
                'webhook_url' => env('BACKUP_SLACK_WEBHOOK'),
                'channel' => '#backups',
            ],
            'discord' => [
                'enabled' => env('BACKUP_DISCORD_ENABLED', false),
                'webhook_url' => env('BACKUP_DISCORD_WEBHOOK'),
            ],
        ],

        'health_checks' => [
            'verify_backup_integrity' => true,
            'test_restore_process' => 'weekly',
            'check_storage_space' => true,
            'validate_encryption' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Cleanup
    |--------------------------------------------------------------------------
    */

    'cleanup' => [
        'enabled' => true,
        'strategy' => 'grandfather_father_son', // or 'simple_retention'
        
        'retention_policy' => [
            'daily' => 7,   // Keep 7 daily backups
            'weekly' => 4,  // Keep 4 weekly backups
            'monthly' => 12, // Keep 12 monthly backups
            'yearly' => 5,  // Keep 5 yearly backups
        ],

        'cleanup_schedule' => [
            'frequency' => 'daily',
            'time' => '04:00',
        ],

        'emergency_cleanup' => [
            'enabled' => true,
            'trigger_when_disk_usage' => 90, // percentage
            'keep_minimum_backups' => 3,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Restore Configuration
    |--------------------------------------------------------------------------
    */

    'restore' => [
        'enabled' => true,
        'require_confirmation' => true,
        'create_restore_point' => true,
        'verify_before_restore' => true,
        'test_mode' => env('BACKUP_RESTORE_TEST_MODE', false),

        'database_restore' => [
            'drop_existing_tables' => false,
            'disable_foreign_keys' => true,
            'timeout' => 3600, // 1 hour
        ],

        'file_restore' => [
            'overwrite_existing' => false,
            'preserve_permissions' => true,
            'timeout' => 7200, // 2 hours
        ],

        'notifications' => [
            'before_restore' => true,
            'after_restore' => true,
            'on_failure' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    */

    'performance' => [
        'compression' => [
            'enabled' => true,
            'algorithm' => 'gzip', // gzip, bzip2, xz
            'level' => 6, // 1-9, higher = better compression but slower
        ],

        'parallel_processing' => [
            'enabled' => true,
            'max_processes' => 4,
            'chunk_size' => 1048576, // 1MB
        ],

        'memory_limit' => '512M',
        'timeout' => 7200, // 2 hours
        'nice_level' => 10, // Lower priority for backup processes
    ],

    /*
    |--------------------------------------------------------------------------
    | Disaster Recovery
    |--------------------------------------------------------------------------
    */

    'disaster_recovery' => [
        'enabled' => true,
        'offsite_backup_required' => true,
        'minimum_backup_locations' => 2,
        'recovery_time_objective' => 4, // hours
        'recovery_point_objective' => 1, // hours

        'emergency_contacts' => [
            'primary' => [
                'name' => 'IT Manager',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
            ],
            'secondary' => [
                'name' => 'CTO',
                'email' => '<EMAIL>',
                'phone' => '+966501234568',
            ],
        ],

        'recovery_procedures' => [
            'documented' => true,
            'tested_frequency' => 'quarterly',
            'last_test_date' => null,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance & Auditing
    |--------------------------------------------------------------------------
    */

    'compliance' => [
        'audit_logging' => [
            'enabled' => true,
            'log_backup_operations' => true,
            'log_restore_operations' => true,
            'log_access_attempts' => true,
            'retention_years' => 7,
        ],

        'regulatory_requirements' => [
            'financial_data_retention' => 7, // years
            'audit_trail_retention' => 10, // years
            'encryption_required' => true,
            'offsite_storage_required' => true,
        ],

        'reporting' => [
            'generate_backup_reports' => true,
            'report_frequency' => 'monthly',
            'include_metrics' => true,
            'include_compliance_status' => true,
        ],
    ],

];
