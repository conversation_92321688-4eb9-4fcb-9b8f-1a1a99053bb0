<?php

namespace App\Events;

use App\Models\FraudDetection;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FraudAlertTriggered implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public FraudDetection $fraudAlert;
    public Transaction $transaction;
    public User $user;

    /**
     * Create a new event instance.
     */
    public function __construct(FraudDetection $fraudAlert, Transaction $transaction, User $user)
    {
        $this->fraudAlert = $fraudAlert;
        $this->transaction = $transaction;
        $this->user = $user;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('admin.fraud-alerts'),
            new PrivateChannel('security.alerts'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'alert' => [
                'id' => $this->fraudAlert->id,
                'risk_score' => $this->fraudAlert->risk_score,
                'risk_level' => $this->fraudAlert->risk_level,
                'risk_factors' => $this->fraudAlert->risk_factors,
                'detection_method' => $this->fraudAlert->detection_method,
                'detected_at' => $this->fraudAlert->detected_at,
            ],
            'transaction' => [
                'id' => $this->transaction->id,
                'transaction_number' => $this->transaction->transaction_number,
                'amount' => $this->transaction->amount,
                'currency' => $this->transaction->currency->code,
                'status' => $this->transaction->status,
                'sender_name' => $this->transaction->sender_name,
                'receiver_name' => $this->transaction->receiver_name,
            ],
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->full_name,
                'email' => $this->user->email,
                'risk_level' => $this->user->risk_level,
                'country' => $this->user->country?->name,
            ],
            'message' => $this->getAlertMessage(),
            'priority' => $this->getAlertPriority(),
            'timestamp' => now(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'fraud.alert.triggered';
    }

    /**
     * Get alert message based on risk level.
     */
    private function getAlertMessage(): string
    {
        switch ($this->fraudAlert->risk_level) {
            case 'high':
                return 'HIGH RISK: Suspicious transaction detected and blocked';
            case 'medium':
                return 'MEDIUM RISK: Transaction flagged for review';
            case 'low':
                return 'LOW RISK: Minor risk indicators detected';
            default:
                return 'Fraud alert triggered';
        }
    }

    /**
     * Get alert priority.
     */
    private function getAlertPriority(): string
    {
        switch ($this->fraudAlert->risk_level) {
            case 'high':
                return 'critical';
            case 'medium':
                return 'high';
            case 'low':
                return 'medium';
            default:
                return 'low';
        }
    }
}
