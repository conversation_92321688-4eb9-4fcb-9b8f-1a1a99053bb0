<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlockchainTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'blockchain_network',
        'transaction_hash',
        'from_address',
        'to_address',
        'amount',
        'gas_fee',
        'gas_used',
        'gas_price',
        'block_number',
        'block_hash',
        'confirmation_count',
        'status',
        'raw_transaction',
        'receipt_data',
        'error_message',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'gas_fee' => 'decimal:8',
        'gas_used' => 'integer',
        'gas_price' => 'decimal:8',
        'block_number' => 'integer',
        'confirmation_count' => 'integer',
        'raw_transaction' => 'array',
        'receipt_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the transaction that this blockchain transaction belongs to.
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Scope for transactions by blockchain network.
     */
    public function scopeByNetwork($query, string $network)
    {
        return $query->where('blockchain_network', $network);
    }

    /**
     * Scope for confirmed transactions.
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope for pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for failed transactions.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Check if transaction is confirmed.
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed' && $this->confirmation_count >= $this->getRequiredConfirmations();
    }

    /**
     * Get required confirmations for the blockchain network.
     */
    public function getRequiredConfirmations(): int
    {
        return match ($this->blockchain_network) {
            'bitcoin' => 6,
            'ethereum' => 12,
            'binance_smart_chain' => 20,
            'polygon' => 30,
            'tron' => 19,
            default => 6,
        };
    }

    /**
     * Get blockchain explorer URL.
     */
    public function getExplorerUrlAttribute(): ?string
    {
        if (!$this->transaction_hash) {
            return null;
        }

        $baseUrls = [
            'bitcoin' => 'https://blockstream.info/tx/',
            'ethereum' => 'https://etherscan.io/tx/',
            'binance_smart_chain' => 'https://bscscan.com/tx/',
            'polygon' => 'https://polygonscan.com/tx/',
            'tron' => 'https://tronscan.org/#/transaction/',
        ];

        $baseUrl = $baseUrls[$this->blockchain_network] ?? null;
        
        return $baseUrl ? $baseUrl . $this->transaction_hash : null;
    }

    /**
     * Get confirmation progress percentage.
     */
    public function getConfirmationProgressAttribute(): float
    {
        $required = $this->getRequiredConfirmations();
        $current = min($this->confirmation_count, $required);
        
        return ($current / $required) * 100;
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'confirmed' => 'green',
            'pending' => 'yellow',
            'failed' => 'red',
            'cancelled' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Update confirmation count.
     */
    public function updateConfirmations(int $confirmations): void
    {
        $this->update([
            'confirmation_count' => $confirmations,
            'status' => $confirmations >= $this->getRequiredConfirmations() ? 'confirmed' : 'pending',
        ]);
    }

    /**
     * Mark as failed.
     */
    public function markAsFailed(string $errorMessage = ''): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Get formatted blockchain transaction data.
     */
    public function getFormattedDataAttribute(): array
    {
        return [
            'id' => $this->id,
            'transaction_id' => $this->transaction_id,
            'blockchain_network' => $this->blockchain_network,
            'transaction_hash' => $this->transaction_hash,
            'from_address' => $this->from_address,
            'to_address' => $this->to_address,
            'amount' => $this->amount,
            'gas_fee' => $this->gas_fee,
            'block_number' => $this->block_number,
            'confirmation_count' => $this->confirmation_count,
            'required_confirmations' => $this->getRequiredConfirmations(),
            'confirmation_progress' => $this->confirmation_progress,
            'status' => $this->status,
            'status_color' => $this->status_color,
            'explorer_url' => $this->explorer_url,
            'is_confirmed' => $this->isConfirmed(),
            'error_message' => $this->error_message,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }

    /**
     * Create blockchain transaction record.
     */
    public static function createTransaction(array $data): self
    {
        return self::create($data);
    }

    /**
     * Find by transaction hash.
     */
    public static function findByHash(string $hash): ?self
    {
        return self::where('transaction_hash', $hash)->first();
    }

    /**
     * Get transactions needing confirmation updates.
     */
    public static function needingConfirmationUpdate()
    {
        return self::where('status', 'pending')
                   ->where('transaction_hash', '!=', null)
                   ->get();
    }
}
