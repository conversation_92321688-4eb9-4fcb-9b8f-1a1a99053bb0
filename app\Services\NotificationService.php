<?php

namespace App\Services;

use App\Models\User;
use App\Models\Notification;
use App\Jobs\SendTransactionEmailJob;
use App\Jobs\SendTransactionSMSJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class NotificationService
{
    protected SMSService $smsService;

    public function __construct(SMSService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Send notification to user.
     */
    public function sendNotification(User $user, array $data): bool
    {
        try {
            // Create notification record
            $notification = $this->createNotification($user, $data);

            // Send through configured channels
            $channels = $data['channels'] ?? ['database'];
            
            foreach ($channels as $channel) {
                $this->sendThroughChannel($user, $notification, $channel, $data);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'data' => $data,
            ]);
            return false;
        }
    }

    /**
     * Create notification record.
     */
    protected function createNotification(User $user, array $data): Notification
    {
        return Notification::create([
            'user_id' => $user->id,
            'type' => $data['type'],
            'title' => $data['title'],
            'message' => $data['message'],
            'data' => $data['data'] ?? [],
            'channel' => $data['primary_channel'] ?? 'database',
            'priority' => $data['priority'] ?? 'normal',
            'metadata' => $data['metadata'] ?? [],
        ]);
    }

    /**
     * Send notification through specific channel.
     */
    protected function sendThroughChannel(User $user, Notification $notification, string $channel, array $data): void
    {
        switch ($channel) {
            case 'email':
                $this->sendEmailNotification($user, $data);
                break;
            case 'sms':
                $this->sendSMSNotification($user, $data);
                break;
            case 'push':
                $this->sendPushNotification($user, $data);
                break;
            case 'database':
                // Already created in database
                break;
            default:
                Log::warning("Unknown notification channel: {$channel}");
        }
    }

    /**
     * Send email notification.
     */
    protected function sendEmailNotification(User $user, array $data): void
    {
        if (!$user->email) {
            return;
        }

        SendTransactionEmailJob::dispatch($user, $data);
    }

    /**
     * Send SMS notification.
     */
    protected function sendSMSNotification(User $user, array $data): void
    {
        if (!$user->phone) {
            return;
        }

        SendTransactionSMSJob::dispatch($user, $data);
    }

    /**
     * Send push notification.
     */
    protected function sendPushNotification(User $user, array $data): void
    {
        // Implementation for push notifications
        // This would integrate with Firebase, Pusher, or other push services
        Log::info('Push notification sent', [
            'user_id' => $user->id,
            'title' => $data['title'],
        ]);
    }

    /**
     * Send transaction notification.
     */
    public function sendTransactionNotification(User $user, string $type, array $transactionData): bool
    {
        $templates = $this->getNotificationTemplates();
        $template = $templates[$type] ?? null;

        if (!$template) {
            Log::warning("No template found for notification type: {$type}");
            return false;
        }

        $data = [
            'type' => $type,
            'title' => $this->replacePlaceholders($template['title'], $transactionData),
            'message' => $this->replacePlaceholders($template['message'], $transactionData),
            'data' => $transactionData,
            'channels' => $template['channels'],
            'priority' => $template['priority'],
        ];

        return $this->sendNotification($user, $data);
    }

    /**
     * Send fraud alert notification.
     */
    public function sendFraudAlert(User $user, array $fraudData): bool
    {
        $data = [
            'type' => 'fraud_alert',
            'title' => 'تنبيه احتيال - Fraud Alert',
            'message' => "تم اكتشاف نشاط مشبوه في حسابك. يرجى مراجعة المعاملة رقم {$fraudData['transaction_id']}",
            'data' => $fraudData,
            'channels' => ['database', 'email', 'sms'],
            'priority' => 'critical',
        ];

        return $this->sendNotification($user, $data);
    }

    /**
     * Send security alert notification.
     */
    public function sendSecurityAlert(User $user, string $alertType, array $alertData): bool
    {
        $messages = [
            'login_from_new_device' => 'تم تسجيل الدخول من جهاز جديد',
            'password_changed' => 'تم تغيير كلمة المرور',
            'suspicious_activity' => 'تم اكتشاف نشاط مشبوه',
        ];

        $data = [
            'type' => 'security_alert',
            'title' => 'تنبيه أمني - Security Alert',
            'message' => $messages[$alertType] ?? 'تنبيه أمني',
            'data' => $alertData,
            'channels' => ['database', 'email', 'sms'],
            'priority' => 'high',
        ];

        return $this->sendNotification($user, $data);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(int $notificationId, int $userId): bool
    {
        $notification = Notification::where('id', $notificationId)
                                  ->where('user_id', $userId)
                                  ->first();

        if ($notification) {
            $notification->markAsRead();
            return true;
        }

        return false;
    }

    /**
     * Mark all notifications as read for user.
     */
    public function markAllAsRead(int $userId): bool
    {
        Notification::where('user_id', $userId)
                   ->where('is_read', false)
                   ->update([
                       'is_read' => true,
                       'read_at' => now(),
                   ]);

        return true;
    }

    /**
     * Get unread notifications count.
     */
    public function getUnreadCount(int $userId): int
    {
        return Notification::where('user_id', $userId)
                          ->where('is_read', false)
                          ->count();
    }

    /**
     * Replace placeholders in template.
     */
    protected function replacePlaceholders(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace("{{$key}}", $value, $template);
        }
        return $template;
    }

    /**
     * Get notification templates.
     */
    protected function getNotificationTemplates(): array
    {
        return [
            'transaction_created' => [
                'title' => 'معاملة جديدة - New Transaction',
                'message' => 'تم إنشاء معاملة جديدة بقيمة {amount} {currency}',
                'channels' => ['database', 'email'],
                'priority' => 'normal',
            ],
            'transaction_completed' => [
                'title' => 'تمت المعاملة بنجاح - Transaction Completed',
                'message' => 'تمت معاملتك بقيمة {amount} {currency} بنجاح',
                'channels' => ['database', 'email', 'sms'],
                'priority' => 'normal',
            ],
            'transaction_failed' => [
                'title' => 'فشلت المعاملة - Transaction Failed',
                'message' => 'فشلت معاملتك بقيمة {amount} {currency}. السبب: {reason}',
                'channels' => ['database', 'email', 'sms'],
                'priority' => 'high',
            ],
            'transaction_blocked' => [
                'title' => 'تم حظر المعاملة - Transaction Blocked',
                'message' => 'تم حظر معاملتك بقيمة {amount} {currency} لأسباب أمنية',
                'channels' => ['database', 'email', 'sms'],
                'priority' => 'high',
            ],
        ];
    }
}
