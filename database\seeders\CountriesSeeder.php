<?php

namespace Database\Seeders;

use App\Models\Country;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CountriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
            [
                'name_ar' => 'المملكة العربية السعودية',
                'name_en' => 'Saudi Arabia',
                'code' => 'SAU',
                'iso2' => 'SA',
                'phone_code' => '+966',
                'currency_code' => 'SAR',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 500000,
                'min_transfer_amount' => 10,
                'commission_rate' => 0.015,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card'],
                'required_documents' => ['national_id', 'passport'],
            ],
            [
                'name_ar' => 'الإمارات العربية المتحدة',
                'name_en' => 'United Arab Emirates',
                'code' => 'ARE',
                'iso2' => 'AE',
                'phone_code' => '+971',
                'currency_code' => 'AED',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => true,
                'max_transfer_amount' => 1000000,
                'min_transfer_amount' => 10,
                'commission_rate' => 0.012,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card', 'crypto'],
                'required_documents' => ['emirates_id', 'passport'],
            ],
            [
                'name_ar' => 'الولايات المتحدة الأمريكية',
                'name_en' => 'United States',
                'code' => 'USA',
                'iso2' => 'US',
                'phone_code' => '+1',
                'currency_code' => 'USD',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => true,
                'max_transfer_amount' => 1000000,
                'min_transfer_amount' => 1,
                'commission_rate' => 0.008,
                'supported_payment_methods' => ['bank_transfer', 'card', 'crypto', 'ach'],
                'required_documents' => ['ssn', 'passport', 'driving_license'],
            ],
            [
                'name_ar' => 'المملكة المتحدة',
                'name_en' => 'United Kingdom',
                'code' => 'GBR',
                'iso2' => 'GB',
                'phone_code' => '+44',
                'currency_code' => 'GBP',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => true,
                'max_transfer_amount' => 750000,
                'min_transfer_amount' => 1,
                'commission_rate' => 0.010,
                'supported_payment_methods' => ['bank_transfer', 'card', 'faster_payments'],
                'required_documents' => ['passport', 'driving_license', 'utility_bill'],
            ],
            [
                'name_ar' => 'مصر',
                'name_en' => 'Egypt',
                'code' => 'EGY',
                'iso2' => 'EG',
                'phone_code' => '+20',
                'currency_code' => 'EGP',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 100000,
                'min_transfer_amount' => 50,
                'commission_rate' => 0.020,
                'supported_payment_methods' => ['cash', 'bank_transfer'],
                'required_documents' => ['national_id', 'passport'],
            ],
            [
                'name_ar' => 'الأردن',
                'name_en' => 'Jordan',
                'code' => 'JOR',
                'iso2' => 'JO',
                'phone_code' => '+962',
                'currency_code' => 'JOD',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 200000,
                'min_transfer_amount' => 25,
                'commission_rate' => 0.018,
                'supported_payment_methods' => ['cash', 'bank_transfer'],
                'required_documents' => ['national_id', 'passport'],
            ],
            [
                'name_ar' => 'لبنان',
                'name_en' => 'Lebanon',
                'code' => 'LBN',
                'iso2' => 'LB',
                'phone_code' => '+961',
                'currency_code' => 'LBP',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 150000,
                'min_transfer_amount' => 100,
                'commission_rate' => 0.025,
                'supported_payment_methods' => ['cash', 'bank_transfer'],
                'required_documents' => ['national_id', 'passport'],
            ],
            [
                'name_ar' => 'الكويت',
                'name_en' => 'Kuwait',
                'code' => 'KWT',
                'iso2' => 'KW',
                'phone_code' => '+965',
                'currency_code' => 'KWD',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 300000,
                'min_transfer_amount' => 5,
                'commission_rate' => 0.012,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card'],
                'required_documents' => ['civil_id', 'passport'],
            ],
            [
                'name_ar' => 'قطر',
                'name_en' => 'Qatar',
                'code' => 'QAT',
                'iso2' => 'QA',
                'phone_code' => '+974',
                'currency_code' => 'QAR',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 400000,
                'min_transfer_amount' => 10,
                'commission_rate' => 0.013,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card'],
                'required_documents' => ['qid', 'passport'],
            ],
            [
                'name_ar' => 'البحرين',
                'name_en' => 'Bahrain',
                'code' => 'BHR',
                'iso2' => 'BH',
                'phone_code' => '+973',
                'currency_code' => 'BHD',
                'is_active' => true,
                'supports_transfers' => true,
                'supports_crypto' => false,
                'max_transfer_amount' => 250000,
                'min_transfer_amount' => 5,
                'commission_rate' => 0.014,
                'supported_payment_methods' => ['cash', 'bank_transfer', 'card'],
                'required_documents' => ['cpr', 'passport'],
            ],
        ];

        foreach ($countries as $country) {
            Country::updateOrCreate(
                ['code' => $country['code']],
                $country
            );
        }
    }
}
