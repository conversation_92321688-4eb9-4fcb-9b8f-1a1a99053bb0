<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Transaction;
use App\Services\PaymentGatewayManager;
use App\Services\JwtService;
use App\Services\FraudDetectionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class PaymentIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $sender;
    private User $receiver;
    private JwtService $jwtService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->sender = User::factory()->create([
            'email' => '<EMAIL>',
            'kyc_status' => 'verified',
            'balance' => 1000.00,
        ]);

        $this->receiver = User::factory()->create([
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
        ]);

        $this->jwtService = new JwtService();

        // Configure payment gateways
        config([
            'payment_gateways.stripe.enabled' => true,
            'payment_gateways.stripe.secret_key' => 'sk_test_123456',
            'payment_gateways.paypal.enabled' => true,
            'payment_gateways.paypal.client_id' => 'test_client_id',
        ]);
    }

    /** @test */
    public function it_can_complete_full_payment_flow_with_stripe()
    {
        // Mock Stripe API responses
        Http::fake([
            'api.stripe.com/v1/payment_intents' => Http::response([
                'id' => 'pi_test_123456',
                'client_secret' => 'pi_test_123456_secret_test',
                'status' => 'requires_payment_method',
                'amount' => 10000,
                'currency' => 'usd',
            ], 200),
            'api.stripe.com/v1/payment_intents/pi_test_123456/confirm' => Http::response([
                'id' => 'pi_test_123456',
                'status' => 'succeeded',
                'amount' => 10000,
                'currency' => 'usd',
            ], 200),
        ]);

        // Get authentication token
        $tokens = $this->jwtService->generateTokens($this->sender);
        
        // Step 1: Create transaction
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson('/api/transactions', [
            'receiver_phone' => $this->receiver->phone,
            'amount' => 100.00,
            'currency' => 'USD',
            'description' => 'Test payment',
        ]);

        $response->assertStatus(201);
        $transactionId = $response->json('data.id');

        // Step 2: Initialize payment
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson("/api/transactions/{$transactionId}/payment/initialize", [
            'gateway' => 'stripe',
            'payment_method' => 'card',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'payment_id',
            'client_secret',
            'gateway_name',
        ]);

        // Step 3: Confirm payment
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson("/api/transactions/{$transactionId}/payment/confirm", [
            'payment_method' => 'pm_card_visa',
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify transaction status
        $transaction = Transaction::find($transactionId);
        $this->assertEquals('completed', $transaction->status);
        $this->assertEquals('Stripe', $transaction->gateway_name);
        $this->assertEquals('pi_test_123456', $transaction->gateway_transaction_id);
    }

    /** @test */
    public function it_can_complete_full_payment_flow_with_paypal()
    {
        // Mock PayPal API responses
        Http::fake([
            'api-m.sandbox.paypal.com/v1/oauth2/token' => Http::response([
                'access_token' => 'test_access_token_123456',
            ], 200),
            'api-m.sandbox.paypal.com/v2/checkout/orders' => Http::response([
                'id' => 'ORDER_123456',
                'status' => 'CREATED',
                'links' => [
                    [
                        'href' => 'https://www.sandbox.paypal.com/checkoutnow?token=ORDER_123456',
                        'rel' => 'approve',
                        'method' => 'GET',
                    ]
                ]
            ], 201),
            'api-m.sandbox.paypal.com/v2/checkout/orders/ORDER_123456/capture' => Http::response([
                'id' => 'ORDER_123456',
                'status' => 'COMPLETED',
                'purchase_units' => [
                    [
                        'payments' => [
                            'captures' => [
                                [
                                    'id' => 'CAPTURE_123456',
                                    'amount' => [
                                        'value' => '100.00',
                                        'currency_code' => 'USD',
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ], 201),
        ]);

        // Get authentication token
        $tokens = $this->jwtService->generateTokens($this->sender);
        
        // Step 1: Create transaction
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson('/api/transactions', [
            'receiver_phone' => $this->receiver->phone,
            'amount' => 100.00,
            'currency' => 'USD',
            'description' => 'Test PayPal payment',
        ]);

        $response->assertStatus(201);
        $transactionId = $response->json('data.id');

        // Step 2: Initialize PayPal payment
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson("/api/transactions/{$transactionId}/payment/initialize", [
            'gateway' => 'paypal',
            'payment_method' => 'paypal',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'payment_id',
            'approval_url',
            'gateway_name',
        ]);

        // Step 3: Capture PayPal payment
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson("/api/transactions/{$transactionId}/payment/capture", [
            'order_id' => 'ORDER_123456',
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify transaction status
        $transaction = Transaction::find($transactionId);
        $this->assertEquals('completed', $transaction->status);
        $this->assertEquals('PayPal', $transaction->gateway_name);
    }

    /** @test */
    public function it_handles_fraud_detection_during_payment()
    {
        // Create a high-risk transaction
        $highRiskTransaction = Transaction::factory()->create([
            'sender_id' => $this->sender->id,
            'amount' => 50000.00, // High amount
            'currency' => 'USD',
            'status' => 'pending',
        ]);

        $tokens = $this->jwtService->generateTokens($this->sender);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson("/api/transactions/{$highRiskTransaction->id}/payment/initialize", [
            'gateway' => 'stripe',
            'payment_method' => 'card',
        ]);

        // Should be blocked due to fraud detection
        $response->assertStatus(403);
        $response->assertJson([
            'success' => false,
            'error' => 'Transaction blocked due to high fraud risk',
        ]);
    }

    /** @test */
    public function it_can_refund_completed_payment()
    {
        // Create completed transaction
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->sender->id,
            'amount' => 100.00,
            'currency' => 'USD',
            'status' => 'completed',
            'gateway_name' => 'Stripe',
            'gateway_transaction_id' => 'pi_test_123456',
        ]);

        // Mock Stripe refund API
        Http::fake([
            'api.stripe.com/v1/refunds' => Http::response([
                'id' => 're_test_123456',
                'status' => 'succeeded',
                'amount' => 5000,
                'currency' => 'usd',
            ], 200)
        ]);

        $tokens = $this->jwtService->generateTokens($this->sender);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson("/api/transactions/{$transaction->id}/refund", [
            'amount' => 50.00,
            'reason' => 'Customer request',
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $transaction->refresh();
        $this->assertEquals('refunded', $transaction->status);
        $this->assertEquals(50.00, $transaction->refund_amount);
    }

    /** @test */
    public function it_handles_webhook_notifications()
    {
        // Create transaction
        $transaction = Transaction::factory()->create([
            'gateway_name' => 'Stripe',
            'gateway_transaction_id' => 'pi_test_123456',
            'status' => 'processing',
        ]);

        // Mock webhook payload
        $payload = json_encode([
            'type' => 'payment_intent.succeeded',
            'data' => [
                'object' => [
                    'id' => 'pi_test_123456',
                    'status' => 'succeeded',
                ]
            ]
        ]);

        $timestamp = time();
        $secret = 'whsec_test_secret';
        config(['payment_gateways.stripe.webhook_secret' => $secret]);
        
        $signedPayload = $timestamp . '.' . $payload;
        $signature = hash_hmac('sha256', $signedPayload, $secret);

        $response = $this->withHeaders([
            'Stripe-Signature' => "t={$timestamp},v1={$signature}",
            'Content-Type' => 'application/json',
        ])->postJson('/api/webhooks/stripe', json_decode($payload, true));

        $response->assertStatus(200);

        $transaction->refresh();
        $this->assertEquals('completed', $transaction->status);
    }

    /** @test */
    public function it_enforces_rate_limiting_on_payment_endpoints()
    {
        $tokens = $this->jwtService->generateTokens($this->sender);

        // Make multiple rapid requests
        for ($i = 0; $i < 15; $i++) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $tokens['access_token'],
                'Accept' => 'application/json',
            ])->postJson('/api/transactions', [
                'receiver_phone' => $this->receiver->phone,
                'amount' => 10.00,
                'currency' => 'USD',
            ]);

            if ($i < 10) {
                $response->assertStatus(201);
            } else {
                // Should be rate limited after 10 requests
                $response->assertStatus(429);
                break;
            }
        }
    }

    /** @test */
    public function it_validates_jwt_tokens_on_protected_endpoints()
    {
        // Request without token
        $response = $this->postJson('/api/transactions', [
            'receiver_phone' => $this->receiver->phone,
            'amount' => 100.00,
            'currency' => 'USD',
        ]);

        $response->assertStatus(401);

        // Request with invalid token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid_token',
            'Accept' => 'application/json',
        ])->postJson('/api/transactions', [
            'receiver_phone' => $this->receiver->phone,
            'amount' => 100.00,
            'currency' => 'USD',
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_handles_concurrent_payment_requests()
    {
        $tokens = $this->jwtService->generateTokens($this->sender);

        // Create multiple transactions simultaneously
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->withHeaders([
                'Authorization' => 'Bearer ' . $tokens['access_token'],
                'Accept' => 'application/json',
            ])->postJson('/api/transactions', [
                'receiver_phone' => $this->receiver->phone,
                'amount' => 20.00,
                'currency' => 'USD',
                'description' => "Concurrent payment {$i}",
            ]);
        }

        // All should succeed
        foreach ($responses as $response) {
            $response->assertStatus(201);
        }

        // Verify all transactions were created
        $this->assertEquals(5, Transaction::where('sender_id', $this->sender->id)->count());
    }

    /** @test */
    public function it_maintains_data_consistency_during_payment_flow()
    {
        $initialBalance = $this->sender->balance;
        
        $tokens = $this->jwtService->generateTokens($this->sender);

        // Mock successful payment
        Http::fake([
            'api.stripe.com/v1/payment_intents' => Http::response([
                'id' => 'pi_test_123456',
                'client_secret' => 'pi_test_123456_secret_test',
                'status' => 'succeeded',
                'amount' => 10000,
                'currency' => 'usd',
            ], 200)
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson('/api/transactions', [
            'receiver_phone' => $this->receiver->phone,
            'amount' => 100.00,
            'currency' => 'USD',
        ]);

        $response->assertStatus(201);
        $transactionId = $response->json('data.id');

        // Complete the payment
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $tokens['access_token'],
            'Accept' => 'application/json',
        ])->postJson("/api/transactions/{$transactionId}/payment/complete");

        // Verify balances are updated correctly
        $this->sender->refresh();
        $this->receiver->refresh();

        $this->assertEquals($initialBalance - 100.00, $this->sender->balance);
        $this->assertEquals(100.00, $this->receiver->balance);
    }

    protected function tearDown(): void
    {
        Http::clearResolvedInstances();
        parent::tearDown();
    }
}
