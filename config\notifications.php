<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the notification system
    |
    */

    'enabled' => env('NOTIFICATIONS_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Default Channels
    |--------------------------------------------------------------------------
    */

    'default_channels' => [
        'database',
        'mail',
    ],

    /*
    |--------------------------------------------------------------------------
    | Channel Configuration
    |--------------------------------------------------------------------------
    */

    'channels' => [
        'database' => [
            'enabled' => true,
            'table' => 'notifications',
            'cleanup_after_days' => 90,
        ],

        'mail' => [
            'enabled' => env('MAIL_NOTIFICATIONS_ENABLED', true),
            'queue' => 'emails',
            'from' => [
                'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'name' => env('MAIL_FROM_NAME', 'Mony Transfer'),
            ],
            'templates_path' => 'emails.notifications',
        ],

        'sms' => [
            'enabled' => env('SMS_NOTIFICATIONS_ENABLED', true),
            'provider' => env('SMS_PROVIDER', 'twilio'),
            'queue' => 'sms',
            'from' => env('SMS_FROM_NUMBER'),
            'providers' => [
                'twilio' => [
                    'sid' => env('TWILIO_SID'),
                    'token' => env('TWILIO_TOKEN'),
                    'from' => env('TWILIO_FROM'),
                ],
                'nexmo' => [
                    'key' => env('NEXMO_KEY'),
                    'secret' => env('NEXMO_SECRET'),
                    'from' => env('NEXMO_FROM'),
                ],
            ],
        ],

        'push' => [
            'enabled' => env('PUSH_NOTIFICATIONS_ENABLED', true),
            'provider' => env('PUSH_PROVIDER', 'firebase'),
            'queue' => 'push',
            'providers' => [
                'firebase' => [
                    'server_key' => env('FIREBASE_SERVER_KEY'),
                    'sender_id' => env('FIREBASE_SENDER_ID'),
                    'project_id' => env('FIREBASE_PROJECT_ID'),
                ],
                'pusher' => [
                    'app_id' => env('PUSHER_APP_ID'),
                    'key' => env('PUSHER_APP_KEY'),
                    'secret' => env('PUSHER_APP_SECRET'),
                    'cluster' => env('PUSHER_APP_CLUSTER'),
                ],
            ],
        ],

        'slack' => [
            'enabled' => env('SLACK_NOTIFICATIONS_ENABLED', false),
            'webhook_url' => env('SLACK_WEBHOOK_URL'),
            'channel' => env('SLACK_DEFAULT_CHANNEL', '#general'),
            'username' => env('SLACK_USERNAME', 'Mony Transfer Bot'),
            'icon' => env('SLACK_ICON', ':money_with_wings:'),
        ],

        'discord' => [
            'enabled' => env('DISCORD_NOTIFICATIONS_ENABLED', false),
            'webhook_url' => env('DISCORD_WEBHOOK_URL'),
            'username' => env('DISCORD_USERNAME', 'Mony Transfer Bot'),
            'avatar_url' => env('DISCORD_AVATAR_URL'),
        ],

        'telegram' => [
            'enabled' => env('TELEGRAM_NOTIFICATIONS_ENABLED', false),
            'bot_token' => env('TELEGRAM_BOT_TOKEN'),
            'chat_id' => env('TELEGRAM_CHAT_ID'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Types
    |--------------------------------------------------------------------------
    */

    'types' => [
        'transaction_created' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms', 'push'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => true,
        ],

        'transaction_completed' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms', 'push'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => true,
        ],

        'transaction_failed' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms'],
            'priority' => 'high',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'transaction_cancelled' => [
            'enabled' => true,
            'channels' => ['database', 'mail'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => true,
        ],

        'transaction_blocked' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms'],
            'priority' => 'high',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'fraud_alert' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms', 'slack'],
            'priority' => 'critical',
            'delay' => 0,
            'user_controllable' => false,
            'admin_only' => true,
        ],

        'security_alert' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms', 'slack'],
            'priority' => 'critical',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'kyc_required' => [
            'enabled' => true,
            'channels' => ['database', 'mail'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'kyc_approved' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'push'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => true,
        ],

        'kyc_rejected' => [
            'enabled' => true,
            'channels' => ['database', 'mail'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'limit_exceeded' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'wallet_low_balance' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'push'],
            'priority' => 'normal',
            'delay' => 0,
            'user_controllable' => true,
        ],

        'login_from_new_device' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms'],
            'priority' => 'high',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'password_changed' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'sms'],
            'priority' => 'high',
            'delay' => 0,
            'user_controllable' => false,
        ],

        'system_maintenance' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'push'],
            'priority' => 'normal',
            'delay' => 3600, // 1 hour advance notice
            'user_controllable' => false,
        ],

        'promotional' => [
            'enabled' => true,
            'channels' => ['database', 'mail', 'push'],
            'priority' => 'low',
            'delay' => 0,
            'user_controllable' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Preferences
    |--------------------------------------------------------------------------
    */

    'user_preferences' => [
        'enabled' => true,
        'default_preferences' => [
            'email_notifications' => true,
            'sms_notifications' => true,
            'push_notifications' => true,
            'marketing_emails' => false,
            'security_alerts' => true,
        ],

        'preference_categories' => [
            'transactions' => [
                'label' => 'Transaction Notifications',
                'types' => [
                    'transaction_created',
                    'transaction_completed',
                    'transaction_cancelled',
                ],
            ],
            'security' => [
                'label' => 'Security Notifications',
                'types' => [
                    'login_from_new_device',
                    'password_changed',
                    'security_alert',
                ],
                'force_enabled' => true,
            ],
            'account' => [
                'label' => 'Account Notifications',
                'types' => [
                    'kyc_approved',
                    'kyc_rejected',
                    'limit_exceeded',
                    'wallet_low_balance',
                ],
            ],
            'marketing' => [
                'label' => 'Marketing & Promotions',
                'types' => [
                    'promotional',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */

    'rate_limiting' => [
        'enabled' => true,
        'limits' => [
            'sms' => [
                'per_user_per_hour' => 5,
                'per_user_per_day' => 20,
                'global_per_minute' => 100,
            ],
            'email' => [
                'per_user_per_hour' => 10,
                'per_user_per_day' => 50,
                'global_per_minute' => 500,
            ],
            'push' => [
                'per_user_per_hour' => 20,
                'per_user_per_day' => 100,
                'global_per_minute' => 1000,
            ],
        ],
        'cooldown_periods' => [
            'duplicate_notification' => 300, // 5 minutes
            'same_type_notification' => 60, // 1 minute
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Templates
    |--------------------------------------------------------------------------
    */

    'templates' => [
        'email' => [
            'layout' => 'emails.layout',
            'default_locale' => 'ar',
            'fallback_locale' => 'en',
            'variables' => [
                'app_name' => env('APP_NAME'),
                'app_url' => env('APP_URL'),
                'support_email' => '<EMAIL>',
                'support_phone' => '+966-11-1234567',
            ],
        ],

        'sms' => [
            'max_length' => 160,
            'include_app_name' => true,
            'include_unsubscribe' => false,
        ],

        'push' => [
            'max_title_length' => 50,
            'max_body_length' => 200,
            'default_icon' => 'notification_icon',
            'default_sound' => 'default',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    */

    'queue' => [
        'enabled' => true,
        'default_queue' => 'notifications',
        'priority_queues' => [
            'critical' => 'critical-notifications',
            'high' => 'high-notifications',
            'normal' => 'notifications',
            'low' => 'low-notifications',
        ],
        'retry_attempts' => 3,
        'retry_delay' => [60, 300, 900], // 1 min, 5 min, 15 min
    ],

    /*
    |--------------------------------------------------------------------------
    | Localization
    |--------------------------------------------------------------------------
    */

    'localization' => [
        'enabled' => true,
        'default_locale' => 'ar',
        'supported_locales' => ['ar', 'en'],
        'auto_detect_locale' => true,
        'fallback_to_default' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics & Tracking
    |--------------------------------------------------------------------------
    */

    'analytics' => [
        'enabled' => true,
        'track_delivery' => true,
        'track_opens' => true,
        'track_clicks' => true,
        'retention_days' => 90,
        'anonymize_after_days' => 365,
    ],

    /*
    |--------------------------------------------------------------------------
    | Debugging & Testing
    |--------------------------------------------------------------------------
    */

    'debug' => [
        'enabled' => env('NOTIFICATIONS_DEBUG', false),
        'log_all_notifications' => env('APP_DEBUG', false),
        'test_mode' => env('NOTIFICATIONS_TEST_MODE', false),
        'test_recipients' => [
            'email' => env('NOTIFICATIONS_TEST_EMAIL'),
            'sms' => env('NOTIFICATIONS_TEST_SMS'),
        ],
    ],

];
