<?php

namespace App\Services;

use App\Models\User;
use App\Models\SecurityEvent;
use App\Models\LoginAttempt;
use App\Models\DeviceFingerprint;
use App\Models\TwoFactorAuth;
use App\Exceptions\SecurityException;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;

class SecurityService
{
    protected int $maxLoginAttempts = 5;
    protected int $lockoutDuration = 900; // 15 minutes
    protected int $passwordHistoryLimit = 5;

    /**
     * Enhanced login with security checks
     */
    public function secureLogin(string $email, string $password, array $deviceInfo = []): array
    {
        try {
            // Check if account is locked
            $this->checkAccountLockout($email);

            // Check for suspicious activity
            $this->checkSuspiciousActivity($email, $deviceInfo);

            // Validate credentials
            $user = User::where('email', $email)->first();
            
            if (!$user || !Hash::check($password, $user->password)) {
                $this->recordFailedLogin($email, $deviceInfo, 'invalid_credentials');
                throw new SecurityException(
                    'Invalid credentials',
                    'INVALID_CREDENTIALS'
                );
            }

            // Check account status
            if ($user->status !== 'active') {
                $this->recordFailedLogin($email, $deviceInfo, 'account_inactive');
                throw new SecurityException(
                    'Account is not active',
                    'ACCOUNT_INACTIVE'
                );
            }

            // Device fingerprinting
            $deviceFingerprint = $this->generateDeviceFingerprint($deviceInfo);
            $isNewDevice = $this->checkNewDevice($user, $deviceFingerprint);

            // Record successful login
            $this->recordSuccessfulLogin($user, $deviceInfo, $deviceFingerprint);

            // Clear failed attempts
            $this->clearFailedAttempts($email);

            // Check if 2FA is required
            $requires2FA = $this->requires2FA($user, $isNewDevice);

            return [
                'success' => true,
                'user' => $user,
                'requires_2fa' => $requires2FA,
                'is_new_device' => $isNewDevice,
                'device_fingerprint' => $deviceFingerprint,
                'security_score' => $this->calculateSecurityScore($user, $deviceInfo),
            ];

        } catch (SecurityException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Secure login failed', [
                'email' => $email,
                'error' => $e->getMessage(),
                'device_info' => $deviceInfo,
            ]);

            throw new SecurityException(
                'Login failed due to security error',
                'SECURITY_ERROR'
            );
        }
    }

    /**
     * Generate and send 2FA code
     */
    public function generate2FACode(User $user, string $method = 'sms'): array
    {
        // Generate 6-digit code
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $expiresAt = now()->addMinutes(10);

        // Store code securely
        TwoFactorAuth::updateOrCreate(
            ['user_id' => $user->id],
            [
                'code' => Hash::make($code),
                'method' => $method,
                'expires_at' => $expiresAt,
                'attempts' => 0,
                'is_used' => false,
            ]
        );

        // Send code based on method
        switch ($method) {
            case 'sms':
                $this->send2FASms($user, $code);
                break;
            case 'email':
                $this->send2FAEmail($user, $code);
                break;
            case 'app':
                // For authenticator apps, return the code for QR generation
                return ['qr_code' => $this->generateQRCode($user, $code)];
        }

        return [
            'success' => true,
            'method' => $method,
            'expires_at' => $expiresAt,
            'masked_destination' => $this->maskDestination($user, $method),
        ];
    }

    /**
     * Verify 2FA code
     */
    public function verify2FACode(User $user, string $code): bool
    {
        $twoFA = TwoFactorAuth::where('user_id', $user->id)
            ->where('is_used', false)
            ->where('expires_at', '>', now())
            ->first();

        if (!$twoFA) {
            throw new SecurityException(
                '2FA code not found or expired',
                'INVALID_2FA_CODE'
            );
        }

        // Check attempts limit
        if ($twoFA->attempts >= 3) {
            $twoFA->update(['is_used' => true]);
            throw new SecurityException(
                'Too many 2FA attempts',
                'TOO_MANY_2FA_ATTEMPTS'
            );
        }

        // Verify code
        if (!Hash::check($code, $twoFA->code)) {
            $twoFA->increment('attempts');
            return false;
        }

        // Mark as used
        $twoFA->update(['is_used' => true, 'verified_at' => now()]);

        // Log successful 2FA
        $this->logSecurityEvent($user, 'two_factor_verified', [
            'method' => $twoFA->method,
            'attempts' => $twoFA->attempts + 1,
        ]);

        return true;
    }

    /**
     * Encrypt sensitive data
     */
    public function encryptSensitiveData(string $data): string
    {
        return Crypt::encrypt($data);
    }

    /**
     * Decrypt sensitive data
     */
    public function decryptSensitiveData(string $encryptedData): string
    {
        return Crypt::decrypt($encryptedData);
    }

    /**
     * Generate secure token
     */
    public function generateSecureToken(int $length = 32): string
    {
        return Str::random($length);
    }

    /**
     * Hash password with salt
     */
    public function hashPassword(string $password): string
    {
        return Hash::make($password);
    }

    /**
     * Verify password strength
     */
    public function verifyPasswordStrength(string $password): array
    {
        $score = 0;
        $feedback = [];

        // Length check
        if (strlen($password) >= 8) {
            $score += 20;
        } else {
            $feedback[] = 'Password must be at least 8 characters long';
        }

        // Uppercase check
        if (preg_match('/[A-Z]/', $password)) {
            $score += 20;
        } else {
            $feedback[] = 'Password must contain at least one uppercase letter';
        }

        // Lowercase check
        if (preg_match('/[a-z]/', $password)) {
            $score += 20;
        } else {
            $feedback[] = 'Password must contain at least one lowercase letter';
        }

        // Number check
        if (preg_match('/[0-9]/', $password)) {
            $score += 20;
        } else {
            $feedback[] = 'Password must contain at least one number';
        }

        // Special character check
        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 20;
        } else {
            $feedback[] = 'Password must contain at least one special character';
        }

        $strength = match(true) {
            $score >= 80 => 'strong',
            $score >= 60 => 'medium',
            $score >= 40 => 'weak',
            default => 'very_weak',
        };

        return [
            'score' => $score,
            'strength' => $strength,
            'feedback' => $feedback,
            'is_valid' => $score >= 60,
        ];
    }

    /**
     * Check password history
     */
    public function checkPasswordHistory(User $user, string $newPassword): bool
    {
        $passwordHistory = $user->passwordHistory()
            ->orderBy('created_at', 'desc')
            ->limit($this->passwordHistoryLimit)
            ->get();

        foreach ($passwordHistory as $oldPassword) {
            if (Hash::check($newPassword, $oldPassword->password_hash)) {
                return false; // Password was used before
            }
        }

        return true; // Password is new
    }

    /**
     * Detect suspicious activity
     */
    public function detectSuspiciousActivity(User $user, array $activityData): array
    {
        $suspiciousFactors = [];
        $riskScore = 0;

        // Check for unusual login times
        if ($this->isUnusualLoginTime($user, $activityData['timestamp'] ?? now())) {
            $suspiciousFactors[] = 'unusual_login_time';
            $riskScore += 20;
        }

        // Check for unusual location
        if (isset($activityData['ip_address']) && $this->isUnusualLocation($user, $activityData['ip_address'])) {
            $suspiciousFactors[] = 'unusual_location';
            $riskScore += 30;
        }

        // Check for rapid successive attempts
        if ($this->hasRapidAttempts($user)) {
            $suspiciousFactors[] = 'rapid_attempts';
            $riskScore += 25;
        }

        // Check for new device
        if (isset($activityData['device_fingerprint']) && $this->isNewDevice($user, $activityData['device_fingerprint'])) {
            $suspiciousFactors[] = 'new_device';
            $riskScore += 15;
        }

        $riskLevel = match(true) {
            $riskScore >= 70 => 'high',
            $riskScore >= 40 => 'medium',
            $riskScore >= 20 => 'low',
            default => 'minimal',
        };

        return [
            'is_suspicious' => $riskScore >= 40,
            'risk_score' => $riskScore,
            'risk_level' => $riskLevel,
            'factors' => $suspiciousFactors,
        ];
    }

    /**
     * Monitor for brute force attacks
     */
    public function monitorBruteForce(string $identifier, string $type = 'login'): array
    {
        $key = "brute_force:{$type}:{$identifier}";
        $attempts = Cache::get($key, 0);
        $windowMinutes = 15;

        if ($attempts >= $this->maxLoginAttempts) {
            return [
                'is_blocked' => true,
                'attempts' => $attempts,
                'reset_time' => now()->addMinutes($windowMinutes),
            ];
        }

        // Increment attempts
        Cache::put($key, $attempts + 1, now()->addMinutes($windowMinutes));

        return [
            'is_blocked' => false,
            'attempts' => $attempts + 1,
            'remaining_attempts' => $this->maxLoginAttempts - ($attempts + 1),
        ];
    }

    /**
     * Generate device fingerprint
     */
    protected function generateDeviceFingerprint(array $deviceInfo): string
    {
        $fingerprint = [
            'user_agent' => $deviceInfo['user_agent'] ?? '',
            'screen_resolution' => $deviceInfo['screen_resolution'] ?? '',
            'timezone' => $deviceInfo['timezone'] ?? '',
            'language' => $deviceInfo['language'] ?? '',
            'platform' => $deviceInfo['platform'] ?? '',
        ];

        return hash('sha256', json_encode($fingerprint));
    }

    /**
     * Check if device is new
     */
    protected function checkNewDevice(User $user, string $fingerprint): bool
    {
        return !DeviceFingerprint::where('user_id', $user->id)
            ->where('fingerprint', $fingerprint)
            ->exists();
    }

    /**
     * Record successful login
     */
    protected function recordSuccessfulLogin(User $user, array $deviceInfo, string $fingerprint): void
    {
        // Record login attempt
        LoginAttempt::create([
            'user_id' => $user->id,
            'email' => $user->email,
            'ip_address' => request()->ip(),
            'user_agent' => $deviceInfo['user_agent'] ?? '',
            'status' => 'success',
            'device_fingerprint' => $fingerprint,
            'location' => $this->getLocationFromIP(request()->ip()),
        ]);

        // Store/update device fingerprint
        DeviceFingerprint::updateOrCreate(
            [
                'user_id' => $user->id,
                'fingerprint' => $fingerprint,
            ],
            [
                'last_used_at' => now(),
                'device_info' => $deviceInfo,
                'is_trusted' => false, // Will be marked as trusted after verification
            ]
        );

        // Update user last login
        $user->update([
            'last_login_at' => now(),
            'last_login_ip' => request()->ip(),
        ]);
    }

    /**
     * Record failed login
     */
    protected function recordFailedLogin(string $email, array $deviceInfo, string $reason): void
    {
        LoginAttempt::create([
            'email' => $email,
            'ip_address' => request()->ip(),
            'user_agent' => $deviceInfo['user_agent'] ?? '',
            'status' => 'failed',
            'failure_reason' => $reason,
            'device_fingerprint' => $this->generateDeviceFingerprint($deviceInfo),
        ]);
    }

    /**
     * Check account lockout
     */
    protected function checkAccountLockout(string $email): void
    {
        $recentFailures = LoginAttempt::where('email', $email)
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subMinutes($this->lockoutDuration / 60))
            ->count();

        if ($recentFailures >= $this->maxLoginAttempts) {
            throw new SecurityException(
                'Account temporarily locked due to too many failed attempts',
                'ACCOUNT_LOCKED'
            );
        }
    }

    /**
     * Check for suspicious activity
     */
    protected function checkSuspiciousActivity(string $email, array $deviceInfo): void
    {
        // Check for rapid attempts from same IP
        $recentAttempts = LoginAttempt::where('ip_address', request()->ip())
            ->where('created_at', '>=', now()->subMinutes(5))
            ->count();

        if ($recentAttempts >= 10) {
            throw new SecurityException(
                'Too many login attempts from this IP address',
                'IP_RATE_LIMITED'
            );
        }
    }

    /**
     * Check if 2FA is required
     */
    protected function requires2FA(User $user, bool $isNewDevice): bool
    {
        // Always require 2FA for new devices
        if ($isNewDevice) {
            return true;
        }

        // Check user's 2FA settings
        return $user->two_factor_enabled ?? false;
    }

    /**
     * Calculate security score
     */
    protected function calculateSecurityScore(User $user, array $deviceInfo): int
    {
        $score = 50; // Base score

        // Email verified
        if ($user->email_verified_at) {
            $score += 10;
        }

        // Phone verified
        if ($user->phone_verified_at) {
            $score += 10;
        }

        // 2FA enabled
        if ($user->two_factor_enabled) {
            $score += 15;
        }

        // KYC verified
        if ($user->kyc_status === 'verified') {
            $score += 15;
        }

        return min(100, $score);
    }

    /**
     * Log security event
     */
    protected function logSecurityEvent(User $user, string $eventType, array $data = []): void
    {
        SecurityEvent::create([
            'user_id' => $user->id,
            'event_type' => $eventType,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'data' => $data,
            'severity' => $this->getEventSeverity($eventType),
        ]);
    }

    /**
     * Get event severity
     */
    protected function getEventSeverity(string $eventType): string
    {
        $severityMap = [
            'login_success' => 'low',
            'login_failed' => 'medium',
            'two_factor_verified' => 'low',
            'password_changed' => 'medium',
            'suspicious_activity' => 'high',
            'account_locked' => 'high',
        ];

        return $severityMap[$eventType] ?? 'medium';
    }

    /**
     * Clear failed attempts
     */
    protected function clearFailedAttempts(string $email): void
    {
        Cache::forget("brute_force:login:{$email}");
    }

    /**
     * Get location from IP (placeholder)
     */
    protected function getLocationFromIP(string $ip): ?array
    {
        // This would integrate with IP geolocation service
        return null;
    }

    /**
     * Send 2FA SMS
     */
    protected function send2FASms(User $user, string $code): void
    {
        // This would integrate with SMS service
        Log::info('2FA SMS sent', [
            'user_id' => $user->id,
            'phone' => $user->phone,
            'code' => $code, // Remove in production
        ]);
    }

    /**
     * Send 2FA Email
     */
    protected function send2FAEmail(User $user, string $code): void
    {
        // This would send actual email
        Log::info('2FA Email sent', [
            'user_id' => $user->id,
            'email' => $user->email,
            'code' => $code, // Remove in production
        ]);
    }

    /**
     * Generate QR code for authenticator apps
     */
    protected function generateQRCode(User $user, string $secret): string
    {
        // This would generate actual QR code
        return "otpauth://totp/{$user->email}?secret={$secret}&issuer=MoneyTransfer";
    }

    /**
     * Mask destination for security
     */
    protected function maskDestination(User $user, string $method): string
    {
        switch ($method) {
            case 'sms':
                return substr($user->phone, 0, -4) . '****';
            case 'email':
                $parts = explode('@', $user->email);
                return substr($parts[0], 0, 2) . '***@' . $parts[1];
            default:
                return 'Authenticator App';
        }
    }

    /**
     * Check unusual login time
     */
    protected function isUnusualLoginTime(User $user, $timestamp): bool
    {
        // This would analyze user's typical login patterns
        return false; // Placeholder
    }

    /**
     * Check unusual location
     */
    protected function isUnusualLocation(User $user, string $ip): bool
    {
        // This would check against user's typical locations
        return false; // Placeholder
    }

    /**
     * Check for rapid attempts
     */
    protected function hasRapidAttempts(User $user): bool
    {
        $recentAttempts = LoginAttempt::where('user_id', $user->id)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->count();

        return $recentAttempts >= 3;
    }
}
