@extends('layouts.app')

@section('content')
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="h3 mb-0">لوحة التحكم</h1>
            <p class="text-muted mb-0">مرحباً {{ Auth::check() ? Auth::user()->first_name : 'زائر' }}، إليك ملخص حسابك</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                </button>
                <a href="/transactions/create" class="btn btn-primary">
                    <i class="bi bi-plus-lg me-1"></i>تحويل جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي التحويلات</h6>
                    <h3 class="mb-0" id="totalTransactions">
                        <span class="loading-text">{{ $stats['total_transactions'] ?? 0 }}</span>
                    </h3>
                    <small class="opacity-75">هذا الشهر</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-arrow-left-right"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">التحويلات المكتملة</h6>
                    <h3 class="mb-0" id="completedTransactions">
                        <span class="loading-text">{{ $stats['completed_transactions'] ?? 0 }}</span>
                    </h3>
                    <small class="opacity-75">نسبة النجاح: <span id="successRate">{{ $stats['success_rate'] ?? 0 }}</span>%</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">المبلغ المحول</h6>
                    <h3 class="mb-0" id="totalAmount">
                        <span class="loading-text">{{ number_format($stats['total_amount'] ?? 0, 2) }}</span>
                    </h3>
                    <small class="opacity-75">ريال سعودي</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-currency-dollar"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">التحويلات المعلقة</h6>
                    <h3 class="mb-0" id="pendingTransactions">
                        <span class="loading-text">{{ $stats['pending_transactions'] ?? 0 }}</span>
                    </h3>
                    <small class="opacity-75">تحتاج مراجعة</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-clock"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions mb-4">
    <h5 class="mb-3">الإجراءات السريعة</h5>
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="/transactions/create" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-primary mb-2">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <h6 class="card-title">تحويل جديد</h6>
                    <p class="card-text text-muted small">إرسال أموال إلى أي مكان في العالم</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="/recipients" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-success mb-2">
                        <i class="bi bi-people"></i>
                    </div>
                    <h6 class="card-title">إدارة المستفيدين</h6>
                    <p class="card-text text-muted small">إضافة وإدارة قائمة المستفيدين</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="/payments" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-warning mb-2">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <h6 class="card-title">طرق الدفع</h6>
                    <p class="card-text text-muted small">إدارة طرق الدفع والبطاقات</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="/reports" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-info mb-2">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h6 class="card-title">التقارير</h6>
                    <p class="card-text text-muted small">عرض التقارير والإحصائيات</p>
                </div>
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Transactions -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">التحويلات الأخيرة</h5>
                <a href="{{ route('transactions.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم التحويل</th>
                                <th>المستفيد</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="recentTransactions">
                            @forelse($recentTransactions ?? [] as $transaction)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $transaction->transaction_id ?? 'N/A' }}</span>
                                </td>
                                <td>{{ $transaction->recipient_name ?? 'N/A' }}</td>
                                <td>
                                    <span class="fw-bold">{{ number_format($transaction->amount ?? 0, 2) }}</span>
                                    <small class="text-muted">{{ $transaction->currency_from ?? 'SAR' }}</small>
                                </td>
                                <td>
                                    <span class="transaction-status status-{{ $transaction->status ?? 'pending' }}">
                                        {{ $transaction->status_text ?? 'معلق' }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ $transaction->created_at ? $transaction->created_at->format('Y/m/d H:i') : 'N/A' }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr id="loadingRow">
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex justify-content-center align-items-center">
                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                        <span>جاري تحميل التحويلات...</span>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts and Analytics -->
    <div class="col-lg-4 mb-4">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">توزيع التحويلات</h6>
            </div>
            <div class="card-body">
                <canvas id="transactionChart" height="200"></canvas>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">أسعار الصرف الحالية</h6>
            </div>
            <div class="card-body">
                <div id="exchangeRates">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>USD/SAR</span>
                        <span class="fw-bold">3.75</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>EUR/SAR</span>
                        <span class="fw-bold">4.12</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>GBP/SAR</span>
                        <span class="fw-bold">4.89</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>AED/SAR</span>
                        <span class="fw-bold">1.02</span>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">آخر تحديث: {{ now()->format('H:i') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notifications -->
@if($notifications ?? false)
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">الإشعارات</h5>
    </div>
    <div class="card-body">
        @foreach($notifications as $notification)
        <div class="alert alert-{{ $notification['type'] }} alert-dismissible fade show">
            <i class="bi bi-{{ $notification['icon'] }} me-2"></i>
            {{ $notification['message'] }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endforeach
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
// Dashboard data refresh
function refreshDashboard() {
    $.get('{{ route("dashboard.data") }}', function(data) {
        if (data.success) {
            updateDashboardStats(data.stats);
            updateRecentTransactions(data.transactions);
            updateChart(data.chartData);
            showToast('تم تحديث البيانات بنجاح', 'success');
        }
    }).fail(function() {
        showToast('فشل في تحديث البيانات', 'error');
    });
}

// Update dashboard statistics
function updateDashboardStats(stats) {
    $('#totalTransactions').text(formatNumber(stats.total_transactions));
    $('#completedTransactions').text(formatNumber(stats.completed_transactions));
    $('#totalAmount').text(formatCurrency(stats.total_amount));
    $('#pendingTransactions').text(formatNumber(stats.pending_transactions));
}

// Update recent transactions table
function updateRecentTransactions(transactions) {
    const tbody = $('#recentTransactions');
    tbody.empty();
    
    if (transactions.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                    لا توجد تحويلات حتى الآن
                </td>
            </tr>
        `);
        return;
    }
    
    transactions.forEach(function(transaction) {
        const row = `
            <tr>
                <td><span class="fw-bold">${transaction.transaction_id}</span></td>
                <td>${transaction.recipient_name}</td>
                <td>
                    <span class="fw-bold">${formatNumber(transaction.amount)}</span>
                    <small class="text-muted">${transaction.currency_from}</small>
                </td>
                <td>
                    <span class="transaction-status status-${transaction.status}">
                        ${getStatusText(transaction.status)}
                    </span>
                </td>
                <td><small>${formatDate(transaction.created_at)}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="/transactions/${transaction.transaction_id}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye"></i>
                        </a>
                        ${transaction.status === 'pending' ? 
                            `<button class="btn btn-outline-danger btn-sm" onclick="cancelTransaction('${transaction.transaction_id}')">
                                <i class="bi bi-x"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// Cancel transaction
function cancelTransaction(transactionId) {
    if (confirm('هل أنت متأكد من إلغاء هذا التحويل؟')) {
        $.post(`/api/v1/transactions/${transactionId}/cancel`, {
            reason: 'إلغاء من قبل المستخدم'
        }, function(data) {
            if (data.success) {
                showToast('تم إلغاء التحويل بنجاح', 'success');
                refreshDashboard();
            } else {
                showToast('فشل في إلغاء التحويل', 'error');
            }
        }).fail(function() {
            showToast('حدث خطأ أثناء إلغاء التحويل', 'error');
        });
    }
}

// Get status text in Arabic
function getStatusText(status) {
    const statusMap = {
        'pending': 'معلق',
        'processing': 'قيد المعالجة',
        'completed': 'مكتمل',
        'failed': 'فاشل',
        'cancelled': 'ملغي'
    };
    return statusMap[status] || status;
}

// Initialize chart
let transactionChart;
function initChart() {
    const ctx = document.getElementById('transactionChart').getContext('2d');
    transactionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['مكتملة', 'معلقة', 'فاشلة'],
            datasets: [{
                data: [
                    {{ $stats['completed_transactions'] ?? 0 }},
                    {{ $stats['pending_transactions'] ?? 0 }},
                    {{ $stats['failed_transactions'] ?? 0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Update chart data
function updateChart(chartData) {
    if (transactionChart && chartData) {
        transactionChart.data.datasets[0].data = [
            chartData.completed,
            chartData.pending,
            chartData.failed
        ];
        transactionChart.update();
    }
}

// Progressive data loading for better performance
let loadingStates = {
    stats: false,
    transactions: false,
    chart: false,
    notifications: false
};

// Load dashboard data progressively
function loadDashboardData() {
    // Load stats first (fastest)
    loadStats();

    // Load transactions after a short delay
    setTimeout(() => loadTransactions(), 100);

    // Load chart data after stats
    setTimeout(() => loadChartData(), 200);

    // Load notifications last
    setTimeout(() => loadNotifications(), 300);
}

// Load statistics
function loadStats() {
    if (loadingStates.stats) return;
    loadingStates.stats = true;

    fetch('{{ route("dashboard.data") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStats(data.stats);
        }
    })
    .catch(error => {
        console.error('Error loading stats:', error);
        showStatsError();
    })
    .finally(() => {
        loadingStates.stats = false;
    });
}

// Load recent transactions
function loadTransactions() {
    if (loadingStates.transactions) return;
    loadingStates.transactions = true;

    fetch('{{ route("dashboard.transactions") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateRecentTransactions(data.transactions);
        }
    })
    .catch(error => {
        console.error('Error loading transactions:', error);
        showTransactionsError();
    })
    .finally(() => {
        loadingStates.transactions = false;
    });
}

// Load chart data
function loadChartData() {
    if (loadingStates.chart) return;
    loadingStates.chart = true;

    fetch('{{ route("dashboard.chart") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateChart(data.chartData);
        }
    })
    .catch(error => {
        console.error('Error loading chart:', error);
    })
    .finally(() => {
        loadingStates.chart = false;
    });
}

// Show loading states
function showLoadingStates() {
    // Show skeleton loaders for stats
    $('#totalTransactions').html('<div class="loading-skeleton" style="width: 60px; height: 32px;"></div>');
    $('#completedTransactions').html('<div class="loading-skeleton" style="width: 60px; height: 32px;"></div>');
    $('#totalAmount').html('<div class="loading-skeleton" style="width: 80px; height: 32px;"></div>');
    $('#pendingTransactions').html('<div class="loading-skeleton" style="width: 60px; height: 32px;"></div>');
    $('#successRate').text('-');

    // Show loading for transactions
    $('#recentTransactions').html(`
        <tr>
            <td colspan="6" class="text-center py-4">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    <span>جاري تحميل التحويلات...</span>
                </div>
            </td>
        </tr>
    `);
}

// Show stats error
function showStatsError() {
    $('#totalTransactions').text('خطأ');
    $('#completedTransactions').text('خطأ');
    $('#totalAmount').text('خطأ');
    $('#pendingTransactions').text('خطأ');
}

// Show transactions error
function showTransactionsError() {
    $('#recentTransactions').html(`
        <tr>
            <td colspan="6" class="text-center text-muted py-4">
                <i class="bi bi-exclamation-triangle fs-1 d-block mb-2"></i>
                فشل في تحميل التحويلات
                <br>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadTransactions()">
                    إعادة المحاولة
                </button>
            </td>
        </tr>
    `);
}

// Optimized refresh function
function refreshDashboard() {
    // Reset loading states
    Object.keys(loadingStates).forEach(key => loadingStates[key] = false);

    // Show loading states
    showLoadingStates();

    // Load data progressively
    loadDashboardData();

    showToast('جاري تحديث البيانات...', 'info');
}

// Auto refresh every 60 seconds (increased from 30 for better performance)
setInterval(refreshDashboard, 60000);

// Optimized dashboard loading
function refreshDashboard() {
    // Show loading states
    showLoadingStates();

    // Load data with timeout protection
    Promise.race([
        loadDashboardData(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
    ]).catch(error => {
        console.error('Dashboard loading error:', error);
        showErrorStates();
    });
}

// Load dashboard data with error handling
function loadDashboardData() {
    return Promise.all([
        loadStats(),
        loadTransactions()
    ]);
}

// Load statistics
function loadStats() {
    return fetch('/dashboard/data', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updateStats(data.stats);
        }
    })
    .catch(error => {
        console.error('Stats loading error:', error);
        // Keep initial values on error
    });
}

// Load transactions
function loadTransactions() {
    return fetch('/dashboard/transactions', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updateRecentTransactions(data.transactions);
        }
    })
    .catch(error => {
        console.error('Transactions loading error:', error);
        showTransactionsError();
    });
}

// Update statistics
function updateStats(stats) {
    document.getElementById('totalTransactions').innerHTML = formatNumber(stats.total_transactions);
    document.getElementById('completedTransactions').innerHTML = formatNumber(stats.completed_transactions);
    document.getElementById('totalAmount').innerHTML = formatCurrency(stats.total_amount);
    document.getElementById('pendingTransactions').innerHTML = formatNumber(stats.pending_transactions);
    document.getElementById('successRate').textContent = stats.success_rate;
}

// Update recent transactions
function updateRecentTransactions(transactions) {
    const tbody = document.getElementById('recentTransactions');
    const loadingRow = document.getElementById('loadingRow');

    if (loadingRow) {
        loadingRow.remove();
    }

    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                    لا توجد تحويلات حتى الآن
                    <br>
                    <a href="/transactions/create" class="btn btn-primary btn-sm mt-2">
                        إنشاء أول تحويل
                    </a>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = '';
    transactions.forEach(transaction => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="fw-bold">${transaction.transaction_id}</span></td>
            <td>${transaction.recipient_name}</td>
            <td>
                <span class="fw-bold">${transaction.amount}</span>
                <small class="text-muted">${transaction.currency_from}</small>
            </td>
            <td>
                <span class="transaction-status status-${transaction.status}">
                    ${transaction.status_text}
                </span>
            </td>
            <td><small>${transaction.created_at}</small></td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Show loading states
function showLoadingStates() {
    const loadingSpinner = '<div class="spinner-border spinner-border-sm" role="status"></div>';

    // Don't show loading for stats as they have initial values
    // Just show loading for transactions if needed
}

// Show error states
function showErrorStates() {
    console.log('Dashboard loading failed, keeping current state');
}

// Show transactions error
function showTransactionsError() {
    const tbody = document.getElementById('recentTransactions');
    tbody.innerHTML = `
        <tr>
            <td colspan="6" class="text-center text-muted py-4">
                <i class="bi bi-exclamation-triangle fs-1 d-block mb-2"></i>
                فشل في تحميل التحويلات
                <br>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadTransactions()">
                    إعادة المحاولة
                </button>
            </td>
        </tr>
    `;
}

// Format number
function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num || 0);
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount || 0);
}

// Initialize on page load with delay to ensure page is ready
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure page is fully rendered
    setTimeout(() => {
        loadDashboardData();
    }, 100);
});

// Auto refresh every 2 minutes (increased interval for better performance)
setInterval(refreshDashboard, 120000);
</script>
@endpush

@push('styles')
<style>
/* Performance optimized styles */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    will-change: transform;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-card.success {
    border-left: 4px solid #28a745;
}

.stat-card.warning {
    border-left: 4px solid #ffc107;
}

.stat-card.info {
    border-left: 4px solid #17a2b8;
}

/* Quick actions */
.quick-actions .card {
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
}

.quick-actions .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #2c5aa0;
}

/* Transaction status badges */
.transaction-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-processing {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Loading states */
.loading-text {
    transition: all 0.3s ease;
}

/* Smooth animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.4s ease-out;
}

/* Table optimizations */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

/* Responsive optimizations */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 1rem;
        padding: 1rem;
    }

    .quick-actions .card-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Performance improvements */
* {
    box-sizing: border-box;
}

.card {
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* Reduce repaints */
.btn {
    will-change: transform;
}

.btn:hover {
    transform: translateY(-1px);
}
</style>
@endpush
