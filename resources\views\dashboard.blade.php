@extends('layouts.app')

@section('content')
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="h3 mb-0">لوحة التحكم</h1>
            <p class="text-muted mb-0">مرحباً بك، {{ Auth::user()->first_name }}. إليك ملخص حسابك</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                </button>
                <a href="{{ route('transactions.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg me-1"></i>تحويل جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي التحويلات</h6>
                    <h3 class="mb-0" id="totalTransactions">{{ $stats['total_transactions'] ?? 0 }}</h3>
                    <small class="opacity-75">هذا الشهر</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-arrow-left-right"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">التحويلات المكتملة</h6>
                    <h3 class="mb-0" id="completedTransactions">{{ $stats['completed_transactions'] ?? 0 }}</h3>
                    <small class="opacity-75">نسبة النجاح: {{ $stats['success_rate'] ?? 0 }}%</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-check-circle"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">المبلغ المحول</h6>
                    <h3 class="mb-0" id="totalAmount">{{ number_format($stats['total_amount'] ?? 0, 2) }}</h3>
                    <small class="opacity-75">ريال سعودي</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-currency-dollar"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">التحويلات المعلقة</h6>
                    <h3 class="mb-0" id="pendingTransactions">{{ $stats['pending_transactions'] ?? 0 }}</h3>
                    <small class="opacity-75">تحتاج مراجعة</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-clock"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions mb-4">
    <h5 class="mb-3">الإجراءات السريعة</h5>
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('transactions.create') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-primary mb-2">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <h6 class="card-title">تحويل جديد</h6>
                    <p class="card-text text-muted small">إرسال أموال إلى أي مكان في العالم</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('recipients.index') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-success mb-2">
                        <i class="bi bi-people"></i>
                    </div>
                    <h6 class="card-title">إدارة المستفيدين</h6>
                    <p class="card-text text-muted small">إضافة وإدارة قائمة المستفيدين</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('payments.index') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-warning mb-2">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <h6 class="card-title">طرق الدفع</h6>
                    <p class="card-text text-muted small">إدارة طرق الدفع والبطاقات</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('reports.index') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-info mb-2">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h6 class="card-title">التقارير</h6>
                    <p class="card-text text-muted small">عرض التقارير والإحصائيات</p>
                </div>
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Transactions -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">التحويلات الأخيرة</h5>
                <a href="{{ route('transactions.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم التحويل</th>
                                <th>المستفيد</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="recentTransactions">
                            @forelse($recentTransactions ?? [] as $transaction)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $transaction->transaction_id }}</span>
                                </td>
                                <td>{{ $transaction->recipient_name }}</td>
                                <td>
                                    <span class="fw-bold">{{ number_format($transaction->amount, 2) }}</span>
                                    <small class="text-muted">{{ $transaction->currency_from }}</small>
                                </td>
                                <td>
                                    <span class="transaction-status status-{{ $transaction->status }}">
                                        {{ __('transaction.status.' . $transaction->status) }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ $transaction->created_at->format('Y/m/d H:i') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('transactions.show', $transaction->transaction_id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        @if($transaction->status === 'pending')
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="cancelTransaction('{{ $transaction->transaction_id }}')">
                                            <i class="bi bi-x"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                    لا توجد تحويلات حتى الآن
                                    <br>
                                    <a href="{{ route('transactions.create') }}" class="btn btn-primary btn-sm mt-2">
                                        إنشاء أول تحويل
                                    </a>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts and Analytics -->
    <div class="col-lg-4 mb-4">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">توزيع التحويلات</h6>
            </div>
            <div class="card-body">
                <canvas id="transactionChart" height="200"></canvas>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">أسعار الصرف الحالية</h6>
            </div>
            <div class="card-body">
                <div id="exchangeRates">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>USD/SAR</span>
                        <span class="fw-bold">3.75</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>EUR/SAR</span>
                        <span class="fw-bold">4.12</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>GBP/SAR</span>
                        <span class="fw-bold">4.89</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>AED/SAR</span>
                        <span class="fw-bold">1.02</span>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">آخر تحديث: {{ now()->format('H:i') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notifications -->
@if($notifications ?? false)
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">الإشعارات</h5>
    </div>
    <div class="card-body">
        @foreach($notifications as $notification)
        <div class="alert alert-{{ $notification['type'] }} alert-dismissible fade show">
            <i class="bi bi-{{ $notification['icon'] }} me-2"></i>
            {{ $notification['message'] }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endforeach
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
// Dashboard data refresh
function refreshDashboard() {
    $.get('{{ route("dashboard.data") }}', function(data) {
        if (data.success) {
            updateDashboardStats(data.stats);
            updateRecentTransactions(data.transactions);
            updateChart(data.chartData);
            showToast('تم تحديث البيانات بنجاح', 'success');
        }
    }).fail(function() {
        showToast('فشل في تحديث البيانات', 'error');
    });
}

// Update dashboard statistics
function updateDashboardStats(stats) {
    $('#totalTransactions').text(formatNumber(stats.total_transactions));
    $('#completedTransactions').text(formatNumber(stats.completed_transactions));
    $('#totalAmount').text(formatCurrency(stats.total_amount));
    $('#pendingTransactions').text(formatNumber(stats.pending_transactions));
}

// Update recent transactions table
function updateRecentTransactions(transactions) {
    const tbody = $('#recentTransactions');
    tbody.empty();
    
    if (transactions.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                    لا توجد تحويلات حتى الآن
                </td>
            </tr>
        `);
        return;
    }
    
    transactions.forEach(function(transaction) {
        const row = `
            <tr>
                <td><span class="fw-bold">${transaction.transaction_id}</span></td>
                <td>${transaction.recipient_name}</td>
                <td>
                    <span class="fw-bold">${formatNumber(transaction.amount)}</span>
                    <small class="text-muted">${transaction.currency_from}</small>
                </td>
                <td>
                    <span class="transaction-status status-${transaction.status}">
                        ${getStatusText(transaction.status)}
                    </span>
                </td>
                <td><small>${formatDate(transaction.created_at)}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="/transactions/${transaction.transaction_id}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye"></i>
                        </a>
                        ${transaction.status === 'pending' ? 
                            `<button class="btn btn-outline-danger btn-sm" onclick="cancelTransaction('${transaction.transaction_id}')">
                                <i class="bi bi-x"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// Cancel transaction
function cancelTransaction(transactionId) {
    if (confirm('هل أنت متأكد من إلغاء هذا التحويل؟')) {
        $.post(`/api/v1/transactions/${transactionId}/cancel`, {
            reason: 'إلغاء من قبل المستخدم'
        }, function(data) {
            if (data.success) {
                showToast('تم إلغاء التحويل بنجاح', 'success');
                refreshDashboard();
            } else {
                showToast('فشل في إلغاء التحويل', 'error');
            }
        }).fail(function() {
            showToast('حدث خطأ أثناء إلغاء التحويل', 'error');
        });
    }
}

// Get status text in Arabic
function getStatusText(status) {
    const statusMap = {
        'pending': 'معلق',
        'processing': 'قيد المعالجة',
        'completed': 'مكتمل',
        'failed': 'فاشل',
        'cancelled': 'ملغي'
    };
    return statusMap[status] || status;
}

// Initialize chart
let transactionChart;
function initChart() {
    const ctx = document.getElementById('transactionChart').getContext('2d');
    transactionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['مكتملة', 'معلقة', 'فاشلة'],
            datasets: [{
                data: [
                    {{ $stats['completed_transactions'] ?? 0 }},
                    {{ $stats['pending_transactions'] ?? 0 }},
                    {{ $stats['failed_transactions'] ?? 0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Update chart data
function updateChart(chartData) {
    if (transactionChart && chartData) {
        transactionChart.data.datasets[0].data = [
            chartData.completed,
            chartData.pending,
            chartData.failed
        ];
        transactionChart.update();
    }
}

// Auto refresh every 30 seconds
setInterval(refreshDashboard, 30000);

// Initialize on page load
$(document).ready(function() {
    initChart();
});
</script>
@endpush
