@extends('layouts.app')

@section('content')
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="h3 mb-0">لوحة التحكم</h1>
            <p class="text-muted mb-0">مرحباً بك، {{ Auth::user()->first_name }}. إليك ملخص حسابك</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                </button>
                <a href="{{ route('transactions.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg me-1"></i>تحويل جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards with Loading States -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي التحويلات</h6>
                    <h3 class="mb-0" id="totalTransactions">
                        <div class="loading-skeleton" style="width: 60px; height: 32px;"></div>
                    </h3>
                    <small class="opacity-75">هذا الشهر</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-arrow-left-right"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">التحويلات المكتملة</h6>
                    <h3 class="mb-0" id="completedTransactions">
                        <div class="loading-skeleton" style="width: 60px; height: 32px;"></div>
                    </h3>
                    <small class="opacity-75">نسبة النجاح: <span id="successRate">-</span>%</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">المبلغ المحول</h6>
                    <h3 class="mb-0" id="totalAmount">
                        <div class="loading-skeleton" style="width: 80px; height: 32px;"></div>
                    </h3>
                    <small class="opacity-75">ريال سعودي</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-currency-dollar"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">التحويلات المعلقة</h6>
                    <h3 class="mb-0" id="pendingTransactions">
                        <div class="loading-skeleton" style="width: 60px; height: 32px;"></div>
                    </h3>
                    <small class="opacity-75">تحتاج مراجعة</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-clock"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions mb-4">
    <h5 class="mb-3">الإجراءات السريعة</h5>
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('transactions.create') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-primary mb-2">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <h6 class="card-title">تحويل جديد</h6>
                    <p class="card-text text-muted small">إرسال أموال إلى أي مكان في العالم</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('recipients.index') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-success mb-2">
                        <i class="bi bi-people"></i>
                    </div>
                    <h6 class="card-title">إدارة المستفيدين</h6>
                    <p class="card-text text-muted small">إضافة وإدارة قائمة المستفيدين</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('payments.index') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-warning mb-2">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <h6 class="card-title">طرق الدفع</h6>
                    <p class="card-text text-muted small">إدارة طرق الدفع والبطاقات</p>
                </div>
            </a>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{{ route('reports.index') }}" class="card text-decoration-none h-100">
                <div class="card-body text-center">
                    <div class="fs-1 text-info mb-2">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h6 class="card-title">التقارير</h6>
                    <p class="card-text text-muted small">عرض التقارير والإحصائيات</p>
                </div>
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Transactions -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">التحويلات الأخيرة</h5>
                <a href="{{ route('transactions.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم التحويل</th>
                                <th>المستفيد</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="recentTransactions">
                            <!-- Loading state -->
                            <tr id="transactionsLoading">
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex justify-content-center align-items-center">
                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                        <span>جاري تحميل التحويلات...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts and Analytics -->
    <div class="col-lg-4 mb-4">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">توزيع التحويلات</h6>
            </div>
            <div class="card-body">
                <canvas id="transactionChart" height="200"></canvas>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">أسعار الصرف الحالية</h6>
            </div>
            <div class="card-body">
                <div id="exchangeRates">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>USD/SAR</span>
                        <span class="fw-bold">3.75</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>EUR/SAR</span>
                        <span class="fw-bold">4.12</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>GBP/SAR</span>
                        <span class="fw-bold">4.89</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>AED/SAR</span>
                        <span class="fw-bold">1.02</span>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">آخر تحديث: {{ now()->format('H:i') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notifications -->
@if($notifications ?? false)
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">الإشعارات</h5>
    </div>
    <div class="card-body">
        @foreach($notifications as $notification)
        <div class="alert alert-{{ $notification['type'] }} alert-dismissible fade show">
            <i class="bi bi-{{ $notification['icon'] }} me-2"></i>
            {{ $notification['message'] }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endforeach
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
// Dashboard data refresh
function refreshDashboard() {
    $.get('{{ route("dashboard.data") }}', function(data) {
        if (data.success) {
            updateDashboardStats(data.stats);
            updateRecentTransactions(data.transactions);
            updateChart(data.chartData);
            showToast('تم تحديث البيانات بنجاح', 'success');
        }
    }).fail(function() {
        showToast('فشل في تحديث البيانات', 'error');
    });
}

// Update dashboard statistics
function updateDashboardStats(stats) {
    $('#totalTransactions').text(formatNumber(stats.total_transactions));
    $('#completedTransactions').text(formatNumber(stats.completed_transactions));
    $('#totalAmount').text(formatCurrency(stats.total_amount));
    $('#pendingTransactions').text(formatNumber(stats.pending_transactions));
}

// Update recent transactions table
function updateRecentTransactions(transactions) {
    const tbody = $('#recentTransactions');
    tbody.empty();
    
    if (transactions.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                    لا توجد تحويلات حتى الآن
                </td>
            </tr>
        `);
        return;
    }
    
    transactions.forEach(function(transaction) {
        const row = `
            <tr>
                <td><span class="fw-bold">${transaction.transaction_id}</span></td>
                <td>${transaction.recipient_name}</td>
                <td>
                    <span class="fw-bold">${formatNumber(transaction.amount)}</span>
                    <small class="text-muted">${transaction.currency_from}</small>
                </td>
                <td>
                    <span class="transaction-status status-${transaction.status}">
                        ${getStatusText(transaction.status)}
                    </span>
                </td>
                <td><small>${formatDate(transaction.created_at)}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="/transactions/${transaction.transaction_id}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye"></i>
                        </a>
                        ${transaction.status === 'pending' ? 
                            `<button class="btn btn-outline-danger btn-sm" onclick="cancelTransaction('${transaction.transaction_id}')">
                                <i class="bi bi-x"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// Cancel transaction
function cancelTransaction(transactionId) {
    if (confirm('هل أنت متأكد من إلغاء هذا التحويل؟')) {
        $.post(`/api/v1/transactions/${transactionId}/cancel`, {
            reason: 'إلغاء من قبل المستخدم'
        }, function(data) {
            if (data.success) {
                showToast('تم إلغاء التحويل بنجاح', 'success');
                refreshDashboard();
            } else {
                showToast('فشل في إلغاء التحويل', 'error');
            }
        }).fail(function() {
            showToast('حدث خطأ أثناء إلغاء التحويل', 'error');
        });
    }
}

// Get status text in Arabic
function getStatusText(status) {
    const statusMap = {
        'pending': 'معلق',
        'processing': 'قيد المعالجة',
        'completed': 'مكتمل',
        'failed': 'فاشل',
        'cancelled': 'ملغي'
    };
    return statusMap[status] || status;
}

// Initialize chart
let transactionChart;
function initChart() {
    const ctx = document.getElementById('transactionChart').getContext('2d');
    transactionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['مكتملة', 'معلقة', 'فاشلة'],
            datasets: [{
                data: [
                    {{ $stats['completed_transactions'] ?? 0 }},
                    {{ $stats['pending_transactions'] ?? 0 }},
                    {{ $stats['failed_transactions'] ?? 0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Update chart data
function updateChart(chartData) {
    if (transactionChart && chartData) {
        transactionChart.data.datasets[0].data = [
            chartData.completed,
            chartData.pending,
            chartData.failed
        ];
        transactionChart.update();
    }
}

// Progressive data loading for better performance
let loadingStates = {
    stats: false,
    transactions: false,
    chart: false,
    notifications: false
};

// Load dashboard data progressively
function loadDashboardData() {
    // Load stats first (fastest)
    loadStats();

    // Load transactions after a short delay
    setTimeout(() => loadTransactions(), 100);

    // Load chart data after stats
    setTimeout(() => loadChartData(), 200);

    // Load notifications last
    setTimeout(() => loadNotifications(), 300);
}

// Load statistics
function loadStats() {
    if (loadingStates.stats) return;
    loadingStates.stats = true;

    fetch('{{ route("dashboard.data") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStats(data.stats);
        }
    })
    .catch(error => {
        console.error('Error loading stats:', error);
        showStatsError();
    })
    .finally(() => {
        loadingStates.stats = false;
    });
}

// Load recent transactions
function loadTransactions() {
    if (loadingStates.transactions) return;
    loadingStates.transactions = true;

    fetch('{{ route("dashboard.transactions") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateRecentTransactions(data.transactions);
        }
    })
    .catch(error => {
        console.error('Error loading transactions:', error);
        showTransactionsError();
    })
    .finally(() => {
        loadingStates.transactions = false;
    });
}

// Load chart data
function loadChartData() {
    if (loadingStates.chart) return;
    loadingStates.chart = true;

    fetch('{{ route("dashboard.chart") }}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateChart(data.chartData);
        }
    })
    .catch(error => {
        console.error('Error loading chart:', error);
    })
    .finally(() => {
        loadingStates.chart = false;
    });
}

// Show loading states
function showLoadingStates() {
    // Show skeleton loaders for stats
    $('#totalTransactions').html('<div class="loading-skeleton" style="width: 60px; height: 32px;"></div>');
    $('#completedTransactions').html('<div class="loading-skeleton" style="width: 60px; height: 32px;"></div>');
    $('#totalAmount').html('<div class="loading-skeleton" style="width: 80px; height: 32px;"></div>');
    $('#pendingTransactions').html('<div class="loading-skeleton" style="width: 60px; height: 32px;"></div>');
    $('#successRate').text('-');

    // Show loading for transactions
    $('#recentTransactions').html(`
        <tr>
            <td colspan="6" class="text-center py-4">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    <span>جاري تحميل التحويلات...</span>
                </div>
            </td>
        </tr>
    `);
}

// Show stats error
function showStatsError() {
    $('#totalTransactions').text('خطأ');
    $('#completedTransactions').text('خطأ');
    $('#totalAmount').text('خطأ');
    $('#pendingTransactions').text('خطأ');
}

// Show transactions error
function showTransactionsError() {
    $('#recentTransactions').html(`
        <tr>
            <td colspan="6" class="text-center text-muted py-4">
                <i class="bi bi-exclamation-triangle fs-1 d-block mb-2"></i>
                فشل في تحميل التحويلات
                <br>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadTransactions()">
                    إعادة المحاولة
                </button>
            </td>
        </tr>
    `);
}

// Optimized refresh function
function refreshDashboard() {
    // Reset loading states
    Object.keys(loadingStates).forEach(key => loadingStates[key] = false);

    // Show loading states
    showLoadingStates();

    // Load data progressively
    loadDashboardData();

    showToast('جاري تحديث البيانات...', 'info');
}

// Auto refresh every 60 seconds (increased from 30 for better performance)
setInterval(refreshDashboard, 60000);

// Initialize on page load
$(document).ready(function() {
    initChart();
    // Load data progressively after page load
    setTimeout(() => loadDashboardData(), 100);
});
</script>
@endpush

@push('styles')
<style>
/* Loading Skeleton Animation */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    display: inline-block;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Smooth transitions for data updates */
.stat-card h3, .stat-card small {
    transition: all 0.3s ease;
}

/* Loading states */
.loading-state {
    opacity: 0.6;
    pointer-events: none;
}

/* Fade in animation for loaded content */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Optimized table loading */
#recentTransactions tr {
    transition: all 0.3s ease;
}

/* Performance optimizations */
.stat-card {
    will-change: transform;
}

.quick-action-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: #2c5aa0;
}

.transaction-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-completed {
    background-color: #d1edff;
    color: #0c5460;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

.status-processing {
    background-color: #cff4fc;
    color: #055160;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 1rem;
    }

    .loading-skeleton {
        width: 50px !important;
        height: 24px !important;
    }
}
</style>
@endpush
