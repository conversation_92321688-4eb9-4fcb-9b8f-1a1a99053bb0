<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public',
        'is_encrypted',
        'validation_rules',
        'default_value',
        'metadata',
    ];

    protected $casts = [
        'value' => 'json',
        'metadata' => 'array',
        'validation_rules' => 'array',
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
    ];

    /**
     * Scope for public settings.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for settings by group.
     */
    public function scopeByGroup($query, string $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Get setting value with caching.
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("system_setting_{$key}", 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->first();
            
            if (!$setting) {
                return $default;
            }

            $value = $setting->value;

            // Decrypt if encrypted
            if ($setting->is_encrypted && $value) {
                try {
                    $value = decrypt($value);
                } catch (\Exception $e) {
                    return $default;
                }
            }

            // Cast to appropriate type
            return self::castValue($value, $setting->type);
        });
    }

    /**
     * Set setting value with caching.
     */
    public static function set(string $key, $value, string $type = 'string', string $group = 'general'): self
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group,
            ]
        );

        // Clear cache
        Cache::forget("system_setting_{$key}");
        Cache::forget('system_settings_all');

        return $setting;
    }

    /**
     * Get all settings with caching.
     */
    public static function getAll(): array
    {
        return Cache::remember('system_settings_all', 3600, function () {
            $settings = self::all();
            $result = [];

            foreach ($settings as $setting) {
                $value = $setting->value;

                // Decrypt if encrypted
                if ($setting->is_encrypted && $value) {
                    try {
                        $value = decrypt($value);
                    } catch (\Exception $e) {
                        continue;
                    }
                }

                $result[$setting->key] = self::castValue($value, $setting->type);
            }

            return $result;
        });
    }

    /**
     * Get settings by group.
     */
    public static function getByGroup(string $group): array
    {
        return Cache::remember("system_settings_group_{$group}", 3600, function () use ($group) {
            $settings = self::where('group', $group)->get();
            $result = [];

            foreach ($settings as $setting) {
                $value = $setting->value;

                // Decrypt if encrypted
                if ($setting->is_encrypted && $value) {
                    try {
                        $value = decrypt($value);
                    } catch (\Exception $e) {
                        continue;
                    }
                }

                $result[$setting->key] = self::castValue($value, $setting->type);
            }

            return $result;
        });
    }

    /**
     * Cast value to appropriate type.
     */
    private static function castValue($value, string $type)
    {
        return match ($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'array' => is_array($value) ? $value : json_decode($value, true),
            'json' => is_array($value) ? $value : json_decode($value, true),
            default => $value,
        };
    }

    /**
     * Clear all settings cache.
     */
    public static function clearCache(): void
    {
        $keys = self::pluck('key');
        
        foreach ($keys as $key) {
            Cache::forget("system_setting_{$key}");
        }
        
        Cache::forget('system_settings_all');
        
        $groups = self::distinct('group')->pluck('group');
        foreach ($groups as $group) {
            Cache::forget("system_settings_group_{$group}");
        }
    }

    /**
     * Validate setting value.
     */
    public function validateValue($value): bool
    {
        if (!$this->validation_rules) {
            return true;
        }

        $validator = validator(['value' => $value], ['value' => $this->validation_rules]);
        
        return !$validator->fails();
    }

    /**
     * Get formatted setting data.
     */
    public function getFormattedDataAttribute(): array
    {
        $value = $this->value;

        // Don't show encrypted values
        if ($this->is_encrypted) {
            $value = '***encrypted***';
        }

        return [
            'id' => $this->id,
            'key' => $this->key,
            'value' => $value,
            'type' => $this->type,
            'group' => $this->group,
            'description' => $this->description,
            'is_public' => $this->is_public,
            'is_encrypted' => $this->is_encrypted,
            'validation_rules' => $this->validation_rules,
            'default_value' => $this->default_value,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when settings are updated
        static::saved(function ($setting) {
            self::clearCache();
        });

        static::deleted(function ($setting) {
            self::clearCache();
        });
    }
}
