@extends('layouts.app')

@section('content')
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="h3 mb-0">لوحة تحكم الإدارة</h1>
            <p class="text-muted mb-0">مرحباً {{ Auth::user()->first_name }}، إليك ملخص شامل للنظام</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshAdminDashboard()">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                </button>
                <a href="{{ route('admin.users') }}" class="btn btn-primary">
                    <i class="bi bi-people me-1"></i>إدارة المستخدمين
                </a>
            </div>
        </div>
    </div>
</div>

<!-- System Overview Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي المستخدمين</h6>
                    <h3 class="mb-0" id="totalUsers">{{ $stats['total_users'] ?? 0 }}</h3>
                    <small class="opacity-75">مستخدم مسجل</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-people"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">التحويلات اليوم</h6>
                    <h3 class="mb-0" id="todayTransactions">{{ $stats['today_transactions'] ?? 0 }}</h3>
                    <small class="opacity-75">تحويل مكتمل</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-arrow-left-right"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">الإيرادات اليوم</h6>
                    <h3 class="mb-0" id="todayRevenue">{{ number_format($stats['today_revenue'] ?? 0, 2) }}</h3>
                    <small class="opacity-75">ريال سعودي</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-currency-dollar"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">المراجعة المطلوبة</h6>
                    <h3 class="mb-0" id="pendingReview">{{ $stats['pending_review'] ?? 0 }}</h3>
                    <small class="opacity-75">تحويل معلق</small>
                </div>
                <div class="fs-1 opacity-50">
                    <i class="bi bi-clock"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">حالة النظام</h5>
                <span class="badge bg-success">متصل</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between mb-2">
                            <span>قاعدة البيانات</span>
                            <span class="badge bg-success">متصلة</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>بوابة الدفع Stripe</span>
                            <span class="badge bg-success">متصلة</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>بوابة الدفع PayPal</span>
                            <span class="badge bg-success">متصلة</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between mb-2">
                            <span>خدمة الرسائل</span>
                            <span class="badge bg-warning">غير مفعلة</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>خدمة البريد</span>
                            <span class="badge bg-warning">غير مفعلة</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>نظام KYC</span>
                            <span class="badge bg-success">نشط</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">الإجراءات السريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.users') }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-people me-1"></i>إدارة المستخدمين
                    </a>
                    <a href="{{ route('admin.transactions') }}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-list-check me-1"></i>مراجعة التحويلات
                    </a>
                    <a href="{{ route('admin.settings') }}" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-gear me-1"></i>إعدادات النظام
                    </a>
                    <button class="btn btn-outline-warning btn-sm" onclick="exportReports()">
                        <i class="bi bi-download me-1"></i>تصدير التقارير
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Transactions -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">التحويلات الأخيرة</h5>
                <a href="{{ route('admin.transactions') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم التحويل</th>
                                <th>المرسل</th>
                                <th>المستفيد</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="recentTransactions">
                            @forelse($recentTransactions ?? [] as $transaction)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $transaction->transaction_id }}</span>
                                </td>
                                <td>{{ $transaction->sender_name }}</td>
                                <td>{{ $transaction->recipient_name }}</td>
                                <td>
                                    <span class="fw-bold">{{ number_format($transaction->amount, 2) }}</span>
                                    <small class="text-muted">{{ $transaction->currency_from }}</small>
                                </td>
                                <td>
                                    <span class="transaction-status status-{{ $transaction->status }}">
                                        {{ __('transaction.status.' . $transaction->status) }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ $transaction->created_at->format('Y/m/d H:i') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('transactions.show', $transaction->transaction_id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        @if($transaction->status === 'pending')
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="approveTransaction('{{ $transaction->transaction_id }}')">
                                            <i class="bi bi-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="rejectTransaction('{{ $transaction->transaction_id }}')">
                                            <i class="bi bi-x"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                    لا توجد تحويلات حتى الآن
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Analytics -->
    <div class="col-lg-4 mb-4">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">إحصائيات الأداء</h6>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="200"></canvas>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">أهم البلدان</h6>
            </div>
            <div class="card-body">
                <div id="topCountries">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>🇸🇦 السعودية</span>
                        <span class="fw-bold">45%</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>🇺🇸 أمريكا</span>
                        <span class="fw-bold">23%</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>🇬🇧 بريطانيا</span>
                        <span class="fw-bold">15%</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>🇦🇪 الإمارات</span>
                        <span class="fw-bold">17%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">الأنشطة الأخيرة</h5>
    </div>
    <div class="card-body">
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-marker bg-success"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">تحويل جديد مكتمل</h6>
                    <p class="text-muted mb-1">تم إكمال تحويل بقيمة 1,500 ريال من أحمد محمد</p>
                    <small class="text-muted">منذ 5 دقائق</small>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-marker bg-info"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">مستخدم جديد</h6>
                    <p class="text-muted mb-1">انضم مستخدم جديد: سارة أحمد</p>
                    <small class="text-muted">منذ 15 دقيقة</small>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-marker bg-warning"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">تحويل يحتاج مراجعة</h6>
                    <p class="text-muted mb-1">تحويل بقيمة 5,000 ريال يحتاج موافقة إدارية</p>
                    <small class="text-muted">منذ 30 دقيقة</small>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}
</style>
@endpush

@push('scripts')
<script>
// Refresh admin dashboard
function refreshAdminDashboard() {
    $.get('{{ route("admin.dashboard.data") }}', function(data) {
        if (data.success) {
            updateAdminStats(data.stats);
            updateRecentTransactions(data.transactions);
            updateChart(data.chartData);
            showToast('تم تحديث البيانات بنجاح', 'success');
        }
    }).fail(function() {
        showToast('فشل في تحديث البيانات', 'error');
    });
}

// Update admin statistics
function updateAdminStats(stats) {
    $('#totalUsers').text(formatNumber(stats.total_users));
    $('#todayTransactions').text(formatNumber(stats.today_transactions));
    $('#todayRevenue').text(formatCurrency(stats.today_revenue));
    $('#pendingReview').text(formatNumber(stats.pending_review));
}

// Approve transaction
function approveTransaction(transactionId) {
    if (confirm('هل أنت متأكد من الموافقة على هذا التحويل؟')) {
        $.post(`/api/v1/admin/transactions/${transactionId}/approve`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        }, function(data) {
            if (data.success) {
                showToast('تم الموافقة على التحويل بنجاح', 'success');
                refreshAdminDashboard();
            } else {
                showToast('فشل في الموافقة على التحويل', 'error');
            }
        }).fail(function() {
            showToast('حدث خطأ أثناء الموافقة على التحويل', 'error');
        });
    }
}

// Reject transaction
function rejectTransaction(transactionId) {
    const reason = prompt('يرجى إدخال سبب الرفض:');
    if (reason) {
        $.post(`/api/v1/admin/transactions/${transactionId}/reject`, {
            reason: reason,
            _token: $('meta[name="csrf-token"]').attr('content')
        }, function(data) {
            if (data.success) {
                showToast('تم رفض التحويل بنجاح', 'success');
                refreshAdminDashboard();
            } else {
                showToast('فشل في رفض التحويل', 'error');
            }
        }).fail(function() {
            showToast('حدث خطأ أثناء رفض التحويل', 'error');
        });
    }
}

// Export reports
function exportReports() {
    window.open('/admin/reports/export', '_blank');
}

// Initialize chart
let performanceChart;
function initPerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
            datasets: [{
                label: 'التحويلات',
                data: [12, 19, 3, 5, 2, 3, 9],
                borderColor: '#2c5aa0',
                backgroundColor: 'rgba(44, 90, 160, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Auto refresh every 30 seconds
setInterval(refreshAdminDashboard, 30000);

// Initialize on page load
$(document).ready(function() {
    initPerformanceChart();
});
</script>
@endpush
