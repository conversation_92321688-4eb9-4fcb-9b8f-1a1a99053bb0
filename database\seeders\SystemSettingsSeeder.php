<?php

namespace Database\Seeders;

use App\Models\SystemSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'app_name',
                'value' => 'Mony Transfer',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Application name',
                'is_public' => true,
            ],
            [
                'key' => 'app_version',
                'value' => '1.0.0',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Application version',
                'is_public' => true,
            ],
            [
                'key' => 'maintenance_mode',
                'value' => false,
                'type' => 'boolean',
                'group' => 'general',
                'description' => 'Enable maintenance mode',
                'is_public' => true,
            ],
            [
                'key' => 'default_currency',
                'value' => 'USD',
                'type' => 'string',
                'group' => 'financial',
                'description' => 'Default system currency',
                'is_public' => true,
            ],
            [
                'key' => 'default_language',
                'value' => 'ar',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Default system language',
                'is_public' => true,
            ],

            // Financial Settings
            [
                'key' => 'min_transaction_amount',
                'value' => 10,
                'type' => 'float',
                'group' => 'financial',
                'description' => 'Minimum transaction amount in USD',
                'is_public' => true,
            ],
            [
                'key' => 'max_transaction_amount',
                'value' => 50000,
                'type' => 'float',
                'group' => 'financial',
                'description' => 'Maximum transaction amount in USD',
                'is_public' => true,
            ],
            [
                'key' => 'default_commission_rate',
                'value' => 2.5,
                'type' => 'float',
                'group' => 'financial',
                'description' => 'Default commission rate percentage',
                'is_public' => false,
            ],
            [
                'key' => 'daily_limit_unverified',
                'value' => 500,
                'type' => 'float',
                'group' => 'financial',
                'description' => 'Daily limit for unverified users',
                'is_public' => false,
            ],
            [
                'key' => 'daily_limit_verified',
                'value' => 10000,
                'type' => 'float',
                'group' => 'financial',
                'description' => 'Daily limit for verified users',
                'is_public' => false,
            ],

            // Security Settings
            [
                'key' => 'two_factor_required',
                'value' => false,
                'type' => 'boolean',
                'group' => 'security',
                'description' => 'Require two-factor authentication',
                'is_public' => true,
            ],
            [
                'key' => 'session_timeout',
                'value' => 30,
                'type' => 'integer',
                'group' => 'security',
                'description' => 'Session timeout in minutes',
                'is_public' => false,
            ],
            [
                'key' => 'max_login_attempts',
                'value' => 5,
                'type' => 'integer',
                'group' => 'security',
                'description' => 'Maximum login attempts before lockout',
                'is_public' => false,
            ],
            [
                'key' => 'lockout_duration',
                'value' => 15,
                'type' => 'integer',
                'group' => 'security',
                'description' => 'Account lockout duration in minutes',
                'is_public' => false,
            ],

            // KYC Settings
            [
                'key' => 'kyc_required',
                'value' => true,
                'type' => 'boolean',
                'group' => 'kyc',
                'description' => 'Require KYC verification',
                'is_public' => true,
            ],
            [
                'key' => 'kyc_verification_timeout',
                'value' => 72,
                'type' => 'integer',
                'group' => 'kyc',
                'description' => 'KYC verification timeout in hours',
                'is_public' => false,
            ],
            [
                'key' => 'document_expiry_years',
                'value' => 5,
                'type' => 'integer',
                'group' => 'kyc',
                'description' => 'Document expiry period in years',
                'is_public' => false,
            ],

            // Notification Settings
            [
                'key' => 'email_notifications',
                'value' => true,
                'type' => 'boolean',
                'group' => 'notifications',
                'description' => 'Enable email notifications',
                'is_public' => true,
            ],
            [
                'key' => 'sms_notifications',
                'value' => true,
                'type' => 'boolean',
                'group' => 'notifications',
                'description' => 'Enable SMS notifications',
                'is_public' => true,
            ],
            [
                'key' => 'push_notifications',
                'value' => true,
                'type' => 'boolean',
                'group' => 'notifications',
                'description' => 'Enable push notifications',
                'is_public' => true,
            ],

            // API Settings
            [
                'key' => 'api_rate_limit',
                'value' => 100,
                'type' => 'integer',
                'group' => 'api',
                'description' => 'API rate limit per minute',
                'is_public' => false,
            ],
            [
                'key' => 'api_version',
                'value' => 'v1',
                'type' => 'string',
                'group' => 'api',
                'description' => 'Current API version',
                'is_public' => true,
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
