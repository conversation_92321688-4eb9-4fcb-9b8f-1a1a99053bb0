# Money Transfer System API Documentation

## Overview
This document provides comprehensive documentation for the Money Transfer System API. The API is built using Laravel and follows RESTful principles.

## Base URL
```
Production: https://your-domain.com/api/v1
Development: http://localhost:8000/api/v1
```

## Authentication
The API uses Laravel Sanctum for authentication. Include the Bearer token in the Authorization header:

```
Authorization: Bearer {your-token}
```

## Response Format
All API responses follow this standard format:

```json
{
    "success": true|false,
    "message": "Response message",
    "data": {
        // Response data
    },
    "errors": {
        // Validation errors (if any)
    }
}
```

## Error Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

---

## Authentication Endpoints

### Register User
**POST** `/auth/register`

Register a new user account.

**Request Body:**
```json
{
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "phone": "+************",
    "password": "password123",
    "password_confirmation": "password123",
    "country_id": 1,
    "date_of_birth": "1990-01-01",
    "address": "الرياض، المملكة العربية السعودية"
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم إنشاء الحساب بنجاح",
    "data": {
        "user": {
            "id": 1,
            "first_name": "أحمد",
            "last_name": "محمد",
            "email": "<EMAIL>",
            "phone": "+************"
        },
        "token": "1|abc123..."
    }
}
```

### Login
**POST** `/auth/login`

Authenticate user and get access token.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم تسجيل الدخول بنجاح",
    "data": {
        "user": {
            "id": 1,
            "first_name": "أحمد",
            "last_name": "محمد",
            "email": "<EMAIL>"
        },
        "token": "1|abc123...",
        "requires_2fa": false
    }
}
```

### Logout
**POST** `/auth/logout`

Logout user and revoke token.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "message": "تم تسجيل الخروج بنجاح"
}
```

---

## Transaction Endpoints

### Create Transaction
**POST** `/transactions`

Create a new money transfer transaction.

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "currency_from": "SAR",
    "currency_to": "USD",
    "amount": 1000.00,
    "purpose": "family_support",
    "delivery_method": "cash_pickup",
    "recipient_name": "John Doe",
    "recipient_phone": "+12345678901",
    "recipient_country_id": 2,
    "recipient_address": "123 Main St, New York, USA",
    "payment_method": "stripe",
    "notes": "Monthly family support"
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم إنشاء التحويل بنجاح",
    "data": {
        "transaction": {
            "transaction_id": "MT20241201ABC123",
            "status": "pending",
            "amount": 1000.00,
            "fee": 20.00,
            "total_amount": 1020.00,
            "recipient_amount": 266.67,
            "exchange_rate": 0.2667,
            "currency_from": "SAR",
            "currency_to": "USD",
            "created_at": "2024-12-01T10:00:00Z"
        }
    }
}
```

### Get Transactions
**GET** `/transactions`

Get user's transaction history with pagination and filters.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (optional) - Page number (default: 1)
- `per_page` (optional) - Items per page (default: 15, max: 100)
- `status` (optional) - Filter by status: pending, processing, completed, failed, cancelled
- `currency_from` (optional) - Filter by source currency
- `currency_to` (optional) - Filter by destination currency
- `date_from` (optional) - Filter from date (YYYY-MM-DD)
- `date_to` (optional) - Filter to date (YYYY-MM-DD)
- `search` (optional) - Search in transaction ID, recipient name, or phone

**Response:**
```json
{
    "success": true,
    "data": {
        "transactions": [
            {
                "transaction_id": "MT20241201ABC123",
                "status": "completed",
                "amount": 1000.00,
                "recipient_amount": 266.67,
                "currency_from": "SAR",
                "currency_to": "USD",
                "recipient_name": "John Doe",
                "created_at": "2024-12-01T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 5,
            "per_page": 15,
            "total": 67
        }
    }
}
```

### Get Transaction Details
**GET** `/transactions/{transactionId}`

Get detailed information about a specific transaction.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "transaction": {
            "transaction_id": "MT20241201ABC123",
            "status": "completed",
            "amount": 1000.00,
            "fee": 20.00,
            "total_amount": 1020.00,
            "recipient_amount": 266.67,
            "exchange_rate": 0.2667,
            "currency_from": "SAR",
            "currency_to": "USD",
            "recipient_name": "John Doe",
            "recipient_phone": "+12345678901",
            "recipient_address": "123 Main St, New York, USA",
            "delivery_method": "cash_pickup",
            "purpose": "family_support",
            "created_at": "2024-12-01T10:00:00Z",
            "completed_at": "2024-12-01T10:15:00Z",
            "tracking_steps": [
                {
                    "step": "created",
                    "title": "تم إنشاء التحويل",
                    "completed_at": "2024-12-01T10:00:00Z",
                    "status": "completed"
                },
                {
                    "step": "payment_confirmed",
                    "title": "تم تأكيد الدفع",
                    "completed_at": "2024-12-01T10:05:00Z",
                    "status": "completed"
                },
                {
                    "step": "completed",
                    "title": "تم إكمال التحويل",
                    "completed_at": "2024-12-01T10:15:00Z",
                    "status": "completed"
                }
            ]
        }
    }
}
```

### Cancel Transaction
**POST** `/transactions/{transactionId}/cancel`

Cancel a pending transaction.

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "reason": "Changed my mind"
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم إلغاء التحويل بنجاح"
}
```

---

## Payment Endpoints

### Get Payment Methods
**GET** `/payments/methods`

Get available payment methods for a specific country and currency.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `country_code` (required) - ISO 2-letter country code
- `currency_code` (required) - ISO 3-letter currency code
- `amount` (optional) - Transaction amount for filtering

**Response:**
```json
{
    "success": true,
    "data": {
        "payment_methods": [
            {
                "id": 1,
                "name": "Stripe Credit Card",
                "type": "stripe",
                "fee_percentage": 2.9,
                "fee_fixed": 0.30,
                "min_amount": 1.00,
                "max_amount": 10000.00,
                "processing_time": "Instant",
                "requires_verification": false
            }
        ],
        "country_code": "SA",
        "currency_code": "SAR"
    }
}
```

### Calculate Payment Fees
**POST** `/payments/calculate-fees`

Calculate payment fees for a specific method and amount.

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "payment_method": "stripe",
    "amount": 1000.00
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "base_amount": 1000.00,
        "percentage_fee": 29.00,
        "fixed_fee": 0.30,
        "total_fee": 29.30,
        "total_amount": 1029.30,
        "fee_breakdown": {
            "percentage": "2.9%",
            "fixed": "0.30 SAR"
        }
    }
}
```

### Process Payment
**POST** `/payments/process`

Process payment for a transaction.

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "transaction_id": "MT20241201ABC123",
    "payment_method": "stripe",
    "payment_data": {
        "card_number": "****************",
        "expiry_month": "12",
        "expiry_year": "2025",
        "cvv": "123",
        "cardholder_name": "Ahmed Mohammed"
    }
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم معالجة الدفع بنجاح",
    "data": {
        "payment_result": {
            "status": "success",
            "reference": "stripe_pi_123456",
            "amount": 1029.30,
            "currency": "SAR"
        },
        "transaction_id": "MT20241201ABC123",
        "status": "success"
    }
}
```

---

## Exchange Rate Endpoints

### Get Exchange Rates
**GET** `/exchange-rates`

Get current exchange rates.

**Query Parameters:**
- `from` (optional) - Source currency code
- `to` (optional) - Target currency code

**Response:**
```json
{
    "success": true,
    "data": {
        "rates": [
            {
                "from_currency": "SAR",
                "to_currency": "USD",
                "buy_rate": 0.2667,
                "sell_rate": 0.2650,
                "updated_at": "2024-12-01T10:00:00Z"
            }
        ]
    }
}
```

### Get Exchange Rate Quote
**GET** `/exchange-rates/quote`

Get exchange rate quote for specific amount.

**Query Parameters:**
- `from` (required) - Source currency code
- `to` (required) - Target currency code
- `amount` (required) - Amount to convert

**Response:**
```json
{
    "success": true,
    "data": {
        "from_currency": "SAR",
        "to_currency": "USD",
        "amount": 1000.00,
        "rate": 0.2667,
        "converted_amount": 266.70,
        "fee": 0.00,
        "total_amount": 266.70,
        "expires_at": "2024-12-01T10:30:00Z"
    }
}
```

---

## User Profile Endpoints

### Get User Profile
**GET** `/user/profile`

Get current user's profile information.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "first_name": "أحمد",
            "last_name": "محمد",
            "email": "<EMAIL>",
            "phone": "+************",
            "country": {
                "id": 1,
                "name_ar": "المملكة العربية السعودية",
                "name_en": "Saudi Arabia",
                "code": "SAU"
            },
            "kyc_level": "basic",
            "kyc_status": "verified",
            "email_verified_at": "2024-12-01T09:00:00Z",
            "phone_verified_at": "2024-12-01T09:05:00Z",
            "created_at": "2024-12-01T08:00:00Z"
        }
    }
}
```

### Update User Profile
**PUT** `/user/profile`

Update user's profile information.

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "first_name": "أحمد",
    "last_name": "محمد علي",
    "phone": "+************",
    "address": "الرياض، المملكة العربية السعودية",
    "date_of_birth": "1990-01-01"
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم تحديث الملف الشخصي بنجاح",
    "data": {
        "user": {
            // Updated user data
        }
    }
}
```

---

## Error Handling

### Validation Errors
When validation fails, the API returns a 422 status code with detailed error information:

```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "email": [
            "The email field is required."
        ],
        "amount": [
            "The amount must be at least 1."
        ]
    }
}
```

### Authentication Errors
When authentication fails, the API returns a 401 status code:

```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

### Authorization Errors
When the user doesn't have permission, the API returns a 403 status code:

```json
{
    "success": false,
    "message": "This action is unauthorized."
}
```

### Not Found Errors
When a resource is not found, the API returns a 404 status code:

```json
{
    "success": false,
    "message": "Transaction not found."
}
```

---

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Authentication endpoints**: 5 requests per minute
- **General API endpoints**: 60 requests per minute
- **Payment processing**: 10 requests per minute

When rate limit is exceeded, the API returns a 429 status code:

```json
{
    "success": false,
    "message": "Too Many Attempts."
}
```

---

## Webhooks

### Payment Webhooks
The system supports webhooks for payment status updates:

**Endpoint**: `POST /webhooks/payments/{gateway}`

**Headers:**
```
Content-Type: application/json
X-Signature: {webhook-signature}
```

**Payload Example (Stripe):**
```json
{
    "id": "evt_123456",
    "type": "payment_intent.succeeded",
    "data": {
        "object": {
            "id": "pi_123456",
            "status": "succeeded",
            "amount": 102930
        }
    }
}
```

---

## SDKs and Libraries

### JavaScript/Node.js
```javascript
const MoneyTransferAPI = require('@your-company/money-transfer-api');

const client = new MoneyTransferAPI({
    baseURL: 'https://your-domain.com/api/v1',
    token: 'your-bearer-token'
});

// Create transaction
const transaction = await client.transactions.create({
    currency_from: 'SAR',
    currency_to: 'USD',
    amount: 1000.00,
    recipient_name: 'John Doe',
    // ... other fields
});
```

### PHP
```php
use YourCompany\MoneyTransferAPI\Client;

$client = new Client([
    'base_url' => 'https://your-domain.com/api/v1',
    'token' => 'your-bearer-token'
]);

// Create transaction
$transaction = $client->transactions()->create([
    'currency_from' => 'SAR',
    'currency_to' => 'USD',
    'amount' => 1000.00,
    'recipient_name' => 'John Doe',
    // ... other fields
]);
```

---

## Testing

### Postman Collection
A Postman collection is available for testing the API. Import the collection from:
`/docs/postman/Money_Transfer_API.postman_collection.json`

### Test Data
Use these test credentials for development:

**Test User:**
- Email: `<EMAIL>`
- Password: `password123`

**Test Cards:**
- Visa: `****************`
- Mastercard: `****************`
- Declined: `****************`

---

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.your-domain.com
- Status Page: https://status.your-domain.com
