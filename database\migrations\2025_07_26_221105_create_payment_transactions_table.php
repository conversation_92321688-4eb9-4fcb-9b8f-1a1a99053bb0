<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_id')->constrained('transactions')->onDelete('cascade');
            $table->foreignId('parent_payment_id')->nullable()->constrained('payment_transactions')->onDelete('set null');
            $table->string('gateway'); // stripe, paypal, etc.
            $table->string('gateway_reference')->nullable(); // Gateway's transaction ID
            $table->decimal('amount', 15, 2); // Can be negative for refunds
            $table->string('currency', 3);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('type', ['payment', 'refund'])->default('payment');
            $table->json('payment_data')->nullable(); // Sanitized payment data
            $table->json('gateway_response')->nullable(); // Full gateway response
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->string('error_code')->nullable();
            $table->text('error_message')->nullable();
            $table->json('metadata')->nullable(); // Additional metadata
            $table->timestamps();
            $table->softDeletes();

            $table->index(['transaction_id', 'status']);
            $table->index(['gateway', 'gateway_reference']);
            $table->index(['status', 'created_at']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
