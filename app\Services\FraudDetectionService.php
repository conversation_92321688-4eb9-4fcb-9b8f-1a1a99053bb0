<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use App\Models\FraudDetection;
use App\Models\AuditLog;
use Illuminate\Support\Facades\Log;

class FraudDetectionService
{
    /**
     * Analyze transaction for fraud indicators.
     */
    public function analyzeTransaction(Transaction $transaction): array
    {
        $riskScore = 0;
        $riskFactors = [];
        $riskLevel = 'low';

        // Amount-based risk analysis
        $amountRisk = $this->analyzeAmount($transaction);
        $riskScore += $amountRisk['score'];
        if ($amountRisk['factors']) {
            $riskFactors = array_merge($riskFactors, $amountRisk['factors']);
        }

        // User behavior analysis
        $behaviorRisk = $this->analyzeUserBehavior($transaction);
        $riskScore += $behaviorRisk['score'];
        if ($behaviorRisk['factors']) {
            $riskFactors = array_merge($riskFactors, $behaviorRisk['factors']);
        }

        // Geographic risk analysis
        $geoRisk = $this->analyzeGeographicRisk($transaction);
        $riskScore += $geoRisk['score'];
        if ($geoRisk['factors']) {
            $riskFactors = array_merge($riskFactors, $geoRisk['factors']);
        }

        // Time-based analysis
        $timeRisk = $this->analyzeTimePatterns($transaction);
        $riskScore += $timeRisk['score'];
        if ($timeRisk['factors']) {
            $riskFactors = array_merge($riskFactors, $timeRisk['factors']);
        }

        // Velocity analysis
        $velocityRisk = $this->analyzeVelocity($transaction);
        $riskScore += $velocityRisk['score'];
        if ($velocityRisk['factors']) {
            $riskFactors = array_merge($riskFactors, $velocityRisk['factors']);
        }

        // Determine risk level
        if ($riskScore >= 80) {
            $riskLevel = 'high';
        } elseif ($riskScore >= 50) {
            $riskLevel = 'medium';
        }

        // Create fraud detection record if risky
        if ($riskScore >= 30) {
            $this->createFraudAlert($transaction, $riskScore, $riskLevel, $riskFactors);
        }

        return [
            'risk_score' => $riskScore,
            'risk_level' => $riskLevel,
            'risk_factors' => $riskFactors,
            'requires_review' => $riskScore >= 50,
            'auto_block' => $riskScore >= 80,
        ];
    }

    /**
     * Analyze transaction amount for risk indicators.
     */
    private function analyzeAmount(Transaction $transaction): array
    {
        $score = 0;
        $factors = [];

        // Large amount risk
        if ($transaction->amount > 50000) {
            $score += 30;
            $factors[] = 'Large transaction amount (>' . number_format(50000) . ')';
        } elseif ($transaction->amount > 20000) {
            $score += 15;
            $factors[] = 'High transaction amount (>' . number_format(20000) . ')';
        }

        // Round number risk (potential money laundering)
        if ($transaction->amount == round($transaction->amount, -3)) {
            $score += 10;
            $factors[] = 'Round number amount (potential structuring)';
        }

        // Compare with user's typical amounts
        $userAverage = $transaction->sender->sentTransactions()
            ->where('status', 'completed')
            ->avg('amount');

        if ($userAverage && $transaction->amount > ($userAverage * 5)) {
            $score += 20;
            $factors[] = 'Amount significantly higher than user average';
        }

        return ['score' => $score, 'factors' => $factors];
    }

    /**
     * Analyze user behavior patterns.
     */
    private function analyzeUserBehavior(Transaction $transaction): array
    {
        $score = 0;
        $factors = [];
        $user = $transaction->sender;

        // New user risk
        if ($user->created_at->diffInDays(now()) < 7) {
            $score += 25;
            $factors[] = 'New user account (less than 7 days old)';
        }

        // Unverified user
        if (!$user->isVerified()) {
            $score += 30;
            $factors[] = 'Unverified user (KYC/AML not completed)';
        }

        // High-risk user classification
        if ($user->risk_level === 'high') {
            $score += 40;
            $factors[] = 'User classified as high risk';
        }

        // Multiple failed transactions
        $failedCount = $user->sentTransactions()
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        if ($failedCount >= 3) {
            $score += 15;
            $factors[] = 'Multiple failed transactions in past week';
        }

        // Rapid account changes
        $recentChanges = AuditLog::where('model_type', 'User')
            ->where('model_id', $user->id)
            ->where('created_at', '>=', now()->subHours(24))
            ->count();

        if ($recentChanges >= 3) {
            $score += 20;
            $factors[] = 'Multiple account changes in past 24 hours';
        }

        return ['score' => $score, 'factors' => $factors];
    }

    /**
     * Analyze geographic risk factors.
     */
    private function analyzeGeographicRisk(Transaction $transaction): array
    {
        $score = 0;
        $factors = [];

        // High-risk countries (simplified list)
        $highRiskCountries = ['AFG', 'IRN', 'PRK', 'SYR'];
        $mediumRiskCountries = ['PAK', 'BGD', 'NGA'];

        $senderCountry = $transaction->senderCountry?->code;
        $receiverCountry = $transaction->receiverCountry?->code;

        if (in_array($senderCountry, $highRiskCountries) || in_array($receiverCountry, $highRiskCountries)) {
            $score += 40;
            $factors[] = 'Transaction involves high-risk country';
        } elseif (in_array($senderCountry, $mediumRiskCountries) || in_array($receiverCountry, $mediumRiskCountries)) {
            $score += 20;
            $factors[] = 'Transaction involves medium-risk country';
        }

        // Cross-border transactions
        if ($senderCountry !== $receiverCountry) {
            $score += 5;
            $factors[] = 'Cross-border transaction';
        }

        // Unusual destination for user
        $userCountries = $transaction->sender->sentTransactions()
            ->where('status', 'completed')
            ->pluck('receiver_country_id')
            ->unique();

        if (!$userCountries->contains($transaction->receiver_country_id)) {
            $score += 15;
            $factors[] = 'First transaction to this destination country';
        }

        return ['score' => $score, 'factors' => $factors];
    }

    /**
     * Analyze time-based patterns.
     */
    private function analyzeTimePatterns(Transaction $transaction): array
    {
        $score = 0;
        $factors = [];

        // Off-hours transactions
        $hour = $transaction->created_at->hour;
        if ($hour < 6 || $hour > 22) {
            $score += 10;
            $factors[] = 'Transaction during off-hours';
        }

        // Weekend transactions
        if ($transaction->created_at->isWeekend()) {
            $score += 5;
            $factors[] = 'Weekend transaction';
        }

        // Holiday transactions (simplified check)
        $holidays = ['2024-01-01', '2024-12-25']; // Add more holidays
        if (in_array($transaction->created_at->format('Y-m-d'), $holidays)) {
            $score += 10;
            $factors[] = 'Transaction on holiday';
        }

        return ['score' => $score, 'factors' => $factors];
    }

    /**
     * Analyze transaction velocity.
     */
    private function analyzeVelocity(Transaction $transaction): array
    {
        $score = 0;
        $factors = [];
        $user = $transaction->sender;

        // Transactions in last hour
        $hourlyCount = $user->sentTransactions()
            ->where('created_at', '>=', now()->subHour())
            ->count();

        if ($hourlyCount >= 5) {
            $score += 30;
            $factors[] = 'High transaction frequency (5+ in last hour)';
        } elseif ($hourlyCount >= 3) {
            $score += 15;
            $factors[] = 'Elevated transaction frequency (3+ in last hour)';
        }

        // Daily volume
        $dailyVolume = $user->sentTransactions()
            ->whereDate('created_at', today())
            ->sum('amount');

        if ($dailyVolume > $user->daily_limit * 0.9) {
            $score += 20;
            $factors[] = 'Approaching daily transaction limit';
        }

        // Rapid successive transactions
        $lastTransaction = $user->sentTransactions()
            ->where('id', '!=', $transaction->id)
            ->latest()
            ->first();

        if ($lastTransaction && $lastTransaction->created_at->diffInMinutes($transaction->created_at) < 5) {
            $score += 15;
            $factors[] = 'Rapid successive transactions (less than 5 minutes apart)';
        }

        return ['score' => $score, 'factors' => $factors];
    }

    /**
     * Create fraud detection alert.
     */
    private function createFraudAlert(Transaction $transaction, int $riskScore, string $riskLevel, array $riskFactors): void
    {
        FraudDetection::create([
            'transaction_id' => $transaction->id,
            'user_id' => $transaction->sender_id,
            'risk_score' => $riskScore,
            'risk_level' => $riskLevel,
            'risk_factors' => $riskFactors,
            'detection_method' => 'automated_analysis',
            'status' => 'open',
            'detected_at' => now(),
            'metadata' => [
                'transaction_amount' => $transaction->amount,
                'transaction_currency' => $transaction->currency->code,
                'sender_country' => $transaction->senderCountry?->code,
                'receiver_country' => $transaction->receiverCountry?->code,
                'analysis_timestamp' => now(),
            ],
        ]);

        // Log the fraud detection
        Log::warning('Fraud alert created', [
            'transaction_id' => $transaction->id,
            'transaction_number' => $transaction->transaction_number,
            'risk_score' => $riskScore,
            'risk_level' => $riskLevel,
            'risk_factors' => $riskFactors,
        ]);

        // Auto-block high-risk transactions
        if ($riskLevel === 'high') {
            $transaction->update([
                'status' => 'blocked',
                'is_suspicious' => true,
            ]);

            Log::critical('High-risk transaction auto-blocked', [
                'transaction_id' => $transaction->id,
                'transaction_number' => $transaction->transaction_number,
                'risk_score' => $riskScore,
            ]);
        } elseif ($riskLevel === 'medium') {
            $transaction->update(['is_suspicious' => true]);
        }
    }

    /**
     * Analyze user for ongoing monitoring.
     */
    public function analyzeUser(User $user): array
    {
        $riskScore = 0;
        $riskFactors = [];

        // Transaction patterns
        $recentTransactions = $user->sentTransactions()
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        // High volume in short time
        $monthlyVolume = $recentTransactions->sum('amount');
        if ($monthlyVolume > $user->monthly_limit * 0.8) {
            $riskScore += 20;
            $riskFactors[] = 'High monthly transaction volume';
        }

        // Multiple destinations
        $uniqueDestinations = $recentTransactions->pluck('receiver_country_id')->unique()->count();
        if ($uniqueDestinations > 5) {
            $riskScore += 15;
            $riskFactors[] = 'Transactions to multiple countries';
        }

        // Failed transaction ratio
        $failedRatio = $recentTransactions->where('status', 'failed')->count() / max($recentTransactions->count(), 1);
        if ($failedRatio > 0.3) {
            $riskScore += 25;
            $riskFactors[] = 'High failed transaction ratio';
        }

        // Update user risk level if needed
        if ($riskScore >= 60 && $user->risk_level !== 'high') {
            $user->update(['risk_level' => 'high']);
        } elseif ($riskScore >= 30 && $user->risk_level === 'low') {
            $user->update(['risk_level' => 'medium']);
        }

        return [
            'risk_score' => $riskScore,
            'risk_factors' => $riskFactors,
            'recommendation' => $this->getRiskRecommendation($riskScore),
        ];
    }

    /**
     * Get risk-based recommendations.
     */
    private function getRiskRecommendation(int $riskScore): string
    {
        if ($riskScore >= 60) {
            return 'Enhanced monitoring required - consider account review';
        } elseif ($riskScore >= 30) {
            return 'Increased monitoring recommended';
        } else {
            return 'Standard monitoring sufficient';
        }
    }
}
