<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('Transaction Confirmation') }} - Mony Transfer</title>
    <style>
        body {
            font-family: {{ app()->getLocale() === 'ar' ? "'Tajawal', 'Arial', sans-serif" : "'Helvetica Neue', 'Arial', sans-serif" }};
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
            direction: {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }};
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 40px 30px;
        }
        .transaction-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
        }
        .transaction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .transaction-type {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .transaction-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-processing { background-color: #dbeafe; color: #1e40af; }
        .status-completed { background-color: #d1fae5; color: #065f46; }
        .status-failed { background-color: #fee2e2; color: #991b1b; }
        .status-cancelled { background-color: #f3f4f6; color: #374151; }
        .status-blocked { background-color: #fecaca; color: #7f1d1d; }
        .transaction-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .detail-item {
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .detail-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }
        .amount-display {
            text-align: center;
            padding: 25px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 12px;
            margin: 25px 0;
        }
        .amount-display .amount {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .amount-display .currency {
            font-size: 14px;
            opacity: 0.9;
        }
        .receiver-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .receiver-info h3 {
            margin: 0 0 15px 0;
            color: #1e40af;
            font-size: 18px;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .footer {
            background-color: #f8fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 5px 0;
            color: #6b7280;
            font-size: 14px;
        }
        .security-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .security-notice .icon {
            color: #d97706;
            font-size: 20px;
            margin-bottom: 10px;
        }
        @media (max-width: 600px) {
            .container { margin: 0; }
            .content { padding: 20px 15px; }
            .transaction-details { grid-template-columns: 1fr; }
            .transaction-header { flex-direction: column; gap: 15px; }
            .amount-display .amount { font-size: 24px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ $statusText['ar'] }}</h1>
            <p>{{ __('Transaction') }} #{{ $transaction->reference_number }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Greeting -->
            <h2>{{ __('Hello') }} {{ $user->first_name }},</h2>
            
            @if($transaction->status === 'completed')
                <p>{{ __('Your transaction has been completed successfully!') }}</p>
            @elseif($transaction->status === 'failed')
                <p>{{ __('Unfortunately, your transaction could not be processed.') }}</p>
            @elseif($transaction->status === 'pending')
                <p>{{ __('Your transaction is being processed and will be completed shortly.') }}</p>
            @else
                <p>{{ __('Your transaction status has been updated.') }}</p>
            @endif

            <!-- Amount Display -->
            <div class="amount-display">
                <div class="amount">{{ $formattedAmount }} {{ $currency->code }}</div>
                <div class="currency">{{ $currency->name }}</div>
            </div>

            <!-- Transaction Card -->
            <div class="transaction-card">
                <div class="transaction-header">
                    <div class="transaction-type">
                        <div class="transaction-icon">
                            @if($transaction->type === 'transfer')
                                💸
                            @elseif($transaction->type === 'deposit')
                                💰
                            @elseif($transaction->type === 'withdrawal')
                                🏧
                            @else
                                💳
                            @endif
                        </div>
                        <div>
                            <h3>{{ __('transactions.type.' . $transaction->type) }}</h3>
                            <p style="margin: 0; color: #6b7280;">{{ $transaction->reference_number }}</p>
                        </div>
                    </div>
                    <div class="status-badge status-{{ $transaction->status }}">
                        {{ $statusText['ar'] }}
                    </div>
                </div>

                <div class="transaction-details">
                    <div class="detail-item">
                        <div class="detail-label">{{ __('Amount') }}</div>
                        <div class="detail-value">{{ $formattedAmount }} {{ $currency->code }}</div>
                    </div>
                    
                    @if($transaction->fee > 0)
                    <div class="detail-item">
                        <div class="detail-label">{{ __('Fee') }}</div>
                        <div class="detail-value">{{ number_format($transaction->fee, 2) }} {{ $currency->code }}</div>
                    </div>
                    @endif
                    
                    <div class="detail-item">
                        <div class="detail-label">{{ __('Date') }}</div>
                        <div class="detail-value">{{ $transaction->created_at->format('Y-m-d H:i') }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">{{ __('Payment Method') }}</div>
                        <div class="detail-value">{{ __('transactions.payment_methods.' . $transaction->payment_method) }}</div>
                    </div>
                </div>
            </div>

            <!-- Receiver Information (for transfers) -->
            @if($transaction->type === 'transfer' && $transaction->receiver_name)
            <div class="receiver-info">
                <h3>{{ __('Receiver Information') }}</h3>
                <p><strong>{{ __('Name') }}:</strong> {{ $transaction->receiver_name }}</p>
                @if($transaction->receiver_phone)
                    <p><strong>{{ __('Phone') }}:</strong> {{ $transaction->receiver_phone }}</p>
                @endif
                @if($transaction->receiver_country)
                    <p><strong>{{ __('Country') }}:</strong> {{ $transaction->receiver_country->name }}</p>
                @endif
            </div>
            @endif

            <!-- Notes -->
            @if($transaction->notes)
            <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <strong>{{ __('Notes') }}:</strong>
                <p style="margin: 5px 0 0 0;">{{ $transaction->notes }}</p>
            </div>
            @endif

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{ config('app.url') }}/transactions/{{ $transaction->id }}" class="btn btn-primary">
                    {{ __('View Transaction Details') }}
                </a>
                <a href="{{ config('app.url') }}/transactions" class="btn btn-secondary">
                    {{ __('View All Transactions') }}
                </a>
            </div>

            <!-- Security Notice -->
            <div class="security-notice">
                <div class="icon">🔒</div>
                <strong>{{ __('Security Notice') }}</strong>
                <p style="margin: 5px 0 0 0; font-size: 14px;">
                    {{ __('This email contains sensitive financial information. Please keep it secure and do not share it with anyone.') }}
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Mony Transfer</strong></p>
            <p>{{ __('Your trusted money transfer service') }}</p>
            <p>{{ __('Support') }}: <EMAIL> | +966-11-1234567</p>
            <p style="font-size: 12px; margin-top: 15px;">
                {{ __('This is an automated email. Please do not reply to this message.') }}
            </p>
        </div>
    </div>
</body>
</html>
