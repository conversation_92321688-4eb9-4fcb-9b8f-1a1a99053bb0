<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class KYCMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $level = 'basic'): Response
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بالوصول',
                'error_code' => 'UNAUTHORIZED',
            ], 401);
        }

        // Check KYC requirements based on level
        switch ($level) {
            case 'basic':
                if (!$this->hasBasicKYC($user)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'يرجى إكمال التحقق الأساسي من الهوية',
                        'error_code' => 'KYC_BASIC_REQUIRED',
                        'required_actions' => $this->getBasicKYCRequirements($user),
                    ], 403);
                }
                break;

            case 'full':
                if (!$this->hasFullKYC($user)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'يرجى إكمال التحقق الكامل من الهوية',
                        'error_code' => 'KYC_FULL_REQUIRED',
                        'required_actions' => $this->getFullKYCRequirements($user),
                    ], 403);
                }
                break;

            case 'enhanced':
                if (!$this->hasEnhancedKYC($user)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'يرجى إكمال التحقق المعزز من الهوية',
                        'error_code' => 'KYC_ENHANCED_REQUIRED',
                        'required_actions' => $this->getEnhancedKYCRequirements($user),
                    ], 403);
                }
                break;
        }

        return $next($request);
    }

    /**
     * Check if user has basic KYC.
     */
    protected function hasBasicKYC($user): bool
    {
        return $user->email_verified_at !== null && 
               $user->phone_verified_at !== null;
    }

    /**
     * Check if user has full KYC.
     */
    protected function hasFullKYC($user): bool
    {
        if (!$this->hasBasicKYC($user)) {
            return false;
        }

        // Check required documents
        $requiredDocuments = ['national_id', 'selfie', 'address_proof'];
        $verifiedDocuments = $user->documents()
                                 ->where('verification_status', 'verified')
                                 ->where('status', 'active')
                                 ->pluck('type')
                                 ->toArray();

        return empty(array_diff($requiredDocuments, $verifiedDocuments));
    }

    /**
     * Check if user has enhanced KYC.
     */
    protected function hasEnhancedKYC($user): bool
    {
        if (!$this->hasFullKYC($user)) {
            return false;
        }

        return $user->kyc_verified_at !== null && 
               $user->aml_verified === true;
    }

    /**
     * Get basic KYC requirements.
     */
    protected function getBasicKYCRequirements($user): array
    {
        $requirements = [];

        if (!$user->email_verified_at) {
            $requirements[] = [
                'type' => 'email_verification',
                'title' => 'تأكيد البريد الإلكتروني',
                'description' => 'يرجى تأكيد بريدك الإلكتروني',
                'action' => 'verify_email',
            ];
        }

        if (!$user->phone_verified_at) {
            $requirements[] = [
                'type' => 'phone_verification',
                'title' => 'تأكيد رقم الهاتف',
                'description' => 'يرجى تأكيد رقم هاتفك',
                'action' => 'verify_phone',
            ];
        }

        return $requirements;
    }

    /**
     * Get full KYC requirements.
     */
    protected function getFullKYCRequirements($user): array
    {
        $requirements = $this->getBasicKYCRequirements($user);

        $requiredDocuments = ['national_id', 'selfie', 'address_proof'];
        $verifiedDocuments = $user->documents()
                                 ->where('verification_status', 'verified')
                                 ->where('status', 'active')
                                 ->pluck('type')
                                 ->toArray();

        $missingDocuments = array_diff($requiredDocuments, $verifiedDocuments);

        foreach ($missingDocuments as $docType) {
            $requirements[] = [
                'type' => 'document_upload',
                'document_type' => $docType,
                'title' => $this->getDocumentName($docType),
                'description' => "يرجى رفع {$this->getDocumentName($docType)}",
                'action' => 'upload_document',
            ];
        }

        return $requirements;
    }

    /**
     * Get enhanced KYC requirements.
     */
    protected function getEnhancedKYCRequirements($user): array
    {
        $requirements = $this->getFullKYCRequirements($user);

        if (!$user->kyc_verified_at) {
            $requirements[] = [
                'type' => 'manual_verification',
                'title' => 'مراجعة يدوية',
                'description' => 'في انتظار المراجعة اليدوية للمستندات',
                'action' => 'wait_for_review',
            ];
        }

        if (!$user->aml_verified) {
            $requirements[] = [
                'type' => 'aml_verification',
                'title' => 'فحص مكافحة غسل الأموال',
                'description' => 'في انتظار فحص مكافحة غسل الأموال',
                'action' => 'wait_for_aml',
            ];
        }

        return $requirements;
    }

    /**
     * Get document name in Arabic.
     */
    protected function getDocumentName(string $documentType): string
    {
        return match ($documentType) {
            'national_id' => 'الهوية الوطنية',
            'passport' => 'جواز السفر',
            'selfie' => 'صورة شخصية',
            'address_proof' => 'إثبات العنوان',
            'utility_bill' => 'فاتورة الخدمات',
            'bank_statement' => 'كشف حساب بنكي',
            'income_proof' => 'إثبات الدخل',
            'business_license' => 'رخصة تجارية',
            default => 'مستند',
        };
    }
}
