<?php

namespace App\Observers;

use App\Models\User;
use App\Models\AuditLog;
use App\Models\Wallet;
use App\Models\Currency;
use App\Mail\WelcomeEmail;
use App\Events\UserRegistered;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;

class UserObserver
{
    /**
     * Handle the User "creating" event.
     */
    public function creating(User $user): void
    {
        // Generate user ID if not set
        if (!$user->user_id) {
            $user->user_id = $this->generateUserId();
        }

        // Hash password if it's not already hashed
        if ($user->password && !Hash::needsRehash($user->password)) {
            $user->password = Hash::make($user->password);
        }

        // Set default values
        $user->status = $user->status ?? 'active';
        $user->role = $user->role ?? 'user';
        $user->risk_score = $user->risk_score ?? 10.0;
        
        // Set notification preferences to true by default
        $user->email_notifications_enabled = $user->email_notifications_enabled ?? true;
        $user->sms_notifications_enabled = $user->sms_notifications_enabled ?? true;
        $user->security_alerts_enabled = $user->security_alerts_enabled ?? true;

        // Set default limits based on role
        $this->setDefaultLimits($user);
    }

    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // Log user creation
        Log::info('User created', [
            'user_id' => $user->id,
            'email' => $user->email,
            'user_id_code' => $user->user_id,
            'country_id' => $user->country_id,
            'role' => $user->role,
        ]);

        // Create audit log
        AuditLog::create([
            'user_id' => $user->id,
            'event_type' => 'user',
            'action' => 'create',
            'resource_type' => 'user',
            'resource_id' => $user->id,
            'old_values' => null,
            'new_values' => $user->makeHidden(['password'])->toArray(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        // Fire user registered event
        event(new UserRegistered($user));

        // Create default wallets for user's country currency
        $this->createDefaultWallets($user);

        // Send welcome email (queued)
        Mail::to($user->email)->later(now()->addMinutes(2), new WelcomeEmail($user));

        // Log successful registration
        Log::info('User registration completed', [
            'user_id' => $user->id,
            'email' => $user->email,
            'wallets_created' => true,
            'welcome_email_queued' => true,
        ]);
    }

    /**
     * Handle the User "updating" event.
     */
    public function updating(User $user): void
    {
        // Store original values for audit
        $user->_original_values = $user->getOriginal();

        // Hash password if it's being changed and not already hashed
        if ($user->isDirty('password') && $user->password && !Hash::needsRehash($user->password)) {
            $user->password = Hash::make($user->password);
        }

        // Update email verification if email changed
        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        // Update phone verification if phone changed
        if ($user->isDirty('phone')) {
            $user->phone_verified_at = null;
        }

        // Set updated by user
        if (auth()->check() && auth()->id() !== $user->id) {
            $user->updated_by = auth()->id();
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        $changes = $user->getChanges();
        $originalValues = $user->_original_values ?? [];

        // Remove sensitive fields from logging
        unset($changes['password'], $originalValues['password']);

        // Log user update
        Log::info('User updated', [
            'user_id' => $user->id,
            'email' => $user->email,
            'changes' => array_keys($changes),
        ]);

        // Create audit log for significant changes
        if (!empty($changes)) {
            AuditLog::create([
                'user_id' => $user->id,
                'event_type' => 'user',
                'action' => 'update',
                'resource_type' => 'user',
                'resource_id' => $user->id,
                'old_values' => $originalValues,
                'new_values' => $changes,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        }

        // Handle specific field changes
        $this->handleFieldChanges($user, $changes, $originalValues);
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        // Log user deletion
        Log::warning('User deleted', [
            'user_id' => $user->id,
            'email' => $user->email,
            'deleted_by' => auth()->id(),
        ]);

        // Create audit log
        AuditLog::create([
            'user_id' => $user->id,
            'event_type' => 'user',
            'action' => 'delete',
            'resource_type' => 'user',
            'resource_id' => $user->id,
            'old_values' => $user->makeHidden(['password'])->toArray(),
            'new_values' => null,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        // Soft delete related data
        $user->wallets()->delete();
        $user->transactions()->update(['deleted_at' => now()]);
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        // Log user restoration
        Log::info('User restored', [
            'user_id' => $user->id,
            'email' => $user->email,
            'restored_by' => auth()->id(),
        ]);

        // Create audit log
        AuditLog::create([
            'user_id' => $user->id,
            'event_type' => 'user',
            'action' => 'restore',
            'resource_type' => 'user',
            'resource_id' => $user->id,
            'old_values' => null,
            'new_values' => $user->makeHidden(['password'])->toArray(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        // Restore related data
        $user->wallets()->restore();
        $user->transactions()->update(['deleted_at' => null]);
    }

    /**
     * Generate unique user ID.
     */
    private function generateUserId(): string
    {
        do {
            $userId = 'MT' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (User::where('user_id', $userId)->exists());

        return $userId;
    }

    /**
     * Set default limits based on user role.
     */
    private function setDefaultLimits(User $user): void
    {
        $limits = config('financial.limits.user', []);

        switch ($user->role) {
            case 'admin':
                $user->daily_limit = $limits['admin']['daily'] ?? 1000000;
                $user->monthly_limit = $limits['admin']['monthly'] ?? 10000000;
                $user->daily_withdrawal_limit = $limits['admin']['daily_withdrawal'] ?? 500000;
                break;

            case 'manager':
                $user->daily_limit = $limits['manager']['daily'] ?? 500000;
                $user->monthly_limit = $limits['manager']['monthly'] ?? 5000000;
                $user->daily_withdrawal_limit = $limits['manager']['daily_withdrawal'] ?? 250000;
                break;

            case 'premium':
                $user->daily_limit = $limits['premium']['daily'] ?? 100000;
                $user->monthly_limit = $limits['premium']['monthly'] ?? 1000000;
                $user->daily_withdrawal_limit = $limits['premium']['daily_withdrawal'] ?? 50000;
                break;

            default: // regular user
                $user->daily_limit = $limits['user']['daily'] ?? 50000;
                $user->monthly_limit = $limits['user']['monthly'] ?? 500000;
                $user->daily_withdrawal_limit = $limits['user']['daily_withdrawal'] ?? 20000;
                break;
        }
    }

    /**
     * Create default wallets for new user.
     */
    private function createDefaultWallets(User $user): void
    {
        try {
            // Get user's country currency
            $countryCurrency = Currency::where('code', $user->country->currency_code ?? 'USD')
                ->where('is_active', true)
                ->first();

            if ($countryCurrency) {
                Wallet::create([
                    'user_id' => $user->id,
                    'currency_id' => $countryCurrency->id,
                    'balance' => 0.00,
                    'is_active' => true,
                ]);
            }

            // Create USD wallet if different from country currency
            if (!$countryCurrency || $countryCurrency->code !== 'USD') {
                $usdCurrency = Currency::where('code', 'USD')
                    ->where('is_active', true)
                    ->first();

                if ($usdCurrency) {
                    Wallet::create([
                        'user_id' => $user->id,
                        'currency_id' => $usdCurrency->id,
                        'balance' => 0.00,
                        'is_active' => true,
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to create default wallets for user', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle specific field changes.
     */
    private function handleFieldChanges(User $user, array $changes, array $originalValues): void
    {
        // Handle status changes
        if (isset($changes['status'])) {
            $this->handleStatusChange($user, $originalValues['status'] ?? null, $changes['status']);
        }

        // Handle role changes
        if (isset($changes['role'])) {
            $this->handleRoleChange($user, $originalValues['role'] ?? null, $changes['role']);
        }

        // Handle KYC verification
        if (isset($changes['kyc_verified_at']) && $changes['kyc_verified_at']) {
            $this->handleKYCVerification($user);
        }

        // Handle email verification
        if (isset($changes['email_verified_at']) && $changes['email_verified_at']) {
            Log::info('User email verified', [
                'user_id' => $user->id,
                'email' => $user->email,
            ]);
        }

        // Handle phone verification
        if (isset($changes['phone_verified_at']) && $changes['phone_verified_at']) {
            Log::info('User phone verified', [
                'user_id' => $user->id,
                'phone' => $user->phone,
            ]);
        }

        // Handle password changes
        if (isset($changes['password'])) {
            Log::info('User password changed', [
                'user_id' => $user->id,
                'email' => $user->email,
                'changed_by' => auth()->id(),
            ]);
        }
    }

    /**
     * Handle user status changes.
     */
    private function handleStatusChange(User $user, ?string $oldStatus, string $newStatus): void
    {
        Log::info('User status changed', [
            'user_id' => $user->id,
            'email' => $user->email,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'changed_by' => auth()->id(),
        ]);

        // Handle account suspension
        if ($newStatus === 'suspended') {
            // Cancel pending transactions
            $user->sentTransactions()
                ->where('status', 'pending')
                ->update([
                    'status' => 'cancelled',
                    'failure_reason' => 'Account suspended',
                ]);
        }

        // Handle account blocking
        if ($newStatus === 'blocked') {
            // Cancel all pending transactions
            $user->sentTransactions()
                ->whereIn('status', ['pending', 'processing'])
                ->update([
                    'status' => 'cancelled',
                    'failure_reason' => 'Account blocked',
                ]);
        }
    }

    /**
     * Handle user role changes.
     */
    private function handleRoleChange(User $user, ?string $oldRole, string $newRole): void
    {
        Log::info('User role changed', [
            'user_id' => $user->id,
            'email' => $user->email,
            'old_role' => $oldRole,
            'new_role' => $newRole,
            'changed_by' => auth()->id(),
        ]);

        // Update limits based on new role
        $this->setDefaultLimits($user);
        $user->saveQuietly(); // Save without triggering observers again
    }

    /**
     * Handle KYC verification.
     */
    private function handleKYCVerification(User $user): void
    {
        Log::info('User KYC verified', [
            'user_id' => $user->id,
            'email' => $user->email,
            'verified_at' => $user->kyc_verified_at,
        ]);

        // Increase limits for verified users
        $user->daily_limit = ($user->daily_limit ?? 0) * 2;
        $user->monthly_limit = ($user->monthly_limit ?? 0) * 2;
        $user->daily_withdrawal_limit = ($user->daily_withdrawal_limit ?? 0) * 2;
        
        // Reduce risk score
        $user->risk_score = max(5.0, ($user->risk_score ?? 10.0) - 5.0);
        
        $user->saveQuietly(); // Save without triggering observers again
    }
}
