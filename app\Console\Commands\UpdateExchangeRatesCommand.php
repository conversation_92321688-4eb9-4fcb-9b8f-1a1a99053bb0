<?php

namespace App\Console\Commands;

use App\Jobs\UpdateExchangeRatesJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateExchangeRatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'financial:update-rates 
                            {--source=external_api : The source to fetch rates from}
                            {--currencies=* : Specific currencies to update}
                            {--force : Force update even if recently updated}';

    /**
     * The console command description.
     */
    protected $description = 'Update exchange rates from external sources';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $source = $this->option('source');
        $currencies = $this->option('currencies');
        $force = $this->option('force');

        $this->info('Starting exchange rates update...');
        $this->info("Source: {$source}");
        
        if (!empty($currencies)) {
            $this->info('Currencies: ' . implode(', ', $currencies));
        } else {
            $this->info('Updating all active currencies');
        }

        try {
            // Check if we should skip update (unless forced)
            if (!$force && $this->shouldSkipUpdate()) {
                $this->warn('Exchange rates were updated recently. Use --force to override.');
                return self::SUCCESS;
            }

            // Dispatch the job
            UpdateExchangeRatesJob::dispatch($currencies, $source);

            $this->info('Exchange rates update job dispatched successfully!');
            
            // If running synchronously (for testing)
            if (config('queue.default') === 'sync') {
                $this->info('Running synchronously...');
                $this->newLine();
                
                // Show progress
                $bar = $this->output->createProgressBar(100);
                $bar->start();
                
                for ($i = 0; $i < 100; $i++) {
                    usleep(50000); // 50ms delay
                    $bar->advance();
                }
                
                $bar->finish();
                $this->newLine(2);
                $this->info('Exchange rates updated successfully!');
            } else {
                $this->info('Job queued for background processing.');
            }

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to update exchange rates: ' . $e->getMessage());
            Log::error('Exchange rates command failed', [
                'error' => $e->getMessage(),
                'source' => $source,
                'currencies' => $currencies,
            ]);
            
            return self::FAILURE;
        }
    }

    /**
     * Check if we should skip the update.
     */
    private function shouldSkipUpdate(): bool
    {
        $lastUpdate = cache('exchange_rates_last_update');
        
        if (!$lastUpdate) {
            return false;
        }

        $updateInterval = config('financial.exchange_rates.update_interval', 300); // 5 minutes default
        
        return now()->diffInSeconds($lastUpdate) < $updateInterval;
    }
}
