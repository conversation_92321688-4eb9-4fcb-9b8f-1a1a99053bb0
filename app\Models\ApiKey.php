<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class ApiKey extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'key_hash',
        'key_prefix',
        'permissions',
        'ip_whitelist',
        'rate_limit_per_minute',
        'rate_limit_per_hour',
        'rate_limit_per_day',
        'is_active',
        'last_used_at',
        'expires_at',
        'usage_count',
        'metadata',
    ];

    protected $casts = [
        'permissions' => 'array',
        'ip_whitelist' => 'array',
        'is_active' => 'boolean',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
        'metadata' => 'array',
    ];

    protected $hidden = [
        'key_hash',
    ];

    /**
     * Get the user that owns the API key
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get active API keys
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>', now());
            });
    }

    /**
     * Scope to get expired API keys
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to find by key prefix
     */
    public function scopeByPrefix(Builder $query, string $prefix): Builder
    {
        return $query->where('key_prefix', $prefix);
    }

    /**
     * Generate a new API key
     */
    public static function generate(
        int $userId,
        string $name,
        array $permissions = [],
        array $options = []
    ): array {
        // Generate random API key
        $apiKey = 'mt_' . Str::random(32);
        $keyHash = Hash::make($apiKey);
        $keyPrefix = substr($apiKey, 0, 8);

        $apiKeyModel = self::create([
            'user_id' => $userId,
            'name' => $name,
            'key_hash' => $keyHash,
            'key_prefix' => $keyPrefix,
            'permissions' => $permissions,
            'ip_whitelist' => $options['ip_whitelist'] ?? null,
            'rate_limit_per_minute' => $options['rate_limit_per_minute'] ?? 60,
            'rate_limit_per_hour' => $options['rate_limit_per_hour'] ?? 1000,
            'rate_limit_per_day' => $options['rate_limit_per_day'] ?? 10000,
            'is_active' => true,
            'expires_at' => $options['expires_at'] ?? null,
            'metadata' => $options['metadata'] ?? [],
        ]);

        return [
            'api_key' => $apiKey,
            'model' => $apiKeyModel,
        ];
    }

    /**
     * Verify API key
     */
    public static function verify(string $apiKey): ?self
    {
        $keyPrefix = substr($apiKey, 0, 8);
        
        $apiKeyModel = self::active()
            ->byPrefix($keyPrefix)
            ->first();

        if (!$apiKeyModel || !Hash::check($apiKey, $apiKeyModel->key_hash)) {
            return null;
        }

        return $apiKeyModel;
    }

    /**
     * Record API key usage
     */
    public function recordUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Check if API key has permission
     */
    public function hasPermission(string $permission): bool
    {
        if (empty($this->permissions)) {
            return false;
        }

        return in_array($permission, $this->permissions) || in_array('*', $this->permissions);
    }

    /**
     * Check if IP is whitelisted
     */
    public function isIpAllowed(string $ip): bool
    {
        if (empty($this->ip_whitelist)) {
            return true; // No whitelist means all IPs allowed
        }

        return in_array($ip, $this->ip_whitelist);
    }

    /**
     * Check if API key is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Revoke API key
     */
    public function revoke(): void
    {
        $this->update([
            'is_active' => false,
            'expires_at' => now(),
        ]);
    }

    /**
     * Regenerate API key
     */
    public function regenerate(): string
    {
        $newApiKey = 'mt_' . Str::random(32);
        $keyHash = Hash::make($newApiKey);
        $keyPrefix = substr($newApiKey, 0, 8);

        $this->update([
            'key_hash' => $keyHash,
            'key_prefix' => $keyPrefix,
            'usage_count' => 0,
            'last_used_at' => null,
        ]);

        return $newApiKey;
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats(int $days = 30): array
    {
        // This would typically query a separate API usage log table
        // For now, return basic stats
        return [
            'total_requests' => $this->usage_count,
            'last_used' => $this->last_used_at,
            'created_at' => $this->created_at,
            'days_active' => $this->created_at->diffInDays(now()),
            'avg_requests_per_day' => $this->usage_count / max(1, $this->created_at->diffInDays(now())),
        ];
    }

    /**
     * Get rate limit status
     */
    public function getRateLimitStatus(): array
    {
        $now = now();
        
        // Get usage from rate_limits table
        $minuteUsage = RateLimit::where('identifier', $this->key_prefix)
            ->where('type', 'api_key')
            ->where('endpoint', 'per_minute')
            ->where('window_start', '>=', $now->subMinute())
            ->sum('attempts');

        $hourUsage = RateLimit::where('identifier', $this->key_prefix)
            ->where('type', 'api_key')
            ->where('endpoint', 'per_hour')
            ->where('window_start', '>=', $now->subHour())
            ->sum('attempts');

        $dayUsage = RateLimit::where('identifier', $this->key_prefix)
            ->where('type', 'api_key')
            ->where('endpoint', 'per_day')
            ->where('window_start', '>=', $now->subDay())
            ->sum('attempts');

        return [
            'per_minute' => [
                'used' => $minuteUsage,
                'limit' => $this->rate_limit_per_minute,
                'remaining' => max(0, $this->rate_limit_per_minute - $minuteUsage),
                'reset_at' => $now->copy()->addMinute()->startOfMinute(),
            ],
            'per_hour' => [
                'used' => $hourUsage,
                'limit' => $this->rate_limit_per_hour,
                'remaining' => max(0, $this->rate_limit_per_hour - $hourUsage),
                'reset_at' => $now->copy()->addHour()->startOfHour(),
            ],
            'per_day' => [
                'used' => $dayUsage,
                'limit' => $this->rate_limit_per_day,
                'remaining' => max(0, $this->rate_limit_per_day - $dayUsage),
                'reset_at' => $now->copy()->addDay()->startOfDay(),
            ],
        ];
    }

    /**
     * Clean expired API keys
     */
    public static function cleanExpired(): int
    {
        return self::expired()->delete();
    }

    /**
     * Get API key statistics for user
     */
    public static function getUserStats(int $userId): array
    {
        $apiKeys = self::where('user_id', $userId)->get();
        
        return [
            'total_keys' => $apiKeys->count(),
            'active_keys' => $apiKeys->where('is_active', true)->count(),
            'expired_keys' => $apiKeys->filter->isExpired()->count(),
            'total_usage' => $apiKeys->sum('usage_count'),
            'last_used' => $apiKeys->max('last_used_at'),
        ];
    }
}
