<?php

// Test login script
$url = 'http://localhost:8000/api/v1/auth/login';
$data = [
    'email' => '<EMAIL>',
    'password' => 'password'
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Error: Could not connect to server\n";
    echo "Make sure the server is running: php artisan serve\n";
} else {
    echo "Login Response:\n";
    echo $result . "\n";
    
    $response = json_decode($result, true);
    if (isset($response['success']) && $response['success']) {
        echo "\n✅ Login successful!\n";
        if (isset($response['data']['token'])) {
            echo "🔑 Token: " . substr($response['data']['token'], 0, 50) . "...\n";
        }
        if (isset($response['data']['user'])) {
            $user = $response['data']['user'];
            echo "👤 User: " . $user['first_name'] . " " . $user['last_name'] . "\n";
            echo "📧 Email: " . $user['email'] . "\n";
            echo "🏷️ Type: " . $user['user_type'] . "\n";
        }
    } else {
        echo "\n❌ Login failed!\n";
        if (isset($response['message'])) {
            echo "Error: " . $response['message'] . "\n";
        }
    }
}
