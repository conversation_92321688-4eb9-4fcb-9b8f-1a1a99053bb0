<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    protected $model = User::class;

    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => fake()->optional(0.8)->dateTimeBetween('-1 year', 'now'),
            'phone' => fake()->phoneNumber(),
            'phone_verified_at' => fake()->optional(0.7)->dateTimeBetween('-1 year', 'now'),
            'password' => static::$password ??= Hash::make('password'),
            'user_type' => fake()->randomElement(['admin', 'agent', 'customer', 'branch_manager']),
            'status' => fake()->randomElement(['active', 'inactive', 'suspended', 'pending_verification']),
            'country_id' => Country::factory(),
            'branch_id' => null,
            'national_id' => fake()->optional()->numerify('##########'),
            'date_of_birth' => fake()->optional()->dateTimeBetween('-80 years', '-18 years'),
            'gender' => fake()->optional()->randomElement(['male', 'female']),
            'address' => fake()->optional()->address(),
            'city' => fake()->optional()->city(),
            'postal_code' => fake()->optional()->postcode(),
            'avatar' => null,
            'preferred_language' => fake()->randomElement(['ar', 'en']),
            'preferred_currency' => fake()->randomElement(['USD', 'SAR', 'AED', 'GBP', 'EUR']),
            'two_factor_enabled' => fake()->boolean(20), // 20% chance
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
            'biometric_enabled' => fake()->boolean(30), // 30% chance
            'biometric_data' => null,
            'last_login_at' => fake()->optional(0.8)->dateTimeBetween('-30 days', 'now'),
            'last_login_ip' => fake()->optional()->ipv4(),
            'login_history' => null,
            'daily_limit' => fake()->randomFloat(2, 1000, 50000),
            'monthly_limit' => fake()->randomFloat(2, 10000, 500000),
            'risk_level' => fake()->randomElement(['low', 'medium', 'high']),
            'kyc_documents' => null,
            'kyc_verified_at' => fake()->optional(0.6)->dateTimeBetween('-1 year', 'now'),
            'aml_verified' => fake()->boolean(70), // 70% chance
            'aml_verified_at' => fake()->optional(0.7)->dateTimeBetween('-1 year', 'now'),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
            'phone_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'aml_verified' => true,
            'aml_verified_at' => now(),
            'kyc_verified_at' => now(),
            'risk_level' => 'low',
        ]);
    }

    /**
     * Indicate that the user is an agent.
     */
    public function agent(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'agent',
            'status' => 'active',
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'aml_verified' => true,
            'aml_verified_at' => now(),
            'kyc_verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user is a customer.
     */
    public function customer(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'customer',
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the user is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
            'kyc_verified_at' => now(),
            'aml_verified' => true,
            'aml_verified_at' => now(),
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the user is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'suspended',
        ]);
    }
}
