<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'activity_type',
        'description',
        'metadata',
        'ip_address',
        'user_agent',
        'severity',
        'session_id',
        'device_fingerprint',
        'location',
    ];

    protected $casts = [
        'metadata' => 'array',
        'location' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'severity' => 'info',
    ];

    /**
     * Get the user that owns the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for activities by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Scope for activities by severity.
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope for activities by user.
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for activities in date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent activities.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for critical activities.
     */
    public function scopeCritical($query)
    {
        return $query->where('severity', 'critical');
    }

    /**
     * Scope for suspicious activities.
     */
    public function scopeSuspicious($query)
    {
        return $query->whereIn('activity_type', [
            'failed_login',
            'fraud_alert',
            'suspicious_transaction',
            'multiple_failed_attempts',
            'unusual_location',
        ]);
    }

    /**
     * Get formatted timestamp.
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->created_at->format('Y-m-d H:i:s');
    }

    /**
     * Get relative time.
     */
    public function getRelativeTimeAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get severity color for UI.
     */
    public function getSeverityColorAttribute(): string
    {
        return match($this->severity) {
            'critical' => 'red',
            'error' => 'red',
            'warning' => 'yellow',
            'info' => 'blue',
            'debug' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Get activity icon.
     */
    public function getActivityIconAttribute(): string
    {
        return match($this->activity_type) {
            'login' => '🔐',
            'logout' => '🚪',
            'transaction_created' => '💸',
            'transaction_completed' => '✅',
            'transaction_failed' => '❌',
            'fraud_alert' => '🚨',
            'kyc_submitted' => '📋',
            'kyc_approved' => '✅',
            'kyc_rejected' => '❌',
            'password_changed' => '🔑',
            'profile_updated' => '👤',
            'failed_login' => '🔒',
            'suspicious_activity' => '⚠️',
            default => '📝'
        };
    }

    /**
     * Get browser name from user agent.
     */
    public function getBrowserAttribute(): string
    {
        if (!$this->user_agent) {
            return 'Unknown';
        }

        $userAgent = $this->user_agent;

        if (str_contains($userAgent, 'Chrome')) {
            return 'Chrome';
        } elseif (str_contains($userAgent, 'Firefox')) {
            return 'Firefox';
        } elseif (str_contains($userAgent, 'Safari')) {
            return 'Safari';
        } elseif (str_contains($userAgent, 'Edge')) {
            return 'Edge';
        } elseif (str_contains($userAgent, 'Opera')) {
            return 'Opera';
        }

        return 'Unknown';
    }

    /**
     * Get operating system from user agent.
     */
    public function getOperatingSystemAttribute(): string
    {
        if (!$this->user_agent) {
            return 'Unknown';
        }

        $userAgent = $this->user_agent;

        if (str_contains($userAgent, 'Windows')) {
            return 'Windows';
        } elseif (str_contains($userAgent, 'Mac')) {
            return 'macOS';
        } elseif (str_contains($userAgent, 'Linux')) {
            return 'Linux';
        } elseif (str_contains($userAgent, 'Android')) {
            return 'Android';
        } elseif (str_contains($userAgent, 'iOS')) {
            return 'iOS';
        }

        return 'Unknown';
    }

    /**
     * Check if activity is suspicious.
     */
    public function isSuspicious(): bool
    {
        return in_array($this->activity_type, [
            'failed_login',
            'fraud_alert',
            'suspicious_transaction',
            'multiple_failed_attempts',
            'unusual_location',
        ]) || $this->severity === 'critical';
    }

    /**
     * Log user activity.
     */
    public static function log(
        int $userId,
        string $activityType,
        string $description,
        array $metadata = [],
        string $severity = 'info'
    ): self {
        return static::create([
            'user_id' => $userId,
            'activity_type' => $activityType,
            'description' => $description,
            'metadata' => $metadata,
            'severity' => $severity,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
        ]);
    }

    /**
     * Get activity statistics for user.
     */
    public static function getStatisticsForUser(int $userId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'total_activities' => static::byUser($userId)->where('created_at', '>=', $startDate)->count(),
            'login_count' => static::byUser($userId)->byType('login')->where('created_at', '>=', $startDate)->count(),
            'transaction_count' => static::byUser($userId)->byType('transaction_created')->where('created_at', '>=', $startDate)->count(),
            'failed_login_count' => static::byUser($userId)->byType('failed_login')->where('created_at', '>=', $startDate)->count(),
            'suspicious_count' => static::byUser($userId)->suspicious()->where('created_at', '>=', $startDate)->count(),
            'last_login' => static::byUser($userId)->byType('login')->latest()->first()?->created_at,
            'last_activity' => static::byUser($userId)->latest()->first()?->created_at,
        ];
    }

    /**
     * Get recent suspicious activities.
     */
    public static function getRecentSuspicious(int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return static::suspicious()
            ->with('user')
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Clean old activities.
     */
    public static function cleanOldActivities(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return static::where('created_at', '<', $cutoffDate)
            ->where('severity', '!=', 'critical')
            ->delete();
    }
}
