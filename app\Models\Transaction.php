<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_number',
        'reference_number',
        'type',
        'category',
        'sender_id',
        'sender_wallet_id',
        'sender_name',
        'sender_phone',
        'sender_national_id',
        'receiver_id',
        'receiver_wallet_id',
        'receiver_name',
        'receiver_phone',
        'receiver_national_id',
        'receiver_bank_account',
        'receiver_bank_name',
        'receiver_bank_code',
        'currency_id',
        'amount',
        'exchange_rate',
        'target_currency_id',
        'target_amount',
        'commission_amount',
        'commission_rate',
        'additional_fees',
        'total_fees',
        'net_amount',
        'status',
        'payment_method',
        'payment_gateway',
        'gateway_transaction_id',
        'blockchain_hash',
        'blockchain_confirmations',
        'branch_id',
        'agent_id',
        'processed_by',
        'sender_country_id',
        'receiver_country_id',
        'initiated_at',
        'processed_at',
        'completed_at',
        'expires_at',
        'purpose',
        'notes',
        'metadata',
        'receipt_url',
        'risk_score',
        'requires_kyc',
        'aml_checked',
        'is_suspicious',
        'compliance_notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:8',
        'target_amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'commission_rate' => 'decimal:4',
        'additional_fees' => 'decimal:2',
        'total_fees' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'blockchain_confirmations' => 'integer',
        'initiated_at' => 'datetime',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
        'metadata' => 'array',
        'requires_kyc' => 'boolean',
        'aml_checked' => 'boolean',
        'is_suspicious' => 'boolean',
    ];

    /**
     * Get the sender user.
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the receiver user.
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    /**
     * Get the sender wallet.
     */
    public function senderWallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class, 'sender_wallet_id');
    }

    /**
     * Get the receiver wallet.
     */
    public function receiverWallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class, 'receiver_wallet_id');
    }

    /**
     * Get the currency.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Get the target currency.
     */
    public function targetCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'target_currency_id');
    }

    /**
     * Get the branch.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the agent.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    /**
     * Get the processor.
     */
    public function processor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Get the sender country.
     */
    public function senderCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'sender_country_id');
    }

    /**
     * Get the receiver country.
     */
    public function receiverCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'receiver_country_id');
    }

    /**
     * Get the blockchain transaction.
     */
    public function blockchainTransaction(): HasOne
    {
        return $this->hasOne(BlockchainTransaction::class);
    }

    /**
     * Get the fraud detection alerts.
     */
    public function fraudAlerts(): HasMany
    {
        return $this->hasMany(FraudDetection::class);
    }

    /**
     * Generate unique transaction number.
     */
    public static function generateTransactionNumber(): string
    {
        do {
            $number = 'TXN' . date('Ymd') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('transaction_number', $number)->exists());

        return $number;
    }

    /**
     * Generate unique reference number.
     */
    public static function generateReferenceNumber(): string
    {
        do {
            $number = 'REF' . date('Ymd') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('reference_number', $number)->exists());

        return $number;
    }

    /**
     * Check if transaction is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if transaction is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if transaction is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if transaction is crypto.
     */
    public function isCrypto(): bool
    {
        return $this->category === 'crypto';
    }

    /**
     * Check if transaction is international.
     */
    public function isInternational(): bool
    {
        return $this->category === 'international';
    }

    /**
     * Scope for pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for completed transactions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for suspicious transactions.
     */
    public function scopeSuspicious($query)
    {
        return $query->where('is_suspicious', true);
    }

    /**
     * Scope for high risk transactions.
     */
    public function scopeHighRisk($query)
    {
        return $query->where('risk_score', 'high');
    }
}
