<?php

echo "🧪 تشغيل الاختبارات الشاملة - Running Comprehensive Tests\n";
echo "=========================================================\n\n";

// Test configuration
$testSuites = [
    'Unit' => [
        'description' => 'Unit Tests - اختبارات الوحدة',
        'command' => 'vendor/bin/phpunit --testsuite=Unit --coverage-text',
        'timeout' => 120,
    ],
    'PaymentGateways' => [
        'description' => 'Payment Gateway Tests - اختبارات بوابات الدفع',
        'command' => 'vendor/bin/phpunit --testsuite=PaymentGateways --coverage-text',
        'timeout' => 180,
    ],
    'Security' => [
        'description' => 'Security Tests - اختبارات الأمان',
        'command' => 'vendor/bin/phpunit --testsuite=Security --coverage-text',
        'timeout' => 150,
    ],
    'Integration' => [
        'description' => 'Integration Tests - اختبارات التكامل',
        'command' => 'vendor/bin/phpunit --testsuite=Integration --coverage-text',
        'timeout' => 300,
    ],
    'Feature' => [
        'description' => 'Feature Tests - اختبارات الميزات',
        'command' => 'vendor/bin/phpunit --testsuite=Feature --coverage-text',
        'timeout' => 240,
    ],
];

$results = [];
$totalTests = 0;
$totalPassed = 0;
$totalFailed = 0;
$totalTime = 0;

echo "🚀 بدء تشغيل الاختبارات...\n";
echo "========================\n\n";

foreach ($testSuites as $suiteName => $config) {
    echo "📋 تشغيل: {$config['description']}\n";
    echo str_repeat('-', 50) . "\n";
    
    $startTime = microtime(true);
    
    // Run the test suite
    $output = [];
    $returnCode = 0;
    
    exec($config['command'] . ' 2>&1', $output, $returnCode);
    
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    $totalTime += $executionTime;
    
    // Parse results
    $suiteResults = parseTestOutput($output);
    $results[$suiteName] = array_merge($suiteResults, [
        'execution_time' => $executionTime,
        'return_code' => $returnCode,
        'success' => $returnCode === 0,
    ]);
    
    $totalTests += $suiteResults['tests'];
    $totalPassed += $suiteResults['passed'];
    $totalFailed += $suiteResults['failed'];
    
    // Display results
    if ($returnCode === 0) {
        echo "✅ نجح: {$suiteResults['tests']} اختبار في {$executionTime}s\n";
    } else {
        echo "❌ فشل: {$suiteResults['failed']} من {$suiteResults['tests']} اختبار في {$executionTime}s\n";
        
        // Show failed test details
        if (!empty($suiteResults['failures'])) {
            echo "   الاختبارات الفاشلة:\n";
            foreach ($suiteResults['failures'] as $failure) {
                echo "   - {$failure}\n";
            }
        }
    }
    
    echo "\n";
}

echo "📊 ملخص النتائج النهائية\n";
echo "========================\n";
echo "إجمالي الاختبارات: {$totalTests}\n";
echo "نجح: {$totalPassed} ✅\n";
echo "فشل: {$totalFailed} ❌\n";
echo "معدل النجاح: " . round(($totalPassed / max($totalTests, 1)) * 100, 2) . "%\n";
echo "إجمالي الوقت: {$totalTime}s\n\n";

// Detailed results by suite
echo "📋 تفاصيل النتائج حسب المجموعة\n";
echo "===============================\n";
foreach ($results as $suiteName => $result) {
    $status = $result['success'] ? '✅' : '❌';
    $coverage = isset($result['coverage']) ? " (تغطية: {$result['coverage']}%)" : '';
    
    echo "{$status} {$suiteName}: {$result['passed']}/{$result['tests']} في {$result['execution_time']}s{$coverage}\n";
}

echo "\n";

// Generate test report
generateTestReport($results, $totalTests, $totalPassed, $totalFailed, $totalTime);

// Check if all tests passed
if ($totalFailed === 0) {
    echo "🎉 جميع الاختبارات نجحت! النظام جاهز للإنتاج.\n";
    exit(0);
} else {
    echo "⚠️  يوجد {$totalFailed} اختبار فاشل. يرجى مراجعة الأخطاء.\n";
    exit(1);
}

/**
 * Parse test output to extract results
 */
function parseTestOutput(array $output): array
{
    $results = [
        'tests' => 0,
        'passed' => 0,
        'failed' => 0,
        'skipped' => 0,
        'coverage' => null,
        'failures' => [],
    ];
    
    $outputText = implode("\n", $output);
    
    // Parse test counts
    if (preg_match('/Tests: (\d+), Assertions: \d+/', $outputText, $matches)) {
        $results['tests'] = (int) $matches[1];
    }
    
    if (preg_match('/OK \((\d+) tests?, \d+ assertions?\)/', $outputText, $matches)) {
        $results['passed'] = (int) $matches[1];
        $results['tests'] = $results['passed'];
    }
    
    if (preg_match('/FAILURES!\s*Tests: (\d+), Assertions: \d+, Failures: (\d+)/', $outputText, $matches)) {
        $results['tests'] = (int) $matches[1];
        $results['failed'] = (int) $matches[2];
        $results['passed'] = $results['tests'] - $results['failed'];
    }
    
    // Parse coverage
    if (preg_match('/Lines:\s+[\d.]+%\s+\((\d+)\/\d+\)/', $outputText, $matches)) {
        $results['coverage'] = $matches[1];
    }
    
    // Parse failures
    if (preg_match_all('/FAIL\s+(.+?)(?=\n\n|\nOK|\nFAILURES|$)/s', $outputText, $matches)) {
        foreach ($matches[1] as $failure) {
            $lines = explode("\n", trim($failure));
            if (!empty($lines[0])) {
                $results['failures'][] = trim($lines[0]);
            }
        }
    }
    
    return $results;
}

/**
 * Generate detailed test report
 */
function generateTestReport(array $results, int $totalTests, int $totalPassed, int $totalFailed, float $totalTime): void
{
    $reportFile = 'test_report_' . date('Y-m-d_H-i-s') . '.html';
    
    $html = "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تقرير الاختبارات - Money Transfer System</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-card.success { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
        .stat-card.danger { background: linear-gradient(135deg, #f44336 0%, #da190b 100%); }
        .stat-card.info { background: linear-gradient(135deg, #2196F3 0%, #0b7dda 100%); }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        .suite-results { margin-bottom: 30px; }
        .suite-card { border: 1px solid #ddd; border-radius: 8px; margin-bottom: 15px; overflow: hidden; }
        .suite-header { padding: 15px; background: #f8f9fa; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center; }
        .suite-body { padding: 15px; }
        .success-badge { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
        .danger-badge { background: #dc3545; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
        .failure-list { background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 10px; margin-top: 10px; }
        .failure-item { margin: 5px 0; font-family: monospace; font-size: 0.9em; }
        .timestamp { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🧪 تقرير الاختبارات الشامل</h1>
            <p class='timestamp'>تم إنشاؤه في: " . date('Y-m-d H:i:s') . "</p>
        </div>
        
        <div class='summary'>
            <div class='stat-card info'>
                <div class='stat-number'>{$totalTests}</div>
                <div class='stat-label'>إجمالي الاختبارات</div>
            </div>
            <div class='stat-card success'>
                <div class='stat-number'>{$totalPassed}</div>
                <div class='stat-label'>نجح</div>
            </div>
            <div class='stat-card danger'>
                <div class='stat-number'>{$totalFailed}</div>
                <div class='stat-label'>فشل</div>
            </div>
            <div class='stat-card'>
                <div class='stat-number'>" . round($totalTime, 1) . "s</div>
                <div class='stat-label'>إجمالي الوقت</div>
            </div>
        </div>
        
        <div class='suite-results'>
            <h2>نتائج مجموعات الاختبارات</h2>";
    
    foreach ($results as $suiteName => $result) {
        $successRate = $result['tests'] > 0 ? round(($result['passed'] / $result['tests']) * 100, 1) : 0;
        $badgeClass = $result['success'] ? 'success-badge' : 'danger-badge';
        $badgeText = $result['success'] ? 'نجح' : 'فشل';
        
        $html .= "
            <div class='suite-card'>
                <div class='suite-header'>
                    <h3>{$suiteName}</h3>
                    <span class='{$badgeClass}'>{$badgeText}</span>
                </div>
                <div class='suite-body'>
                    <div class='progress-bar'>
                        <div class='progress-fill' style='width: {$successRate}%'></div>
                    </div>
                    <p>الاختبارات: {$result['passed']}/{$result['tests']} ({$successRate}%)</p>
                    <p>وقت التنفيذ: {$result['execution_time']}s</p>";
        
        if (!empty($result['failures'])) {
            $html .= "<div class='failure-list'>
                        <strong>الاختبارات الفاشلة:</strong>";
            foreach ($result['failures'] as $failure) {
                $html .= "<div class='failure-item'>• " . htmlspecialchars($failure) . "</div>";
            }
            $html .= "</div>";
        }
        
        $html .= "</div></div>";
    }
    
    $overallSuccessRate = $totalTests > 0 ? round(($totalPassed / $totalTests) * 100, 1) : 0;
    
    $html .= "
        </div>
        
        <div class='summary'>
            <h2>الملخص النهائي</h2>
            <div class='progress-bar'>
                <div class='progress-fill' style='width: {$overallSuccessRate}%'></div>
            </div>
            <p style='text-align: center; font-size: 1.2em; margin-top: 15px;'>
                معدل النجاح الإجمالي: <strong>{$overallSuccessRate}%</strong>
            </p>
        </div>
    </div>
</body>
</html>";
    
    file_put_contents($reportFile, $html);
    echo "📄 تم إنشاء تقرير مفصل: {$reportFile}\n";
}
