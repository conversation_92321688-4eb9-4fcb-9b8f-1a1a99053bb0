<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\FraudDetectionService;
use App\Models\User;
use App\Models\Transaction;
use App\Models\Currency;
use App\Models\Country;
use App\Models\FraudDetection;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FraudDetectionTest extends TestCase
{
    use RefreshDatabase;

    protected $fraudDetectionService;
    protected $user;
    protected $currency;
    protected $country;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->fraudDetectionService = new FraudDetectionService();
        
        // Create test data
        $this->country = Country::factory()->create([
            'name' => 'Saudi Arabia',
            'code' => 'SA',
            'currency_code' => 'SAR',
        ]);

        $this->currency = Currency::factory()->create([
            'name' => 'Saudi Riyal',
            'code' => 'SAR',
            'symbol' => 'ر.س',
            'rate_to_usd' => 0.27,
            'is_active' => true,
        ]);

        $this->user = User::factory()->create([
            'country_id' => $this->country->id,
            'status' => 'active',
            'kyc_verified_at' => now(),
            'created_at' => now()->subDays(30), // 30 days old account
        ]);
    }

    /** @test */
    public function it_calculates_basic_risk_score_correctly()
    {
        $transaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $riskScore = $this->fraudDetectionService->calculateRiskScore($transaction);

        $this->assertIsFloat($riskScore);
        $this->assertGreaterThanOrEqual(0, $riskScore);
        $this->assertLessThanOrEqual(100, $riskScore);
    }

    /** @test */
    public function high_amount_transaction_increases_risk_score()
    {
        $normalTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $highAmountTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 50000.00, // High amount
            'type' => 'transfer',
        ]);

        $normalRisk = $this->fraudDetectionService->calculateRiskScore($normalTransaction);
        $highRisk = $this->fraudDetectionService->calculateRiskScore($highAmountTransaction);

        $this->assertGreaterThan($normalRisk, $highRisk);
    }

    /** @test */
    public function new_user_account_increases_risk_score()
    {
        $newUser = User::factory()->create([
            'country_id' => $this->country->id,
            'status' => 'active',
            'kyc_verified_at' => now(),
            'created_at' => now()->subDays(5), // 5 days old account
        ]);

        $oldUserTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id, // 30 days old
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $newUserTransaction = Transaction::factory()->make([
            'sender_id' => $newUser->id, // 5 days old
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $oldUserRisk = $this->fraudDetectionService->calculateRiskScore($oldUserTransaction);
        $newUserRisk = $this->fraudDetectionService->calculateRiskScore($newUserTransaction);

        $this->assertGreaterThan($oldUserRisk, $newUserRisk);
    }

    /** @test */
    public function unverified_user_increases_risk_score()
    {
        $unverifiedUser = User::factory()->create([
            'country_id' => $this->country->id,
            'status' => 'active',
            'kyc_verified_at' => null, // Not verified
            'created_at' => now()->subDays(30),
        ]);

        $verifiedTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id, // Verified
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $unverifiedTransaction = Transaction::factory()->make([
            'sender_id' => $unverifiedUser->id, // Not verified
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $verifiedRisk = $this->fraudDetectionService->calculateRiskScore($verifiedTransaction);
        $unverifiedRisk = $this->fraudDetectionService->calculateRiskScore($unverifiedTransaction);

        $this->assertGreaterThan($verifiedRisk, $unverifiedRisk);
    }

    /** @test */
    public function it_detects_velocity_fraud_pattern()
    {
        // Create multiple recent transactions
        Transaction::factory()->count(5)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'created_at' => now()->subMinutes(30),
            'amount' => 1000.00,
        ]);

        $newTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $isVelocityFraud = $this->fraudDetectionService->checkVelocityFraud($newTransaction);

        $this->assertTrue($isVelocityFraud);
    }

    /** @test */
    public function it_detects_unusual_time_pattern()
    {
        // Set current time to 3 AM (unusual hour)
        $this->travelTo(now()->setTime(3, 0, 0));

        $transaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $isUnusualTime = $this->fraudDetectionService->checkUnusualTimePattern($transaction);

        $this->assertTrue($isUnusualTime);
    }

    /** @test */
    public function it_detects_round_number_pattern()
    {
        $roundAmountTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 10000.00, // Round number
            'type' => 'transfer',
        ]);

        $normalAmountTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1234.56, // Not round
            'type' => 'transfer',
        ]);

        $roundRisk = $this->fraudDetectionService->calculateRiskScore($roundAmountTransaction);
        $normalRisk = $this->fraudDetectionService->calculateRiskScore($normalAmountTransaction);

        $this->assertGreaterThan($normalRisk, $roundRisk);
    }

    /** @test */
    public function it_determines_correct_risk_level()
    {
        // Low risk
        $lowRiskScore = 15.0;
        $lowRiskLevel = $this->fraudDetectionService->determineRiskLevel($lowRiskScore);
        $this->assertEquals('low', $lowRiskLevel);

        // Medium risk
        $mediumRiskScore = 45.0;
        $mediumRiskLevel = $this->fraudDetectionService->determineRiskLevel($mediumRiskScore);
        $this->assertEquals('medium', $mediumRiskLevel);

        // High risk
        $highRiskScore = 75.0;
        $highRiskLevel = $this->fraudDetectionService->determineRiskLevel($highRiskScore);
        $this->assertEquals('high', $highRiskLevel);

        // Critical risk
        $criticalRiskScore = 95.0;
        $criticalRiskLevel = $this->fraudDetectionService->determineRiskLevel($criticalRiskScore);
        $this->assertEquals('critical', $criticalRiskLevel);
    }

    /** @test */
    public function it_analyzes_transaction_and_creates_fraud_detection_record()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 50000.00, // High amount to trigger detection
            'type' => 'transfer',
        ]);

        $result = $this->fraudDetectionService->analyzeTransaction($transaction);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('risk_score', $result);
        $this->assertArrayHasKey('risk_level', $result);
        $this->assertArrayHasKey('fraud_indicators', $result);

        // Check if fraud detection record was created
        $this->assertDatabaseHas('fraud_detections', [
            'user_id' => $this->user->id,
            'transaction_id' => $transaction->id,
        ]);
    }

    /** @test */
    public function it_handles_machine_learning_prediction()
    {
        $transaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $features = $this->fraudDetectionService->extractFeatures($transaction);
        $prediction = $this->fraudDetectionService->getMachineLearningPrediction($features);

        $this->assertIsArray($prediction);
        $this->assertArrayHasKey('is_fraud', $prediction);
        $this->assertArrayHasKey('confidence', $prediction);
        $this->assertIsBool($prediction['is_fraud']);
        $this->assertIsFloat($prediction['confidence']);
        $this->assertGreaterThanOrEqual(0, $prediction['confidence']);
        $this->assertLessThanOrEqual(1, $prediction['confidence']);
    }

    /** @test */
    public function it_extracts_correct_features_from_transaction()
    {
        $transaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000.00,
            'type' => 'transfer',
        ]);

        $features = $this->fraudDetectionService->extractFeatures($transaction);

        $this->assertIsArray($features);
        $this->assertArrayHasKey('amount', $features);
        $this->assertArrayHasKey('hour_of_day', $features);
        $this->assertArrayHasKey('day_of_week', $features);
        $this->assertArrayHasKey('user_age_days', $features);
        $this->assertArrayHasKey('is_kyc_verified', $features);
        $this->assertArrayHasKey('recent_transaction_count', $features);
        $this->assertArrayHasKey('is_round_amount', $features);
    }

    /** @test */
    public function it_monitors_user_behavior_correctly()
    {
        // Create transaction history
        Transaction::factory()->count(3)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'created_at' => now()->subDays(10),
        ]);

        $behaviorAnalysis = $this->fraudDetectionService->analyzeUserBehavior($this->user);

        $this->assertIsArray($behaviorAnalysis);
        $this->assertArrayHasKey('transaction_frequency', $behaviorAnalysis);
        $this->assertArrayHasKey('average_amount', $behaviorAnalysis);
        $this->assertArrayHasKey('preferred_hours', $behaviorAnalysis);
        $this->assertArrayHasKey('risk_indicators', $behaviorAnalysis);
    }

    /** @test */
    public function it_updates_user_risk_score_after_analysis()
    {
        $initialRiskScore = $this->user->risk_score ?? 0;

        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 50000.00, // High amount
            'type' => 'transfer',
        ]);

        $this->fraudDetectionService->analyzeTransaction($transaction);

        $updatedUser = $this->user->fresh();
        $this->assertNotEquals($initialRiskScore, $updatedUser->risk_score);
    }

    /** @test */
    public function it_generates_fraud_alerts_for_high_risk_transactions()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 100000.00, // Very high amount
            'type' => 'transfer',
        ]);

        $result = $this->fraudDetectionService->analyzeTransaction($transaction);

        // Should create a fraud detection record with high/critical risk
        $fraudDetection = FraudDetection::where('transaction_id', $transaction->id)->first();
        
        $this->assertNotNull($fraudDetection);
        $this->assertContains($fraudDetection->risk_level, ['high', 'critical']);
    }

    /** @test */
    public function it_handles_whitelist_and_blacklist_correctly()
    {
        // Test blacklisted user
        $blacklistedUser = User::factory()->create([
            'country_id' => $this->country->id,
            'status' => 'blacklisted',
        ]);

        $blacklistedTransaction = Transaction::factory()->make([
            'sender_id' => $blacklistedUser->id,
            'currency_id' => $this->currency->id,
            'amount' => 100.00,
            'type' => 'transfer',
        ]);

        $isBlacklisted = $this->fraudDetectionService->checkBlacklist($blacklistedTransaction);
        $this->assertTrue($isBlacklisted);

        // Test normal user
        $normalTransaction = Transaction::factory()->make([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 100.00,
            'type' => 'transfer',
        ]);

        $isBlacklistedNormal = $this->fraudDetectionService->checkBlacklist($normalTransaction);
        $this->assertFalse($isBlacklistedNormal);
    }
}
