<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    // Financial System External Services
    'paypal' => [
        'client_id' => env('PAYPAL_CLIENT_ID'),
        'client_secret' => env('PAYPAL_CLIENT_SECRET'),
        'mode' => env('PAYPAL_MODE', 'sandbox'),
        'webhook_id' => env('PAYPAL_WEBHOOK_ID'),
    ],

    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
    ],

    'wise' => [
        'api_key' => env('WISE_API_KEY'),
        'environment' => env('WISE_ENVIRONMENT', 'sandbox'),
        'webhook_secret' => env('WISE_WEBHOOK_SECRET'),
    ],

    'fixer_io' => [
        'api_key' => env('FIXER_IO_API_KEY'),
        'base_url' => 'http://data.fixer.io/api',
    ],

    'currency_api' => [
        'api_key' => env('CURRENCY_API_KEY'),
        'base_url' => 'https://api.currencyapi.com/v3',
    ],

    'coingecko' => [
        'api_key' => env('COINGECKO_API_KEY'),
        'base_url' => 'https://api.coingecko.com/api/v3',
    ],

    'twilio' => [
        'sid' => env('TWILIO_SID'),
        'token' => env('TWILIO_TOKEN'),
        'from' => env('TWILIO_FROM'),
    ],

    'firebase' => [
        'server_key' => env('FIREBASE_SERVER_KEY'),
        'sender_id' => env('FIREBASE_SENDER_ID'),
    ],

    'blockchain' => [
        'ethereum' => [
            'rpc_url' => env('ETHEREUM_RPC_URL'),
            'private_key' => env('ETHEREUM_PRIVATE_KEY'),
            'contract_address' => env('ETHEREUM_CONTRACT_ADDRESS'),
        ],
        'bitcoin' => [
            'rpc_url' => env('BITCOIN_RPC_URL'),
            'username' => env('BITCOIN_RPC_USERNAME'),
            'password' => env('BITCOIN_RPC_PASSWORD'),
        ],
    ],

    'ai_services' => [
        'fraud_detection' => [
            'api_key' => env('AI_FRAUD_API_KEY'),
            'endpoint' => env('AI_FRAUD_ENDPOINT'),
        ],
        'risk_analysis' => [
            'api_key' => env('AI_RISK_API_KEY'),
            'endpoint' => env('AI_RISK_ENDPOINT'),
        ],
    ],

];
