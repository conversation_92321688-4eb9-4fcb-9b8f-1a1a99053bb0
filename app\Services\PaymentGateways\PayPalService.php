<?php

namespace App\Services\PaymentGateways;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\Transaction;
use App\Models\PaymentGateway;
use App\Contracts\PaymentGatewayInterface;

class PayPalService implements PaymentGatewayInterface
{
    protected array $config;
    protected string $baseUrl;
    protected ?string $accessToken = null;

    public function __construct()
    {
        $this->config = config('payment_gateways.paypal', []);
        $this->baseUrl = $this->config['sandbox'] ? 
            'https://api.sandbox.paypal.com' : 
            'https://api.paypal.com';
    }

    /**
     * Process payment through PayPal.
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        try {
            // Get access token
            $accessToken = $this->getAccessToken();
            
            if (!$accessToken) {
                throw new \Exception('Failed to get PayPal access token');
            }

            // Create PayPal order
            $order = $this->createOrder($transaction, $paymentData);
            
            if (!$order) {
                throw new \Exception('Failed to create PayPal order');
            }

            // Store order ID for later reference
            $transaction->update([
                'gateway_transaction_id' => $order['id'],
                'gateway_response' => $order,
            ]);

            return [
                'success' => true,
                'transaction_id' => $order['id'],
                'approval_url' => $this->getApprovalUrl($order),
                'status' => 'pending_approval',
                'gateway_response' => $order,
            ];

        } catch (\Exception $e) {
            Log::error('PayPal payment processing failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'PAYPAL_PROCESSING_ERROR',
            ];
        }
    }

    /**
     * Capture approved payment.
     */
    public function capturePayment(string $orderId, array $captureData = []): array
    {
        try {
            $accessToken = $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$accessToken}",
                'Content-Type' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post("{$this->baseUrl}/v2/checkout/orders/{$orderId}/capture", $captureData);

            if (!$response->successful()) {
                throw new \Exception('PayPal capture failed: ' . $response->body());
            }

            $captureResult = $response->json();

            return [
                'success' => true,
                'capture_id' => $captureResult['purchase_units'][0]['payments']['captures'][0]['id'] ?? null,
                'status' => $captureResult['status'],
                'amount' => $captureResult['purchase_units'][0]['payments']['captures'][0]['amount'] ?? null,
                'gateway_response' => $captureResult,
            ];

        } catch (\Exception $e) {
            Log::error('PayPal capture failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'PAYPAL_CAPTURE_ERROR',
            ];
        }
    }

    /**
     * Refund payment.
     */
    public function refundPayment(string $captureId, float $amount, string $currency = 'USD'): array
    {
        try {
            $accessToken = $this->getAccessToken();
            
            $refundData = [
                'amount' => [
                    'value' => number_format($amount, 2, '.', ''),
                    'currency_code' => $currency,
                ],
                'note_to_payer' => 'Refund processed by Mony Transfer',
            ];

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$accessToken}",
                'Content-Type' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post("{$this->baseUrl}/v2/payments/captures/{$captureId}/refund", $refundData);

            if (!$response->successful()) {
                throw new \Exception('PayPal refund failed: ' . $response->body());
            }

            $refundResult = $response->json();

            return [
                'success' => true,
                'refund_id' => $refundResult['id'],
                'status' => $refundResult['status'],
                'amount' => $refundResult['amount'],
                'gateway_response' => $refundResult,
            ];

        } catch (\Exception $e) {
            Log::error('PayPal refund failed', [
                'capture_id' => $captureId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'PAYPAL_REFUND_ERROR',
            ];
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $transactionId): array
    {
        try {
            $accessToken = $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$accessToken}",
                'Content-Type' => 'application/json',
            ])->get("{$this->baseUrl}/v2/checkout/orders/{$transactionId}");

            if (!$response->successful()) {
                throw new \Exception('Failed to get PayPal order status: ' . $response->body());
            }

            $order = $response->json();

            return [
                'success' => true,
                'status' => $this->mapPayPalStatus($order['status']),
                'gateway_status' => $order['status'],
                'gateway_response' => $order,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get PayPal payment status', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'PAYPAL_STATUS_ERROR',
            ];
        }
    }

    /**
     * Validate webhook signature.
     */
    public function validateWebhook(array $headers, string $payload): bool
    {
        try {
            $webhookId = $this->config['webhook_id'] ?? null;
            
            if (!$webhookId) {
                Log::warning('PayPal webhook ID not configured');
                return false;
            }

            $accessToken = $this->getAccessToken();
            
            $verificationData = [
                'auth_algo' => $headers['PAYPAL-AUTH-ALGO'] ?? '',
                'cert_id' => $headers['PAYPAL-CERT-ID'] ?? '',
                'transmission_id' => $headers['PAYPAL-TRANSMISSION-ID'] ?? '',
                'transmission_sig' => $headers['PAYPAL-TRANSMISSION-SIG'] ?? '',
                'transmission_time' => $headers['PAYPAL-TRANSMISSION-TIME'] ?? '',
                'webhook_id' => $webhookId,
                'webhook_event' => json_decode($payload, true),
            ];

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$accessToken}",
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/v1/notifications/verify-webhook-signature", $verificationData);

            if (!$response->successful()) {
                Log::error('PayPal webhook verification failed', [
                    'response' => $response->body(),
                ]);
                return false;
            }

            $result = $response->json();
            return $result['verification_status'] === 'SUCCESS';

        } catch (\Exception $e) {
            Log::error('PayPal webhook validation error', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get access token from PayPal.
     */
    protected function getAccessToken(): ?string
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        // Check cache first
        $cacheKey = 'paypal_access_token';
        $cachedToken = Cache::get($cacheKey);
        
        if ($cachedToken) {
            $this->accessToken = $cachedToken;
            return $this->accessToken;
        }

        try {
            $clientId = $this->config['client_id'];
            $clientSecret = $this->config['client_secret'];
            
            $response = Http::withBasicAuth($clientId, $clientSecret)
                ->asForm()
                ->post("{$this->baseUrl}/v1/oauth2/token", [
                    'grant_type' => 'client_credentials',
                ]);

            if (!$response->successful()) {
                throw new \Exception('Failed to get PayPal access token: ' . $response->body());
            }

            $tokenData = $response->json();
            $this->accessToken = $tokenData['access_token'];
            
            // Cache token for slightly less than its expiry time
            $expiresIn = $tokenData['expires_in'] - 60; // 60 seconds buffer
            Cache::put($cacheKey, $this->accessToken, $expiresIn);

            return $this->accessToken;

        } catch (\Exception $e) {
            Log::error('Failed to get PayPal access token', [
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create PayPal order.
     */
    protected function createOrder(Transaction $transaction, array $paymentData): ?array
    {
        $accessToken = $this->getAccessToken();
        
        $orderData = [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'reference_id' => $transaction->reference_number,
                    'amount' => [
                        'currency_code' => $transaction->currency->code,
                        'value' => number_format($transaction->amount + $transaction->fee, 2, '.', ''),
                        'breakdown' => [
                            'item_total' => [
                                'currency_code' => $transaction->currency->code,
                                'value' => number_format($transaction->amount, 2, '.', ''),
                            ],
                            'handling' => [
                                'currency_code' => $transaction->currency->code,
                                'value' => number_format($transaction->fee, 2, '.', ''),
                            ],
                        ],
                    ],
                    'description' => "Money transfer - {$transaction->reference_number}",
                    'items' => [
                        [
                            'name' => 'Money Transfer',
                            'description' => "Transfer to {$transaction->receiver_name}",
                            'quantity' => '1',
                            'unit_amount' => [
                                'currency_code' => $transaction->currency->code,
                                'value' => number_format($transaction->amount, 2, '.', ''),
                            ],
                        ],
                    ],
                ],
            ],
            'application_context' => [
                'brand_name' => 'Mony Transfer',
                'locale' => 'en-US',
                'landing_page' => 'BILLING',
                'shipping_preference' => 'NO_SHIPPING',
                'user_action' => 'PAY_NOW',
                'return_url' => $paymentData['return_url'] ?? config('app.url') . '/payment/success',
                'cancel_url' => $paymentData['cancel_url'] ?? config('app.url') . '/payment/cancel',
            ],
        ];

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$accessToken}",
            'Content-Type' => 'application/json',
            'PayPal-Request-Id' => uniqid(),
        ])->post("{$this->baseUrl}/v2/checkout/orders", $orderData);

        if (!$response->successful()) {
            Log::error('Failed to create PayPal order', [
                'response' => $response->body(),
                'order_data' => $orderData,
            ]);
            return null;
        }

        return $response->json();
    }

    /**
     * Get approval URL from order response.
     */
    protected function getApprovalUrl(array $order): ?string
    {
        foreach ($order['links'] ?? [] as $link) {
            if ($link['rel'] === 'approve') {
                return $link['href'];
            }
        }
        
        return null;
    }

    /**
     * Map PayPal status to our internal status.
     */
    protected function mapPayPalStatus(string $paypalStatus): string
    {
        return match($paypalStatus) {
            'CREATED', 'SAVED' => 'pending',
            'APPROVED' => 'pending_capture',
            'VOIDED' => 'cancelled',
            'COMPLETED' => 'completed',
            'PAYER_ACTION_REQUIRED' => 'pending_approval',
            default => 'unknown'
        };
    }

    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(): array
    {
        return [
            'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'NOK', 'SEK', 'DKK',
            'PLN', 'CZK', 'HUF', 'ILS', 'MXN', 'BRL', 'MYR', 'PHP', 'TWD', 'THB',
            'SGD', 'HKD', 'NZD', 'RUB', 'INR', 'CNY', 'KRW'
        ];
    }

    /**
     * Get gateway configuration.
     */
    public function getConfig(): array
    {
        return [
            'name' => 'PayPal',
            'supports_refunds' => true,
            'supports_partial_refunds' => true,
            'supports_webhooks' => true,
            'supported_currencies' => $this->getSupportedCurrencies(),
            'min_amount' => 0.01,
            'max_amount' => 10000.00,
            'processing_fee_percentage' => 2.9,
            'processing_fee_fixed' => 0.30,
        ];
    }
}
