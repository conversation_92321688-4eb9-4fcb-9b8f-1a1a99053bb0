<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Financial System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Mony Transfer
    | Global Financial System.
    |
    */

    'system' => [
        'name' => env('FINANCIAL_SYSTEM_NAME', 'Mony Transfer Global'),
        'version' => env('FINANCIAL_SYSTEM_VERSION', '1.0.0'),
        'license' => env('FINANCIAL_SYSTEM_LICENSE', 'Enterprise'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */

    'security' => [
        'encryption_key' => env('ENCRYPTION_KEY'),
        'jwt_secret' => env('JWT_SECRET'),
        'biometric_enabled' => env('BIOMETRIC_ENABLED', true),
        'webauthn_enabled' => env('WEBAUTHN_ENABLED', true),
        'two_factor_enabled' => env('TWO_FACTOR_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Blockchain Configuration
    |--------------------------------------------------------------------------
    */

    'blockchain' => [
        'enabled' => env('BLOCKCHAIN_ENABLED', true),
        'network' => env('BLOCKCHAIN_NETWORK', 'ethereum'),
        'private_key' => env('BLOCKCHAIN_PRIVATE_KEY'),
        'contract_address' => env('BLOCKCHAIN_CONTRACT_ADDRESS'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cryptocurrency Configuration
    |--------------------------------------------------------------------------
    */

    'crypto' => [
        'enabled' => env('CRYPTO_ENABLED', true),
        'wallet_enabled' => env('CRYPTO_WALLET_ENABLED', true),
        'supported_cryptos' => explode(',', env('SUPPORTED_CRYPTOS', 'BTC,ETH,USDT,BNB')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Exchange Rates Configuration
    |--------------------------------------------------------------------------
    */

    'exchange_rates' => [
        'api_key' => env('EXCHANGE_RATE_API_KEY'),
        'provider' => env('EXCHANGE_RATE_PROVIDER', 'fixer.io'),
        'update_interval' => env('EXCHANGE_RATE_UPDATE_INTERVAL', 300), // seconds
        'default_spread' => 0.005, // 0.5%
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Gateway Configuration
    |--------------------------------------------------------------------------
    */

    'payment_gateways' => [
        'paypal' => [
            'client_id' => env('PAYPAL_CLIENT_ID'),
            'client_secret' => env('PAYPAL_CLIENT_SECRET'),
            'mode' => env('PAYPAL_MODE', 'sandbox'),
        ],
        'stripe' => [
            'key' => env('STRIPE_KEY'),
            'secret' => env('STRIPE_SECRET'),
        ],
        'wise' => [
            'api_key' => env('WISE_API_KEY'),
            'environment' => env('WISE_ENVIRONMENT', 'sandbox'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Banking Integration Configuration
    |--------------------------------------------------------------------------
    */

    'banking' => [
        'swift_enabled' => env('SWIFT_ENABLED', true),
        'sepa_enabled' => env('SEPA_ENABLED', true),
        'openbanking_enabled' => env('OPENBANKING_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | AI & Risk Management Configuration
    |--------------------------------------------------------------------------
    */

    'ai' => [
        'fraud_detection_enabled' => env('AI_FRAUD_DETECTION_ENABLED', true),
        'risk_analysis_enabled' => env('AI_RISK_ANALYSIS_ENABLED', true),
        'model_endpoint' => env('AI_MODEL_ENDPOINT'),
        'api_key' => env('AI_API_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Compliance & Reporting Configuration
    |--------------------------------------------------------------------------
    */

    'compliance' => [
        'aml_enabled' => env('AML_ENABLED', true),
        'kyc_enabled' => env('KYC_ENABLED', true),
        'regulatory_reporting_enabled' => env('REGULATORY_REPORTING_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Mobile App Configuration
    |--------------------------------------------------------------------------
    */

    'mobile' => [
        'app_enabled' => env('MOBILE_APP_ENABLED', true),
        'push_notifications' => env('MOBILE_PUSH_NOTIFICATIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | System Limits Configuration
    |--------------------------------------------------------------------------
    */

    'limits' => [
        'max_transfer_amount' => env('MAX_TRANSFER_AMOUNT', 1000000),
        'min_transfer_amount' => env('MIN_TRANSFER_AMOUNT', 1),
        'daily_transfer_limit' => env('DAILY_TRANSFER_LIMIT', 50000),
        'monthly_transfer_limit' => env('MONTHLY_TRANSFER_LIMIT', 500000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Commission & Fees Configuration
    |--------------------------------------------------------------------------
    */

    'fees' => [
        'default_commission_rate' => env('DEFAULT_COMMISSION_RATE', 0.02),
        'min_commission_amount' => env('MIN_COMMISSION_AMOUNT', 1),
        'max_commission_amount' => env('MAX_COMMISSION_AMOUNT', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Multi-language Support Configuration
    |--------------------------------------------------------------------------
    */

    'localization' => [
        'supported_languages' => explode(',', env('SUPPORTED_LANGUAGES', 'ar,en,fr,es')),
        'default_currency' => env('DEFAULT_CURRENCY', 'USD'),
        'supported_currencies' => explode(',', env('SUPPORTED_CURRENCIES', 'USD,EUR,GBP,SAR,AED,EGP')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Risk Scoring Configuration
    |--------------------------------------------------------------------------
    */

    'risk_scoring' => [
        'amount_thresholds' => [
            'low' => 1000,
            'medium' => 5000,
            'high' => 10000,
        ],
        'frequency_thresholds' => [
            'daily' => 5,
            'weekly' => 20,
            'monthly' => 50,
        ],
        'country_risk_levels' => [
            'low' => ['USA', 'GBR', 'SAU', 'ARE'],
            'medium' => ['EGY', 'JOR', 'LBN'],
            'high' => [],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    */

    'notifications' => [
        'channels' => ['database', 'email', 'sms', 'push'],
        'transaction_notifications' => true,
        'security_notifications' => true,
        'marketing_notifications' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Audit & Logging Configuration
    |--------------------------------------------------------------------------
    */

    'audit' => [
        'enabled' => true,
        'log_all_requests' => false,
        'log_sensitive_data' => false,
        'retention_days' => 365,
    ],

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    */

    'api' => [
        'rate_limit' => 1000, // requests per minute
        'version' => 'v1',
        'documentation_url' => '/api/documentation',
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    */

    'uploads' => [
        'max_file_size' => 10240, // KB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
        'kyc_documents_path' => 'kyc_documents',
        'receipts_path' => 'receipts',
        'qr_codes_path' => 'qr_codes',
    ],

];
