<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Requests\TransactionRequest;
use App\Models\Transaction;
use App\Models\Country;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Services\PaymentGatewayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TransactionController extends Controller
{
    protected PaymentGatewayService $paymentService;

    public function __construct(PaymentGatewayService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Display a listing of transactions
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = Transaction::where('user_id', $user->id)
            ->with(['senderCountry', 'recipientCountry']);

        // Apply filters
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        if ($request->has('currency_from') && $request->currency_from !== '') {
            $query->where('currency_from', $request->currency_from);
        }

        if ($request->has('date_from') && $request->date_from !== '') {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to !== '') {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('recipient_name', 'like', "%{$search}%")
                  ->orWhere('recipient_phone', 'like', "%{$search}%");
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')
            ->paginate(15);

        // Get filter options
        $currencies = Currency::where('is_active', true)->pluck('code', 'code');
        $statuses = [
            'pending' => 'معلق',
            'processing' => 'قيد المعالجة',
            'completed' => 'مكتمل',
            'failed' => 'فاشل',
            'cancelled' => 'ملغي',
        ];

        return view('transactions.index', compact('transactions', 'currencies', 'statuses'));
    }

    /**
     * Show the form for creating a new transaction
     */
    public function create()
    {
        $countries = Country::where('is_active', true)
            ->where('supports_transfers', true)
            ->orderBy('name_en')
            ->get();

        $currencies = Currency::where('is_active', true)
            ->orderBy('code')
            ->get();

        return view('transactions.create', compact('countries', 'currencies'));
    }

    /**
     * Store a newly created transaction
     */
    public function store(TransactionRequest $request)
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            $data = $request->validated();
            
            // Generate transaction ID
            $transactionId = 'MT' . date('Ymd') . strtoupper(substr(md5(uniqid()), 0, 6));

            // Get exchange rate
            $fromCurrency = Currency::where('code', $data['currency_from'])->firstOrFail();
            $toCurrency = Currency::where('code', $data['currency_to'])->firstOrFail();

            $exchangeRate = ExchangeRate::where('from_currency_id', $fromCurrency->id)
                ->where('to_currency_id', $toCurrency->id)
                ->where('is_active', true)
                ->firstOrFail();

            // Calculate amounts
            $fee = max(5, $data['amount'] * 0.02);
            $totalAmount = $data['amount'] + $fee;
            $recipientAmount = $data['amount'] * $exchangeRate->sell_rate;

            // Create transaction
            $transaction = Transaction::create([
                'transaction_id' => $transactionId,
                'transaction_number' => 'TXN-' . time(),
                'user_id' => $user->id,
                'type' => 'money_transfer',
                'status' => 'pending',
                'priority' => $data['priority'] ?? 'normal',
                'amount' => $data['amount'],
                'fee' => $fee,
                'exchange_rate' => $exchangeRate->rate,
                'total_amount' => $totalAmount,
                'recipient_amount' => $recipientAmount,
                'currency_from' => $data['currency_from'],
                'currency_to' => $data['currency_to'],
                'sender_name' => $data['sender_name'] ?? $user->first_name . ' ' . $user->last_name,
                'sender_phone' => $data['sender_phone'] ?? $user->phone,
                'sender_email' => $data['sender_email'] ?? $user->email,
                'sender_country_id' => $data['sender_country_id'] ?? $user->country_id,
                'sender_address' => $data['sender_address'] ?? $user->address,
                'sender_id_number' => $data['sender_id_number'] ?? null,
                'sender_id_type' => $data['sender_id_type'] ?? null,
                'recipient_name' => $data['recipient_name'],
                'recipient_phone' => $data['recipient_phone'],
                'recipient_email' => $data['recipient_email'] ?? null,
                'recipient_country_id' => $data['recipient_country_id'],
                'recipient_address' => $data['recipient_address'],
                'recipient_bank_name' => $data['recipient_bank_name'] ?? null,
                'recipient_bank_account' => $data['recipient_bank_account'] ?? null,
                'recipient_bank_code' => $data['recipient_bank_code'] ?? null,
                'payment_method' => $data['payment_method'] ?? 'stripe',
                'delivery_method' => $data['delivery_method'],
                'purpose' => $data['purpose'],
                'notes' => $data['notes'] ?? null,
                'relationship_to_recipient' => $data['relationship_to_recipient'] ?? null,
                'source_of_funds' => $data['source_of_funds'] ?? null,
                'expected_delivery_date' => $data['expected_delivery_date'] ?? null,
                'risk_level' => 'medium',
                'risk_score' => 50,
                'risk_factors' => [],
                'metadata' => [
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'created_via' => 'web',
                ],
            ]);

            // Process payment if payment data provided
            if ($request->has('payment_data')) {
                $paymentData = json_decode($request->payment_data, true);
                if ($paymentData) {
                    $paymentResult = $this->paymentService->processPayment($transaction, [
                        'payment_method' => $data['payment_method'],
                        'payment_data' => $paymentData,
                    ]);
                    
                    if ($paymentResult['status'] !== 'success') {
                        throw new \Exception('Payment processing failed: ' . ($paymentResult['error_message'] ?? 'Unknown error'));
                    }
                }
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء التحويل بنجاح',
                    'data' => [
                        'transaction' => $transaction,
                    ],
                ]);
            }

            return redirect()->route('transactions.show', $transaction->transaction_id)
                ->with('success', 'تم إنشاء التحويل بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إنشاء التحويل: ' . $e->getMessage(),
                ], 500);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء التحويل: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified transaction
     */
    public function show(string $transactionId)
    {
        $user = Auth::user();
        
        $transaction = Transaction::where('transaction_id', $transactionId)
            ->where('user_id', $user->id)
            ->with(['senderCountry', 'recipientCountry', 'paymentTransactions'])
            ->firstOrFail();

        // Get tracking steps
        $trackingSteps = $this->getTrackingSteps($transaction);

        return view('transactions.show', compact('transaction', 'trackingSteps'));
    }

    /**
     * Cancel a transaction
     */
    public function cancel(string $transactionId, Request $request)
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $user = Auth::user();
            
            $transaction = Transaction::where('transaction_id', $transactionId)
                ->where('user_id', $user->id)
                ->firstOrFail();

            if (!in_array($transaction->status, ['pending', 'pending_review'])) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'لا يمكن إلغاء هذا التحويل في الحالة الحالية',
                    ], 400);
                }

                return redirect()->back()->with('error', 'لا يمكن إلغاء هذا التحويل في الحالة الحالية');
            }

            $transaction->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancelled_by' => $user->id,
                'cancellation_reason' => $request->reason ?? 'إلغاء من قبل المستخدم',
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إلغاء التحويل بنجاح',
                ]);
            }

            return redirect()->route('transactions.show', $transaction->transaction_id)
                ->with('success', 'تم إلغاء التحويل بنجاح');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في إلغاء التحويل: ' . $e->getMessage(),
                ], 500);
            }

            return redirect()->back()->with('error', 'فشل في إلغاء التحويل: ' . $e->getMessage());
        }
    }

    /**
     * Download transaction receipt
     */
    public function receipt(string $transactionId)
    {
        $user = Auth::user();
        
        $transaction = Transaction::where('transaction_id', $transactionId)
            ->where('user_id', $user->id)
            ->with(['senderCountry', 'recipientCountry'])
            ->firstOrFail();

        return view('transactions.receipt', compact('transaction'));
    }

    /**
     * Get tracking steps for transaction
     */
    protected function getTrackingSteps(Transaction $transaction): array
    {
        $steps = [
            [
                'step' => 'created',
                'title' => 'تم إنشاء التحويل',
                'description' => 'تم إنشاء طلب التحويل بنجاح',
                'completed_at' => $transaction->created_at,
                'status' => 'completed',
                'icon' => 'plus-circle',
            ],
        ];

        if ($transaction->payment_confirmed_at) {
            $steps[] = [
                'step' => 'payment_confirmed',
                'title' => 'تم تأكيد الدفع',
                'description' => 'تم استلام وتأكيد الدفع',
                'completed_at' => $transaction->payment_confirmed_at,
                'status' => 'completed',
                'icon' => 'credit-card',
            ];
        }

        if ($transaction->processed_at) {
            $steps[] = [
                'step' => 'processing',
                'title' => 'قيد المعالجة',
                'description' => 'جاري معالجة التحويل',
                'completed_at' => $transaction->processed_at,
                'status' => 'completed',
                'icon' => 'gear',
            ];
        }

        if ($transaction->completed_at) {
            $steps[] = [
                'step' => 'completed',
                'title' => 'تم إكمال التحويل',
                'description' => 'تم إكمال التحويل بنجاح',
                'completed_at' => $transaction->completed_at,
                'status' => 'completed',
                'icon' => 'check-circle',
            ];
        } elseif ($transaction->status === 'pending') {
            $steps[] = [
                'step' => 'pending',
                'title' => 'في انتظار المعالجة',
                'description' => 'التحويل في انتظار المراجعة والمعالجة',
                'completed_at' => null,
                'status' => 'pending',
                'icon' => 'clock',
            ];
        } elseif ($transaction->status === 'failed') {
            $steps[] = [
                'step' => 'failed',
                'title' => 'فشل التحويل',
                'description' => $transaction->failure_reason ?? 'فشل في معالجة التحويل',
                'completed_at' => $transaction->failed_at,
                'status' => 'failed',
                'icon' => 'x-circle',
            ];
        } elseif ($transaction->status === 'cancelled') {
            $steps[] = [
                'step' => 'cancelled',
                'title' => 'تم إلغاء التحويل',
                'description' => $transaction->cancellation_reason ?? 'تم إلغاء التحويل',
                'completed_at' => $transaction->cancelled_at,
                'status' => 'cancelled',
                'icon' => 'x-circle',
            ];
        }

        return $steps;
    }
}
