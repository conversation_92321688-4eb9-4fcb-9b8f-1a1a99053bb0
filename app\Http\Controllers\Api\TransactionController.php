<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Currency;
use App\Models\Wallet;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class TransactionController extends Controller
{
    /**
     * Get all transactions for authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $transactions = Transaction::where(function ($query) use ($user) {
            $query->where('sender_id', $user->id)
                  ->orWhere('receiver_id', $user->id);
        })
        ->with(['currency', 'targetCurrency', 'sender', 'receiver', 'branch'])
        ->orderBy('created_at', 'desc')
        ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $transactions,
        ]);
    }

    /**
     * Get specific transaction details.
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $transaction = Transaction::where(function ($query) use ($user) {
            $query->where('sender_id', $user->id)
                  ->orWhere('receiver_id', $user->id);
        })
        ->with([
            'currency', 'targetCurrency', 'sender', 'receiver', 
            'branch', 'senderCountry', 'receiverCountry',
            'blockchainTransaction', 'fraudAlerts'
        ])
        ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $transaction,
        ]);
    }

    /**
     * Create a new transaction.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:transfer,deposit,withdrawal,exchange',
            'category' => 'required|in:domestic,international,crypto,bank_transfer,cash',
            'receiver_name' => 'required|string|max:255',
            'receiver_phone' => 'nullable|string|max:20',
            'receiver_national_id' => 'nullable|string|max:50',
            'receiver_bank_account' => 'nullable|string|max:100',
            'receiver_bank_name' => 'nullable|string|max:255',
            'currency_id' => 'required|exists:currencies,id',
            'amount' => 'required|numeric|min:0.01',
            'target_currency_id' => 'nullable|exists:currencies,id',
            'payment_method' => 'required|string',
            'purpose' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        
        try {
            DB::beginTransaction();

            // Get sender wallet
            $senderWallet = $user->getWalletForCurrency($request->currency_id);
            if (!$senderWallet) {
                return response()->json([
                    'success' => false,
                    'message' => 'Sender wallet not found for this currency',
                ], 400);
            }

            // Check if sender has sufficient balance
            if (!$senderWallet->hasSufficientBalance($request->amount)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient balance',
                ], 400);
            }

            // Check daily and monthly limits
            if (!$senderWallet->checkDailyLimit($request->amount)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Daily limit exceeded',
                ], 400);
            }

            if (!$senderWallet->checkMonthlyLimit($request->amount)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Monthly limit exceeded',
                ], 400);
            }

            // Calculate exchange rate and target amount
            $exchangeRate = 1.0;
            $targetAmount = $request->amount;
            $targetCurrencyId = $request->target_currency_id ?? $request->currency_id;

            if ($request->target_currency_id && $request->target_currency_id != $request->currency_id) {
                $rate = ExchangeRate::getRate($request->currency_id, $request->target_currency_id);
                if (!$rate) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Exchange rate not available',
                    ], 400);
                }
                $exchangeRate = $rate;
                $targetAmount = $request->amount * $exchangeRate;
            }

            // Calculate commission and fees
            $commissionRate = $user->country->commission_rate ?? 0.02;
            $commissionAmount = $request->amount * $commissionRate;
            $additionalFees = 0; // Can be calculated based on payment method
            $totalFees = $commissionAmount + $additionalFees;
            $netAmount = $targetAmount - $totalFees;

            // Create transaction
            $transaction = Transaction::create([
                'transaction_number' => Transaction::generateTransactionNumber(),
                'reference_number' => Transaction::generateReferenceNumber(),
                'type' => $request->type,
                'category' => $request->category,
                'sender_id' => $user->id,
                'sender_wallet_id' => $senderWallet->id,
                'sender_name' => $user->full_name,
                'sender_phone' => $user->phone,
                'sender_national_id' => $user->national_id,
                'receiver_name' => $request->receiver_name,
                'receiver_phone' => $request->receiver_phone,
                'receiver_national_id' => $request->receiver_national_id,
                'receiver_bank_account' => $request->receiver_bank_account,
                'receiver_bank_name' => $request->receiver_bank_name,
                'currency_id' => $request->currency_id,
                'amount' => $request->amount,
                'exchange_rate' => $exchangeRate,
                'target_currency_id' => $targetCurrencyId,
                'target_amount' => $targetAmount,
                'commission_amount' => $commissionAmount,
                'commission_rate' => $commissionRate,
                'additional_fees' => $additionalFees,
                'total_fees' => $totalFees,
                'net_amount' => $netAmount,
                'status' => 'pending',
                'payment_method' => $request->payment_method,
                'sender_country_id' => $user->country_id,
                'initiated_at' => now(),
                'purpose' => $request->purpose,
                'notes' => $request->notes,
                'risk_score' => $this->calculateRiskScore($user, $request->amount),
            ]);

            // Freeze funds in sender wallet
            $senderWallet->freezeFunds($request->amount);

            DB::commit();

            // Load relationships for response
            $transaction->load(['currency', 'targetCurrency', 'sender', 'receiver']);

            return response()->json([
                'success' => true,
                'message' => 'Transaction created successfully',
                'data' => $transaction,
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create transaction',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel a pending transaction.
     */
    public function cancel(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $transaction = Transaction::where('sender_id', $user->id)
            ->where('status', 'pending')
            ->findOrFail($id);

        try {
            DB::beginTransaction();

            // Update transaction status
            $transaction->update(['status' => 'cancelled']);

            // Unfreeze funds in sender wallet
            if ($transaction->senderWallet) {
                $transaction->senderWallet->unfreezeFunds($transaction->amount);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transaction cancelled successfully',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel transaction',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get transaction statistics for user.
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $stats = [
            'total_sent' => Transaction::where('sender_id', $user->id)
                ->where('status', 'completed')
                ->sum('amount'),
            'total_received' => Transaction::where('receiver_id', $user->id)
                ->where('status', 'completed')
                ->sum('net_amount'),
            'total_transactions' => Transaction::where(function ($query) use ($user) {
                $query->where('sender_id', $user->id)
                      ->orWhere('receiver_id', $user->id);
            })->count(),
            'pending_transactions' => Transaction::where('sender_id', $user->id)
                ->where('status', 'pending')
                ->count(),
            'this_month_sent' => Transaction::where('sender_id', $user->id)
                ->where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Calculate risk score for transaction.
     */
    private function calculateRiskScore(User $user, float $amount): string
    {
        $score = 0;

        // Amount-based risk
        if ($amount > 10000) $score += 30;
        elseif ($amount > 5000) $score += 20;
        elseif ($amount > 1000) $score += 10;

        // User risk level
        switch ($user->risk_level) {
            case 'high': $score += 40; break;
            case 'medium': $score += 20; break;
            case 'low': $score += 0; break;
        }

        // Verification status
        if (!$user->isVerified()) $score += 30;

        // Transaction frequency (simplified)
        $recentTransactions = Transaction::where('sender_id', $user->id)
            ->where('created_at', '>=', now()->subHours(24))
            ->count();
        
        if ($recentTransactions > 10) $score += 25;
        elseif ($recentTransactions > 5) $score += 15;

        // Determine risk level
        if ($score >= 70) return 'high';
        elseif ($score >= 40) return 'medium';
        else return 'low';
    }
}
