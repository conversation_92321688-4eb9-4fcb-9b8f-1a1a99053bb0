<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class TransactionController extends Controller
{
    /**
     * Get user transactions
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $transactions = Transaction::where('sender_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        return response()->json([
            'success' => true,
            'data' => $transactions,
        ]);
    }

    /**
     * Create new transaction
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'receiver_name' => 'required|string|max:255',
            'receiver_phone' => 'required|string|max:20',
            'amount' => 'required|numeric|min:1',
            'currency_id' => 'required|exists:currencies,id',
            'purpose' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // For demo purposes, return success
        return response()->json([
            'success' => true,
            'message' => 'Transaction created successfully',
            'data' => [
                'transaction_number' => 'TXN' . time(),
                'status' => 'pending',
            ],
        ]);
    }

    /**
     * Get transaction quote
     */
    public function getQuote(Request $request)
    {
        $amount = $request->input('amount', 100);
        $fromCurrency = $request->input('from_currency', 'SAR');
        $toCurrency = $request->input('to_currency', 'USD');
        
        // Demo exchange rate
        $exchangeRate = 3.75;
        $fee = $amount * 0.02; // 2% fee
        $totalAmount = $amount + $fee;
        $receivedAmount = $amount / $exchangeRate;
        
        return response()->json([
            'success' => true,
            'data' => [
                'amount' => $amount,
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'exchange_rate' => $exchangeRate,
                'fee' => $fee,
                'total_amount' => $totalAmount,
                'received_amount' => round($receivedAmount, 2),
            ],
        ]);
    }

    /**
     * Get supported options
     */
    public function getSupportedOptions()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'currencies' => ['SAR', 'USD', 'EUR', 'GBP'],
                'payment_methods' => ['bank_transfer', 'credit_card', 'debit_card'],
                'purposes' => ['family_support', 'education', 'business', 'personal'],
            ],
        ]);
    }

    /**
     * Get transaction statistics
     */
    public function statistics()
    {
        $user = Auth::user();
        
        // Demo statistics
        return response()->json([
            'success' => true,
            'data' => [
                'total_transactions' => 12,
                'completed_transactions' => 8,
                'pending_transactions' => 3,
                'failed_transactions' => 1,
                'total_amount' => 5750.00,
                'success_rate' => 66.7,
            ],
        ]);
    }

    /**
     * Show specific transaction
     */
    public function show($transactionId)
    {
        // Demo transaction data
        return response()->json([
            'success' => true,
            'data' => [
                'transaction_number' => $transactionId,
                'status' => 'completed',
                'amount' => 500.00,
                'currency' => 'SAR',
                'receiver_name' => 'أحمد محمد',
                'created_at' => now()->subDays(1),
            ],
        ]);
    }

    /**
     * Cancel transaction
     */
    public function cancel($transactionId)
    {
        return response()->json([
            'success' => true,
            'message' => 'Transaction cancelled successfully',
        ]);
    }

    /**
     * Confirm transaction
     */
    public function confirm($id)
    {
        return response()->json([
            'success' => true,
            'message' => 'Transaction confirmed successfully',
        ]);
    }

    /**
     * Reject transaction
     */
    public function reject($id)
    {
        return response()->json([
            'success' => true,
            'message' => 'Transaction rejected successfully',
        ]);
    }

    /**
     * Get transaction receipt
     */
    public function receipt($id)
    {
        return response()->json([
            'success' => true,
            'data' => [
                'transaction_number' => $id,
                'receipt_url' => url("/receipts/{$id}.pdf"),
            ],
        ]);
    }

    /**
     * Track transaction
     */
    public function track($id)
    {
        return response()->json([
            'success' => true,
            'data' => [
                'transaction_number' => $id,
                'status' => 'completed',
                'tracking_steps' => [
                    ['step' => 'initiated', 'completed' => true, 'timestamp' => now()->subHours(2)],
                    ['step' => 'processing', 'completed' => true, 'timestamp' => now()->subHours(1)],
                    ['step' => 'completed', 'completed' => true, 'timestamp' => now()],
                ],
            ],
        ]);
    }

    /**
     * Crypto transfer
     */
    public function cryptoTransfer(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Crypto transfer initiated',
            'data' => [
                'transaction_hash' => '0x' . bin2hex(random_bytes(32)),
                'status' => 'pending',
            ],
        ]);
    }

    /**
     * Get blockchain status
     */
    public function blockchainStatus($hash)
    {
        return response()->json([
            'success' => true,
            'data' => [
                'hash' => $hash,
                'status' => 'confirmed',
                'confirmations' => 6,
                'block_number' => rand(1000000, 9999999),
            ],
        ]);
    }
}
