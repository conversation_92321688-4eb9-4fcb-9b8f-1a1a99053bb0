import { createApp, h } from 'vue'
import { createInertiaApp } from '@inertiajs/vue3'
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers'
import { ZiggyVue } from '../../vendor/tightenco/ziggy/dist/vue.m.js'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'

// Import global styles
import '../css/app.css'

// Import language files
import en from './lang/en.json'
import ar from './lang/ar.json'

// Create i18n instance
const i18n = createI18n({
  legacy: false,
  locale: document.documentElement.lang || 'en',
  fallbackLocale: 'en',
  messages: {
    en,
    ar
  }
})

// Create Pinia store
const pinia = createPinia()

// Toast configuration
const toastOptions = {
  position: 'top-right',
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: document.documentElement.dir === 'rtl'
}

const appName = window.document.getElementsByTagName('title')[0]?.innerText || 'Mony Transfer'

createInertiaApp({
  title: (title) => `${title} - ${appName}`,
  resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
  setup({ el, App, props, plugin }) {
    return createApp({ render: () => h(App, props) })
      .use(plugin)
      .use(ZiggyVue, Ziggy)
      .use(pinia)
      .use(i18n)
      .use(Toast, toastOptions)
      .mixin({
        methods: {
          // Global helper methods
          formatCurrency(amount, currency = 'SAR') {
            return new Intl.NumberFormat(this.$i18n.locale, {
              style: 'currency',
              currency: currency,
              minimumFractionDigits: 2
            }).format(amount)
          },

          formatDate(date, options = {}) {
            const defaultOptions = {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            }
            return new Intl.DateTimeFormat(this.$i18n.locale, { ...defaultOptions, ...options }).format(new Date(date))
          },

          formatNumber(number) {
            return new Intl.NumberFormat(this.$i18n.locale).format(number)
          },

          // Translation helper
          __(key, replace = {}) {
            let translation = this.$t(key)

            Object.keys(replace).forEach(placeholder => {
              translation = translation.replace(`:${placeholder}`, replace[placeholder])
            })

            return translation
          },

          // Success toast
          showSuccess(message) {
            this.$toast.success(message)
          },

          // Error toast
          showError(message) {
            this.$toast.error(message)
          },

          // Copy to clipboard
          async copyToClipboard(text) {
            try {
              await navigator.clipboard.writeText(text)
              this.showSuccess(this.__('common.copied_to_clipboard'))
            } catch (err) {
              this.showError(this.__('common.copy_failed'))
            }
          },

          // Get transaction status color
          getTransactionStatusColor(status) {
            const colors = {
              pending: 'text-yellow-600 bg-yellow-100',
              processing: 'text-blue-600 bg-blue-100',
              completed: 'text-green-600 bg-green-100',
              failed: 'text-red-600 bg-red-100',
              cancelled: 'text-gray-600 bg-gray-100',
              blocked: 'text-red-800 bg-red-200'
            }
            return colors[status] || 'text-gray-600 bg-gray-100'
          },

          // Handle API errors
          handleApiError(error) {
            if (error.response) {
              const { status, data } = error.response

              if (status === 422 && data.errors) {
                Object.values(data.errors).flat().forEach(message => {
                  this.showError(message)
                })
              } else if (data.message) {
                this.showError(data.message)
              } else {
                this.showError(this.__('common.something_went_wrong'))
              }
            } else {
              this.showError(this.__('common.network_error'))
            }
          }
        }
      })
      .mount(el)
  },
  progress: {
    color: '#4F46E5',
    showSpinner: true,
  },
})
