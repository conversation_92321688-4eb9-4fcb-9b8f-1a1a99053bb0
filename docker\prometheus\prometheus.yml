global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'money-transfer-monitor'

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Application metrics
  - job_name: 'money-transfer-app'
    static_configs:
      - targets: ['app:80']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # PostgreSQL metrics
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis metrics
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Nginx metrics
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # PHP-FPM metrics
  - job_name: 'php-fpm-exporter'
    static_configs:
      - targets: ['php-fpm-exporter:9253']

  # Application-specific metrics
  - job_name: 'money-transfer-metrics'
    static_configs:
      - targets: ['app:80']
    metrics_path: '/api/metrics'
    scrape_interval: 60s
    params:
      format: ['prometheus']

  # Payment gateway health checks
  - job_name: 'payment-gateways'
    static_configs:
      - targets: ['app:80']
    metrics_path: '/api/health/payment-gateways'
    scrape_interval: 120s

  # Security metrics
  - job_name: 'security-metrics'
    static_configs:
      - targets: ['app:80']
    metrics_path: '/api/metrics/security'
    scrape_interval: 30s

  # Business metrics
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['app:80']
    metrics_path: '/api/metrics/business'
    scrape_interval: 300s

  # Blackbox exporter for external monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://api.stripe.com/v1/charges
        - https://api.paypal.com/v1/oauth2/token
        - https://api.exchangerate-api.com/v4/latest/USD
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Custom application endpoints monitoring
  - job_name: 'app-endpoints'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://app:80/health
        - http://app:80/api/health
        - http://app:80/api/status
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Recording rules for aggregated metrics
recording_rules:
  - name: money_transfer_rules
    rules:
      # Transaction success rate
      - record: money_transfer:transaction_success_rate
        expr: |
          (
            sum(rate(money_transfer_transactions_total{status="completed"}[5m])) /
            sum(rate(money_transfer_transactions_total[5m]))
          ) * 100

      # Average transaction value
      - record: money_transfer:avg_transaction_value
        expr: |
          sum(rate(money_transfer_transaction_amount_sum[5m])) /
          sum(rate(money_transfer_transaction_amount_count[5m]))

      # API response time percentiles
      - record: money_transfer:api_response_time_p95
        expr: |
          histogram_quantile(0.95, 
            sum(rate(money_transfer_http_request_duration_seconds_bucket[5m])) by (le, endpoint)
          )

      # Error rate by endpoint
      - record: money_transfer:error_rate_by_endpoint
        expr: |
          (
            sum(rate(money_transfer_http_requests_total{status=~"5.."}[5m])) by (endpoint) /
            sum(rate(money_transfer_http_requests_total[5m])) by (endpoint)
          ) * 100

      # Payment gateway availability
      - record: money_transfer:payment_gateway_availability
        expr: |
          (
            sum(up{job="payment-gateways"}) /
            count(up{job="payment-gateways"})
          ) * 100

      # Database connection pool usage
      - record: money_transfer:db_connection_pool_usage
        expr: |
          (
            money_transfer_db_connections_active /
            money_transfer_db_connections_max
          ) * 100

      # Redis memory usage
      - record: money_transfer:redis_memory_usage_percent
        expr: |
          (
            redis_memory_used_bytes /
            redis_memory_max_bytes
          ) * 100

      # Queue processing rate
      - record: money_transfer:queue_processing_rate
        expr: |
          sum(rate(money_transfer_queue_jobs_processed_total[5m])) by (queue)

      # Failed login attempts rate
      - record: money_transfer:failed_login_rate
        expr: |
          sum(rate(money_transfer_auth_failed_attempts_total[5m]))

      # Fraud detection rate
      - record: money_transfer:fraud_detection_rate
        expr: |
          (
            sum(rate(money_transfer_fraud_detected_total[5m])) /
            sum(rate(money_transfer_transactions_total[5m]))
          ) * 100

# Alert rules
alert_rules:
  - name: money_transfer_alerts
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: money_transfer:error_rate_by_endpoint > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }}% for endpoint {{ $labels.endpoint }}"

      # Low transaction success rate
      - alert: LowTransactionSuccessRate
        expr: money_transfer:transaction_success_rate < 95
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "Low transaction success rate"
          description: "Transaction success rate is {{ $value }}%"

      # High API response time
      - alert: HighAPIResponseTime
        expr: money_transfer:api_response_time_p95 > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.endpoint }}"

      # Payment gateway down
      - alert: PaymentGatewayDown
        expr: money_transfer:payment_gateway_availability < 100
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Payment gateway unavailable"
          description: "Payment gateway availability is {{ $value }}%"

      # Database connection pool exhaustion
      - alert: DatabaseConnectionPoolHigh
        expr: money_transfer:db_connection_pool_usage > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool usage high"
          description: "Database connection pool usage is {{ $value }}%"

      # Redis memory usage high
      - alert: RedisMemoryHigh
        expr: money_transfer:redis_memory_usage_percent > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value }}%"

      # Queue processing lag
      - alert: QueueProcessingLag
        expr: money_transfer_queue_jobs_pending > 1000
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Queue processing lag detected"
          description: "{{ $value }} jobs pending in queue {{ $labels.queue }}"

      # High fraud detection rate
      - alert: HighFraudDetectionRate
        expr: money_transfer:fraud_detection_rate > 10
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "High fraud detection rate"
          description: "Fraud detection rate is {{ $value }}%"

      # Failed login attempts spike
      - alert: FailedLoginSpike
        expr: money_transfer:failed_login_rate > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High number of failed login attempts"
          description: "{{ $value }} failed login attempts per second"

      # Disk space low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space low"
          description: "Disk space is {{ $value }}% full on {{ $labels.instance }}"

      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"
