<?php

namespace App\Services;

use App\Models\User;
use App\Models\Document;
use App\Models\AuditLog;
use App\Services\NotificationService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class KYCService
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Upload KYC document.
     */
    public function uploadDocument(User $user, UploadedFile $file, string $documentType, array $metadata = []): Document
    {
        try {
            // Validate file
            $this->validateDocument($file, $documentType);

            // Store file securely
            $filePath = $this->storeDocument($file, $user, $documentType);

            // Create document record
            $document = Document::create([
                'user_id' => $user->id,
                'type' => $documentType,
                'name' => $this->getDocumentName($documentType),
                'description' => $metadata['description'] ?? null,
                'file_path' => $filePath,
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'status' => 'active',
                'verification_status' => 'pending',
                'expiry_date' => $this->getDocumentExpiryDate($documentType),
                'is_sensitive' => true,
                'metadata' => array_merge($metadata, [
                    'upload_ip' => request()->ip(),
                    'upload_user_agent' => request()->userAgent(),
                    'original_name' => $file->getClientOriginalName(),
                ]),
            ]);

            // Log activity
            AuditLog::logUserActivity($user, 'kyc_document_upload', 'uploaded', [
                'document_id' => $document->id,
                'document_type' => $documentType,
                'file_size' => $file->getSize(),
            ]);

            // Send notification
            $this->notificationService->sendNotification($user, [
                'type' => 'document_uploaded',
                'title' => 'تم رفع المستند بنجاح - Document Uploaded Successfully',
                'message' => "تم رفع مستند {$this->getDocumentName($documentType)} بنجاح وهو قيد المراجعة",
                'data' => [
                    'document_id' => $document->id,
                    'document_type' => $documentType,
                ],
                'channels' => ['database', 'email'],
                'priority' => 'normal',
            ]);

            Log::info('KYC document uploaded successfully', [
                'user_id' => $user->id,
                'document_id' => $document->id,
                'document_type' => $documentType,
            ]);

            return $document;

        } catch (\Exception $e) {
            Log::error('Failed to upload KYC document', [
                'user_id' => $user->id,
                'document_type' => $documentType,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Verify KYC document.
     */
    public function verifyDocument(Document $document, bool $approved, string $notes = '', int $verifiedBy = null): bool
    {
        try {
            $verifiedBy = $verifiedBy ?? auth()->id();

            if ($approved) {
                $document->markAsVerified($verifiedBy);
                
                // Send approval notification
                $this->notificationService->sendNotification($document->user, [
                    'type' => 'kyc_approved',
                    'title' => 'تم قبول المستند - Document Approved',
                    'message' => "تم قبول مستند {$this->getDocumentName($document->type)} بنجاح",
                    'data' => [
                        'document_id' => $document->id,
                        'document_type' => $document->type,
                    ],
                    'channels' => ['database', 'email'],
                    'priority' => 'normal',
                ]);

                // Check if user KYC is complete
                $this->checkKYCCompletion($document->user);

            } else {
                $document->markAsRejected($notes);
                
                // Send rejection notification
                $this->notificationService->sendNotification($document->user, [
                    'type' => 'kyc_rejected',
                    'title' => 'تم رفض المستند - Document Rejected',
                    'message' => "تم رفض مستند {$this->getDocumentName($document->type)}. السبب: {$notes}",
                    'data' => [
                        'document_id' => $document->id,
                        'document_type' => $document->type,
                        'rejection_reason' => $notes,
                    ],
                    'channels' => ['database', 'email'],
                    'priority' => 'high',
                ]);
            }

            // Log verification activity
            AuditLog::logUserActivity($document->user, 'kyc_document_verification', $approved ? 'approved' : 'rejected', [
                'document_id' => $document->id,
                'document_type' => $document->type,
                'verified_by' => $verifiedBy,
                'notes' => $notes,
            ]);

            Log::info('KYC document verification completed', [
                'document_id' => $document->id,
                'user_id' => $document->user_id,
                'approved' => $approved,
                'verified_by' => $verifiedBy,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to verify KYC document', [
                'document_id' => $document->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Check if user's KYC is complete.
     */
    public function checkKYCCompletion(User $user): bool
    {
        $requiredDocuments = $this->getRequiredDocuments($user);
        $verifiedDocuments = $user->documents()
                                 ->where('verification_status', 'verified')
                                 ->where('status', 'active')
                                 ->pluck('type')
                                 ->toArray();

        $isComplete = empty(array_diff($requiredDocuments, $verifiedDocuments));

        if ($isComplete && !$user->kyc_verified_at) {
            // Mark user as KYC verified
            $user->update([
                'kyc_verified_at' => now(),
                'aml_verified' => true,
                'aml_verified_at' => now(),
            ]);

            // Send KYC completion notification
            $this->notificationService->sendNotification($user, [
                'type' => 'kyc_completed',
                'title' => 'تم التحقق من الهوية بنجاح - KYC Verification Completed',
                'message' => 'تهانينا! تم التحقق من هويتك بنجاح. يمكنك الآن استخدام جميع خدماتنا.',
                'data' => [
                    'kyc_completed_at' => now()->toISOString(),
                ],
                'channels' => ['database', 'email', 'sms'],
                'priority' => 'normal',
            ]);

            // Log KYC completion
            AuditLog::logUserActivity($user, 'kyc_completion', 'completed', [
                'verified_documents' => $verifiedDocuments,
                'completion_date' => now()->toISOString(),
            ]);

            Log::info('User KYC verification completed', [
                'user_id' => $user->id,
                'verified_documents' => $verifiedDocuments,
            ]);
        }

        return $isComplete;
    }

    /**
     * Get KYC status for user.
     */
    public function getKYCStatus(User $user): array
    {
        $requiredDocuments = $this->getRequiredDocuments($user);
        $userDocuments = $user->documents()->get();

        $status = [
            'is_complete' => $user->kyc_verified_at !== null,
            'completion_percentage' => 0,
            'required_documents' => [],
            'uploaded_documents' => [],
            'pending_verification' => 0,
            'verified_documents' => 0,
            'rejected_documents' => 0,
        ];

        foreach ($requiredDocuments as $docType) {
            $document = $userDocuments->where('type', $docType)->first();
            
            $docStatus = [
                'type' => $docType,
                'name' => $this->getDocumentName($docType),
                'required' => true,
                'uploaded' => $document !== null,
                'verification_status' => $document?->verification_status ?? 'not_uploaded',
                'uploaded_at' => $document?->created_at?->toISOString(),
                'verified_at' => $document?->verified_at?->toISOString(),
                'rejection_reason' => $document?->rejection_reason,
            ];

            $status['required_documents'][] = $docStatus;

            if ($document) {
                $status['uploaded_documents'][] = $docStatus;
                
                switch ($document->verification_status) {
                    case 'pending':
                        $status['pending_verification']++;
                        break;
                    case 'verified':
                        $status['verified_documents']++;
                        break;
                    case 'rejected':
                        $status['rejected_documents']++;
                        break;
                }
            }
        }

        $status['completion_percentage'] = count($requiredDocuments) > 0 
            ? ($status['verified_documents'] / count($requiredDocuments)) * 100 
            : 0;

        return $status;
    }

    /**
     * Validate document file.
     */
    protected function validateDocument(UploadedFile $file, string $documentType): void
    {
        $maxSize = 10 * 1024 * 1024; // 10MB
        $allowedMimes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
        }

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('نوع الملف غير مدعوم. يُسمح فقط بـ JPEG, PNG, PDF');
        }
    }

    /**
     * Store document securely.
     */
    protected function storeDocument(UploadedFile $file, User $user, string $documentType): string
    {
        $directory = "kyc/{$user->id}/{$documentType}";
        $filename = time() . '_' . $documentType . '.' . $file->getClientOriginalExtension();
        
        return $file->storeAs($directory, $filename, 'private');
    }

    /**
     * Get required documents for user.
     */
    protected function getRequiredDocuments(User $user): array
    {
        $baseDocuments = ['national_id', 'selfie', 'address_proof'];
        
        // Add additional documents based on user type or country
        if ($user->user_type === 'business') {
            $baseDocuments[] = 'business_license';
        }

        return $baseDocuments;
    }

    /**
     * Get document name in Arabic.
     */
    protected function getDocumentName(string $documentType): string
    {
        return match ($documentType) {
            'national_id' => 'الهوية الوطنية',
            'passport' => 'جواز السفر',
            'driving_license' => 'رخصة القيادة',
            'utility_bill' => 'فاتورة الخدمات',
            'bank_statement' => 'كشف حساب بنكي',
            'selfie' => 'صورة شخصية',
            'address_proof' => 'إثبات العنوان',
            'income_proof' => 'إثبات الدخل',
            'business_license' => 'رخصة تجارية',
            'tax_certificate' => 'شهادة ضريبية',
            default => 'مستند',
        };
    }

    /**
     * Get document expiry date.
     */
    protected function getDocumentExpiryDate(string $documentType): ?Carbon
    {
        return match ($documentType) {
            'national_id', 'passport', 'driving_license' => now()->addYears(5),
            'utility_bill', 'bank_statement' => now()->addMonths(3),
            default => null,
        };
    }
}
