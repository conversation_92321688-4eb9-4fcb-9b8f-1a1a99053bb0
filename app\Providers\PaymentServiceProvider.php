<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\PaymentGateways\PayPalService;
use App\Services\PaymentGateways\StripeService;
use App\Services\PaymentGateways\WiseService;
use App\Contracts\PaymentGatewayInterface;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register PayPal Service
        $this->app->bind('payment.paypal', function ($app) {
            return new PayPalService();
        });

        // Register Stripe Service
        $this->app->bind('payment.stripe', function ($app) {
            return new StripeService();
        });

        // Register Wise Service
        $this->app->bind('payment.wise', function ($app) {
            return new WiseService();
        });

        // Register Payment Gateway Manager
        $this->app->singleton('payment.manager', function ($app) {
            return new \App\Services\PaymentGatewayManager($app);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
