<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\PaymentMethod;
use App\Models\PaymentTransaction;
use App\Exceptions\PaymentException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class PaymentGatewayService
{
    protected array $gateways = [
        'stripe' => \App\Services\PaymentGateways\StripeGateway::class,
        'paypal' => \App\Services\PaymentGateways\PayPalGateway::class,
        'visa' => \App\Services\PaymentGateways\VisaGateway::class,
        'mastercard' => \App\Services\PaymentGateways\MastercardGateway::class,
        'bank_transfer' => \App\Services\PaymentGateways\BankTransferGateway::class,
    ];

    /**
     * Process payment for transaction
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        try {
            $gateway = $this->getGateway($transaction->payment_method);
            
            // Validate payment data
            $this->validatePaymentData($paymentData, $transaction->payment_method);
            
            // Create payment transaction record
            $paymentTransaction = $this->createPaymentTransaction($transaction, $paymentData);
            
            // Process payment through gateway
            $result = $gateway->processPayment($transaction, $paymentData);
            
            // Update payment transaction with result
            $this->updatePaymentTransaction($paymentTransaction, $result);
            
            // Handle payment result
            if ($result['status'] === 'success') {
                $this->handleSuccessfulPayment($transaction, $paymentTransaction, $result);
            } else {
                $this->handleFailedPayment($transaction, $paymentTransaction, $result);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage(),
                'payment_data' => $paymentData,
            ]);
            
            throw new PaymentException(
                'Payment processing failed: ' . $e->getMessage(),
                'PAYMENT_PROCESSING_FAILED',
                ['transaction_id' => $transaction->transaction_id]
            );
        }
    }

    /**
     * Refund payment
     */
    public function refundPayment(Transaction $transaction, float $amount = null): array
    {
        try {
            $paymentTransaction = PaymentTransaction::where('transaction_id', $transaction->id)
                ->where('status', 'completed')
                ->first();
                
            if (!$paymentTransaction) {
                throw new PaymentException(
                    'No successful payment found for refund',
                    'NO_PAYMENT_FOUND'
                );
            }
            
            $gateway = $this->getGateway($transaction->payment_method);
            $refundAmount = $amount ?? $transaction->total_amount;
            
            $result = $gateway->refundPayment($paymentTransaction, $refundAmount);
            
            // Create refund record
            $this->createRefundRecord($transaction, $paymentTransaction, $result, $refundAmount);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Payment refund failed', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage(),
            ]);
            
            throw new PaymentException(
                'Payment refund failed: ' . $e->getMessage(),
                'PAYMENT_REFUND_FAILED'
            );
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(string $paymentReference, string $gateway): array
    {
        try {
            $gatewayInstance = $this->getGateway($gateway);
            return $gatewayInstance->verifyPayment($paymentReference);
        } catch (\Exception $e) {
            Log::error('Payment verification failed', [
                'payment_reference' => $paymentReference,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);
            
            throw new PaymentException(
                'Payment verification failed: ' . $e->getMessage(),
                'PAYMENT_VERIFICATION_FAILED'
            );
        }
    }

    /**
     * Get available payment methods for country/currency
     */
    public function getAvailablePaymentMethods(string $countryCode, string $currencyCode): array
    {
        return PaymentMethod::where('is_active', true)
            ->where(function($query) use ($countryCode) {
                $query->whereJsonContains('supported_countries', $countryCode)
                      ->orWhereNull('supported_countries');
            })
            ->where(function($query) use ($currencyCode) {
                $query->whereJsonContains('supported_currencies', $currencyCode)
                      ->orWhereNull('supported_currencies');
            })
            ->get()
            ->map(function($method) {
                return [
                    'id' => $method->id,
                    'name' => $method->name,
                    'type' => $method->type,
                    'fee_percentage' => $method->fee_percentage,
                    'fee_fixed' => $method->fee_fixed,
                    'min_amount' => $method->min_amount,
                    'max_amount' => $method->max_amount,
                    'processing_time' => $method->processing_time,
                    'requires_verification' => $method->requires_verification,
                ];
            })
            ->toArray();
    }

    /**
     * Calculate payment fees
     */
    public function calculatePaymentFees(string $paymentMethod, float $amount): array
    {
        $method = PaymentMethod::where('type', $paymentMethod)
            ->where('is_active', true)
            ->first();
            
        if (!$method) {
            throw new PaymentException(
                'Payment method not found',
                'PAYMENT_METHOD_NOT_FOUND'
            );
        }
        
        $percentageFee = ($amount * $method->fee_percentage) / 100;
        $totalFee = $percentageFee + $method->fee_fixed;
        
        return [
            'base_amount' => $amount,
            'percentage_fee' => $percentageFee,
            'fixed_fee' => $method->fee_fixed,
            'total_fee' => $totalFee,
            'total_amount' => $amount + $totalFee,
            'fee_breakdown' => [
                'percentage' => $method->fee_percentage . '%',
                'fixed' => $method->fee_fixed,
            ],
        ];
    }

    /**
     * Handle webhook notifications
     */
    public function handleWebhook(string $gateway, array $payload): array
    {
        try {
            $gatewayInstance = $this->getGateway($gateway);
            
            // Verify webhook signature
            if (!$gatewayInstance->verifyWebhookSignature($payload)) {
                throw new PaymentException(
                    'Invalid webhook signature',
                    'INVALID_WEBHOOK_SIGNATURE'
                );
            }
            
            // Process webhook
            $result = $gatewayInstance->processWebhook($payload);
            
            // Update transaction status if needed
            if (isset($result['transaction_reference'])) {
                $this->updateTransactionFromWebhook($result);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'gateway' => $gateway,
                'payload' => $payload,
                'error' => $e->getMessage(),
            ]);
            
            throw new PaymentException(
                'Webhook processing failed: ' . $e->getMessage(),
                'WEBHOOK_PROCESSING_FAILED'
            );
        }
    }

    /**
     * Get gateway instance
     */
    protected function getGateway(string $gatewayType): object
    {
        if (!isset($this->gateways[$gatewayType])) {
            throw new PaymentException(
                'Unsupported payment gateway: ' . $gatewayType,
                'UNSUPPORTED_GATEWAY'
            );
        }
        
        $gatewayClass = $this->gateways[$gatewayType];
        return new $gatewayClass();
    }

    /**
     * Validate payment data
     */
    protected function validatePaymentData(array $paymentData, string $paymentMethod): void
    {
        $requiredFields = $this->getRequiredFields($paymentMethod);
        
        foreach ($requiredFields as $field) {
            if (!isset($paymentData[$field]) || empty($paymentData[$field])) {
                throw new PaymentException(
                    "Required field missing: {$field}",
                    'MISSING_REQUIRED_FIELD',
                    ['field' => $field]
                );
            }
        }
    }

    /**
     * Get required fields for payment method
     */
    protected function getRequiredFields(string $paymentMethod): array
    {
        return match($paymentMethod) {
            'stripe', 'visa', 'mastercard' => [
                'card_number', 'expiry_month', 'expiry_year', 'cvv', 'cardholder_name'
            ],
            'paypal' => ['email'],
            'bank_transfer' => [
                'bank_name', 'account_number', 'routing_number', 'account_holder_name'
            ],
            default => [],
        };
    }

    /**
     * Create payment transaction record
     */
    protected function createPaymentTransaction(Transaction $transaction, array $paymentData): PaymentTransaction
    {
        return PaymentTransaction::create([
            'transaction_id' => $transaction->id,
            'gateway' => $transaction->payment_method,
            'amount' => $transaction->total_amount,
            'currency' => $transaction->currency_from,
            'status' => 'pending',
            'payment_data' => $this->sanitizePaymentData($paymentData),
            'created_at' => now(),
        ]);
    }

    /**
     * Update payment transaction with result
     */
    protected function updatePaymentTransaction(PaymentTransaction $paymentTransaction, array $result): void
    {
        $paymentTransaction->update([
            'status' => $result['status'],
            'gateway_reference' => $result['reference'] ?? null,
            'gateway_response' => $result,
            'processed_at' => now(),
        ]);
    }

    /**
     * Handle successful payment
     */
    protected function handleSuccessfulPayment(Transaction $transaction, PaymentTransaction $paymentTransaction, array $result): void
    {
        $transaction->update([
            'status' => 'processing',
            'payment_reference' => $result['reference'] ?? null,
            'payment_confirmed_at' => now(),
        ]);
        
        // Log successful payment
        Log::info('Payment processed successfully', [
            'transaction_id' => $transaction->transaction_id,
            'payment_reference' => $result['reference'] ?? null,
            'amount' => $transaction->total_amount,
        ]);
    }

    /**
     * Handle failed payment
     */
    protected function handleFailedPayment(Transaction $transaction, PaymentTransaction $paymentTransaction, array $result): void
    {
        $transaction->update([
            'status' => 'payment_failed',
            'failure_reason' => $result['error_message'] ?? 'Payment failed',
            'failed_at' => now(),
        ]);
        
        // Log failed payment
        Log::warning('Payment failed', [
            'transaction_id' => $transaction->transaction_id,
            'error' => $result['error_message'] ?? 'Unknown error',
            'amount' => $transaction->total_amount,
        ]);
    }

    /**
     * Create refund record
     */
    protected function createRefundRecord(Transaction $transaction, PaymentTransaction $paymentTransaction, array $result, float $amount): void
    {
        PaymentTransaction::create([
            'transaction_id' => $transaction->id,
            'parent_payment_id' => $paymentTransaction->id,
            'gateway' => $paymentTransaction->gateway,
            'amount' => -$amount, // Negative amount for refund
            'currency' => $paymentTransaction->currency,
            'status' => $result['status'],
            'gateway_reference' => $result['reference'] ?? null,
            'gateway_response' => $result,
            'type' => 'refund',
            'processed_at' => now(),
        ]);
    }

    /**
     * Update transaction from webhook
     */
    protected function updateTransactionFromWebhook(array $webhookResult): void
    {
        $paymentTransaction = PaymentTransaction::where('gateway_reference', $webhookResult['transaction_reference'])
            ->first();
            
        if ($paymentTransaction) {
            $transaction = $paymentTransaction->transaction;
            
            // Update based on webhook status
            switch ($webhookResult['status']) {
                case 'completed':
                    $transaction->update(['status' => 'processing']);
                    break;
                case 'failed':
                    $transaction->update([
                        'status' => 'payment_failed',
                        'failure_reason' => $webhookResult['error_message'] ?? 'Payment failed',
                    ]);
                    break;
                case 'refunded':
                    $transaction->update(['status' => 'refunded']);
                    break;
            }
        }
    }

    /**
     * Sanitize payment data for storage
     */
    protected function sanitizePaymentData(array $paymentData): array
    {
        // Remove sensitive data before storing
        $sanitized = $paymentData;
        
        // Mask card number if present
        if (isset($sanitized['card_number'])) {
            $sanitized['card_number'] = '**** **** **** ' . substr($sanitized['card_number'], -4);
        }
        
        // Remove CVV
        unset($sanitized['cvv']);
        
        return $sanitized;
    }
}
