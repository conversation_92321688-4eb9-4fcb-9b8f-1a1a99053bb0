<template>
  <div class="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg p-6 text-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
          </pattern>
        </defs>
        <rect width="100" height="100" fill="url(#grid)" />
      </svg>
    </div>

    <!-- Currency Icon and Name -->
    <div class="flex items-center justify-between mb-4 relative z-10">
      <div class="flex items-center space-x-3 rtl:space-x-reverse">
        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
          <span class="text-2xl">{{ getCurrencyIcon(wallet.currency.code) }}</span>
        </div>
        <div>
          <h3 class="text-lg font-semibold">{{ wallet.currency.name }}</h3>
          <p class="text-sm opacity-80">{{ wallet.currency.code }}</p>
        </div>
      </div>
      
      <!-- Status Indicator -->
      <div class="flex items-center space-x-2 rtl:space-x-reverse">
        <div 
          :class="wallet.is_active ? 'bg-green-400' : 'bg-red-400'"
          class="w-3 h-3 rounded-full"
        ></div>
        <span class="text-sm opacity-80">
          {{ wallet.is_active ? $t('common.active') : $t('common.inactive') }}
        </span>
      </div>
    </div>

    <!-- Balance Display -->
    <div class="mb-6 relative z-10">
      <div class="text-sm opacity-80 mb-1">{{ $t('wallets.available_balance') }}</div>
      <div class="text-3xl font-bold mb-2">
        {{ formatCurrency(wallet.balance, wallet.currency.code) }}
      </div>
      
      <!-- USD Equivalent -->
      <div v-if="wallet.currency.code !== 'USD'" class="text-sm opacity-80">
        ≈ {{ formatCurrency(convertToUSD(wallet.balance, wallet.currency.rate_to_usd), 'USD') }}
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-2 gap-4 mb-6 relative z-10">
      <div class="bg-white bg-opacity-10 rounded-lg p-3">
        <div class="text-xs opacity-80 mb-1">{{ $t('dashboard.total_sent') }}</div>
        <div class="text-lg font-semibold">
          {{ formatCurrency(walletStats.total_sent || 0, wallet.currency.code) }}
        </div>
      </div>
      
      <div class="bg-white bg-opacity-10 rounded-lg p-3">
        <div class="text-xs opacity-80 mb-1">{{ $t('dashboard.total_received') }}</div>
        <div class="text-lg font-semibold">
          {{ formatCurrency(walletStats.total_received || 0, wallet.currency.code) }}
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex space-x-2 rtl:space-x-reverse relative z-10">
      <button
        @click="addMoney"
        class="flex-1 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg py-2 px-4 text-sm font-medium transition-all duration-200 backdrop-blur-sm"
      >
        <span class="mr-2 rtl:ml-2">💰</span>
        {{ $t('wallets.add_money') }}
      </button>
      
      <button
        @click="sendMoney"
        class="flex-1 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg py-2 px-4 text-sm font-medium transition-all duration-200 backdrop-blur-sm"
      >
        <span class="mr-2 rtl:ml-2">💸</span>
        {{ $t('wallets.transfer_money') }}
      </button>
      
      <button
        @click="withdrawMoney"
        class="flex-1 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg py-2 px-4 text-sm font-medium transition-all duration-200 backdrop-blur-sm"
      >
        <span class="mr-2 rtl:ml-2">🏧</span>
        {{ $t('wallets.withdraw_money') }}
      </button>
    </div>

    <!-- Recent Transactions -->
    <div v-if="showRecentTransactions && recentTransactions.length > 0" class="mt-6 relative z-10">
      <div class="text-sm opacity-80 mb-3">{{ $t('dashboard.recent_transactions') }}</div>
      <div class="space-y-2">
        <div 
          v-for="transaction in recentTransactions.slice(0, 3)" 
          :key="transaction.id"
          class="flex items-center justify-between bg-white bg-opacity-10 rounded-lg p-2"
        >
          <div class="flex items-center space-x-2 rtl:space-x-reverse">
            <span class="text-sm">{{ getTransactionTypeIcon(transaction.type) }}</span>
            <div>
              <div class="text-xs font-medium">{{ $t(`transactions.type.${transaction.type}`) }}</div>
              <div class="text-xs opacity-80">{{ formatDate(transaction.created_at, { month: 'short', day: 'numeric' }) }}</div>
            </div>
          </div>
          
          <div class="text-right rtl:text-left">
            <div class="text-sm font-medium">
              {{ transaction.type === 'deposit' ? '+' : '-' }}{{ formatCurrency(transaction.amount, wallet.currency.code) }}
            </div>
            <div 
              :class="getTransactionStatusColor(transaction.status)"
              class="text-xs px-2 py-1 rounded-full"
            >
              {{ $t(`transactions.status.${transaction.status}`) }}
            </div>
          </div>
        </div>
      </div>
      
      <button
        @click="viewAllTransactions"
        class="w-full mt-3 text-center text-sm opacity-80 hover:opacity-100 transition-opacity"
      >
        {{ $t('dashboard.view_all_transactions') }} →
      </button>
    </div>

    <!-- Loading Overlay -->
    <div v-if="loading" class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center rounded-xl">
      <div class="bg-white bg-opacity-90 rounded-lg p-4 flex items-center space-x-3 rtl:space-x-reverse">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-700">{{ $t('common.loading') }}...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'

const props = defineProps({
  wallet: {
    type: Object,
    required: true
  },
  showRecentTransactions: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['action'])

const loading = ref(false)
const walletStats = ref({
  total_sent: 0,
  total_received: 0
})
const recentTransactions = ref([])

// Methods
const getCurrencyIcon = (currencyCode) => {
  const icons = {
    'SAR': '🇸🇦',
    'USD': '💵',
    'EUR': '💶',
    'GBP': '💷',
    'AED': '🇦🇪',
    'BTC': '₿',
    'ETH': 'Ξ'
  }
  return icons[currencyCode] || '💰'
}

const formatCurrency = (amount, currency = 'SAR') => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount)
}

const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  return new Intl.DateTimeFormat('ar-SA', { ...defaultOptions, ...options }).format(new Date(date))
}

const convertToUSD = (amount, rate) => {
  return amount * rate
}

const getTransactionTypeIcon = (type) => {
  const icons = {
    transfer: '💸',
    deposit: '💰',
    withdrawal: '🏧',
    exchange: '💱'
  }
  return icons[type] || '💳'
}

const getTransactionStatusColor = (status) => {
  const colors = {
    pending: 'text-yellow-600 bg-yellow-100',
    processing: 'text-blue-600 bg-blue-100',
    completed: 'text-green-600 bg-green-100',
    failed: 'text-red-600 bg-red-100',
    cancelled: 'text-gray-600 bg-gray-100',
    blocked: 'text-red-800 bg-red-200'
  }
  return colors[status] || 'text-gray-600 bg-gray-100'
}

const addMoney = () => {
  emit('action', { type: 'deposit', wallet: props.wallet })
  router.visit(`/wallets/${props.wallet.id}/deposit`)
}

const sendMoney = () => {
  emit('action', { type: 'transfer', wallet: props.wallet })
  router.visit('/transactions/create', {
    data: { 
      currency_id: props.wallet.currency_id,
      payment_method: 'wallet'
    }
  })
}

const withdrawMoney = () => {
  emit('action', { type: 'withdrawal', wallet: props.wallet })
  router.visit(`/wallets/${props.wallet.id}/withdraw`)
}

const viewAllTransactions = () => {
  router.visit(`/wallets/${props.wallet.id}/transactions`)
}

const loadWalletStats = async () => {
  try {
    loading.value = true
    
    // This would be an API call to get wallet statistics
    const response = await fetch(`/api/wallets/${props.wallet.id}/stats`)
    const data = await response.json()
    
    walletStats.value = data.stats
    
    if (props.showRecentTransactions) {
      recentTransactions.value = data.recent_transactions || []
    }
  } catch (error) {
    console.error('Failed to load wallet stats:', error)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (props.showRecentTransactions) {
    loadWalletStats()
  }
})
</script>

<style scoped>
.rtl {
  direction: rtl;
}

.rtl .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Custom gradient backgrounds for different currencies */
.wallet-sar {
  background: linear-gradient(135deg, #10b981, #059669);
}

.wallet-usd {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.wallet-eur {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.wallet-btc {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.wallet-eth {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}
</style>
