<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use App\Models\AuditLog;
use App\Services\NotificationService;
use App\Services\AuditService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TransactionManagementController extends Controller
{
    protected NotificationService $notificationService;
    protected AuditService $auditService;

    public function __construct(
        NotificationService $notificationService,
        AuditService $auditService
    ) {
        $this->notificationService = $notificationService;
        $this->auditService = $auditService;
    }

    /**
     * Get all transactions with advanced filtering
     */
    public function index(Request $request): JsonResponse
    {
        $query = Transaction::with(['user', 'senderCountry', 'recipientCountry']);

        // Apply filters
        $this->applyFilters($query, $request);

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $transactions = $query->paginate($request->get('per_page', 25));

        return response()->json([
            'success' => true,
            'data' => [
                'transactions' => $transactions->items(),
                'pagination' => [
                    'current_page' => $transactions->currentPage(),
                    'last_page' => $transactions->lastPage(),
                    'per_page' => $transactions->perPage(),
                    'total' => $transactions->total(),
                ],
                'filters' => $this->getAvailableFilters(),
            ],
        ]);
    }

    /**
     * Get transaction details
     */
    public function show(int $id): JsonResponse
    {
        $transaction = Transaction::with([
            'user',
            'senderCountry',
            'recipientCountry',
            'auditLogs' => function($query) {
                $query->orderBy('created_at', 'desc');
            }
        ])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'transaction' => $this->formatTransactionDetails($transaction),
                'audit_trail' => $transaction->auditLogs,
                'risk_analysis' => $this->getRiskAnalysis($transaction),
                'related_transactions' => $this->getRelatedTransactions($transaction),
            ],
        ]);
    }

    /**
     * Approve a pending transaction
     */
    public function approve(int $id, Request $request): JsonResponse
    {
        $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $transaction = Transaction::findOrFail($id);
            $admin = Auth::user();

            if (!in_array($transaction->status, ['pending', 'pending_review'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction cannot be approved in current status',
                ], 400);
            }

            // Update transaction
            $transaction->update([
                'status' => 'processing',
                'processed_at' => now(),
                'processed_by' => $admin->id,
                'admin_notes' => $request->notes,
            ]);

            // Log the action
            $this->auditService->logAdminAction('transaction_approved', [
                'transaction_id' => $transaction->transaction_id,
                'admin_id' => $admin->id,
                'notes' => $request->notes,
                'old_values' => ['status' => 'pending'],
                'new_values' => ['status' => 'processing'],
            ]);

            // Send notification to user
            $this->notificationService->sendTransactionProcessedNotification($transaction);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transaction approved successfully',
                'data' => [
                    'transaction' => $this->formatTransactionDetails($transaction),
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve transaction',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reject a pending transaction
     */
    public function reject(int $id, Request $request): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:1000',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $transaction = Transaction::findOrFail($id);
            $admin = Auth::user();

            if (!in_array($transaction->status, ['pending', 'pending_review'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction cannot be rejected in current status',
                ], 400);
            }

            // Update transaction
            $transaction->update([
                'status' => 'rejected',
                'rejected_at' => now(),
                'rejected_by' => $admin->id,
                'rejection_reason' => $request->reason,
                'admin_notes' => $request->notes,
            ]);

            // Log the action
            $this->auditService->logAdminAction('transaction_rejected', [
                'transaction_id' => $transaction->transaction_id,
                'admin_id' => $admin->id,
                'reason' => $request->reason,
                'notes' => $request->notes,
                'old_values' => ['status' => $transaction->getOriginal('status')],
                'new_values' => ['status' => 'rejected'],
            ]);

            // Send notification to user
            $this->notificationService->sendTransactionFailedNotification($transaction);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transaction rejected successfully',
                'data' => [
                    'transaction' => $this->formatTransactionDetails($transaction),
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject transaction',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark transaction as completed
     */
    public function complete(int $id, Request $request): JsonResponse
    {
        $request->validate([
            'completion_notes' => 'nullable|string|max:1000',
            'actual_delivery_date' => 'nullable|date',
        ]);

        try {
            DB::beginTransaction();

            $transaction = Transaction::findOrFail($id);
            $admin = Auth::user();

            if ($transaction->status !== 'processing') {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction must be in processing status to complete',
                ], 400);
            }

            // Update transaction
            $transaction->update([
                'status' => 'completed',
                'completed_at' => now(),
                'completed_by' => $admin->id,
                'completion_notes' => $request->completion_notes,
                'actual_delivery_date' => $request->actual_delivery_date ?? now(),
            ]);

            // Log the action
            $this->auditService->logAdminAction('transaction_completed', [
                'transaction_id' => $transaction->transaction_id,
                'admin_id' => $admin->id,
                'completion_notes' => $request->completion_notes,
                'old_values' => ['status' => 'processing'],
                'new_values' => ['status' => 'completed'],
            ]);

            // Send notification to user
            $this->notificationService->sendTransactionCompletedNotification($transaction);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transaction completed successfully',
                'data' => [
                    'transaction' => $this->formatTransactionDetails($transaction),
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete transaction',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Flag transaction for review
     */
    public function flagForReview(int $id, Request $request): JsonResponse
    {
        $request->validate([
            'flag_reason' => 'required|string|max:1000',
            'priority' => 'required|in:low,medium,high,urgent',
        ]);

        try {
            $transaction = Transaction::findOrFail($id);
            $admin = Auth::user();

            $transaction->update([
                'status' => 'pending_review',
                'flag_reason' => $request->flag_reason,
                'priority' => $request->priority,
                'flagged_by' => $admin->id,
                'flagged_at' => now(),
            ]);

            // Log the action
            $this->auditService->logAdminAction('transaction_flagged', [
                'transaction_id' => $transaction->transaction_id,
                'admin_id' => $admin->id,
                'flag_reason' => $request->flag_reason,
                'priority' => $request->priority,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Transaction flagged for review',
                'data' => [
                    'transaction' => $this->formatTransactionDetails($transaction),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to flag transaction',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk operations on transactions
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:approve,reject,flag,export',
            'transaction_ids' => 'required|array|min:1',
            'transaction_ids.*' => 'integer|exists:transactions,id',
            'reason' => 'required_if:action,reject|string|max:1000',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $transactions = Transaction::whereIn('id', $request->transaction_ids)->get();
            $admin = Auth::user();
            $results = [];

            foreach ($transactions as $transaction) {
                try {
                    switch ($request->action) {
                        case 'approve':
                            if (in_array($transaction->status, ['pending', 'pending_review'])) {
                                $transaction->update([
                                    'status' => 'processing',
                                    'processed_at' => now(),
                                    'processed_by' => $admin->id,
                                    'admin_notes' => $request->notes,
                                ]);
                                $results[] = ['id' => $transaction->id, 'status' => 'approved'];
                            } else {
                                $results[] = ['id' => $transaction->id, 'status' => 'skipped', 'reason' => 'Invalid status'];
                            }
                            break;

                        case 'reject':
                            if (in_array($transaction->status, ['pending', 'pending_review'])) {
                                $transaction->update([
                                    'status' => 'rejected',
                                    'rejected_at' => now(),
                                    'rejected_by' => $admin->id,
                                    'rejection_reason' => $request->reason,
                                    'admin_notes' => $request->notes,
                                ]);
                                $results[] = ['id' => $transaction->id, 'status' => 'rejected'];
                            } else {
                                $results[] = ['id' => $transaction->id, 'status' => 'skipped', 'reason' => 'Invalid status'];
                            }
                            break;

                        case 'flag':
                            $transaction->update([
                                'status' => 'pending_review',
                                'flag_reason' => $request->reason ?? 'Bulk flagged for review',
                                'flagged_by' => $admin->id,
                                'flagged_at' => now(),
                            ]);
                            $results[] = ['id' => $transaction->id, 'status' => 'flagged'];
                            break;
                    }
                } catch (\Exception $e) {
                    $results[] = ['id' => $transaction->id, 'status' => 'error', 'reason' => $e->getMessage()];
                }
            }

            // Log bulk action
            $this->auditService->logAdminAction('bulk_transaction_action', [
                'action' => $request->action,
                'admin_id' => $admin->id,
                'transaction_count' => count($request->transaction_ids),
                'results' => $results,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bulk action completed',
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total' => count($request->transaction_ids),
                        'successful' => count(array_filter($results, fn($r) => !in_array($r['status'], ['error', 'skipped']))),
                        'failed' => count(array_filter($results, fn($r) => $r['status'] === 'error')),
                        'skipped' => count(array_filter($results, fn($r) => $r['status'] === 'skipped')),
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, Request $request): void
    {
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        if ($request->has('currency_from')) {
            $query->where('currency_from', $request->currency_from);
        }

        if ($request->has('currency_to')) {
            $query->where('currency_to', $request->currency_to);
        }

        if ($request->has('amount_min')) {
            $query->where('amount', '>=', $request->amount_min);
        }

        if ($request->has('amount_max')) {
            $query->where('amount', '<=', $request->amount_max);
        }

        if ($request->has('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('sender_name', 'like', "%{$search}%")
                  ->orWhere('recipient_name', 'like', "%{$search}%")
                  ->orWhere('sender_phone', 'like', "%{$search}%")
                  ->orWhere('recipient_phone', 'like', "%{$search}%");
            });
        }
    }

    /**
     * Get available filters
     */
    protected function getAvailableFilters(): array
    {
        return [
            'statuses' => ['pending', 'pending_review', 'processing', 'completed', 'failed', 'cancelled', 'rejected'],
            'risk_levels' => ['low', 'medium', 'high'],
            'currencies' => \App\Models\Currency::where('is_active', true)->pluck('code')->toArray(),
            'payment_methods' => ['cash', 'bank_transfer', 'card', 'wallet', 'crypto'],
        ];
    }

    /**
     * Format transaction details
     */
    protected function formatTransactionDetails(Transaction $transaction): array
    {
        return [
            'id' => $transaction->id,
            'transaction_id' => $transaction->transaction_id,
            'status' => $transaction->status,
            'type' => $transaction->type,
            'priority' => $transaction->priority,
            'risk_level' => $transaction->risk_level,
            'risk_score' => $transaction->risk_score,
            'amount' => $transaction->amount,
            'fee' => $transaction->fee,
            'total_amount' => $transaction->total_amount,
            'recipient_amount' => $transaction->recipient_amount,
            'exchange_rate' => $transaction->exchange_rate,
            'currency_from' => $transaction->currency_from,
            'currency_to' => $transaction->currency_to,
            'sender' => [
                'name' => $transaction->sender_name,
                'phone' => $transaction->sender_phone,
                'email' => $transaction->sender_email,
                'country' => $transaction->senderCountry?->name_en,
                'address' => $transaction->sender_address,
                'id_number' => $transaction->sender_id_number,
                'id_type' => $transaction->sender_id_type,
            ],
            'recipient' => [
                'name' => $transaction->recipient_name,
                'phone' => $transaction->recipient_phone,
                'email' => $transaction->recipient_email,
                'country' => $transaction->recipientCountry?->name_en,
                'address' => $transaction->recipient_address,
                'bank_name' => $transaction->recipient_bank_name,
                'bank_account' => $transaction->recipient_bank_account,
            ],
            'payment_method' => $transaction->payment_method,
            'delivery_method' => $transaction->delivery_method,
            'purpose' => $transaction->purpose,
            'notes' => $transaction->notes,
            'admin_notes' => $transaction->admin_notes,
            'flag_reason' => $transaction->flag_reason,
            'rejection_reason' => $transaction->rejection_reason,
            'user' => [
                'id' => $transaction->user->id,
                'name' => $transaction->user->first_name . ' ' . $transaction->user->last_name,
                'email' => $transaction->user->email,
                'phone' => $transaction->user->phone,
            ],
            'timestamps' => [
                'created_at' => $transaction->created_at,
                'processed_at' => $transaction->processed_at,
                'completed_at' => $transaction->completed_at,
                'cancelled_at' => $transaction->cancelled_at,
                'rejected_at' => $transaction->rejected_at,
                'flagged_at' => $transaction->flagged_at,
                'expected_delivery_date' => $transaction->expected_delivery_date,
                'actual_delivery_date' => $transaction->actual_delivery_date,
            ],
        ];
    }

    /**
     * Get risk analysis for transaction
     */
    protected function getRiskAnalysis(Transaction $transaction): array
    {
        return [
            'risk_level' => $transaction->risk_level,
            'risk_score' => $transaction->risk_score,
            'risk_factors' => $transaction->risk_factors ?? [],
            'user_risk_profile' => [
                'total_transactions' => Transaction::where('user_id', $transaction->user_id)->count(),
                'failed_transactions' => Transaction::where('user_id', $transaction->user_id)
                    ->where('status', 'failed')->count(),
                'average_amount' => Transaction::where('user_id', $transaction->user_id)
                    ->where('status', 'completed')->avg('amount'),
                'account_age_days' => $transaction->user->created_at->diffInDays(now()),
            ],
        ];
    }

    /**
     * Get related transactions
     */
    protected function getRelatedTransactions(Transaction $transaction): array
    {
        return Transaction::where('user_id', $transaction->user_id)
            ->where('id', '!=', $transaction->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get(['id', 'transaction_id', 'status', 'amount', 'currency_from', 'created_at'])
            ->toArray();
    }
}
