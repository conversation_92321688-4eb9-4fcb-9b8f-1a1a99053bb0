<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Commission;
use Carbon\Carbon;

class AgentController extends Controller
{
    /**
     * Get agent dashboard data.
     */
    public function dashboard(Request $request): JsonResponse
    {
        $agent = $request->user();
        
        // Get dashboard statistics
        $stats = [
            'total_transactions' => $agent->agentTransactions()->count(),
            'total_commission' => $agent->commissions()->sum('amount'),
            'monthly_transactions' => $agent->agentTransactions()
                ->whereMonth('created_at', now()->month)
                ->count(),
            'monthly_commission' => $agent->commissions()
                ->whereMonth('created_at', now()->month)
                ->sum('amount'),
            'pending_transactions' => $agent->agentTransactions()
                ->where('status', 'pending')
                ->count(),
        ];

        // Get recent transactions
        $recentTransactions = $agent->agentTransactions()
            ->with(['sender', 'currency'])
            ->latest()
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'recent_transactions' => $recentTransactions,
            ],
        ]);
    }

    /**
     * Get agent transactions.
     */
    public function transactions(Request $request): JsonResponse
    {
        $agent = $request->user();
        
        $query = $agent->agentTransactions()
            ->with(['sender', 'currency', 'receiver']);

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $transactions = $query->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $transactions,
        ]);
    }

    /**
     * Process a transaction.
     */
    public function processTransaction(Request $request, int $id): JsonResponse
    {
        $agent = $request->user();
        
        $transaction = Transaction::where('id', $id)
            ->where('agent_id', $agent->id)
            ->where('status', 'pending')
            ->first();

        if (!$transaction) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction not found or cannot be processed',
            ], 404);
        }

        $request->validate([
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            if ($request->action === 'approve') {
                $transaction->update([
                    'status' => 'processing',
                    'processed_by' => $agent->id,
                    'processed_at' => now(),
                    'agent_notes' => $request->notes,
                ]);

                // Calculate and create commission
                $this->createCommission($agent, $transaction);

                $message = 'Transaction approved successfully';
            } else {
                $transaction->update([
                    'status' => 'rejected',
                    'processed_by' => $agent->id,
                    'processed_at' => now(),
                    'agent_notes' => $request->notes,
                    'failure_reason' => 'Rejected by agent',
                ]);

                $message = 'Transaction rejected successfully';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $transaction->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process transaction: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get agent customers.
     */
    public function customers(Request $request): JsonResponse
    {
        $agent = $request->user();
        
        $query = User::whereHas('sentTransactions', function ($q) use ($agent) {
            $q->where('agent_id', $agent->id);
        })->with(['country']);

        // Apply search filter
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $customers = $query->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * Verify a customer.
     */
    public function verifyCustomer(Request $request, int $id): JsonResponse
    {
        $agent = $request->user();
        
        $customer = User::find($id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        $request->validate([
            'verification_status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            if ($request->verification_status === 'verified') {
                $customer->update([
                    'kyc_verified_at' => now(),
                    'kyc_verified_by' => $agent->id,
                    'kyc_notes' => $request->notes,
                ]);

                $message = 'Customer verified successfully';
            } else {
                $customer->update([
                    'kyc_verified_at' => null,
                    'kyc_verified_by' => null,
                    'kyc_notes' => $request->notes,
                ]);

                $message = 'Customer verification rejected';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $customer->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to verify customer: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get agent commission data.
     */
    public function commission(Request $request): JsonResponse
    {
        $agent = $request->user();
        
        $query = $agent->commissions()->with(['transaction']);

        // Apply date filters
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $commissions = $query->paginate(20);

        // Calculate totals
        $totalCommission = $agent->commissions()->sum('amount');
        $monthlyCommission = $agent->commissions()
            ->whereMonth('created_at', now()->month)
            ->sum('amount');

        return response()->json([
            'success' => true,
            'data' => [
                'commissions' => $commissions,
                'totals' => [
                    'total_commission' => $totalCommission,
                    'monthly_commission' => $monthlyCommission,
                ],
            ],
        ]);
    }

    /**
     * Create commission for agent.
     */
    private function createCommission(User $agent, Transaction $transaction): void
    {
        $commissionRate = $agent->commission_rate ?? 0.01; // 1% default
        $commissionAmount = $transaction->fee * $commissionRate;

        Commission::create([
            'agent_id' => $agent->id,
            'transaction_id' => $transaction->id,
            'amount' => $commissionAmount,
            'rate' => $commissionRate,
            'status' => 'pending',
        ]);
    }
}
