<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

try {
    $users = User::all();
    echo "Found " . $users->count() . " users:\n";
    
    foreach ($users as $user) {
        echo "- " . $user->email . " (" . $user->user_type . ")\n";
    }
    
    if ($users->count() == 0) {
        echo "No users found. Creating admin user...\n";
        
        $country = \App\Models\Country::first();
        if (!$country) {
            echo "No countries found!\n";
            exit(1);
        }
        
        $user = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => bcrypt('password123'),
            'user_type' => 'admin',
            'status' => 'active',
            'country_id' => $country->id,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);
        
        echo "✅ Admin user created: " . $user->email . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
