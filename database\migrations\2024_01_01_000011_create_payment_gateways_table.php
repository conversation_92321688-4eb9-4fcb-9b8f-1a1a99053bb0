<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // PayPal, Stripe, Wise, etc.
            $table->string('code')->unique(); // paypal, stripe, wise, etc.
            $table->string('type'); // bank, card, crypto, digital_wallet
            $table->boolean('is_active')->default(true);
            $table->json('supported_countries')->nullable();
            $table->json('supported_currencies')->nullable();
            $table->decimal('min_amount', 15, 2)->default(1.00);
            $table->decimal('max_amount', 15, 2)->default(100000.00);
            $table->decimal('fee_percentage', 5, 4)->default(0.0000);
            $table->decimal('fixed_fee', 15, 2)->default(0.00);
            $table->json('configuration')->nullable(); // API keys, endpoints, etc.
            $table->string('api_endpoint')->nullable();
            $table->string('webhook_url')->nullable();
            $table->enum('environment', ['sandbox', 'production'])->default('sandbox');
            $table->integer('processing_time_minutes')->default(0); // 0 = instant
            $table->text('description')->nullable();
            $table->string('logo_url')->nullable();
            $table->timestamps();
            
            $table->index(['is_active', 'type']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_gateways');
    }
};
