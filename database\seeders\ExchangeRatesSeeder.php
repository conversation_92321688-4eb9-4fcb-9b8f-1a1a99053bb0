<?php

namespace Database\Seeders;

use App\Models\ExchangeRate;
use App\Models\Currency;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExchangeRatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get currency IDs
        $usd = Currency::where('code', 'USD')->first();
        $sar = Currency::where('code', 'SAR')->first();
        $aed = Currency::where('code', 'AED')->first();

        if (!$usd || !$sar || !$aed) {
            $this->command->error('Required currencies not found. Please run CurrenciesSeeder first.');
            return;
        }

        // Create basic exchange rates
        $basicRates = [
            ['from' => $usd, 'to' => $sar, 'rate' => 3.75],
            ['from' => $usd, 'to' => $aed, 'rate' => 3.67],
            ['from' => $sar, 'to' => $usd, 'rate' => 0.27],
            ['from' => $aed, 'to' => $usd, 'rate' => 0.27],
        ];

        foreach ($basicRates as $basicRate) {
            $spread = $basicRate['rate'] * 0.01; // 1% spread

            ExchangeRate::updateOrCreate(
                [
                    'from_currency_id' => $basicRate['from']->id,
                    'to_currency_id' => $basicRate['to']->id,
                ],
                [
                    'rate' => $basicRate['rate'],
                    'buy_rate' => $basicRate['rate'] + $spread,
                    'sell_rate' => $basicRate['rate'] - $spread,
                    'spread' => $spread * 2,
                    'source' => 'manual',
                    'is_active' => true,
                    'last_updated' => now(),
                    'metadata' => [
                        'provider' => 'system',
                        'confidence' => 95.0,
                        'volatility' => 'medium',
                    ],
                ]
            );
        }

        $this->command->info('Exchange rates seeded successfully!');
    }
}
