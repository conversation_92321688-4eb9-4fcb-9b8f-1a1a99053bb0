<?php

namespace App\Services;

use App\Models\User;
use App\Models\Transaction;
use App\Models\AnalyticsEvent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AnalyticsService
{
    private array $config;
    private array $metrics;

    public function __construct()
    {
        $this->config = config('analytics', []);
        $this->metrics = [];
    }

    /**
     * Track user event
     */
    public function trackEvent(string $event, array $properties = [], ?int $userId = null): bool
    {
        try {
            $eventData = [
                'event' => $event,
                'properties' => $properties,
                'user_id' => $userId,
                'session_id' => session()->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'url' => request()->fullUrl(),
                'referrer' => request()->header('referer'),
                'timestamp' => now(),
            ];

            // Store in database
            AnalyticsEvent::create($eventData);

            // Send to external analytics services
            $this->sendToExternalServices($eventData);

            return true;

        } catch (\Exception $e) {
            Log::error('Analytics tracking failed', [
                'event' => $event,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get dashboard analytics
     */
    public function getDashboardAnalytics(array $filters = []): array
    {
        $cacheKey = 'dashboard_analytics_' . md5(serialize($filters));
        
        return Cache::remember($cacheKey, 300, function () use ($filters) {
            $dateRange = $this->getDateRange($filters);
            
            return [
                'overview' => $this->getOverviewMetrics($dateRange),
                'transactions' => $this->getTransactionAnalytics($dateRange),
                'users' => $this->getUserAnalytics($dateRange),
                'revenue' => $this->getRevenueAnalytics($dateRange),
                'performance' => $this->getPerformanceMetrics($dateRange),
                'security' => $this->getSecurityMetrics($dateRange),
                'trends' => $this->getTrendAnalytics($dateRange),
            ];
        });
    }

    /**
     * Get overview metrics
     */
    private function getOverviewMetrics(array $dateRange): array
    {
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        return [
            'total_transactions' => Transaction::whereBetween('created_at', [$startDate, $endDate])->count(),
            'total_volume' => Transaction::whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('amount'),
            'total_users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_users' => User::whereHas('transactions', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })->count(),
            'success_rate' => $this->calculateSuccessRate($startDate, $endDate),
            'average_transaction_value' => $this->getAverageTransactionValue($startDate, $endDate),
        ];
    }

    /**
     * Get transaction analytics
     */
    private function getTransactionAnalytics(array $dateRange): array
    {
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        return [
            'by_status' => Transaction::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('status, COUNT(*) as count, SUM(amount) as volume')
                ->groupBy('status')
                ->get()
                ->keyBy('status'),
            'by_currency' => Transaction::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('currency, COUNT(*) as count, SUM(amount) as volume')
                ->groupBy('currency')
                ->get()
                ->keyBy('currency'),
            'by_gateway' => Transaction::whereBetween('created_at', [$startDate, $endDate])
                ->whereNotNull('gateway_name')
                ->selectRaw('gateway_name, COUNT(*) as count, SUM(amount) as volume')
                ->groupBy('gateway_name')
                ->get()
                ->keyBy('gateway_name'),
            'hourly_distribution' => $this->getHourlyTransactionDistribution($startDate, $endDate),
            'daily_trends' => $this->getDailyTransactionTrends($startDate, $endDate),
            'top_corridors' => $this->getTopTransactionCorridors($startDate, $endDate),
        ];
    }

    /**
     * Get user analytics
     */
    private function getUserAnalytics(array $dateRange): array
    {
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        return [
            'new_registrations' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'kyc_completion_rate' => $this->getKycCompletionRate($startDate, $endDate),
            'user_retention' => $this->getUserRetentionMetrics($startDate, $endDate),
            'by_country' => User::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('country, COUNT(*) as count')
                ->groupBy('country')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            'activity_levels' => $this->getUserActivityLevels($startDate, $endDate),
            'demographics' => $this->getUserDemographics($startDate, $endDate),
        ];
    }

    /**
     * Get revenue analytics
     */
    private function getRevenueAnalytics(array $dateRange): array
    {
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        return [
            'total_revenue' => $this->calculateTotalRevenue($startDate, $endDate),
            'revenue_by_source' => $this->getRevenueBySource($startDate, $endDate),
            'average_revenue_per_user' => $this->getAverageRevenuePerUser($startDate, $endDate),
            'revenue_trends' => $this->getRevenueTrends($startDate, $endDate),
            'fee_analysis' => $this->getFeeAnalysis($startDate, $endDate),
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(array $dateRange): array
    {
        return [
            'api_response_times' => $this->getApiResponseTimes($dateRange),
            'error_rates' => $this->getErrorRates($dateRange),
            'system_uptime' => $this->getSystemUptime($dateRange),
            'database_performance' => $this->getDatabasePerformance($dateRange),
            'cache_hit_rates' => $this->getCacheHitRates($dateRange),
        ];
    }

    /**
     * Get security metrics
     */
    private function getSecurityMetrics(array $dateRange): array
    {
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        return [
            'fraud_detection_rate' => $this->getFraudDetectionRate($startDate, $endDate),
            'blocked_transactions' => $this->getBlockedTransactions($startDate, $endDate),
            'security_incidents' => $this->getSecurityIncidents($startDate, $endDate),
            'failed_login_attempts' => $this->getFailedLoginAttempts($startDate, $endDate),
            'suspicious_activities' => $this->getSuspiciousActivities($startDate, $endDate),
        ];
    }

    /**
     * Get trend analytics
     */
    private function getTrendAnalytics(array $dateRange): array
    {
        return [
            'growth_rate' => $this->calculateGrowthRate($dateRange),
            'seasonal_patterns' => $this->getSeasonalPatterns($dateRange),
            'user_behavior_trends' => $this->getUserBehaviorTrends($dateRange),
            'market_trends' => $this->getMarketTrends($dateRange),
        ];
    }

    /**
     * Calculate success rate
     */
    private function calculateSuccessRate(string $startDate, string $endDate): float
    {
        $total = Transaction::whereBetween('created_at', [$startDate, $endDate])->count();
        $successful = Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->count();

        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    /**
     * Get average transaction value
     */
    private function getAverageTransactionValue(string $startDate, string $endDate): float
    {
        return Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->avg('amount') ?? 0;
    }

    /**
     * Get hourly transaction distribution
     */
    private function getHourlyTransactionDistribution(string $startDate, string $endDate): array
    {
        return Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour')
            ->toArray();
    }

    /**
     * Get daily transaction trends
     */
    private function getDailyTransactionTrends(string $startDate, string $endDate): array
    {
        return Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as volume')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get top transaction corridors
     */
    private function getTopTransactionCorridors(string $startDate, string $endDate): array
    {
        return Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('sender_country, receiver_country, COUNT(*) as count, SUM(amount) as volume')
            ->groupBy('sender_country', 'receiver_country')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get KYC completion rate
     */
    private function getKycCompletionRate(string $startDate, string $endDate): float
    {
        $total = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $verified = User::whereBetween('created_at', [$startDate, $endDate])
            ->where('kyc_status', 'verified')
            ->count();

        return $total > 0 ? round(($verified / $total) * 100, 2) : 0;
    }

    /**
     * Get user retention metrics
     */
    private function getUserRetentionMetrics(string $startDate, string $endDate): array
    {
        // Calculate 7-day, 30-day retention rates
        $users = User::whereBetween('created_at', [$startDate, $endDate])->get();
        
        $retention7Day = 0;
        $retention30Day = 0;
        
        foreach ($users as $user) {
            $hasActivity7Days = Transaction::where('sender_id', $user->id)
                ->whereBetween('created_at', [
                    $user->created_at->addDays(7),
                    $user->created_at->addDays(14)
                ])
                ->exists();
                
            $hasActivity30Days = Transaction::where('sender_id', $user->id)
                ->whereBetween('created_at', [
                    $user->created_at->addDays(30),
                    $user->created_at->addDays(37)
                ])
                ->exists();
                
            if ($hasActivity7Days) $retention7Day++;
            if ($hasActivity30Days) $retention30Day++;
        }
        
        $totalUsers = $users->count();
        
        return [
            '7_day' => $totalUsers > 0 ? round(($retention7Day / $totalUsers) * 100, 2) : 0,
            '30_day' => $totalUsers > 0 ? round(($retention30Day / $totalUsers) * 100, 2) : 0,
        ];
    }

    /**
     * Get user activity levels
     */
    private function getUserActivityLevels(string $startDate, string $endDate): array
    {
        return [
            'highly_active' => User::whereHas('transactions', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->havingRaw('COUNT(*) >= 10');
            })->count(),
            'moderately_active' => User::whereHas('transactions', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->havingRaw('COUNT(*) BETWEEN 3 AND 9');
            })->count(),
            'low_active' => User::whereHas('transactions', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->havingRaw('COUNT(*) BETWEEN 1 AND 2');
            })->count(),
        ];
    }

    /**
     * Send to external analytics services
     */
    private function sendToExternalServices(array $eventData): void
    {
        // Google Analytics
        if ($this->config['google_analytics']['enabled'] ?? false) {
            $this->sendToGoogleAnalytics($eventData);
        }

        // Mixpanel
        if ($this->config['mixpanel']['enabled'] ?? false) {
            $this->sendToMixpanel($eventData);
        }

        // Custom analytics endpoint
        if ($this->config['custom_endpoint']['enabled'] ?? false) {
            $this->sendToCustomEndpoint($eventData);
        }
    }

    /**
     * Get date range from filters
     */
    private function getDateRange(array $filters): array
    {
        $period = $filters['period'] ?? '7d';
        
        switch ($period) {
            case '1d':
                return [
                    'start' => now()->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
            case '7d':
                return [
                    'start' => now()->subDays(7)->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
            case '30d':
                return [
                    'start' => now()->subDays(30)->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
            case '90d':
                return [
                    'start' => now()->subDays(90)->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
            case 'custom':
                return [
                    'start' => Carbon::parse($filters['start_date'])->startOfDay(),
                    'end' => Carbon::parse($filters['end_date'])->endOfDay(),
                ];
            default:
                return [
                    'start' => now()->subDays(7)->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
        }
    }

    /**
     * Generate analytics report
     */
    public function generateReport(array $filters = []): array
    {
        $analytics = $this->getDashboardAnalytics($filters);
        
        return [
            'generated_at' => now()->toISOString(),
            'period' => $filters['period'] ?? '7d',
            'filters' => $filters,
            'data' => $analytics,
            'insights' => $this->generateInsights($analytics),
            'recommendations' => $this->generateRecommendations($analytics),
        ];
    }

    /**
     * Generate insights from analytics data
     */
    private function generateInsights(array $analytics): array
    {
        $insights = [];

        // Transaction insights
        if ($analytics['overview']['success_rate'] < 95) {
            $insights[] = [
                'type' => 'warning',
                'category' => 'transactions',
                'message' => 'Transaction success rate is below 95%. Consider investigating payment gateway issues.',
                'metric' => 'success_rate',
                'value' => $analytics['overview']['success_rate'],
            ];
        }

        // User insights
        if ($analytics['users']['kyc_completion_rate'] < 80) {
            $insights[] = [
                'type' => 'info',
                'category' => 'users',
                'message' => 'KYC completion rate is low. Consider improving the onboarding process.',
                'metric' => 'kyc_completion_rate',
                'value' => $analytics['users']['kyc_completion_rate'],
            ];
        }

        return $insights;
    }

    /**
     * Generate recommendations
     */
    private function generateRecommendations(array $analytics): array
    {
        $recommendations = [];

        // Performance recommendations
        if (isset($analytics['performance']['api_response_times']['average']) && 
            $analytics['performance']['api_response_times']['average'] > 1000) {
            $recommendations[] = [
                'category' => 'performance',
                'priority' => 'high',
                'title' => 'Optimize API Response Times',
                'description' => 'API response times are above 1 second. Consider optimizing database queries and implementing caching.',
            ];
        }

        return $recommendations;
    }
}
