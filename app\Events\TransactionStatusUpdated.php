<?php

namespace App\Events;

use App\Models\Transaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TransactionStatusUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Transaction $transaction;
    public string $previousStatus;

    /**
     * Create a new event instance.
     */
    public function __construct(Transaction $transaction, string $previousStatus)
    {
        $this->transaction = $transaction;
        $this->previousStatus = $previousStatus;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel('user.' . $this->transaction->sender_id),
            new PrivateChannel('transaction.' . $this->transaction->id),
        ];

        // Also broadcast to receiver if they have an account
        if ($this->transaction->receiver_id) {
            $channels[] = new PrivateChannel('user.' . $this->transaction->receiver_id);
        }

        // Broadcast to admin channel for monitoring
        $channels[] = new PrivateChannel('admin.transactions');

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'transaction' => [
                'id' => $this->transaction->id,
                'transaction_number' => $this->transaction->transaction_number,
                'amount' => $this->transaction->amount,
                'currency' => $this->transaction->currency->code,
                'status' => $this->transaction->status,
                'previous_status' => $this->previousStatus,
                'receiver_name' => $this->transaction->receiver_name,
                'updated_at' => $this->transaction->updated_at,
            ],
            'message' => $this->getStatusMessage(),
            'timestamp' => now(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'transaction.status.updated';
    }

    /**
     * Get status message based on current status.
     */
    private function getStatusMessage(): string
    {
        switch ($this->transaction->status) {
            case 'completed':
                return 'Transaction completed successfully';
            case 'failed':
                return 'Transaction failed';
            case 'cancelled':
                return 'Transaction cancelled';
            case 'pending_review':
                return 'Transaction is under review';
            case 'blocked':
                return 'Transaction blocked for security reasons';
            default:
                return 'Transaction status updated';
        }
    }
}
