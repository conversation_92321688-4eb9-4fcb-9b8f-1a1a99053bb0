<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Currency;
use App\Models\Country;
use App\Models\Wallet;
use App\Models\Transaction;
use App\Services\TransactionService;
use App\Services\FraudDetectionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class TransactionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TransactionService $transactionService;
    protected FraudDetectionService $fraudService;
    protected User $user;
    protected Currency $currency;
    protected Country $country;
    protected Wallet $wallet;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->fraudService = Mockery::mock(FraudDetectionService::class);
        $this->transactionService = new TransactionService($this->fraudService);
        
        $this->country = Country::factory()->create();
        $this->currency = Currency::factory()->create(['code' => 'USD']);
        $this->user = User::factory()->create([
            'country_id' => $this->country->id,
            'status' => 'active',
            'daily_limit' => 10000,
            'monthly_limit' => 100000,
        ]);
        $this->wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'balance' => 5000,
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_process_transaction_successfully()
    {
        $this->fraudService->shouldReceive('analyzeTransaction')
                          ->once()
                          ->andReturn([
                              'risk_score' => 20,
                              'risk_level' => 'low',
                              'risk_factors' => [],
                              'requires_review' => false,
                              'auto_block' => false,
                          ]);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'category' => 'personal',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'receiver_phone' => '+966501234567',
            'receiver_country_id' => $this->country->id,
            'payment_method' => 'wallet',
        ];

        $result = $this->transactionService->processTransaction($transactionData);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('transaction', $result);
        $this->assertArrayHasKey('fraud_analysis', $result);

        $this->assertDatabaseHas('transactions', [
            'sender_id' => $this->user->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'status' => 'pending',
        ]);
    }

    public function test_process_transaction_with_insufficient_balance_fails()
    {
        $this->wallet->update(['balance' => 500]);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'payment_method' => 'wallet',
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Insufficient balance');

        $this->transactionService->processTransaction($transactionData);
    }

    public function test_process_transaction_with_inactive_user_fails()
    {
        $this->user->update(['status' => 'suspended']);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'payment_method' => 'wallet',
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Sender account is not active');

        $this->transactionService->processTransaction($transactionData);
    }

    public function test_process_transaction_exceeding_daily_limit_fails()
    {
        $this->user->update(['daily_limit' => 500]);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'payment_method' => 'wallet',
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Daily limit exceeded');

        $this->transactionService->processTransaction($transactionData);
    }

    public function test_process_transaction_with_high_fraud_risk_gets_blocked()
    {
        $this->fraudService->shouldReceive('analyzeTransaction')
                          ->once()
                          ->andReturn([
                              'risk_score' => 90,
                              'risk_level' => 'high',
                              'risk_factors' => ['High amount', 'New user'],
                              'requires_review' => true,
                              'auto_block' => true,
                          ]);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'payment_method' => 'wallet',
        ];

        $result = $this->transactionService->processTransaction($transactionData);

        $this->assertFalse($result['success']);
        $this->assertEquals('Transaction blocked due to high risk', $result['message']);

        $this->assertDatabaseHas('transactions', [
            'sender_id' => $this->user->id,
            'status' => 'blocked',
        ]);
    }

    public function test_process_transaction_with_medium_fraud_risk_requires_review()
    {
        $this->fraudService->shouldReceive('analyzeTransaction')
                          ->once()
                          ->andReturn([
                              'risk_score' => 60,
                              'risk_level' => 'medium',
                              'risk_factors' => ['High amount'],
                              'requires_review' => true,
                              'auto_block' => false,
                          ]);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'payment_method' => 'wallet',
        ];

        $result = $this->transactionService->processTransaction($transactionData);

        $this->assertTrue($result['success']);

        $this->assertDatabaseHas('transactions', [
            'sender_id' => $this->user->id,
            'status' => 'pending_review',
        ]);
    }

    public function test_complete_transaction_successfully()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'sender_wallet_id' => $this->wallet->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'net_amount' => 980,
            'status' => 'pending',
        ]);

        $result = $this->transactionService->completeTransaction($transaction);

        $this->assertTrue($result);

        $transaction->refresh();
        $this->assertEquals('completed', $transaction->status);
        $this->assertNotNull($transaction->completed_at);

        $this->wallet->refresh();
        $this->assertEquals(4000, $this->wallet->balance); // 5000 - 1000
    }

    public function test_complete_transaction_fails_for_non_pending_transaction()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'status' => 'completed',
        ]);

        $result = $this->transactionService->completeTransaction($transaction);

        $this->assertFalse($result);
    }

    public function test_cancel_transaction_successfully()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'sender_wallet_id' => $this->wallet->id,
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'status' => 'pending',
        ]);

        // Simulate frozen funds
        $this->wallet->update(['frozen_balance' => 1000]);

        $result = $this->transactionService->cancelTransaction($transaction, 'User request');

        $this->assertTrue($result);

        $transaction->refresh();
        $this->assertEquals('cancelled', $transaction->status);
        $this->assertStringContains('Cancelled: User request', $transaction->notes);

        $this->wallet->refresh();
        $this->assertEquals(0, $this->wallet->frozen_balance);
    }

    public function test_cancel_transaction_fails_for_completed_transaction()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'status' => 'completed',
        ]);

        $result = $this->transactionService->cancelTransaction($transaction);

        $this->assertFalse($result);
    }

    public function test_commission_rate_calculation_with_volume_discount()
    {
        // Create historical transactions to simulate high volume
        Transaction::factory()->count(10)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'amount' => 15000,
            'status' => 'completed',
            'created_at' => now()->subDays(15),
        ]);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'payment_method' => 'wallet',
        ];

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->transactionService);
        $method = $reflection->getMethod('calculateCommissionRate');
        $method->setAccessible(true);

        $rate = $method->invoke($this->transactionService, $transactionData);

        // Should be less than base rate due to volume discount
        $this->assertLessThan(0.02, $rate);
    }

    public function test_business_user_gets_commission_discount()
    {
        $this->user->update(['user_type' => 'business']);

        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'payment_method' => 'wallet',
        ];

        $reflection = new \ReflectionClass($this->transactionService);
        $method = $reflection->getMethod('calculateCommissionRate');
        $method->setAccessible(true);

        $rate = $method->invoke($this->transactionService, $transactionData);

        // Should be less than base rate due to business discount
        $this->assertLessThan(0.02, $rate);
    }

    public function test_crypto_payment_method_gets_lower_fees()
    {
        $transactionData = [
            'sender_id' => $this->user->id,
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'payment_method' => 'crypto',
        ];

        $reflection = new \ReflectionClass($this->transactionService);
        $method = $reflection->getMethod('calculateCommissionRate');
        $method->setAccessible(true);

        $cryptoRate = $method->invoke($this->transactionService, $transactionData);

        $transactionData['payment_method'] = 'bank_transfer';
        $bankRate = $method->invoke($this->transactionService, $transactionData);

        $this->assertLessThan($bankRate, $cryptoRate);
    }

    public function test_cross_border_transaction_has_additional_fees()
    {
        $otherCountry = Country::factory()->create();

        $transactionData = [
            'sender_id' => $this->user->id,
            'sender_country_id' => $this->country->id,
            'receiver_country_id' => $otherCountry->id,
            'amount' => 1000,
        ];

        $reflection = new \ReflectionClass($this->transactionService);
        $method = $reflection->getMethod('calculateAdditionalFees');
        $method->setAccessible(true);

        $fees = $method->invoke($this->transactionService, $transactionData);

        $this->assertGreaterThan(1.0, $fees); // Should include cross-border fee
    }

    public function test_urgent_transaction_has_additional_fees()
    {
        $transactionData = [
            'sender_id' => $this->user->id,
            'priority' => 'urgent',
            'amount' => 1000,
        ];

        $reflection = new \ReflectionClass($this->transactionService);
        $method = $reflection->getMethod('calculateAdditionalFees');
        $method->setAccessible(true);

        $fees = $method->invoke($this->transactionService, $transactionData);

        $this->assertGreaterThan(10.0, $fees); // Should include urgent fee
    }

    public function test_weekend_transaction_has_additional_fees()
    {
        // Mock weekend
        $weekend = now()->next('Saturday');
        $this->travelTo($weekend);

        $transactionData = [
            'sender_id' => $this->user->id,
            'amount' => 1000,
        ];

        $reflection = new \ReflectionClass($this->transactionService);
        $method = $reflection->getMethod('calculateAdditionalFees');
        $method->setAccessible(true);

        $fees = $method->invoke($this->transactionService, $transactionData);

        $this->assertGreaterThan(1.0, $fees); // Should include weekend fee
    }
}
