// Service Worker for Money Transfer PWA
const CACHE_NAME = 'money-transfer-v1.0.0';
const OFFLINE_URL = '/offline.html';

// Files to cache for offline functionality
const CACHE_FILES = [
    '/',
    '/dashboard',
    '/login',
    '/offline.html',
    '/css/app.css',
    '/js/app.js',
    '/images/logo.png',
    // Bootstrap CSS
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    // Bootstrap JS
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    // Bootstrap Icons
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css',
];

// Install event - cache essential files
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching files');
                return cache.addAll(CACHE_FILES);
            })
            .then(() => {
                console.log('Service Worker: Cached all files successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Cache failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip chrome-extension and other non-http requests
    if (!event.request.url.startsWith('http')) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then((cachedResponse) => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }

                // Try to fetch from network
                return fetch(event.request)
                    .then((response) => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // Clone the response
                        const responseToCache = response.clone();

                        // Cache the response for future use
                        caches.open(CACHE_NAME)
                            .then((cache) => {
                                cache.put(event.request, responseToCache);
                            });

                        return response;
                    })
                    .catch(() => {
                        // Network failed, try to serve offline page for navigation requests
                        if (event.request.mode === 'navigate') {
                            return caches.match(OFFLINE_URL);
                        }
                        
                        // For other requests, return a generic offline response
                        return new Response('Offline', {
                            status: 503,
                            statusText: 'Service Unavailable',
                            headers: new Headers({
                                'Content-Type': 'text/plain'
                            })
                        });
                    });
            })
    );
});

// Background sync for offline transactions
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered', event.tag);
    
    if (event.tag === 'background-sync-transactions') {
        event.waitUntil(syncTransactions());
    }
});

// Push notification handler
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'New notification from Money Transfer',
        icon: '/images/icon-192x192.png',
        badge: '/images/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View Details',
                icon: '/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/images/xmark.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('Money Transfer', options)
    );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked', event.action);
    
    event.notification.close();

    if (event.action === 'explore') {
        // Open the app
        event.waitUntil(
            clients.openWindow('/dashboard')
        );
    }
});

// Function to sync offline transactions
async function syncTransactions() {
    try {
        console.log('Service Worker: Syncing offline transactions...');
        
        // Get offline transactions from IndexedDB
        const offlineTransactions = await getOfflineTransactions();
        
        for (const transaction of offlineTransactions) {
            try {
                // Try to send the transaction to the server
                const response = await fetch('/api/transactions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(transaction.data)
                });

                if (response.ok) {
                    // Remove from offline storage if successful
                    await removeOfflineTransaction(transaction.id);
                    console.log('Service Worker: Transaction synced successfully', transaction.id);
                }
            } catch (error) {
                console.error('Service Worker: Failed to sync transaction', transaction.id, error);
            }
        }
    } catch (error) {
        console.error('Service Worker: Background sync failed', error);
    }
}

// Helper functions for IndexedDB operations
async function getOfflineTransactions() {
    // This would typically use IndexedDB to store offline data
    // For now, return empty array
    return [];
}

async function removeOfflineTransaction(id) {
    // This would remove the transaction from IndexedDB
    console.log('Service Worker: Removing offline transaction', id);
}

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});

// Periodic background sync for exchange rates
self.addEventListener('periodicsync', event => {
    if (event.tag === 'exchange-rates-sync') {
        event.waitUntil(syncExchangeRates());
    }
});

async function syncExchangeRates() {
    try {
        console.log('Service Worker: Syncing exchange rates');

        const response = await fetch('/api/exchange-rates/latest');
        if (response.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put('/api/exchange-rates/latest', response.clone());
        }
    } catch (error) {
        console.error('Exchange rates sync failed:', error);
    }
}

// Advanced API request handling
async function handleApiRequest(request) {
    const url = new URL(request.url);
    const isReadOnlyRequest = request.method === 'GET';

    try {
        // Try network first
        const networkResponse = await fetch(request);

        if (networkResponse.ok && isReadOnlyRequest) {
            // Cache successful GET requests
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', url.pathname);

        if (isReadOnlyRequest) {
            // For GET requests, try to serve from cache
            const cachedResponse = await caches.match(request);
            if (cachedResponse) {
                return cachedResponse;
            }
        }

        // Return appropriate offline response
        return new Response(
            JSON.stringify({
                error: 'Offline',
                message: 'This feature requires an internet connection',
                offline: true
            }),
            {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

console.log('Service Worker: Loaded successfully');
