<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // stripe, paypal, visa, mastercard, bank_transfer
            $table->string('gateway'); // Gateway identifier
            $table->boolean('is_active')->default(true);
            $table->decimal('fee_percentage', 5, 4)->default(0); // e.g., 2.9000 for 2.9%
            $table->decimal('fee_fixed', 10, 2)->default(0); // Fixed fee amount
            $table->decimal('min_amount', 15, 2)->nullable(); // Minimum transaction amount
            $table->decimal('max_amount', 15, 2)->nullable(); // Maximum transaction amount
            $table->string('processing_time')->nullable(); // e.g., "1-3 business days"
            $table->boolean('requires_verification')->default(false);
            $table->json('supported_countries')->nullable(); // Array of country codes
            $table->json('supported_currencies')->nullable(); // Array of currency codes
            $table->json('configuration')->nullable(); // Gateway-specific configuration
            $table->integer('display_order')->default(0);
            $table->text('description')->nullable();
            $table->string('icon_url')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['type', 'is_active']);
            $table->index('display_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
