<?php

namespace App\Listeners;

use App\Events\TransactionCreated;
use App\Events\TransactionStatusUpdated;
use App\Jobs\SendTransactionEmailJob;
use App\Jobs\SendTransactionSMSJob;
use App\Models\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendTransactionNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TransactionCreated|TransactionStatusUpdated $event): void
    {
        try {
            $transaction = $event->transaction;
            $eventType = $this->getEventType($event);

            // Send notifications to sender
            $this->sendNotificationToSender($transaction, $eventType);

            // Send notifications to receiver if they have an account
            if ($transaction->receiver_id) {
                $this->sendNotificationToReceiver($transaction, $eventType);
            }

            // Send admin notifications for high-value transactions
            if ($this->shouldNotifyAdmin($transaction, $eventType)) {
                $this->sendAdminNotification($transaction, $eventType);
            }

        } catch (\Exception $e) {
            Log::error('Failed to send transaction notification', [
                'transaction_id' => $event->transaction->id,
                'event_type' => get_class($event),
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send notification to transaction sender.
     */
    private function sendNotificationToSender($transaction, string $eventType): void
    {
        $sender = $transaction->sender;

        // Create in-app notification
        $this->createInAppNotification($sender->id, $transaction, $eventType, 'sender');

        // Send email notification
        if ($sender->email && $sender->email_notifications) {
            SendTransactionEmailJob::dispatch($transaction, $eventType, $sender->id);
        }

        // Send SMS notification
        if ($sender->phone && $sender->sms_notifications) {
            SendTransactionSMSJob::dispatch($transaction, $eventType, $sender->id);
        }
    }

    /**
     * Send notification to transaction receiver.
     */
    private function sendNotificationToReceiver($transaction, string $eventType): void
    {
        $receiver = $transaction->receiver;

        if (!$receiver) {
            return;
        }

        // Create in-app notification
        $this->createInAppNotification($receiver->id, $transaction, $eventType, 'receiver');

        // Send email notification
        if ($receiver->email && $receiver->email_notifications) {
            SendTransactionEmailJob::dispatch($transaction, $eventType, $receiver->id);
        }

        // Send SMS notification
        if ($receiver->phone && $receiver->sms_notifications) {
            SendTransactionSMSJob::dispatch($transaction, $eventType, $receiver->id);
        }
    }

    /**
     * Send admin notification.
     */
    private function sendAdminNotification($transaction, string $eventType): void
    {
        // Get admin users
        $adminUsers = \App\Models\User::where('user_type', 'admin')
            ->where('status', 'active')
            ->get();

        foreach ($adminUsers as $admin) {
            $this->createInAppNotification($admin->id, $transaction, $eventType, 'admin');

            if ($admin->email) {
                SendTransactionEmailJob::dispatch($transaction, $eventType . '_admin', $admin->id);
            }
        }
    }

    /**
     * Create in-app notification.
     */
    private function createInAppNotification(int $userId, $transaction, string $eventType, string $userRole): void
    {
        $title = $this->getNotificationTitle($eventType, $userRole);
        $message = $this->getNotificationMessage($transaction, $eventType, $userRole);
        $priority = $this->getNotificationPriority($eventType);

        Notification::create([
            'user_id' => $userId,
            'type' => 'transaction',
            'title' => $title,
            'message' => $message,
            'data' => [
                'transaction_id' => $transaction->id,
                'transaction_number' => $transaction->transaction_number,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency->code,
                'status' => $transaction->status,
                'event_type' => $eventType,
                'user_role' => $userRole,
            ],
            'channel' => 'database',
            'priority' => $priority,
            'is_sent' => true,
            'sent_at' => now(),
        ]);
    }

    /**
     * Get event type from event instance.
     */
    private function getEventType($event): string
    {
        if ($event instanceof TransactionCreated) {
            return 'created';
        }

        if ($event instanceof TransactionStatusUpdated) {
            return match ($event->transaction->status) {
                'completed' => 'completed',
                'failed' => 'failed',
                'cancelled' => 'cancelled',
                'pending_review' => 'review_required',
                'blocked' => 'blocked',
                default => 'status_updated',
            };
        }

        return 'unknown';
    }

    /**
     * Get notification title.
     */
    private function getNotificationTitle(string $eventType, string $userRole): string
    {
        $titles = [
            'created' => [
                'sender' => 'Transaction Initiated',
                'receiver' => 'Money Transfer Received',
                'admin' => 'New Transaction Created',
            ],
            'completed' => [
                'sender' => 'Transaction Completed',
                'receiver' => 'Money Received',
                'admin' => 'Transaction Completed',
            ],
            'failed' => [
                'sender' => 'Transaction Failed',
                'receiver' => 'Transaction Failed',
                'admin' => 'Transaction Failed',
            ],
            'cancelled' => [
                'sender' => 'Transaction Cancelled',
                'receiver' => 'Transaction Cancelled',
                'admin' => 'Transaction Cancelled',
            ],
            'review_required' => [
                'sender' => 'Transaction Under Review',
                'receiver' => 'Transaction Under Review',
                'admin' => 'Transaction Requires Review',
            ],
            'blocked' => [
                'sender' => 'Transaction Blocked',
                'receiver' => 'Transaction Blocked',
                'admin' => 'Transaction Blocked',
            ],
        ];

        return $titles[$eventType][$userRole] ?? 'Transaction Update';
    }

    /**
     * Get notification message.
     */
    private function getNotificationMessage($transaction, string $eventType, string $userRole): string
    {
        $amount = number_format($transaction->amount, 2);
        $currency = $transaction->currency->code;
        $transactionNumber = $transaction->transaction_number;

        switch ($eventType) {
            case 'created':
                if ($userRole === 'sender') {
                    return "Your transfer of {$currency} {$amount} to {$transaction->receiver_name} has been initiated. Transaction: {$transactionNumber}";
                } elseif ($userRole === 'receiver') {
                    return "You have received a transfer of {$currency} {$amount} from {$transaction->sender_name}. Transaction: {$transactionNumber}";
                } else {
                    return "New transaction created: {$currency} {$amount}. Transaction: {$transactionNumber}";
                }

            case 'completed':
                if ($userRole === 'sender') {
                    return "Your transfer of {$currency} {$amount} has been completed successfully. Transaction: {$transactionNumber}";
                } elseif ($userRole === 'receiver') {
                    return "You have successfully received {$currency} {$amount}. The funds are now available in your wallet.";
                } else {
                    return "Transaction completed: {$currency} {$amount}. Transaction: {$transactionNumber}";
                }

            case 'failed':
                return "Transaction {$transactionNumber} for {$currency} {$amount} has failed. Please contact support if you need assistance.";

            case 'cancelled':
                return "Transaction {$transactionNumber} for {$currency} {$amount} has been cancelled. Funds have been returned to your wallet.";

            case 'review_required':
                return "Transaction {$transactionNumber} for {$currency} {$amount} is under security review. We will notify you once the review is complete.";

            case 'blocked':
                return "Transaction {$transactionNumber} for {$currency} {$amount} has been blocked for security reasons. Please contact support.";

            default:
                return "Transaction {$transactionNumber} status has been updated.";
        }
    }

    /**
     * Get notification priority.
     */
    private function getNotificationPriority(string $eventType): string
    {
        return match ($eventType) {
            'blocked', 'failed' => 'high',
            'review_required' => 'medium',
            default => 'normal',
        };
    }

    /**
     * Check if admin should be notified.
     */
    private function shouldNotifyAdmin($transaction, string $eventType): bool
    {
        // Always notify for blocked or failed transactions
        if (in_array($eventType, ['blocked', 'failed', 'review_required'])) {
            return true;
        }

        // Notify for high-value transactions
        if ($transaction->amount > 50000) {
            return true;
        }

        // Notify for suspicious transactions
        if ($transaction->is_suspicious) {
            return true;
        }

        return false;
    }
}
