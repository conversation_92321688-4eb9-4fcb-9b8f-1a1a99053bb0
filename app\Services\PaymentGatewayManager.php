<?php

namespace App\Services;

use Illuminate\Container\Container;
use Illuminate\Support\Facades\Log;
use App\Models\PaymentGateway;
use App\Models\Transaction;
use App\Contracts\PaymentGatewayInterface;
use App\Services\PaymentGateways\PayPalService;
use App\Services\PaymentGateways\StripeService;
use App\Services\PaymentGateways\WiseService;

class PaymentGatewayManager
{
    protected Container $app;
    protected array $gateways = [];

    public function __construct(Container $app)
    {
        $this->app = $app;
        $this->registerGateways();
    }

    /**
     * Register available payment gateways.
     */
    protected function registerGateways(): void
    {
        $this->gateways = [
            'paypal' => PayPalService::class,
            'stripe' => StripeService::class,
            'wise' => WiseService::class,
        ];
    }

    /**
     * Get payment gateway instance.
     */
    public function gateway(string $name): PaymentGatewayInterface
    {
        if (!isset($this->gateways[$name])) {
            throw new \InvalidArgumentException("Payment gateway [{$name}] not supported.");
        }

        return $this->app->make($this->gateways[$name]);
    }

    /**
     * Process payment through appropriate gateway.
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        try {
            $gatewayName = $this->determineGateway($transaction, $paymentData);
            $gateway = $this->gateway($gatewayName);

            Log::info('Processing payment through gateway', [
                'transaction_id' => $transaction->id,
                'gateway' => $gatewayName,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency->code,
            ]);

            $result = $gateway->processPayment($transaction, $paymentData);

            // Log the result
            if ($result['success']) {
                Log::info('Payment processed successfully', [
                    'transaction_id' => $transaction->id,
                    'gateway' => $gatewayName,
                    'gateway_transaction_id' => $result['transaction_id'] ?? null,
                ]);
            } else {
                Log::error('Payment processing failed', [
                    'transaction_id' => $transaction->id,
                    'gateway' => $gatewayName,
                    'error' => $result['error'] ?? 'Unknown error',
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Payment gateway manager error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'Payment processing failed: ' . $e->getMessage(),
                'error_code' => 'GATEWAY_MANAGER_ERROR',
            ];
        }
    }

    /**
     * Refund payment through appropriate gateway.
     */
    public function refundPayment(Transaction $transaction, float $amount = null): array
    {
        try {
            $gatewayName = $this->getTransactionGateway($transaction);
            $gateway = $this->gateway($gatewayName);

            $refundAmount = $amount ?? $transaction->amount;

            Log::info('Processing refund through gateway', [
                'transaction_id' => $transaction->id,
                'gateway' => $gatewayName,
                'refund_amount' => $refundAmount,
                'original_amount' => $transaction->amount,
            ]);

            $result = $gateway->refundPayment(
                $transaction->gateway_transaction_id,
                $refundAmount,
                $transaction->currency->code
            );

            // Log the result
            if ($result['success']) {
                Log::info('Refund processed successfully', [
                    'transaction_id' => $transaction->id,
                    'gateway' => $gatewayName,
                    'refund_id' => $result['refund_id'] ?? null,
                    'refund_amount' => $refundAmount,
                ]);
            } else {
                Log::error('Refund processing failed', [
                    'transaction_id' => $transaction->id,
                    'gateway' => $gatewayName,
                    'error' => $result['error'] ?? 'Unknown error',
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Refund processing error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'Refund processing failed: ' . $e->getMessage(),
                'error_code' => 'REFUND_ERROR',
            ];
        }
    }

    /**
     * Get payment status from appropriate gateway.
     */
    public function getPaymentStatus(Transaction $transaction): array
    {
        try {
            $gatewayName = $this->getTransactionGateway($transaction);
            $gateway = $this->gateway($gatewayName);

            $result = $gateway->getPaymentStatus($transaction->gateway_transaction_id);

            Log::info('Payment status retrieved', [
                'transaction_id' => $transaction->id,
                'gateway' => $gatewayName,
                'status' => $result['status'] ?? 'unknown',
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Failed to get payment status', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to get payment status: ' . $e->getMessage(),
                'error_code' => 'STATUS_ERROR',
            ];
        }
    }

    /**
     * Validate webhook from appropriate gateway.
     */
    public function validateWebhook(string $gatewayName, array $headers, string $payload): bool
    {
        try {
            $gateway = $this->gateway($gatewayName);
            return $gateway->validateWebhook($headers, $payload);

        } catch (\Exception $e) {
            Log::error('Webhook validation error', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get available payment gateways for a transaction.
     */
    public function getAvailableGateways(Transaction $transaction): array
    {
        $availableGateways = [];

        foreach ($this->gateways as $name => $class) {
            try {
                $gateway = $this->gateway($name);
                $config = $gateway->getConfig();

                // Check if gateway supports the transaction currency
                if (in_array($transaction->currency->code, $config['supported_currencies'])) {
                    // Check amount limits
                    $totalAmount = $transaction->amount + $transaction->fee;
                    
                    if ($totalAmount >= $config['min_amount'] && $totalAmount <= $config['max_amount']) {
                        $availableGateways[$name] = [
                            'name' => $config['name'],
                            'supports_refunds' => $config['supports_refunds'],
                            'processing_fee_percentage' => $config['processing_fee_percentage'],
                            'processing_fee_fixed' => $config['processing_fee_fixed'],
                        ];
                    }
                }

            } catch (\Exception $e) {
                Log::warning('Failed to check gateway availability', [
                    'gateway' => $name,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $availableGateways;
    }

    /**
     * Determine the best gateway for a transaction.
     */
    protected function determineGateway(Transaction $transaction, array $paymentData): string
    {
        // If gateway is specified in payment data, use it
        if (isset($paymentData['gateway']) && isset($this->gateways[$paymentData['gateway']])) {
            return $paymentData['gateway'];
        }

        // If transaction has a payment gateway assigned, use it
        if ($transaction->paymentGateway) {
            return $transaction->paymentGateway->slug;
        }

        // Determine based on payment method
        $paymentMethod = $paymentData['payment_method'] ?? $transaction->payment_method;

        return match($paymentMethod) {
            'paypal' => 'paypal',
            'credit_card', 'debit_card' => 'stripe',
            'bank_transfer' => 'wise',
            default => 'stripe' // Default fallback
        };
    }

    /**
     * Get the gateway used for a transaction.
     */
    protected function getTransactionGateway(Transaction $transaction): string
    {
        if ($transaction->paymentGateway) {
            return $transaction->paymentGateway->slug;
        }

        // Try to determine from gateway_transaction_id format
        if ($transaction->gateway_transaction_id) {
            if (str_starts_with($transaction->gateway_transaction_id, 'pi_')) {
                return 'stripe';
            } elseif (str_starts_with($transaction->gateway_transaction_id, 'PAY-')) {
                return 'paypal';
            }
        }

        // Fallback to default
        return 'stripe';
    }

    /**
     * Get all registered gateways.
     */
    public function getRegisteredGateways(): array
    {
        return array_keys($this->gateways);
    }

    /**
     * Check if a gateway is registered.
     */
    public function hasGateway(string $name): bool
    {
        return isset($this->gateways[$name]);
    }

    /**
     * Get gateway configuration.
     */
    public function getGatewayConfig(string $name): array
    {
        if (!$this->hasGateway($name)) {
            throw new \InvalidArgumentException("Payment gateway [{$name}] not supported.");
        }

        return $this->gateway($name)->getConfig();
    }

    /**
     * Calculate processing fees for a gateway.
     */
    public function calculateProcessingFee(string $gatewayName, float $amount): float
    {
        try {
            $config = $this->getGatewayConfig($gatewayName);
            
            $percentageFee = $amount * ($config['processing_fee_percentage'] / 100);
            $fixedFee = $config['processing_fee_fixed'];
            
            return round($percentageFee + $fixedFee, 2);

        } catch (\Exception $e) {
            Log::error('Failed to calculate processing fee', [
                'gateway' => $gatewayName,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return 0.0;
        }
    }
}
