<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Transaction;
use App\Models\FraudDetection;
use App\Models\AuditLog;
use App\Models\Branch;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\PaymentGateway;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Get all users with pagination and filters.
     */
    public function users(Request $request): JsonResponse
    {
        $query = User::with(['country', 'branch', 'wallets']);

        // Search by name or email
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by user type
        if ($request->has('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        // Filter by verification status
        if ($request->has('verified')) {
            if ($request->boolean('verified')) {
                $query->whereNotNull('kyc_verified_at')->where('aml_verified', true);
            } else {
                $query->where(function ($q) {
                    $q->whereNull('kyc_verified_at')->orWhere('aml_verified', false);
                });
            }
        }

        // Filter by country
        if ($request->has('country_id')) {
            $query->where('country_id', $request->country_id);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $users,
        ]);
    }

    /**
     * Get specific user details.
     */
    public function showUser($id): JsonResponse
    {
        $user = User::with([
            'country',
            'branch',
            'wallets.currency',
            'sentTransactions' => function ($query) {
                $query->latest()->limit(10);
            },
            'receivedTransactions' => function ($query) {
                $query->latest()->limit(10);
            },
            'fraudAlerts' => function ($query) {
                $query->latest()->limit(5);
            }
        ])->findOrFail($id);

        // Calculate user statistics
        $stats = [
            'total_sent' => $user->sentTransactions()->where('status', 'completed')->sum('amount'),
            'total_received' => $user->receivedTransactions()->where('status', 'completed')->sum('net_amount'),
            'total_transactions' => $user->sentTransactions()->count() + $user->receivedTransactions()->count(),
            'pending_transactions' => $user->sentTransactions()->where('status', 'pending')->count(),
            'failed_transactions' => $user->sentTransactions()->where('status', 'failed')->count(),
            'total_fees_paid' => $user->sentTransactions()->sum('total_fees'),
            'average_transaction_amount' => $user->sentTransactions()->where('status', 'completed')->avg('amount'),
            'last_transaction_at' => $user->sentTransactions()->latest()->first()?->created_at,
            'fraud_alerts_count' => $user->fraudAlerts()->count(),
            'high_risk_alerts' => $user->fraudAlerts()->where('risk_level', 'high')->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'statistics' => $stats,
            ],
        ]);
    }

    /**
     * Update user status.
     */
    public function updateUserStatus(Request $request, $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:active,suspended,blocked,pending_verification',
            'reason' => 'nullable|string|max:500',
        ]);

        $user = User::findOrFail($id);
        $oldStatus = $user->status;

        $user->update(['status' => $request->status]);

        // Log the status change
        AuditLog::create([
            'user_id' => $request->user()->id,
            'event_type' => 'user_status_change',
            'action' => 'update',
            'model_type' => 'User',
            'model_id' => $user->id,
            'old_values' => ['status' => $oldStatus],
            'new_values' => ['status' => $request->status],
            'description' => "User status changed from {$oldStatus} to {$request->status}. Reason: " . ($request->reason ?? 'Not specified'),
            'ip_address' => $request->ip(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully',
            'data' => $user->fresh(),
        ]);
    }

    /**
     * Update user limits.
     */
    public function updateUserLimits(Request $request, $id): JsonResponse
    {
        $request->validate([
            'daily_limit' => 'required|numeric|min:0',
            'monthly_limit' => 'required|numeric|min:0|gte:daily_limit',
        ]);

        $user = User::findOrFail($id);
        $oldLimits = [
            'daily_limit' => $user->daily_limit,
            'monthly_limit' => $user->monthly_limit,
        ];

        $user->update([
            'daily_limit' => $request->daily_limit,
            'monthly_limit' => $request->monthly_limit,
        ]);

        // Update wallet limits
        $user->wallets()->update([
            'daily_limit' => $request->daily_limit,
            'monthly_limit' => $request->monthly_limit,
        ]);

        // Log the change
        AuditLog::create([
            'user_id' => $request->user()->id,
            'event_type' => 'user_limits_change',
            'action' => 'update',
            'model_type' => 'User',
            'model_id' => $user->id,
            'old_values' => $oldLimits,
            'new_values' => [
                'daily_limit' => $request->daily_limit,
                'monthly_limit' => $request->monthly_limit,
            ],
            'description' => 'User transaction limits updated',
            'ip_address' => $request->ip(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'User limits updated successfully',
            'data' => $user->fresh(),
        ]);
    }

    /**
     * Get all transactions for admin.
     */
    public function allTransactions(Request $request): JsonResponse
    {
        $query = Transaction::with([
            'currency',
            'targetCurrency',
            'sender',
            'receiver',
            'branch',
            'senderCountry',
            'receiverCountry'
        ]);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by risk score
        if ($request->has('risk_score')) {
            $query->where('risk_score', $request->risk_score);
        }

        // Filter by amount range
        if ($request->has('min_amount')) {
            $query->where('amount', '>=', $request->min_amount);
        }
        if ($request->has('max_amount')) {
            $query->where('amount', '<=', $request->max_amount);
        }

        // Filter by date range
        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Search by transaction number or reference
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_number', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%");
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $transactions,
        ]);
    }

    /**
     * Get pending transactions.
     */
    public function pendingTransactions(): JsonResponse
    {
        $transactions = Transaction::with([
            'currency',
            'sender',
            'receiver',
            'branch'
        ])
        ->where('status', 'pending')
        ->orderBy('created_at', 'asc')
        ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $transactions,
        ]);
    }

    /**
     * Get suspicious transactions.
     */
    public function suspiciousTransactions(): JsonResponse
    {
        $transactions = Transaction::with([
            'currency',
            'sender',
            'receiver',
            'fraudAlerts'
        ])
        ->where(function ($query) {
            $query->where('is_suspicious', true)
                  ->orWhere('risk_score', 'high')
                  ->orWhereHas('fraudAlerts', function ($q) {
                      $q->where('status', 'open');
                  });
        })
        ->orderBy('created_at', 'desc')
        ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $transactions,
        ]);
    }

    /**
     * Approve transaction.
     */
    public function approveTransaction(Request $request, $id): JsonResponse
    {
        $transaction = Transaction::findOrFail($id);

        if ($transaction->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Transaction is not in pending status',
            ], 400);
        }

        $transaction->update([
            'status' => 'completed',
            'processed_by' => $request->user()->id,
            'processed_at' => now(),
            'completed_at' => now(),
        ]);

        // Process the transaction (update wallets, etc.)
        if ($transaction->receiverWallet) {
            $transaction->receiverWallet->addFunds($transaction->net_amount);
        }

        if ($transaction->senderWallet) {
            $transaction->senderWallet->unfreezeFunds($transaction->amount);
        }

        // Log the approval
        AuditLog::create([
            'user_id' => $request->user()->id,
            'event_type' => 'transaction_approval',
            'action' => 'approve',
            'model_type' => 'Transaction',
            'model_id' => $transaction->id,
            'description' => "Transaction {$transaction->transaction_number} approved",
            'ip_address' => $request->ip(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Transaction approved successfully',
            'data' => $transaction->fresh(),
        ]);
    }

    /**
     * Get system statistics.
     */
    public function systemStatistics(): JsonResponse
    {
        $stats = [
            'users' => [
                'total' => User::count(),
                'active' => User::where('status', 'active')->count(),
                'verified' => User::whereNotNull('kyc_verified_at')->where('aml_verified', true)->count(),
                'new_today' => User::whereDate('created_at', today())->count(),
                'new_this_month' => User::whereMonth('created_at', now()->month)->count(),
            ],
            'transactions' => [
                'total' => Transaction::count(),
                'completed' => Transaction::where('status', 'completed')->count(),
                'pending' => Transaction::where('status', 'pending')->count(),
                'failed' => Transaction::where('status', 'failed')->count(),
                'today' => Transaction::whereDate('created_at', today())->count(),
                'volume_today' => Transaction::whereDate('created_at', today())
                    ->where('status', 'completed')
                    ->sum('amount'),
                'volume_this_month' => Transaction::whereMonth('created_at', now()->month)
                    ->where('status', 'completed')
                    ->sum('amount'),
            ],
            'security' => [
                'fraud_alerts' => FraudDetection::where('status', 'open')->count(),
                'high_risk_alerts' => FraudDetection::where('risk_level', 'high')
                    ->where('status', 'open')
                    ->count(),
                'suspicious_transactions' => Transaction::where('is_suspicious', true)->count(),
                'blocked_users' => User::where('status', 'blocked')->count(),
            ],
            'financial' => [
                'total_volume' => Transaction::where('status', 'completed')->sum('amount'),
                'total_fees' => Transaction::where('status', 'completed')->sum('total_fees'),
                'average_transaction' => Transaction::where('status', 'completed')->avg('amount'),
                'largest_transaction' => Transaction::where('status', 'completed')->max('amount'),
            ],
            'system' => [
                'active_branches' => Branch::where('is_active', true)->count(),
                'active_currencies' => Currency::where('is_active', true)->count(),
                'active_gateways' => PaymentGateway::where('is_active', true)->count(),
                'uptime' => '99.9%', // Mock data
                'last_backup' => now()->subHours(6), // Mock data
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'generated_at' => now(),
        ]);
    }

    /**
     * Get audit logs.
     */
    public function auditLogs(Request $request): JsonResponse
    {
        $query = AuditLog::with(['user']);

        // Filter by event type
        if ($request->has('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by date range
        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $logs = $query->orderBy('created_at', 'desc')->paginate(50);

        return response()->json([
            'success' => true,
            'data' => $logs,
        ]);
    }

    /**
     * Get fraud alerts.
     */
    public function fraudAlerts(): JsonResponse
    {
        $alerts = FraudDetection::with(['transaction', 'user'])
            ->where('status', 'open')
            ->orderBy('risk_level', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $alerts,
        ]);
    }

    /**
     * Resolve fraud alert.
     */
    public function resolveFraudAlert(Request $request, $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:resolved,false_positive',
            'resolution_notes' => 'required|string|max:1000',
        ]);

        $alert = FraudDetection::findOrFail($id);

        $alert->update([
            'status' => $request->status,
            'assigned_to' => $request->user()->id,
            'resolution_notes' => $request->resolution_notes,
            'resolved_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Fraud alert resolved successfully',
            'data' => $alert->fresh(),
        ]);
    }
}
