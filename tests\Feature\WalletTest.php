<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Currency;
use App\Models\Wallet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class WalletTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Currency $currency;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->currency = Currency::factory()->create(['code' => 'USD']);
        $this->user = User::factory()->create();
    }

    public function test_user_can_create_wallet()
    {
        $walletData = [
            'currency_id' => $this->currency->id,
            'name' => 'My USD Wallet',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/wallets', $walletData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'wallet' => [
                            'id',
                            'name',
                            'balance',
                            'currency',
                        ],
                    ],
                ]);

        $this->assertDatabaseHas('wallets', [
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'name' => 'My USD Wallet',
        ]);
    }

    public function test_user_cannot_create_duplicate_currency_wallet()
    {
        Wallet::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
        ]);

        $walletData = [
            'currency_id' => $this->currency->id,
            'name' => 'Another USD Wallet',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/wallets', $walletData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['currency_id']);
    }

    public function test_user_can_get_wallet_list()
    {
        Wallet::factory()->count(3)->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson('/api/v1/wallets');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'balance',
                            'currency',
                            'is_active',
                        ],
                    ],
                ]);
    }

    public function test_user_can_get_specific_wallet()
    {
        $wallet = Wallet::factory()->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/wallets/{$wallet->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'wallet' => [
                            'id',
                            'name',
                            'balance',
                            'available_balance',
                            'pending_balance',
                            'frozen_balance',
                            'currency',
                            'statistics',
                        ],
                    ],
                ]);
    }

    public function test_user_cannot_access_other_users_wallet()
    {
        $otherUser = User::factory()->create();
        $wallet = Wallet::factory()->create(['user_id' => $otherUser->id]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/wallets/{$wallet->id}");

        $response->assertStatus(403);
    }

    public function test_user_can_add_funds_to_wallet()
    {
        $wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'balance' => 1000,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson("/api/v1/wallets/{$wallet->id}/add-funds", [
                            'amount' => 500,
                            'payment_method' => 'bank_transfer',
                            'reference' => 'TEST123',
                        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Funds added successfully',
                ]);

        $wallet->refresh();
        $this->assertEquals(1500, $wallet->balance);
    }

    public function test_user_cannot_add_negative_funds()
    {
        $wallet = Wallet::factory()->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson("/api/v1/wallets/{$wallet->id}/add-funds", [
                            'amount' => -100,
                            'payment_method' => 'bank_transfer',
                        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['amount']);
    }

    public function test_user_can_withdraw_funds_from_wallet()
    {
        $wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'balance' => 1000,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson("/api/v1/wallets/{$wallet->id}/withdraw", [
                            'amount' => 300,
                            'withdrawal_method' => 'bank_transfer',
                            'bank_account' => '**********',
                        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Withdrawal request submitted',
                ]);

        $wallet->refresh();
        $this->assertEquals(700, $wallet->balance);
    }

    public function test_user_cannot_withdraw_more_than_balance()
    {
        $wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'balance' => 500,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson("/api/v1/wallets/{$wallet->id}/withdraw", [
                            'amount' => 1000,
                            'withdrawal_method' => 'bank_transfer',
                        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Insufficient balance',
                ]);
    }

    public function test_user_can_transfer_between_wallets()
    {
        $fromWallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'balance' => 1000,
        ]);

        $toCurrency = Currency::factory()->create(['code' => 'EUR']);
        $toWallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $toCurrency->id,
            'balance' => 0,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson("/api/v1/wallets/{$fromWallet->id}/transfer", [
                            'to_wallet_id' => $toWallet->id,
                            'amount' => 500,
                        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Transfer completed successfully',
                ]);

        $fromWallet->refresh();
        $toWallet->refresh();

        $this->assertEquals(500, $fromWallet->balance);
        $this->assertGreaterThan(0, $toWallet->balance); // After exchange rate conversion
    }

    public function test_user_can_freeze_wallet()
    {
        $wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->putJson("/api/v1/wallets/{$wallet->id}/freeze");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Wallet frozen successfully',
                ]);

        $wallet->refresh();
        $this->assertFalse($wallet->is_active);
    }

    public function test_user_can_unfreeze_wallet()
    {
        $wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'is_active' => false,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->putJson("/api/v1/wallets/{$wallet->id}/unfreeze");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Wallet unfrozen successfully',
                ]);

        $wallet->refresh();
        $this->assertTrue($wallet->is_active);
    }

    public function test_user_can_get_wallet_transactions()
    {
        $wallet = Wallet::factory()->create(['user_id' => $this->user->id]);

        // Create some transactions for this wallet
        \App\Models\Transaction::factory()->count(5)->create([
            'sender_wallet_id' => $wallet->id,
            'currency_id' => $wallet->currency_id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/wallets/{$wallet->id}/transactions");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'transaction_number',
                                'amount',
                                'status',
                                'created_at',
                            ],
                        ],
                    ],
                ]);
    }

    public function test_user_can_get_wallet_balance_history()
    {
        $wallet = Wallet::factory()->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/wallets/{$wallet->id}/balance-history");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'wallet',
                        'history' => [
                            '*' => [
                                'date',
                                'balance',
                                'change',
                                'type',
                            ],
                        ],
                    ],
                ]);
    }

    public function test_wallet_respects_daily_limits()
    {
        $wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'balance' => 10000,
            'daily_limit' => 1000,
        ]);

        // First transaction within limit
        $response1 = $this->actingAs($this->user, 'sanctum')
                         ->postJson("/api/v1/wallets/{$wallet->id}/withdraw", [
                             'amount' => 500,
                             'withdrawal_method' => 'bank_transfer',
                         ]);

        $response1->assertStatus(200);

        // Second transaction exceeding limit
        $response2 = $this->actingAs($this->user, 'sanctum')
                         ->postJson("/api/v1/wallets/{$wallet->id}/withdraw", [
                             'amount' => 600,
                             'withdrawal_method' => 'bank_transfer',
                         ]);

        $response2->assertStatus(422)
                  ->assertJson([
                      'success' => false,
                      'message' => 'Daily limit exceeded',
                  ]);
    }

    public function test_wallet_statistics_are_calculated_correctly()
    {
        $wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'balance' => 5000,
        ]);

        // Create some transactions
        \App\Models\Transaction::factory()->count(3)->create([
            'sender_wallet_id' => $wallet->id,
            'amount' => 1000,
            'status' => 'completed',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/wallets/{$wallet->id}/statistics");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'wallet',
                        'statistics' => [
                            'total_sent',
                            'total_received',
                            'transaction_count',
                            'average_transaction',
                            'monthly_volume',
                        ],
                    ],
                ]);

        $stats = $response->json('data.statistics');
        $this->assertEquals(3000, $stats['total_sent']);
        $this->assertEquals(3, $stats['transaction_count']);
    }
}
