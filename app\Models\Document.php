<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Document extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'name',
        'description',
        'file_path',
        'file_name',
        'file_size',
        'mime_type',
        'status',
        'verification_status',
        'verified_at',
        'verified_by',
        'rejection_reason',
        'expiry_date',
        'is_sensitive',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'file_size' => 'integer',
        'is_sensitive' => 'boolean',
        'verified_at' => 'datetime',
        'expiry_date' => 'datetime',
    ];

    /**
     * Get the user that owns the document.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user that verified the document.
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope for documents by type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for verified documents.
     */
    public function scopeVerified($query)
    {
        return $query->where('verification_status', 'verified');
    }

    /**
     * Scope for pending documents.
     */
    public function scopePending($query)
    {
        return $query->where('verification_status', 'pending');
    }

    /**
     * Scope for rejected documents.
     */
    public function scopeRejected($query)
    {
        return $query->where('verification_status', 'rejected');
    }

    /**
     * Scope for expired documents.
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    /**
     * Scope for expiring soon documents.
     */
    public function scopeExpiringSoon($query, int $days = 30)
    {
        return $query->whereBetween('expiry_date', [now(), now()->addDays($days)]);
    }

    /**
     * Mark document as verified.
     */
    public function markAsVerified(int $verifiedBy = null): void
    {
        $this->update([
            'verification_status' => 'verified',
            'verified_at' => now(),
            'verified_by' => $verifiedBy ?? auth()->id(),
            'rejection_reason' => null,
        ]);
    }

    /**
     * Mark document as rejected.
     */
    public function markAsRejected(string $reason): void
    {
        $this->update([
            'verification_status' => 'rejected',
            'rejection_reason' => $reason,
            'verified_at' => null,
            'verified_by' => null,
        ]);
    }

    /**
     * Get file download URL.
     */
    public function getDownloadUrlAttribute(): ?string
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            return route('documents.download', $this->id);
        }
        return null;
    }

    /**
     * Get file preview URL.
     */
    public function getPreviewUrlAttribute(): ?string
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            // Only allow preview for image files
            if (str_starts_with($this->mime_type, 'image/')) {
                return route('documents.preview', $this->id);
            }
        }
        return null;
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'N/A';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if document is expired.
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * Check if document is expiring soon.
     */
    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->expiry_date && 
               $this->expiry_date->isFuture() && 
               $this->expiry_date->lte(now()->addDays($days));
    }

    /**
     * Get verification status color.
     */
    public function getVerificationStatusColorAttribute(): string
    {
        return match ($this->verification_status) {
            'verified' => 'green',
            'pending' => 'yellow',
            'rejected' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get formatted document data.
     */
    public function getFormattedDataAttribute(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'name' => $this->name,
            'description' => $this->description,
            'file_name' => $this->file_name,
            'file_size' => $this->formatted_file_size,
            'mime_type' => $this->mime_type,
            'status' => $this->status,
            'verification_status' => $this->verification_status,
            'verification_color' => $this->verification_status_color,
            'rejection_reason' => $this->rejection_reason,
            'download_url' => $this->download_url,
            'preview_url' => $this->preview_url,
            'is_sensitive' => $this->is_sensitive,
            'is_expired' => $this->isExpired(),
            'is_expiring_soon' => $this->isExpiringSoon(),
            'verified_at' => $this->verified_at?->toISOString(),
            'expiry_date' => $this->expiry_date?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'user' => $this->user ? [
                'id' => $this->user->id,
                'name' => $this->user->first_name . ' ' . $this->user->last_name,
            ] : null,
            'verified_by_user' => $this->verifiedBy ? [
                'id' => $this->verifiedBy->id,
                'name' => $this->verifiedBy->first_name . ' ' . $this->verifiedBy->last_name,
            ] : null,
        ];
    }

    /**
     * Delete document file from storage.
     */
    public function deleteFile(): bool
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            return Storage::delete($this->file_path);
        }
        return true;
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        // Delete file when document is deleted
        static::deleting(function ($document) {
            $document->deleteFile();
        });
    }
}
