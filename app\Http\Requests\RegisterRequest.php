<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:50', 'regex:/^[\p{L}\s\-\.\']+$/u'],
            'last_name' => ['required', 'string', 'max:50', 'regex:/^[\p{L}\s\-\.\']+$/u'],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:users,email'],
            'phone' => ['required', 'string', 'regex:/^\+[1-9]\d{1,14}$/', 'unique:users,phone'],
            'password' => ['required', 'confirmed', Password::min(8)->letters()->mixedCase()->numbers()->symbols()],
            'password_confirmation' => ['required', 'string'],
            'country_id' => ['required', 'integer', 'exists:countries,id'],
            'date_of_birth' => ['nullable', 'date', 'before:today', 'after:1900-01-01'],
            'gender' => ['nullable', 'string', 'in:male,female'],
            'national_id' => ['nullable', 'string', 'max:50'],
            'address' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:100'],
            'postal_code' => ['nullable', 'string', 'max:20'],
            'preferred_language' => ['nullable', 'string', 'in:ar,en'],
            'preferred_currency' => ['nullable', 'string', 'size:3', 'exists:currencies,code'],
            'terms_accepted' => ['required', 'accepted'],
            'privacy_accepted' => ['required', 'accepted'],
            'marketing_consent' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'الاسم الأول مطلوب',
            'first_name.regex' => 'الاسم الأول يجب أن يحتوي على أحرف فقط',
            'last_name.required' => 'اسم العائلة مطلوب',
            'last_name.regex' => 'اسم العائلة يجب أن يحتوي على أحرف فقط',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم مسبقاً',
            'phone.required' => 'رقم الهاتف مطلوب',
            'phone.regex' => 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ + ورمز الدولة)',
            'phone.unique' => 'رقم الهاتف مستخدم مسبقاً',
            'password.required' => 'كلمة المرور مطلوبة',
            'password.min' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق',
            'country_id.required' => 'الدولة مطلوبة',
            'country_id.exists' => 'الدولة المحددة غير صحيحة',
            'date_of_birth.before' => 'تاريخ الميلاد يجب أن يكون في الماضي',
            'date_of_birth.after' => 'تاريخ الميلاد غير صحيح',
            'gender.in' => 'الجنس يجب أن يكون ذكر أو أنثى',
            'preferred_language.in' => 'اللغة المفضلة يجب أن تكون العربية أو الإنجليزية',
            'preferred_currency.exists' => 'العملة المفضلة غير صحيحة',
            'terms_accepted.required' => 'يجب الموافقة على الشروط والأحكام',
            'terms_accepted.accepted' => 'يجب الموافقة على الشروط والأحكام',
            'privacy_accepted.required' => 'يجب الموافقة على سياسة الخصوصية',
            'privacy_accepted.accepted' => 'يجب الموافقة على سياسة الخصوصية',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'الاسم الأول',
            'last_name' => 'اسم العائلة',
            'email' => 'البريد الإلكتروني',
            'phone' => 'رقم الهاتف',
            'password' => 'كلمة المرور',
            'password_confirmation' => 'تأكيد كلمة المرور',
            'country_id' => 'الدولة',
            'date_of_birth' => 'تاريخ الميلاد',
            'gender' => 'الجنس',
            'national_id' => 'رقم الهوية',
            'address' => 'العنوان',
            'city' => 'المدينة',
            'postal_code' => 'الرمز البريدي',
            'preferred_language' => 'اللغة المفضلة',
            'preferred_currency' => 'العملة المفضلة',
            'terms_accepted' => 'الشروط والأحكام',
            'privacy_accepted' => 'سياسة الخصوصية',
            'marketing_consent' => 'الموافقة التسويقية',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Validation\ValidationException($validator, response()->json([
            'success' => false,
            'message' => 'بيانات التسجيل غير صحيحة',
            'errors' => $validator->errors(),
            'error_code' => 'VALIDATION_FAILED',
        ], 422));
    }
}
