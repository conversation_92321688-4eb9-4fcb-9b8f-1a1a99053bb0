<?php

namespace App\Services\PaymentGateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\Transaction;
use App\Services\AuditLogService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class StripePaymentGateway implements PaymentGatewayInterface
{
    private string $apiKey;
    private string $webhookSecret;
    private string $baseUrl;
    private array $config;

    public function __construct()
    {
        $this->apiKey = config('payment_gateways.stripe.secret_key');
        $this->webhookSecret = config('payment_gateways.stripe.webhook_secret');
        $this->baseUrl = 'https://api.stripe.com/v1';
        $this->config = config('payment_gateways.stripe', []);
    }

    /**
     * Initialize payment
     */
    public function initializePayment(array $paymentData): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->post($this->baseUrl . '/payment_intents', [
                'amount' => $this->convertToStripeAmount($paymentData['amount'], $paymentData['currency']),
                'currency' => strtolower($paymentData['currency']),
                'payment_method_types' => ['card'],
                'metadata' => [
                    'transaction_id' => $paymentData['transaction_id'] ?? '',
                    'user_id' => $paymentData['user_id'] ?? '',
                    'receiver_name' => $paymentData['receiver_name'] ?? '',
                ],
                'description' => $paymentData['description'] ?? 'Money Transfer',
                'receipt_email' => $paymentData['email'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'payment_id' => $data['id'],
                    'client_secret' => $data['client_secret'],
                    'status' => $data['status'],
                    'amount' => $this->convertFromStripeAmount($data['amount'], $data['currency']),
                    'currency' => strtoupper($data['currency']),
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to initialize payment',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment initialization failed', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process payment
     */
    public function processPayment(string $paymentId, array $paymentData): array
    {
        try {
            // Confirm payment intent
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->post($this->baseUrl . "/payment_intents/{$paymentId}/confirm", [
                'payment_method' => $paymentData['payment_method'] ?? null,
                'return_url' => $paymentData['return_url'] ?? config('app.url'),
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'payment_id' => $data['id'],
                    'status' => $data['status'],
                    'amount' => $this->convertFromStripeAmount($data['amount'], $data['currency']),
                    'currency' => strtoupper($data['currency']),
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Payment processing failed',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment processing failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process payment through the gateway
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        return $this->processPayment($transaction->gateway_transaction_id, $paymentData);
    }

    /**
     * Verify payment
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . "/payment_intents/{$paymentId}");

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'payment_id' => $data['id'],
                    'status' => $data['status'],
                    'amount' => $this->convertFromStripeAmount($data['amount'], $data['currency']),
                    'currency' => strtoupper($data['currency']),
                    'verified' => in_array($data['status'], ['succeeded', 'processing']),
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Payment verification failed',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment verification failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Refund payment
     */
    public function refundPayment(string $transactionId, float $amount, string $currency = 'USD'): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->post($this->baseUrl . '/refunds', [
                'payment_intent' => $transactionId,
                'amount' => $this->convertToStripeAmount($amount, $currency),
                'reason' => 'requested_by_customer',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'refund_id' => $data['id'],
                    'status' => $data['status'],
                    'amount' => $this->convertFromStripeAmount($data['amount'], $data['currency']),
                    'currency' => strtoupper($data['currency']),
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Refund failed',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('Stripe refund failed', [
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $transactionId): array
    {
        return $this->verifyPayment($transactionId);
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhook(array $headers, string $payload): bool
    {
        try {
            $signature = $headers['stripe-signature'] ?? '';
            $elements = explode(',', $signature);
            
            $timestamp = null;
            $signatures = [];
            
            foreach ($elements as $element) {
                if (strpos($element, 't=') === 0) {
                    $timestamp = substr($element, 2);
                } elseif (strpos($element, 'v1=') === 0) {
                    $signatures[] = substr($element, 3);
                }
            }
            
            if (!$timestamp || empty($signatures)) {
                return false;
            }
            
            $signedPayload = $timestamp . '.' . $payload;
            $expectedSignature = hash_hmac('sha256', $signedPayload, $this->webhookSecret);
            
            foreach ($signatures as $signature) {
                if (hash_equals($expectedSignature, $signature)) {
                    return true;
                }
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error('Stripe webhook validation failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        return [
            'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK',
            'PLN', 'CZK', 'HUF', 'BGN', 'RON', 'HRK', 'ISK', 'MXN', 'BRL', 'SGD',
            'HKD', 'NZD', 'KRW', 'MYR', 'THB', 'PHP', 'INR', 'AED', 'SAR', 'QAR',
        ];
    }

    /**
     * Get gateway configuration
     */
    public function getConfig(): array
    {
        return [
            'name' => 'Stripe',
            'version' => '2023-10-16',
            'supported_methods' => ['card', 'bank_transfer', 'wallet'],
            'supported_currencies' => $this->getSupportedCurrencies(),
            'supported_countries' => $this->getSupportedCountries(),
            'fees' => $this->config['fees'] ?? [],
            'limits' => $this->getTransactionLimits(),
        ];
    }

    /**
     * Get supported countries
     */
    public function getSupportedCountries(): array
    {
        return [
            'US', 'CA', 'GB', 'AU', 'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK',
            'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU',
            'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'CH', 'NO',
            'IS', 'LI', 'JP', 'SG', 'HK', 'NZ', 'MX', 'BR', 'MY', 'TH', 'PH',
            'IN', 'AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'JO', 'LB', 'EG', 'MA',
        ];
    }

    /**
     * Get payment methods
     */
    public function getPaymentMethods(): array
    {
        return [
            'card' => [
                'name' => 'Credit/Debit Card',
                'types' => ['visa', 'mastercard', 'amex', 'discover'],
                'fees' => '2.9% + $0.30',
            ],
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'types' => ['ach', 'sepa'],
                'fees' => '0.8%',
            ],
            'wallet' => [
                'name' => 'Digital Wallet',
                'types' => ['apple_pay', 'google_pay'],
                'fees' => '2.9% + $0.30',
            ],
        ];
    }

    /**
     * Calculate fees
     */
    public function calculateFees(float $amount, string $currency, string $method): array
    {
        $feeStructure = [
            'card' => ['percentage' => 2.9, 'fixed' => 0.30],
            'bank_transfer' => ['percentage' => 0.8, 'fixed' => 0],
            'wallet' => ['percentage' => 2.9, 'fixed' => 0.30],
        ];

        $fees = $feeStructure[$method] ?? $feeStructure['card'];
        $percentageFee = ($amount * $fees['percentage']) / 100;
        $totalFee = $percentageFee + $fees['fixed'];

        return [
            'percentage_fee' => round($percentageFee, 2),
            'fixed_fee' => $fees['fixed'],
            'total_fee' => round($totalFee, 2),
            'net_amount' => round($amount - $totalFee, 2),
        ];
    }

    /**
     * Convert amount to Stripe format (cents)
     */
    private function convertToStripeAmount(float $amount, string $currency): int
    {
        $zeroDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP'];
        
        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return (int) $amount;
        }
        
        return (int) ($amount * 100);
    }

    /**
     * Convert amount from Stripe format
     */
    private function convertFromStripeAmount(int $amount, string $currency): float
    {
        $zeroDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP'];
        
        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return (float) $amount;
        }
        
        return $amount / 100;
    }

    /**
     * Validate payment data
     */
    public function validatePaymentData(array $paymentData): array
    {
        $errors = [];

        if (!isset($paymentData['amount']) || $paymentData['amount'] <= 0) {
            $errors[] = 'Amount must be greater than 0';
        }

        if (!isset($paymentData['currency']) || !in_array($paymentData['currency'], $this->getSupportedCurrencies())) {
            $errors[] = 'Invalid or unsupported currency';
        }

        if (isset($paymentData['amount']) && $paymentData['amount'] > 999999) {
            $errors[] = 'Amount exceeds maximum limit';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Test connection
     */
    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/account');

            return [
                'success' => $response->successful(),
                'status' => $response->status(),
                'message' => $response->successful() ? 'Connection successful' : 'Connection failed',
                'response' => $response->json(),
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Handle webhook
     */
    public function handleWebhook(array $webhookData): array
    {
        try {
            $eventType = $webhookData['type'] ?? '';
            $eventData = $webhookData['data']['object'] ?? [];

            switch ($eventType) {
                case 'payment_intent.succeeded':
                    return $this->handlePaymentSucceeded($eventData);
                case 'payment_intent.payment_failed':
                    return $this->handlePaymentFailed($eventData);
                case 'payment_intent.requires_action':
                    return $this->handlePaymentRequiresAction($eventData);
                default:
                    return ['success' => true, 'message' => 'Event type not handled'];
            }

        } catch (\Exception $e) {
            Log::error('Stripe webhook handling failed', [
                'error' => $e->getMessage(),
                'webhook_data' => $webhookData,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle payment succeeded webhook
     */
    private function handlePaymentSucceeded(array $eventData): array
    {
        $paymentId = $eventData['id'] ?? '';
        
        // Update transaction status in database
        $transaction = Transaction::where('gateway_transaction_id', $paymentId)->first();
        if ($transaction) {
            $transaction->update(['status' => 'completed']);
            
            AuditLogService::logUserAction(
                'payment_succeeded',
                'Payment succeeded via Stripe webhook',
                ['payment_id' => $paymentId],
                $transaction->sender_id
            );
        }

        return ['success' => true, 'message' => 'Payment succeeded processed'];
    }

    /**
     * Handle payment failed webhook
     */
    private function handlePaymentFailed(array $eventData): array
    {
        $paymentId = $eventData['id'] ?? '';
        
        // Update transaction status in database
        $transaction = Transaction::where('gateway_transaction_id', $paymentId)->first();
        if ($transaction) {
            $transaction->update(['status' => 'failed']);
            
            AuditLogService::logUserAction(
                'payment_failed',
                'Payment failed via Stripe webhook',
                ['payment_id' => $paymentId],
                $transaction->sender_id
            );
        }

        return ['success' => true, 'message' => 'Payment failed processed'];
    }

    /**
     * Handle payment requires action webhook
     */
    private function handlePaymentRequiresAction(array $eventData): array
    {
        $paymentId = $eventData['id'] ?? '';
        
        // Update transaction status in database
        $transaction = Transaction::where('gateway_transaction_id', $paymentId)->first();
        if ($transaction) {
            $transaction->update(['status' => 'pending']);
        }

        return ['success' => true, 'message' => 'Payment requires action processed'];
    }

    /**
     * Get transaction limits
     */
    public function getTransactionLimits(): array
    {
        return [
            'min_amount' => 0.50,
            'max_amount' => 999999.99,
            'daily_limit' => 50000.00,
            'monthly_limit' => 200000.00,
        ];
    }

    /**
     * Check if gateway is available
     */
    public function isAvailable(): bool
    {
        return !empty($this->apiKey) && !empty($this->webhookSecret);
    }

    /**
     * Get gateway name
     */
    public function getName(): string
    {
        return 'Stripe';
    }

    /**
     * Get gateway version
     */
    public function getVersion(): string
    {
        return '2023-10-16';
    }
}
