<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->string('code', 3)->unique(); // ISO 3166-1 alpha-3
            $table->string('iso2', 2)->unique(); // ISO 3166-1 alpha-2
            $table->string('phone_code', 10);
            $table->string('currency_code', 3);
            $table->string('flag_url')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('supports_transfers')->default(true);
            $table->boolean('supports_crypto')->default(false);
            $table->decimal('max_transfer_amount', 15, 2)->default(100000);
            $table->decimal('min_transfer_amount', 15, 2)->default(1);
            $table->decimal('commission_rate', 5, 4)->default(0.02); // 2%
            $table->json('supported_payment_methods')->nullable();
            $table->json('required_documents')->nullable();
            $table->text('regulations')->nullable();
            $table->timestamps();
            
            $table->index(['is_active', 'supports_transfers']);
            $table->index('currency_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
