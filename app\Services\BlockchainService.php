<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\Transaction;
use App\Models\Currency;
use App\Models\Wallet;

class BlockchainService
{
    protected array $config;
    protected array $networks;

    public function __construct()
    {
        $this->config = config('blockchain', []);
        $this->networks = $this->config['networks'] ?? [];
    }

    /**
     * Generate a new wallet address for a cryptocurrency.
     */
    public function generateWalletAddress(string $cryptocurrency): array
    {
        $currency = Currency::where('code', $cryptocurrency)->first();
        
        if (!$currency || !$currency->is_crypto) {
            throw new \Exception("Invalid cryptocurrency: {$cryptocurrency}");
        }

        $cryptoConfig = $this->config['cryptocurrencies'][$cryptocurrency] ?? null;
        
        if (!$cryptoConfig) {
            throw new \Exception("Cryptocurrency not configured: {$cryptocurrency}");
        }

        $network = $cryptoConfig['network'];
        $networkConfig = $this->networks[$network] ?? null;

        if (!$networkConfig || !($networkConfig['enabled'] ?? false)) {
            throw new \Exception("Network not available: {$network}");
        }

        try {
            switch ($network) {
                case 'bitcoin':
                    return $this->generateBitcoinAddress();
                case 'ethereum':
                    return $this->generateEthereumAddress();
                case 'binance_smart_chain':
                    return $this->generateBSCAddress();
                default:
                    throw new \Exception("Unsupported network: {$network}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to generate wallet address for {$cryptocurrency}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get wallet balance for a cryptocurrency address.
     */
    public function getWalletBalance(string $cryptocurrency, string $address): array
    {
        $currency = Currency::where('code', $cryptocurrency)->first();
        
        if (!$currency || !$currency->is_crypto) {
            throw new \Exception("Invalid cryptocurrency: {$cryptocurrency}");
        }

        $cryptoConfig = $this->config['cryptocurrencies'][$cryptocurrency] ?? null;
        $network = $cryptoConfig['network'];

        try {
            switch ($network) {
                case 'bitcoin':
                    return $this->getBitcoinBalance($address);
                case 'ethereum':
                    if ($cryptocurrency === 'ETH') {
                        return $this->getEthereumBalance($address);
                    } else {
                        // ERC-20 token
                        $contractAddress = $cryptoConfig['contract_address'] ?? null;
                        return $this->getERC20Balance($address, $contractAddress, $cryptoConfig['decimals']);
                    }
                case 'binance_smart_chain':
                    return $this->getBSCBalance($address, $cryptoConfig);
                default:
                    throw new \Exception("Unsupported network: {$network}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to get balance for {$cryptocurrency} address {$address}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send cryptocurrency transaction.
     */
    public function sendTransaction(array $transactionData): array
    {
        $cryptocurrency = $transactionData['cryptocurrency'];
        $fromAddress = $transactionData['from_address'];
        $toAddress = $transactionData['to_address'];
        $amount = $transactionData['amount'];
        $privateKey = $transactionData['private_key'] ?? null;

        $currency = Currency::where('code', $cryptocurrency)->first();
        
        if (!$currency || !$currency->is_crypto) {
            throw new \Exception("Invalid cryptocurrency: {$cryptocurrency}");
        }

        $cryptoConfig = $this->config['cryptocurrencies'][$cryptocurrency] ?? null;
        $network = $cryptoConfig['network'];

        // Validate minimum amount
        $minAmount = $cryptoConfig['min_amount'] ?? 0;
        if ($amount < $minAmount) {
            throw new \Exception("Amount below minimum: {$minAmount}");
        }

        // Validate maximum amount
        $maxAmount = $cryptoConfig['max_amount'] ?? PHP_FLOAT_MAX;
        if ($amount > $maxAmount) {
            throw new \Exception("Amount exceeds maximum: {$maxAmount}");
        }

        try {
            switch ($network) {
                case 'bitcoin':
                    return $this->sendBitcoinTransaction($fromAddress, $toAddress, $amount, $privateKey);
                case 'ethereum':
                    if ($cryptocurrency === 'ETH') {
                        return $this->sendEthereumTransaction($fromAddress, $toAddress, $amount, $privateKey);
                    } else {
                        // ERC-20 token
                        $contractAddress = $cryptoConfig['contract_address'] ?? null;
                        return $this->sendERC20Transaction($fromAddress, $toAddress, $amount, $contractAddress, $privateKey);
                    }
                case 'binance_smart_chain':
                    return $this->sendBSCTransaction($fromAddress, $toAddress, $amount, $cryptoConfig, $privateKey);
                default:
                    throw new \Exception("Unsupported network: {$network}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to send {$cryptocurrency} transaction: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get transaction status and confirmations.
     */
    public function getTransactionStatus(string $cryptocurrency, string $txHash): array
    {
        $currency = Currency::where('code', $cryptocurrency)->first();
        
        if (!$currency || !$currency->is_crypto) {
            throw new \Exception("Invalid cryptocurrency: {$cryptocurrency}");
        }

        $cryptoConfig = $this->config['cryptocurrencies'][$cryptocurrency] ?? null;
        $network = $cryptoConfig['network'];

        try {
            switch ($network) {
                case 'bitcoin':
                    return $this->getBitcoinTransactionStatus($txHash);
                case 'ethereum':
                case 'binance_smart_chain':
                    return $this->getEthereumTransactionStatus($txHash, $network);
                default:
                    throw new \Exception("Unsupported network: {$network}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to get transaction status for {$txHash}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Monitor blockchain for incoming transactions.
     */
    public function monitorIncomingTransactions(string $cryptocurrency, array $addresses): array
    {
        $incomingTransactions = [];

        foreach ($addresses as $address) {
            try {
                $transactions = $this->getAddressTransactions($cryptocurrency, $address);
                
                foreach ($transactions as $tx) {
                    if ($this->isIncomingTransaction($tx, $address)) {
                        $incomingTransactions[] = [
                            'address' => $address,
                            'tx_hash' => $tx['hash'],
                            'amount' => $tx['amount'],
                            'confirmations' => $tx['confirmations'],
                            'timestamp' => $tx['timestamp'],
                        ];
                    }
                }
            } catch (\Exception $e) {
                Log::error("Failed to monitor address {$address}: " . $e->getMessage());
            }
        }

        return $incomingTransactions;
    }

    /**
     * Get current network fees.
     */
    public function getNetworkFees(string $cryptocurrency): array
    {
        $currency = Currency::where('code', $cryptocurrency)->first();
        
        if (!$currency || !$currency->is_crypto) {
            throw new \Exception("Invalid cryptocurrency: {$cryptocurrency}");
        }

        $cryptoConfig = $this->config['cryptocurrencies'][$cryptocurrency] ?? null;
        $network = $cryptoConfig['network'];

        $cacheKey = "network_fees_{$cryptocurrency}";
        
        return Cache::remember($cacheKey, 300, function () use ($network, $cryptocurrency) {
            try {
                switch ($network) {
                    case 'bitcoin':
                        return $this->getBitcoinNetworkFees();
                    case 'ethereum':
                        return $this->getEthereumNetworkFees();
                    case 'binance_smart_chain':
                        return $this->getBSCNetworkFees();
                    default:
                        throw new \Exception("Unsupported network: {$network}");
                }
            } catch (\Exception $e) {
                Log::error("Failed to get network fees for {$cryptocurrency}: " . $e->getMessage());
                return [
                    'slow' => 0,
                    'standard' => 0,
                    'fast' => 0,
                ];
            }
        });
    }

    /**
     * Validate cryptocurrency address.
     */
    public function validateAddress(string $cryptocurrency, string $address): bool
    {
        $currency = Currency::where('code', $cryptocurrency)->first();
        
        if (!$currency || !$currency->is_crypto) {
            return false;
        }

        $cryptoConfig = $this->config['cryptocurrencies'][$cryptocurrency] ?? null;
        $network = $cryptoConfig['network'];

        try {
            switch ($network) {
                case 'bitcoin':
                    return $this->validateBitcoinAddress($address);
                case 'ethereum':
                case 'binance_smart_chain':
                    return $this->validateEthereumAddress($address);
                default:
                    return false;
            }
        } catch (\Exception $e) {
            Log::error("Failed to validate address {$address}: " . $e->getMessage());
            return false;
        }
    }

    // Bitcoin specific methods
    private function generateBitcoinAddress(): array
    {
        // Mock implementation - in production, use proper Bitcoin library
        $privateKey = bin2hex(random_bytes(32));
        $address = '1' . substr(hash('sha256', $privateKey), 0, 33);
        
        return [
            'address' => $address,
            'private_key' => $privateKey,
            'network' => 'bitcoin',
        ];
    }

    private function getBitcoinBalance(string $address): array
    {
        $apiConfig = $this->config['apis']['blockchain_info'] ?? [];
        
        if (!($apiConfig['enabled'] ?? false)) {
            throw new \Exception("Blockchain.info API not enabled");
        }

        $response = Http::timeout(30)->get("{$apiConfig['base_url']}/balance", [
            'active' => $address,
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to get Bitcoin balance: " . $response->body());
        }

        $data = $response->json();
        $balance = ($data[$address]['final_balance'] ?? 0) / 100000000; // Convert satoshis to BTC

        return [
            'balance' => $balance,
            'confirmed' => $balance,
            'unconfirmed' => 0,
            'currency' => 'BTC',
        ];
    }

    private function sendBitcoinTransaction(string $from, string $to, float $amount, ?string $privateKey): array
    {
        // Mock implementation - in production, use proper Bitcoin library
        $txHash = hash('sha256', $from . $to . $amount . time());
        
        return [
            'tx_hash' => $txHash,
            'status' => 'pending',
            'confirmations' => 0,
            'network_fee' => 0.0001,
        ];
    }

    private function getBitcoinTransactionStatus(string $txHash): array
    {
        $apiConfig = $this->config['apis']['blockchain_info'] ?? [];
        
        $response = Http::timeout(30)->get("{$apiConfig['base_url']}/rawtx/{$txHash}");

        if (!$response->successful()) {
            throw new \Exception("Failed to get Bitcoin transaction status");
        }

        $data = $response->json();
        
        return [
            'tx_hash' => $txHash,
            'confirmations' => $data['confirmations'] ?? 0,
            'status' => ($data['confirmations'] ?? 0) >= 3 ? 'confirmed' : 'pending',
            'block_height' => $data['block_height'] ?? null,
            'timestamp' => $data['time'] ?? null,
        ];
    }

    private function getBitcoinNetworkFees(): array
    {
        // Mock implementation
        return [
            'slow' => 0.00001,
            'standard' => 0.00005,
            'fast' => 0.0001,
        ];
    }

    private function validateBitcoinAddress(string $address): bool
    {
        // Basic Bitcoin address validation
        return preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) === 1;
    }

    // Ethereum specific methods
    private function generateEthereumAddress(): array
    {
        // Mock implementation - in production, use proper Ethereum library
        $privateKey = bin2hex(random_bytes(32));
        $address = '0x' . substr(hash('sha256', $privateKey), 0, 40);
        
        return [
            'address' => $address,
            'private_key' => $privateKey,
            'network' => 'ethereum',
        ];
    }

    private function getEthereumBalance(string $address): array
    {
        $networkConfig = $this->networks['ethereum'] ?? [];
        $rpcUrl = $networkConfig['rpc_url'] ?? '';

        $response = Http::timeout(30)->post($rpcUrl, [
            'jsonrpc' => '2.0',
            'method' => 'eth_getBalance',
            'params' => [$address, 'latest'],
            'id' => 1,
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to get Ethereum balance");
        }

        $data = $response->json();
        $balanceWei = hexdec($data['result'] ?? '0');
        $balance = $balanceWei / 1000000000000000000; // Convert Wei to ETH

        return [
            'balance' => $balance,
            'confirmed' => $balance,
            'unconfirmed' => 0,
            'currency' => 'ETH',
        ];
    }

    private function getERC20Balance(string $address, string $contractAddress, int $decimals): array
    {
        // Mock implementation - in production, use proper Web3 library
        $balance = rand(0, 1000000) / pow(10, $decimals);
        
        return [
            'balance' => $balance,
            'confirmed' => $balance,
            'unconfirmed' => 0,
            'currency' => 'ERC20',
            'contract_address' => $contractAddress,
        ];
    }

    private function sendEthereumTransaction(string $from, string $to, float $amount, ?string $privateKey): array
    {
        // Mock implementation
        $txHash = '0x' . hash('sha256', $from . $to . $amount . time());
        
        return [
            'tx_hash' => $txHash,
            'status' => 'pending',
            'confirmations' => 0,
            'network_fee' => 0.002,
        ];
    }

    private function sendERC20Transaction(string $from, string $to, float $amount, string $contractAddress, ?string $privateKey): array
    {
        // Mock implementation
        $txHash = '0x' . hash('sha256', $from . $to . $amount . $contractAddress . time());
        
        return [
            'tx_hash' => $txHash,
            'status' => 'pending',
            'confirmations' => 0,
            'network_fee' => 0.003,
            'contract_address' => $contractAddress,
        ];
    }

    private function getEthereumTransactionStatus(string $txHash, string $network): array
    {
        $networkConfig = $this->networks[$network] ?? [];
        $rpcUrl = $networkConfig['rpc_url'] ?? '';

        $response = Http::timeout(30)->post($rpcUrl, [
            'jsonrpc' => '2.0',
            'method' => 'eth_getTransactionReceipt',
            'params' => [$txHash],
            'id' => 1,
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to get transaction status");
        }

        $data = $response->json();
        $receipt = $data['result'] ?? null;

        if (!$receipt) {
            return [
                'tx_hash' => $txHash,
                'confirmations' => 0,
                'status' => 'pending',
            ];
        }

        $blockNumber = hexdec($receipt['blockNumber']);
        $currentBlock = $this->getCurrentBlockNumber($network);
        $confirmations = max(0, $currentBlock - $blockNumber + 1);

        return [
            'tx_hash' => $txHash,
            'confirmations' => $confirmations,
            'status' => $confirmations >= 12 ? 'confirmed' : 'pending',
            'block_number' => $blockNumber,
            'gas_used' => hexdec($receipt['gasUsed'] ?? '0'),
        ];
    }

    private function getEthereumNetworkFees(): array
    {
        // Mock implementation
        return [
            'slow' => 0.001,
            'standard' => 0.002,
            'fast' => 0.005,
        ];
    }

    private function validateEthereumAddress(string $address): bool
    {
        return preg_match('/^0x[a-fA-F0-9]{40}$/', $address) === 1;
    }

    // BSC specific methods
    private function generateBSCAddress(): array
    {
        // BSC uses same address format as Ethereum
        return $this->generateEthereumAddress();
    }

    private function getBSCBalance(string $address, array $cryptoConfig): array
    {
        // Similar to Ethereum but on BSC network
        return $this->getEthereumBalance($address);
    }

    private function sendBSCTransaction(string $from, string $to, float $amount, array $cryptoConfig, ?string $privateKey): array
    {
        // Mock implementation
        $txHash = '0x' . hash('sha256', $from . $to . $amount . 'bsc' . time());
        
        return [
            'tx_hash' => $txHash,
            'status' => 'pending',
            'confirmations' => 0,
            'network_fee' => 0.0005,
        ];
    }

    private function getBSCNetworkFees(): array
    {
        return [
            'slow' => 0.0001,
            'standard' => 0.0005,
            'fast' => 0.001,
        ];
    }

    // Helper methods
    private function getCurrentBlockNumber(string $network): int
    {
        $networkConfig = $this->networks[$network] ?? [];
        $rpcUrl = $networkConfig['rpc_url'] ?? '';

        $response = Http::timeout(30)->post($rpcUrl, [
            'jsonrpc' => '2.0',
            'method' => 'eth_blockNumber',
            'params' => [],
            'id' => 1,
        ]);

        if (!$response->successful()) {
            return 0;
        }

        $data = $response->json();
        return hexdec($data['result'] ?? '0');
    }

    private function getAddressTransactions(string $cryptocurrency, string $address): array
    {
        // Mock implementation - in production, query actual blockchain APIs
        return [
            [
                'hash' => hash('sha256', $address . time()),
                'amount' => rand(1, 1000) / 100,
                'confirmations' => rand(0, 20),
                'timestamp' => time(),
                'type' => 'incoming',
            ],
        ];
    }

    private function isIncomingTransaction(array $transaction, string $address): bool
    {
        return $transaction['type'] === 'incoming';
    }

    /**
     * Get cryptocurrency price from external API.
     */
    public function getCryptocurrencyPrice(string $cryptocurrency): array
    {
        $cacheKey = "crypto_price_{$cryptocurrency}";
        
        return Cache::remember($cacheKey, 120, function () use ($cryptocurrency) {
            try {
                $priceFeeds = $this->config['price_feeds'] ?? [];
                $provider = $priceFeeds['primary'] ?? 'coingecko';
                
                switch ($provider) {
                    case 'coingecko':
                        return $this->getCoinGeckoPrice($cryptocurrency);
                    case 'coinmarketcap':
                        return $this->getCoinMarketCapPrice($cryptocurrency);
                    default:
                        throw new \Exception("Unknown price provider: {$provider}");
                }
            } catch (\Exception $e) {
                Log::error("Failed to get price for {$cryptocurrency}: " . $e->getMessage());
                return [
                    'price_usd' => 0,
                    'change_24h' => 0,
                    'last_updated' => now()->toISOString(),
                ];
            }
        });
    }

    private function getCoinGeckoPrice(string $cryptocurrency): array
    {
        $coinMap = [
            'BTC' => 'bitcoin',
            'ETH' => 'ethereum',
            'USDT' => 'tether',
            'USDC' => 'usd-coin',
            'BNB' => 'binancecoin',
        ];
        
        $coinId = $coinMap[strtoupper($cryptocurrency)] ?? strtolower($cryptocurrency);
        
        $response = Http::timeout(30)->get('https://api.coingecko.com/api/v3/simple/price', [
            'ids' => $coinId,
            'vs_currencies' => 'usd',
            'include_24hr_change' => 'true',
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to get price from CoinGecko");
        }

        $data = $response->json();
        $priceData = $data[$coinId] ?? [];

        return [
            'price_usd' => $priceData['usd'] ?? 0,
            'change_24h' => $priceData['usd_24h_change'] ?? 0,
            'last_updated' => now()->toISOString(),
        ];
    }

    private function getCoinMarketCapPrice(string $cryptocurrency): array
    {
        // Mock implementation
        return [
            'price_usd' => rand(1, 50000),
            'change_24h' => rand(-10, 10),
            'last_updated' => now()->toISOString(),
        ];
    }

    /**
     * Update cryptocurrency prices for all supported currencies.
     */
    public function updateAllCryptocurrencyPrices(): array
    {
        $cryptocurrencies = Currency::where('is_crypto', true)
            ->where('is_active', true)
            ->pluck('code')
            ->toArray();

        $results = [];

        foreach ($cryptocurrencies as $crypto) {
            try {
                $priceData = $this->getCryptocurrencyPrice($crypto);
                
                Currency::where('code', $crypto)->update([
                    'rate_to_usd' => $priceData['price_usd'],
                    'last_updated' => now(),
                ]);

                $results[$crypto] = [
                    'status' => 'success',
                    'price' => $priceData['price_usd'],
                    'change_24h' => $priceData['change_24h'],
                ];
            } catch (\Exception $e) {
                $results[$crypto] = [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
            }
        }

        Log::info("Updated cryptocurrency prices", $results);

        return $results;
    }

    /**
     * Get crypto price from CoinGecko API (enhanced version).
     */
    private function getCryptoPriceFromCoinGecko(string $symbol): ?array
    {
        try {
            $coinId = $this->getCoinGeckoId($symbol);

            if (!$coinId) {
                return null;
            }

            $response = Http::timeout(10)->get('https://api.coingecko.com/api/v3/simple/price', [
                'ids' => $coinId,
                'vs_currencies' => 'usd',
                'include_24hr_change' => 'true',
                'include_market_cap' => 'true',
                'include_24hr_vol' => 'true',
            ]);

            if (!$response->successful()) {
                return null;
            }

            $data = $response->json();

            if (!isset($data[$coinId])) {
                return null;
            }

            return [
                'price_usd' => $data[$coinId]['usd'],
                'change_24h' => $data[$coinId]['usd_24h_change'] ?? 0,
                'market_cap' => $data[$coinId]['usd_market_cap'] ?? 0,
                'volume_24h' => $data[$coinId]['usd_24h_vol'] ?? 0,
                'source' => 'coingecko',
                'timestamp' => now()->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::warning('CoinGecko API failed', [
                'symbol' => $symbol,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get crypto price from CoinMarketCap API (enhanced version).
     */
    private function getCryptoPriceFromCoinMarketCap(string $symbol): ?array
    {
        try {
            $apiKey = config('services.coinmarketcap.api_key');

            if (!$apiKey) {
                return null;
            }

            $response = Http::withHeaders([
                'X-CMC_PRO_API_KEY' => $apiKey,
                'Accept' => 'application/json',
            ])->timeout(10)->get('https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest', [
                'symbol' => strtoupper($symbol),
                'convert' => 'USD',
            ]);

            if (!$response->successful()) {
                return null;
            }

            $data = $response->json();

            if (!isset($data['data'][strtoupper($symbol)])) {
                return null;
            }

            $cryptoData = $data['data'][strtoupper($symbol)];
            $quote = $cryptoData['quote']['USD'];

            return [
                'price_usd' => $quote['price'],
                'change_24h' => $quote['percent_change_24h'] ?? 0,
                'market_cap' => $quote['market_cap'] ?? 0,
                'volume_24h' => $quote['volume_24h'] ?? 0,
                'source' => 'coinmarketcap',
                'timestamp' => now()->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::warning('CoinMarketCap API failed', [
                'symbol' => $symbol,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get mock crypto price for fallback.
     */
    private function getMockCryptoPrice(string $symbol): array
    {
        $mockPrices = [
            'BTC' => ['price_usd' => 45000.00, 'change_24h' => 2.5, 'market_cap' => 850000000000, 'volume_24h' => 25000000000],
            'ETH' => ['price_usd' => 3200.00, 'change_24h' => -1.2, 'market_cap' => 380000000000, 'volume_24h' => 15000000000],
            'LTC' => ['price_usd' => 180.00, 'change_24h' => 0.8, 'market_cap' => 13000000000, 'volume_24h' => 2500000000],
            'XRP' => ['price_usd' => 0.75, 'change_24h' => -0.5, 'market_cap' => 35000000000, 'volume_24h' => 3000000000],
            'ADA' => ['price_usd' => 1.25, 'change_24h' => 1.8, 'market_cap' => 40000000000, 'volume_24h' => 2000000000],
            'DOT' => ['price_usd' => 35.50, 'change_24h' => -0.3, 'market_cap' => 35000000000, 'volume_24h' => 1500000000],
            'LINK' => ['price_usd' => 28.75, 'change_24h' => 3.2, 'market_cap' => 13000000000, 'volume_24h' => 1200000000],
            'BNB' => ['price_usd' => 420.00, 'change_24h' => 1.5, 'market_cap' => 70000000000, 'volume_24h' => 2800000000],
        ];

        $price = $mockPrices[strtoupper($symbol)] ?? [
            'price_usd' => 0,
            'change_24h' => 0,
            'market_cap' => 0,
            'volume_24h' => 0
        ];

        $price['source'] = 'mock';
        $price['timestamp'] = now()->toISOString();

        return $price;
    }

    /**
     * Get CoinGecko ID for symbol.
     */
    private function getCoinGeckoId(string $symbol): ?string
    {
        $symbolToId = [
            'BTC' => 'bitcoin',
            'ETH' => 'ethereum',
            'LTC' => 'litecoin',
            'XRP' => 'ripple',
            'ADA' => 'cardano',
            'DOT' => 'polkadot',
            'LINK' => 'chainlink',
            'BNB' => 'binancecoin',
            'SOL' => 'solana',
            'MATIC' => 'matic-network',
            'AVAX' => 'avalanche-2',
            'ATOM' => 'cosmos',
            'UNI' => 'uniswap',
            'AAVE' => 'aave',
            'COMP' => 'compound-governance-token',
            'MKR' => 'maker',
            'SNX' => 'havven',
            'YFI' => 'yearn-finance',
        ];

        return $symbolToId[strtoupper($symbol)] ?? null;
    }

    /**
     * Get real-time Bitcoin network stats.
     */
    public function getBitcoinNetworkStats(): array
    {
        try {
            $response = Http::timeout(10)->get('https://blockstream.info/api/stats');

            if (!$response->successful()) {
                return $this->getMockBitcoinStats();
            }

            $stats = $response->json();

            return [
                'hash_rate' => $stats['hashrate_1d'] ?? 0,
                'difficulty' => $stats['difficulty'] ?? 0,
                'mempool_size' => $stats['mempool_count'] ?? 0,
                'mempool_bytes' => $stats['mempool_vsize'] ?? 0,
                'avg_fee_rate' => $stats['fee_rate'] ?? 0,
                'blocks_today' => $stats['blocks_count'] ?? 0,
                'source' => 'blockstream',
                'timestamp' => now()->toISOString(),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get Bitcoin network stats', [
                'error' => $e->getMessage(),
            ]);

            return $this->getMockBitcoinStats();
        }
    }

    /**
     * Get mock Bitcoin stats for fallback.
     */
    private function getMockBitcoinStats(): array
    {
        return [
            'hash_rate' => 150000000000000000000, // 150 EH/s
            'difficulty' => 25000000000000,
            'mempool_size' => 50000,
            'mempool_bytes' => 100000000,
            'avg_fee_rate' => 20,
            'blocks_today' => 144,
            'source' => 'mock',
            'timestamp' => now()->toISOString(),
        ];
    }
}
