<?php

namespace App\Observers;

use App\Events\TransactionCreated;
use App\Events\TransactionStatusUpdated;
use App\Models\Transaction;
use App\Services\FraudDetectionService;
use Illuminate\Support\Facades\Log;

class TransactionObserver
{
    protected FraudDetectionService $fraudDetectionService;

    public function __construct(FraudDetectionService $fraudDetectionService)
    {
        $this->fraudDetectionService = $fraudDetectionService;
    }

    /**
     * Handle the Transaction "created" event.
     */
    public function created(Transaction $transaction): void
    {
        try {
            Log::channel('transactions')->info('Transaction created', [
                'transaction_id' => $transaction->id,
                'transaction_number' => $transaction->transaction_number,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency->code,
                'sender_id' => $transaction->sender_id,
                'receiver_name' => $transaction->receiver_name,
            ]);

            // Dispatch event for notifications
            TransactionCreated::dispatch($transaction);

            // Update user statistics
            $this->updateUserStatistics($transaction);

            // Update wallet statistics
            $this->updateWalletStatistics($transaction);

        } catch (\Exception $e) {
            Log::error('Error in TransactionObserver::created', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle the Transaction "updated" event.
     */
    public function updated(Transaction $transaction): void
    {
        try {
            $changes = $transaction->getChanges();
            
            Log::channel('transactions')->info('Transaction updated', [
                'transaction_id' => $transaction->id,
                'transaction_number' => $transaction->transaction_number,
                'changes' => $changes,
            ]);

            // Check if status changed
            if (isset($changes['status'])) {
                $previousStatus = $transaction->getOriginal('status');
                $this->handleStatusChange($transaction, $previousStatus);
            }

            // Check if amount changed (potential fraud)
            if (isset($changes['amount'])) {
                Log::warning('Transaction amount changed', [
                    'transaction_id' => $transaction->id,
                    'old_amount' => $transaction->getOriginal('amount'),
                    'new_amount' => $transaction->amount,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error in TransactionObserver::updated', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle the Transaction "deleted" event.
     */
    public function deleted(Transaction $transaction): void
    {
        try {
            Log::channel('audit')->warning('Transaction deleted', [
                'transaction_id' => $transaction->id,
                'transaction_number' => $transaction->transaction_number,
                'amount' => $transaction->amount,
                'status' => $transaction->status,
                'deleted_at' => now(),
            ]);

            // Reverse any wallet operations if transaction was completed
            if ($transaction->status === 'completed') {
                $this->reverseWalletOperations($transaction);
            }

        } catch (\Exception $e) {
            Log::error('Error in TransactionObserver::deleted', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle transaction status changes.
     */
    private function handleStatusChange(Transaction $transaction, string $previousStatus): void
    {
        Log::channel('transactions')->info('Transaction status changed', [
            'transaction_id' => $transaction->id,
            'transaction_number' => $transaction->transaction_number,
            'previous_status' => $previousStatus,
            'new_status' => $transaction->status,
        ]);

        // Dispatch status update event
        TransactionStatusUpdated::dispatch($transaction, $previousStatus);

        // Handle specific status changes
        switch ($transaction->status) {
            case 'completed':
                $this->handleTransactionCompleted($transaction);
                break;
            case 'failed':
                $this->handleTransactionFailed($transaction);
                break;
            case 'cancelled':
                $this->handleTransactionCancelled($transaction);
                break;
            case 'blocked':
                $this->handleTransactionBlocked($transaction);
                break;
        }
    }

    /**
     * Handle completed transaction.
     */
    private function handleTransactionCompleted(Transaction $transaction): void
    {
        // Update completion timestamp
        if (!$transaction->completed_at) {
            $transaction->update(['completed_at' => now()]);
        }

        // Update user transaction counts
        $sender = $transaction->sender;
        $sender->increment('total_transactions_sent');
        $sender->increment('total_amount_sent', $transaction->amount);

        if ($transaction->receiver_id) {
            $receiver = $transaction->receiver;
            $receiver->increment('total_transactions_received');
            $receiver->increment('total_amount_received', $transaction->net_amount);
        }

        // Update currency statistics
        $currency = $transaction->currency;
        $currency->increment('total_transactions');
        $currency->increment('total_volume', $transaction->amount);

        Log::channel('financial')->info('Transaction completed successfully', [
            'transaction_id' => $transaction->id,
            'transaction_number' => $transaction->transaction_number,
            'amount' => $transaction->amount,
            'net_amount' => $transaction->net_amount,
        ]);
    }

    /**
     * Handle failed transaction.
     */
    private function handleTransactionFailed(Transaction $transaction): void
    {
        // Unfreeze sender funds if they were frozen
        if ($transaction->senderWallet) {
            $transaction->senderWallet->unfreezeFunds($transaction->amount);
        }

        // Remove pending balance from receiver wallet
        if ($transaction->receiverWallet) {
            $transaction->receiverWallet->removePendingBalance($transaction->net_amount);
        }

        Log::channel('financial')->warning('Transaction failed', [
            'transaction_id' => $transaction->id,
            'transaction_number' => $transaction->transaction_number,
            'amount' => $transaction->amount,
            'reason' => $transaction->notes,
        ]);
    }

    /**
     * Handle cancelled transaction.
     */
    private function handleTransactionCancelled(Transaction $transaction): void
    {
        // Unfreeze sender funds
        if ($transaction->senderWallet) {
            $transaction->senderWallet->unfreezeFunds($transaction->amount);
        }

        // Remove pending balance from receiver wallet
        if ($transaction->receiverWallet) {
            $transaction->receiverWallet->removePendingBalance($transaction->net_amount);
        }

        Log::channel('financial')->info('Transaction cancelled', [
            'transaction_id' => $transaction->id,
            'transaction_number' => $transaction->transaction_number,
            'amount' => $transaction->amount,
        ]);
    }

    /**
     * Handle blocked transaction.
     */
    private function handleTransactionBlocked(Transaction $transaction): void
    {
        // Mark as suspicious
        $transaction->update(['is_suspicious' => true]);

        // Unfreeze sender funds
        if ($transaction->senderWallet) {
            $transaction->senderWallet->unfreezeFunds($transaction->amount);
        }

        // Remove pending balance from receiver wallet
        if ($transaction->receiverWallet) {
            $transaction->receiverWallet->removePendingBalance($transaction->net_amount);
        }

        Log::channel('security')->warning('Transaction blocked', [
            'transaction_id' => $transaction->id,
            'transaction_number' => $transaction->transaction_number,
            'amount' => $transaction->amount,
            'sender_id' => $transaction->sender_id,
        ]);
    }

    /**
     * Update user statistics.
     */
    private function updateUserStatistics(Transaction $transaction): void
    {
        $sender = $transaction->sender;
        
        // Update last transaction date
        $sender->update(['last_transaction_at' => now()]);

        // Update daily/monthly limits usage
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();

        $dailyUsage = $sender->sentTransactions()
            ->where('created_at', '>=', $today)
            ->where('status', '!=', 'failed')
            ->sum('amount');

        $monthlyUsage = $sender->sentTransactions()
            ->where('created_at', '>=', $thisMonth)
            ->where('status', '!=', 'failed')
            ->sum('amount');

        $sender->update([
            'daily_usage' => $dailyUsage,
            'monthly_usage' => $monthlyUsage,
        ]);
    }

    /**
     * Update wallet statistics.
     */
    private function updateWalletStatistics(Transaction $transaction): void
    {
        if ($transaction->senderWallet) {
            $wallet = $transaction->senderWallet;
            $wallet->update([
                'last_transaction_at' => now(),
                'transaction_count' => $wallet->transaction_count + 1,
                'total_sent' => $wallet->total_sent + $transaction->amount,
            ]);
        }

        if ($transaction->receiverWallet) {
            $wallet = $transaction->receiverWallet;
            $wallet->update([
                'last_transaction_at' => now(),
                'transaction_count' => $wallet->transaction_count + 1,
                'total_received' => $wallet->total_received + $transaction->net_amount,
            ]);
        }
    }

    /**
     * Reverse wallet operations for deleted transaction.
     */
    private function reverseWalletOperations(Transaction $transaction): void
    {
        if ($transaction->senderWallet) {
            $transaction->senderWallet->addFunds($transaction->amount, 'reversal');
        }

        if ($transaction->receiverWallet) {
            $transaction->receiverWallet->deductFunds($transaction->net_amount, 'reversal');
        }

        Log::channel('audit')->info('Wallet operations reversed for deleted transaction', [
            'transaction_id' => $transaction->id,
            'amount' => $transaction->amount,
            'net_amount' => $transaction->net_amount,
        ]);
    }
}
