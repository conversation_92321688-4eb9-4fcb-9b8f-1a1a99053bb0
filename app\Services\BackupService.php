<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;
use ZipArchive;

class BackupService
{
    protected array $config;
    protected string $backupPath;
    protected array $notifications = [];

    public function __construct()
    {
        $this->config = config('backup', []);
        $this->backupPath = storage_path('backups');
        
        if (!File::exists($this->backupPath)) {
            File::makeDirectory($this->backupPath, 0755, true);
        }
    }

    /**
     * Create a full system backup.
     */
    public function createFullBackup(): array
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupName = "full_backup_{$timestamp}";
        
        Log::info("Starting full backup: {$backupName}");
        
        try {
            $results = [
                'backup_name' => $backupName,
                'timestamp' => $timestamp,
                'components' => [],
                'total_size' => 0,
                'duration' => 0,
                'status' => 'success',
            ];
            
            $startTime = microtime(true);
            
            // Database backup
            $dbResult = $this->backupDatabase($backupName);
            $results['components']['database'] = $dbResult;
            $results['total_size'] += $dbResult['size'] ?? 0;
            
            // Files backup
            $filesResult = $this->backupFiles($backupName);
            $results['components']['files'] = $filesResult;
            $results['total_size'] += $filesResult['size'] ?? 0;
            
            // Configuration backup
            $configResult = $this->backupConfiguration($backupName);
            $results['components']['configuration'] = $configResult;
            $results['total_size'] += $configResult['size'] ?? 0;
            
            // User uploads backup
            $uploadsResult = $this->backupUserUploads($backupName);
            $results['components']['uploads'] = $uploadsResult;
            $results['total_size'] += $uploadsResult['size'] ?? 0;
            
            // Create manifest
            $this->createBackupManifest($backupName, $results);
            
            // Compress backup
            if ($this->config['performance']['compression']['enabled'] ?? true) {
                $this->compressBackup($backupName);
            }
            
            // Encrypt backup
            if ($this->config['security']['encryption']['enabled'] ?? true) {
                $this->encryptBackup($backupName);
            }
            
            // Upload to remote destinations
            $this->uploadToRemoteDestinations($backupName);
            
            $results['duration'] = round(microtime(true) - $startTime, 2);
            
            Log::info("Full backup completed successfully", $results);
            
            // Send success notification
            $this->sendNotification('backup_success', $results);
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error("Full backup failed: " . $e->getMessage(), [
                'backup_name' => $backupName,
                'error' => $e->getTraceAsString(),
            ]);
            
            // Send failure notification
            $this->sendNotification('backup_failure', [
                'backup_name' => $backupName,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Create database backup.
     */
    public function backupDatabase(string $backupName): array
    {
        $filename = "{$backupName}_database.sql";
        $filepath = "{$this->backupPath}/{$filename}";
        
        try {
            $connection = config('database.default');
            $database = config("database.connections.{$connection}.database");
            $username = config("database.connections.{$connection}.username");
            $password = config("database.connections.{$connection}.password");
            $host = config("database.connections.{$connection}.host");
            $port = config("database.connections.{$connection}.port", 3306);
            
            // Build mysqldump command
            $command = sprintf(
                'mysqldump --user=%s --password=%s --host=%s --port=%s --single-transaction --routines --triggers %s > %s',
                escapeshellarg($username),
                escapeshellarg($password),
                escapeshellarg($host),
                escapeshellarg($port),
                escapeshellarg($database),
                escapeshellarg($filepath)
            );
            
            // Execute backup
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0) {
                throw new \Exception("Database backup failed with return code: {$returnCode}");
            }
            
            $size = File::size($filepath);
            
            // Verify backup integrity
            $this->verifyDatabaseBackup($filepath);
            
            return [
                'status' => 'success',
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => $size,
                'tables_count' => $this->getDatabaseTablesCount(),
                'checksum' => hash_file('sha256', $filepath),
            ];
            
        } catch (\Exception $e) {
            Log::error("Database backup failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create files backup.
     */
    public function backupFiles(string $backupName): array
    {
        $filename = "{$backupName}_files.tar.gz";
        $filepath = "{$this->backupPath}/{$filename}";
        
        try {
            $sourcePaths = $this->config['sources']['files']['paths'] ?? [
                storage_path('app'),
                storage_path('logs'),
            ];
            
            $excludePaths = $this->config['sources']['files']['exclude'] ?? [];
            
            // Create tar command
            $command = "tar -czf " . escapeshellarg($filepath);
            
            // Add exclude patterns
            foreach ($excludePaths as $exclude) {
                $command .= " --exclude=" . escapeshellarg($exclude);
            }
            
            // Add source paths
            foreach ($sourcePaths as $path) {
                if (File::exists($path)) {
                    $command .= " " . escapeshellarg($path);
                }
            }
            
            // Execute backup
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0) {
                throw new \Exception("Files backup failed with return code: {$returnCode}");
            }
            
            $size = File::size($filepath);
            
            return [
                'status' => 'success',
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => $size,
                'files_count' => $this->countFilesInPaths($sourcePaths, $excludePaths),
                'checksum' => hash_file('sha256', $filepath),
            ];
            
        } catch (\Exception $e) {
            Log::error("Files backup failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create configuration backup.
     */
    public function backupConfiguration(string $backupName): array
    {
        $filename = "{$backupName}_config.tar.gz";
        $filepath = "{$this->backupPath}/{$filename}";
        
        try {
            $configPaths = [
                base_path('config'),
                base_path('routes'),
                base_path('database/migrations'),
                base_path('database/seeders'),
                base_path('.env'),
                base_path('composer.json'),
                base_path('composer.lock'),
                base_path('package.json'),
            ];
            
            // Create tar command
            $command = "tar -czf " . escapeshellarg($filepath);
            
            foreach ($configPaths as $path) {
                if (File::exists($path)) {
                    $command .= " " . escapeshellarg($path);
                }
            }
            
            // Execute backup
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0) {
                throw new \Exception("Configuration backup failed with return code: {$returnCode}");
            }
            
            $size = File::size($filepath);
            
            return [
                'status' => 'success',
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => $size,
                'checksum' => hash_file('sha256', $filepath),
            ];
            
        } catch (\Exception $e) {
            Log::error("Configuration backup failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create user uploads backup.
     */
    public function backupUserUploads(string $backupName): array
    {
        $filename = "{$backupName}_uploads.tar.gz";
        $filepath = "{$this->backupPath}/{$filename}";
        
        try {
            $uploadPaths = $this->config['sources']['user_uploads']['paths'] ?? [
                storage_path('app/documents'),
                storage_path('app/receipts'),
                storage_path('app/kyc'),
            ];
            
            // Create tar command
            $command = "tar -czf " . escapeshellarg($filepath);
            
            $existingPaths = [];
            foreach ($uploadPaths as $path) {
                if (File::exists($path)) {
                    $command .= " " . escapeshellarg($path);
                    $existingPaths[] = $path;
                }
            }
            
            if (empty($existingPaths)) {
                // Create empty archive
                $command = "tar -czf " . escapeshellarg($filepath) . " --files-from /dev/null";
            }
            
            // Execute backup
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0 && !empty($existingPaths)) {
                throw new \Exception("User uploads backup failed with return code: {$returnCode}");
            }
            
            $size = File::exists($filepath) ? File::size($filepath) : 0;
            
            return [
                'status' => 'success',
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => $size,
                'files_count' => $this->countFilesInPaths($existingPaths),
                'checksum' => $size > 0 ? hash_file('sha256', $filepath) : null,
            ];
            
        } catch (\Exception $e) {
            Log::error("User uploads backup failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create backup manifest.
     */
    public function createBackupManifest(string $backupName, array $backupData): void
    {
        $manifest = [
            'backup_name' => $backupName,
            'created_at' => now()->toISOString(),
            'system_info' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'server_os' => PHP_OS,
                'hostname' => gethostname(),
            ],
            'backup_config' => [
                'compression_enabled' => $this->config['performance']['compression']['enabled'] ?? false,
                'encryption_enabled' => $this->config['security']['encryption']['enabled'] ?? false,
                'backup_type' => 'full',
            ],
            'components' => $backupData['components'] ?? [],
            'total_size' => $backupData['total_size'] ?? 0,
            'checksums' => $this->generateChecksums($backupName),
        ];
        
        $manifestPath = "{$this->backupPath}/{$backupName}_manifest.json";
        File::put($manifestPath, json_encode($manifest, JSON_PRETTY_PRINT));
    }

    /**
     * Compress backup files.
     */
    public function compressBackup(string $backupName): void
    {
        if (!class_exists('ZipArchive')) {
            Log::warning("ZipArchive not available, skipping compression");
            return;
        }
        
        $zipPath = "{$this->backupPath}/{$backupName}.zip";
        $zip = new ZipArchive();
        
        if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception("Cannot create zip file: {$zipPath}");
        }
        
        // Add all backup files to zip
        $files = File::glob("{$this->backupPath}/{$backupName}_*");
        
        foreach ($files as $file) {
            if (File::isFile($file)) {
                $zip->addFile($file, basename($file));
            }
        }
        
        $zip->close();
        
        // Remove individual files after compression
        foreach ($files as $file) {
            if (File::isFile($file) && !str_contains($file, '_manifest.json')) {
                File::delete($file);
            }
        }
        
        Log::info("Backup compressed successfully: {$zipPath}");
    }

    /**
     * Encrypt backup files.
     */
    public function encryptBackup(string $backupName): void
    {
        $encryptionKey = $this->config['security']['encryption']['key'] ?? config('app.key');
        
        if (!$encryptionKey) {
            throw new \Exception("Encryption key not configured");
        }
        
        $files = File::glob("{$this->backupPath}/{$backupName}*");
        
        foreach ($files as $file) {
            if (File::isFile($file) && !str_ends_with($file, '.encrypted')) {
                $this->encryptFile($file, $encryptionKey);
            }
        }
        
        Log::info("Backup encrypted successfully");
    }

    /**
     * Upload backup to remote destinations.
     */
    public function uploadToRemoteDestinations(string $backupName): void
    {
        $destinations = $this->config['destinations'] ?? [];
        
        foreach ($destinations as $name => $config) {
            if (!($config['enabled'] ?? false)) {
                continue;
            }
            
            try {
                $this->uploadToDestination($backupName, $name, $config);
                Log::info("Backup uploaded to {$name} successfully");
            } catch (\Exception $e) {
                Log::error("Failed to upload backup to {$name}: " . $e->getMessage());
                // Continue with other destinations
            }
        }
    }

    /**
     * Upload to specific destination.
     */
    private function uploadToDestination(string $backupName, string $destinationName, array $config): void
    {
        $files = File::glob("{$this->backupPath}/{$backupName}*");
        
        switch ($destinationName) {
            case 's3':
                $this->uploadToS3($files, $config);
                break;
            case 'ftp':
                $this->uploadToFTP($files, $config);
                break;
            case 'sftp':
                $this->uploadToSFTP($files, $config);
                break;
            default:
                Log::warning("Unknown destination type: {$destinationName}");
        }
    }

    /**
     * Clean up old backups.
     */
    public function cleanupOldBackups(): array
    {
        $retentionPolicy = $this->config['cleanup']['retention_policy'] ?? [
            'daily' => 7,
            'weekly' => 4,
            'monthly' => 12,
        ];
        
        $cleaned = [
            'local' => $this->cleanupLocalBackups($retentionPolicy),
            'remote' => $this->cleanupRemoteBackups($retentionPolicy),
        ];
        
        Log::info("Backup cleanup completed", $cleaned);
        
        return $cleaned;
    }

    /**
     * Verify backup integrity.
     */
    public function verifyBackupIntegrity(string $backupName): array
    {
        $manifestPath = "{$this->backupPath}/{$backupName}_manifest.json";
        
        if (!File::exists($manifestPath)) {
            throw new \Exception("Backup manifest not found: {$manifestPath}");
        }
        
        $manifest = json_decode(File::get($manifestPath), true);
        $results = [
            'backup_name' => $backupName,
            'verification_time' => now()->toISOString(),
            'status' => 'success',
            'checks' => [],
        ];
        
        // Verify file existence
        foreach ($manifest['components'] as $component => $data) {
            $filepath = $data['filepath'] ?? null;
            
            if ($filepath && File::exists($filepath)) {
                $currentChecksum = hash_file('sha256', $filepath);
                $originalChecksum = $data['checksum'] ?? null;
                
                $results['checks'][$component] = [
                    'file_exists' => true,
                    'checksum_match' => $currentChecksum === $originalChecksum,
                    'current_checksum' => $currentChecksum,
                    'original_checksum' => $originalChecksum,
                ];
                
                if ($currentChecksum !== $originalChecksum) {
                    $results['status'] = 'corrupted';
                }
            } else {
                $results['checks'][$component] = [
                    'file_exists' => false,
                    'checksum_match' => false,
                ];
                $results['status'] = 'missing_files';
            }
        }
        
        return $results;
    }

    /**
     * Restore from backup.
     */
    public function restoreFromBackup(string $backupName, array $components = []): array
    {
        if (!$this->config['restore']['enabled'] ?? true) {
            throw new \Exception("Restore functionality is disabled");
        }
        
        // Verify backup integrity first
        $verification = $this->verifyBackupIntegrity($backupName);
        
        if ($verification['status'] !== 'success') {
            throw new \Exception("Backup verification failed: " . $verification['status']);
        }
        
        // Create restore point
        if ($this->config['restore']['create_restore_point'] ?? true) {
            $this->createRestorePoint();
        }
        
        $results = [
            'backup_name' => $backupName,
            'restore_time' => now()->toISOString(),
            'components_restored' => [],
            'status' => 'success',
        ];
        
        // Restore components
        if (empty($components)) {
            $components = ['database', 'files', 'configuration', 'uploads'];
        }
        
        foreach ($components as $component) {
            try {
                $this->restoreComponent($backupName, $component);
                $results['components_restored'][] = $component;
            } catch (\Exception $e) {
                Log::error("Failed to restore component {$component}: " . $e->getMessage());
                $results['status'] = 'partial_failure';
                $results['errors'][$component] = $e->getMessage();
            }
        }
        
        Log::info("Restore completed", $results);
        
        return $results;
    }

    // Helper methods
    private function verifyDatabaseBackup(string $filepath): void
    {
        $content = File::get($filepath);
        
        if (empty($content)) {
            throw new \Exception("Database backup file is empty");
        }
        
        if (!str_contains($content, 'CREATE TABLE')) {
            throw new \Exception("Database backup appears to be invalid");
        }
    }

    private function getDatabaseTablesCount(): int
    {
        try {
            $tables = DB::select('SHOW TABLES');
            return count($tables);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function countFilesInPaths(array $paths, array $excludes = []): int
    {
        $count = 0;
        
        foreach ($paths as $path) {
            if (File::exists($path)) {
                if (File::isFile($path)) {
                    $count++;
                } else {
                    $files = File::allFiles($path);
                    $count += count($files);
                }
            }
        }
        
        return $count;
    }

    private function generateChecksums(string $backupName): array
    {
        $checksums = [];
        $files = File::glob("{$this->backupPath}/{$backupName}_*");
        
        foreach ($files as $file) {
            if (File::isFile($file)) {
                $checksums[basename($file)] = hash_file('sha256', $file);
            }
        }
        
        return $checksums;
    }

    private function encryptFile(string $filepath, string $key): void
    {
        $data = File::get($filepath);
        $encrypted = encrypt($data);
        
        File::put($filepath . '.encrypted', $encrypted);
        File::delete($filepath);
    }

    private function uploadToS3(array $files, array $config): void
    {
        // Implementation for S3 upload
        // This would use AWS SDK
        Log::info("S3 upload not implemented yet");
    }

    private function uploadToFTP(array $files, array $config): void
    {
        // Implementation for FTP upload
        Log::info("FTP upload not implemented yet");
    }

    private function uploadToSFTP(array $files, array $config): void
    {
        // Implementation for SFTP upload
        Log::info("SFTP upload not implemented yet");
    }

    private function cleanupLocalBackups(array $retentionPolicy): array
    {
        $cleaned = [];
        $backups = $this->getLocalBackups();
        
        // Group backups by type (daily, weekly, monthly)
        $grouped = $this->groupBackupsByType($backups);
        
        foreach ($grouped as $type => $typeBackups) {
            $retention = $retentionPolicy[$type] ?? 0;
            
            if ($retention > 0 && count($typeBackups) > $retention) {
                $toDelete = array_slice($typeBackups, $retention);
                
                foreach ($toDelete as $backup) {
                    $this->deleteLocalBackup($backup);
                    $cleaned[] = $backup;
                }
            }
        }
        
        return $cleaned;
    }

    private function cleanupRemoteBackups(array $retentionPolicy): array
    {
        // Implementation for remote backup cleanup
        return [];
    }

    private function getLocalBackups(): array
    {
        $backups = [];
        $files = File::glob("{$this->backupPath}/*_manifest.json");
        
        foreach ($files as $file) {
            $manifest = json_decode(File::get($file), true);
            $backups[] = [
                'name' => $manifest['backup_name'],
                'created_at' => Carbon::parse($manifest['created_at']),
                'size' => $manifest['total_size'],
            ];
        }
        
        // Sort by creation date (newest first)
        usort($backups, function ($a, $b) {
            return $b['created_at']->timestamp - $a['created_at']->timestamp;
        });
        
        return $backups;
    }

    private function groupBackupsByType(array $backups): array
    {
        $grouped = [
            'daily' => [],
            'weekly' => [],
            'monthly' => [],
        ];
        
        foreach ($backups as $backup) {
            $age = $backup['created_at']->diffInDays(now());
            
            if ($age <= 7) {
                $grouped['daily'][] = $backup;
            } elseif ($age <= 30) {
                $grouped['weekly'][] = $backup;
            } else {
                $grouped['monthly'][] = $backup;
            }
        }
        
        return $grouped;
    }

    private function deleteLocalBackup(array $backup): void
    {
        $files = File::glob("{$this->backupPath}/{$backup['name']}*");
        
        foreach ($files as $file) {
            File::delete($file);
        }
        
        Log::info("Deleted local backup: {$backup['name']}");
    }

    private function createRestorePoint(): string
    {
        $restorePointName = "restore_point_" . now()->format('Y-m-d_H-i-s');
        
        // Create a quick backup before restore
        $this->createFullBackup();
        
        return $restorePointName;
    }

    private function restoreComponent(string $backupName, string $component): void
    {
        switch ($component) {
            case 'database':
                $this->restoreDatabase($backupName);
                break;
            case 'files':
                $this->restoreFiles($backupName);
                break;
            case 'configuration':
                $this->restoreConfiguration($backupName);
                break;
            case 'uploads':
                $this->restoreUserUploads($backupName);
                break;
            default:
                throw new \Exception("Unknown component: {$component}");
        }
    }

    private function restoreDatabase(string $backupName): void
    {
        $filepath = "{$this->backupPath}/{$backupName}_database.sql";
        
        if (!File::exists($filepath)) {
            throw new \Exception("Database backup file not found: {$filepath}");
        }
        
        $connection = config('database.default');
        $database = config("database.connections.{$connection}.database");
        $username = config("database.connections.{$connection}.username");
        $password = config("database.connections.{$connection}.password");
        $host = config("database.connections.{$connection}.host");
        $port = config("database.connections.{$connection}.port", 3306);
        
        $command = sprintf(
            'mysql --user=%s --password=%s --host=%s --port=%s %s < %s',
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($database),
            escapeshellarg($filepath)
        );
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new \Exception("Database restore failed with return code: {$returnCode}");
        }
        
        Log::info("Database restored successfully from: {$filepath}");
    }

    private function restoreFiles(string $backupName): void
    {
        $filepath = "{$this->backupPath}/{$backupName}_files.tar.gz";
        
        if (!File::exists($filepath)) {
            throw new \Exception("Files backup not found: {$filepath}");
        }
        
        $command = "tar -xzf " . escapeshellarg($filepath) . " -C /";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new \Exception("Files restore failed with return code: {$returnCode}");
        }
        
        Log::info("Files restored successfully from: {$filepath}");
    }

    private function restoreConfiguration(string $backupName): void
    {
        $filepath = "{$this->backupPath}/{$backupName}_config.tar.gz";
        
        if (!File::exists($filepath)) {
            throw new \Exception("Configuration backup not found: {$filepath}");
        }
        
        $command = "tar -xzf " . escapeshellarg($filepath) . " -C /";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new \Exception("Configuration restore failed with return code: {$returnCode}");
        }
        
        Log::info("Configuration restored successfully from: {$filepath}");
    }

    private function restoreUserUploads(string $backupName): void
    {
        $filepath = "{$this->backupPath}/{$backupName}_uploads.tar.gz";
        
        if (!File::exists($filepath)) {
            throw new \Exception("User uploads backup not found: {$filepath}");
        }
        
        $command = "tar -xzf " . escapeshellarg($filepath) . " -C /";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new \Exception("User uploads restore failed with return code: {$returnCode}");
        }
        
        Log::info("User uploads restored successfully from: {$filepath}");
    }

    private function sendNotification(string $type, array $data): void
    {
        // Implementation for sending notifications
        // This would integrate with the notification system
        Log::info("Backup notification: {$type}", $data);
    }
}
