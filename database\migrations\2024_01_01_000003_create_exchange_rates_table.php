<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exchange_rates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_currency_id')->constrained('currencies')->onDelete('cascade');
            $table->foreignId('to_currency_id')->constrained('currencies')->onDelete('cascade');
            $table->decimal('rate', 15, 8);
            $table->decimal('buy_rate', 15, 8)->nullable();
            $table->decimal('sell_rate', 15, 8)->nullable();
            $table->decimal('spread', 5, 4)->default(0.0050); // 0.5% spread
            $table->string('source')->default('manual'); // manual, api, blockchain
            $table->timestamp('last_updated');
            $table->boolean('is_active')->default(true);
            $table->json('metadata')->nullable(); // Additional rate info
            $table->timestamps();
            
            $table->unique(['from_currency_id', 'to_currency_id']);
            $table->index(['is_active', 'last_updated']);
            $table->index('source');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exchange_rates');
    }
};
