<?php

namespace App\Services\PaymentGateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\Transaction;
use App\Models\PaymentTransaction;
use App\Exceptions\PaymentException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class PayPalGateway implements PaymentGatewayInterface
{
    protected string $clientId;
    protected string $clientSecret;
    protected string $baseUrl;
    protected string $webhookId;
    protected ?string $accessToken = null;

    public function __construct()
    {
        $this->clientId = config('services.paypal.client_id');
        $this->clientSecret = config('services.paypal.client_secret');
        $this->webhookId = config('services.paypal.webhook_id');
        $this->baseUrl = config('services.paypal.sandbox', true) 
            ? 'https://api.sandbox.paypal.com' 
            : 'https://api.paypal.com';
        
        if (!$this->clientId || !$this->clientSecret) {
            throw new PaymentException(
                'PayPal credentials not configured',
                'PAYPAL_CONFIG_MISSING'
            );
        }
    }

    /**
     * Process payment through PayPal
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        try {
            // Get access token
            $this->getAccessToken();
            
            // Create order
            $order = $this->createOrder($transaction, $paymentData);
            
            // Capture payment
            $captureResult = $this->captureOrder($order['id']);
            
            if ($captureResult['status'] === 'COMPLETED') {
                return [
                    'status' => 'success',
                    'reference' => $captureResult['id'],
                    'gateway_response' => $captureResult,
                    'amount' => floatval($captureResult['purchase_units'][0]['payments']['captures'][0]['amount']['value']),
                    'currency' => $captureResult['purchase_units'][0]['payments']['captures'][0]['amount']['currency_code'],
                    'processed_at' => now(),
                ];
            } else {
                return [
                    'status' => 'failed',
                    'error_code' => 'payment_not_completed',
                    'error_message' => 'PayPal payment not completed',
                    'gateway_response' => $captureResult,
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal payment processing failed', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'failed',
                'error_code' => 'processing_error',
                'error_message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Refund payment through PayPal
     */
    public function refundPayment(PaymentTransaction $paymentTransaction, float $amount): array
    {
        try {
            $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
                'PayPal-Request-Id' => uniqid('refund_'),
            ])->post($this->baseUrl . "/v2/payments/captures/{$paymentTransaction->gateway_reference}/refund", [
                'amount' => [
                    'value' => number_format($amount, 2, '.', ''),
                    'currency_code' => $paymentTransaction->currency,
                ],
                'note_to_payer' => 'Refund for money transfer',
            ]);

            if ($response->successful()) {
                $refund = $response->json();
                
                return [
                    'status' => 'success',
                    'reference' => $refund['id'],
                    'amount' => floatval($refund['amount']['value']),
                    'gateway_response' => $refund,
                ];
            } else {
                $error = $response->json();
                return [
                    'status' => 'failed',
                    'error_code' => $error['name'] ?? 'refund_failed',
                    'error_message' => $error['message'] ?? 'Refund failed',
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal refund failed', [
                'payment_transaction_id' => $paymentTransaction->id,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'failed',
                'error_code' => 'refund_error',
                'error_message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(string $paymentReference): array
    {
        try {
            $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
            ])->get($this->baseUrl . "/v2/checkout/orders/{$paymentReference}");

            if ($response->successful()) {
                $order = $response->json();
                
                return [
                    'status' => $this->mapPayPalStatus($order['status']),
                    'amount' => floatval($order['purchase_units'][0]['amount']['value']),
                    'currency' => $order['purchase_units'][0]['amount']['currency_code'],
                    'gateway_response' => $order,
                ];
            } else {
                return [
                    'status' => 'unknown',
                    'error' => 'Payment not found',
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal payment verification failed', [
                'payment_reference' => $paymentReference,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(array $payload): bool
    {
        try {
            $this->getAccessToken();
            
            $headers = getallheaders();
            $authAlgo = $headers['PAYPAL-AUTH-ALGO'] ?? '';
            $transmission = $headers['PAYPAL-TRANSMISSION-ID'] ?? '';
            $certId = $headers['PAYPAL-CERT-ID'] ?? '';
            $signature = $headers['PAYPAL-TRANSMISSION-SIG'] ?? '';
            $timestamp = $headers['PAYPAL-TRANSMISSION-TIME'] ?? '';
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/v1/notifications/verify-webhook-signature', [
                'auth_algo' => $authAlgo,
                'cert_id' => $certId,
                'transmission_id' => $transmission,
                'transmission_sig' => $signature,
                'transmission_time' => $timestamp,
                'webhook_id' => $this->webhookId,
                'webhook_event' => $payload,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['verification_status'] === 'SUCCESS';
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('PayPal webhook signature verification failed', [
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Process webhook notification
     */
    public function processWebhook(array $payload): array
    {
        try {
            $eventType = $payload['event_type'] ?? '';
            
            switch ($eventType) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    return $this->handlePaymentCompleted($payload['resource']);
                    
                case 'PAYMENT.CAPTURE.DENIED':
                    return $this->handlePaymentDenied($payload['resource']);
                    
                case 'PAYMENT.CAPTURE.REFUNDED':
                    return $this->handlePaymentRefunded($payload['resource']);
                    
                default:
                    return [
                        'status' => 'ignored',
                        'message' => 'Event type not handled: ' . $eventType,
                    ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal webhook processing failed', [
                'payload' => $payload,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get gateway configuration
     */
    public function getConfiguration(): array
    {
        return [
            'name' => 'PayPal',
            'supports_refunds' => true,
            'supports_partial_refunds' => true,
            'supports_webhooks' => true,
            'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
            'supported_countries' => ['US', 'GB', 'CA', 'AU', 'DE', 'FR'],
            'fee_structure' => [
                'percentage' => 3.49,
                'fixed' => 0.49,
            ],
        ];
    }

    /**
     * Test gateway connection
     */
    public function testConnection(): bool
    {
        try {
            $this->getAccessToken();
            return !empty($this->accessToken);
        } catch (\Exception $e) {
            Log::error('PayPal connection test failed', [
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Get PayPal access token
     */
    protected function getAccessToken(): void
    {
        if ($this->accessToken) {
            return; // Token already exists
        }
        
        $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
            ->asForm()
            ->post($this->baseUrl . '/v1/oauth2/token', [
                'grant_type' => 'client_credentials',
            ]);

        if (!$response->successful()) {
            throw new PaymentException(
                'Failed to get PayPal access token',
                'PAYPAL_AUTH_FAILED'
            );
        }

        $data = $response->json();
        $this->accessToken = $data['access_token'];
    }

    /**
     * Create PayPal order
     */
    protected function createOrder(Transaction $transaction, array $paymentData): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->accessToken,
            'Content-Type' => 'application/json',
            'PayPal-Request-Id' => $transaction->transaction_id,
        ])->post($this->baseUrl . '/v2/checkout/orders', [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'reference_id' => $transaction->transaction_id,
                    'amount' => [
                        'currency_code' => $transaction->currency_from,
                        'value' => number_format($transaction->total_amount, 2, '.', ''),
                    ],
                    'description' => "Money transfer: {$transaction->transaction_id}",
                ]
            ],
            'payment_source' => [
                'paypal' => [
                    'experience_context' => [
                        'payment_method_preference' => 'IMMEDIATE_PAYMENT_REQUIRED',
                        'user_action' => 'PAY_NOW',
                    ]
                ]
            ],
        ]);

        if (!$response->successful()) {
            $error = $response->json();
            throw new PaymentException(
                $error['message'] ?? 'Failed to create PayPal order',
                'PAYPAL_ORDER_CREATION_FAILED'
            );
        }

        return $response->json();
    }

    /**
     * Capture PayPal order
     */
    protected function captureOrder(string $orderId): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->accessToken,
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . "/v2/checkout/orders/{$orderId}/capture");

        if (!$response->successful()) {
            $error = $response->json();
            throw new PaymentException(
                $error['message'] ?? 'Failed to capture PayPal order',
                'PAYPAL_ORDER_CAPTURE_FAILED'
            );
        }

        return $response->json();
    }

    /**
     * Map PayPal status to our internal status
     */
    protected function mapPayPalStatus(string $paypalStatus): string
    {
        return match($paypalStatus) {
            'COMPLETED' => 'completed',
            'APPROVED' => 'processing',
            'CREATED', 'SAVED' => 'pending',
            'VOIDED', 'CANCELLED' => 'cancelled',
            default => 'failed',
        };
    }

    /**
     * Handle payment completed webhook
     */
    protected function handlePaymentCompleted(array $resource): array
    {
        return [
            'status' => 'completed',
            'transaction_reference' => $resource['id'],
            'amount' => floatval($resource['amount']['value']),
            'currency' => $resource['amount']['currency_code'],
        ];
    }

    /**
     * Handle payment denied webhook
     */
    protected function handlePaymentDenied(array $resource): array
    {
        return [
            'status' => 'failed',
            'transaction_reference' => $resource['id'],
            'error_message' => 'Payment denied by PayPal',
        ];
    }

    /**
     * Handle payment refunded webhook
     */
    protected function handlePaymentRefunded(array $resource): array
    {
        return [
            'status' => 'refunded',
            'transaction_reference' => $resource['id'],
            'refund_amount' => floatval($resource['amount']['value']),
            'currency' => $resource['amount']['currency_code'],
        ];
    }
}
