<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Currency;
use App\Models\Country;
use App\Models\Wallet;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class TransactionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Currency $currency;
    protected Country $country;
    protected Wallet $wallet;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->country = Country::factory()->create();
        $this->currency = Currency::factory()->create(['code' => 'USD']);
        $this->user = User::factory()->create(['country_id' => $this->country->id]);
        $this->wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'balance' => 10000,
        ]);
    }

    public function test_user_can_create_transaction()
    {
        $transactionData = [
            'type' => 'transfer',
            'category' => 'personal',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'receiver_phone' => '+966501234568',
            'receiver_country_id' => $this->country->id,
            'purpose' => 'مساعدة عائلية',
            'payment_method' => 'wallet',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/transactions', $transactionData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'transaction' => [
                            'id',
                            'transaction_number',
                            'amount',
                            'status',
                            'currency',
                        ],
                    ],
                ]);

        $this->assertDatabaseHas('transactions', [
            'sender_id' => $this->user->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
        ]);
    }

    public function test_user_cannot_create_transaction_with_insufficient_balance()
    {
        $this->wallet->update(['balance' => 500]);

        $transactionData = [
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'receiver_phone' => '+966501234568',
            'payment_method' => 'wallet',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/transactions', $transactionData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Insufficient balance',
                ]);
    }

    public function test_user_can_get_transaction_list()
    {
        Transaction::factory()->count(5)->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson('/api/v1/transactions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'transaction_number',
                                'amount',
                                'status',
                                'created_at',
                            ],
                        ],
                    ],
                ]);
    }

    public function test_user_can_get_specific_transaction()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/transactions/{$transaction->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'transaction' => [
                            'id',
                            'transaction_number',
                            'amount',
                            'status',
                            'currency',
                            'sender',
                        ],
                    ],
                ]);
    }

    public function test_user_cannot_access_other_users_transaction()
    {
        $otherUser = User::factory()->create();
        $transaction = Transaction::factory()->create([
            'sender_id' => $otherUser->id,
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/transactions/{$transaction->id}");

        $response->assertStatus(403);
    }

    public function test_user_can_cancel_pending_transaction()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->putJson("/api/v1/transactions/{$transaction->id}/cancel");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Transaction cancelled successfully',
                ]);

        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'status' => 'cancelled',
        ]);
    }

    public function test_user_cannot_cancel_completed_transaction()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'completed',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->putJson("/api/v1/transactions/{$transaction->id}/cancel");

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Cannot cancel completed transaction',
                ]);
    }

    public function test_user_can_track_transaction()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/transactions/{$transaction->id}/track");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'transaction',
                        'tracking_info' => [
                            'current_status',
                            'estimated_completion',
                            'timeline',
                        ],
                    ],
                ]);
    }

    public function test_transaction_fees_are_calculated_correctly()
    {
        $transactionData = [
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'receiver_phone' => '+966501234568',
            'payment_method' => 'wallet',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/transactions/calculate-fees', $transactionData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'amount',
                        'commission_amount',
                        'additional_fees',
                        'total_fees',
                        'net_amount',
                    ],
                ]);

        $data = $response->json('data');
        $this->assertEquals(1000, $data['amount']);
        $this->assertGreaterThan(0, $data['total_fees']);
        $this->assertEquals($data['amount'] - $data['total_fees'], $data['net_amount']);
    }

    public function test_transaction_respects_daily_limit()
    {
        $this->user->update(['daily_limit' => 500]);

        $transactionData = [
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'receiver_phone' => '+966501234568',
            'payment_method' => 'wallet',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/transactions', $transactionData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Daily limit exceeded',
                ]);
    }

    public function test_transaction_creates_audit_log()
    {
        $transactionData = [
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 1000,
            'receiver_name' => 'محمد أحمد',
            'receiver_phone' => '+966501234568',
            'payment_method' => 'wallet',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/transactions', $transactionData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('audit_logs', [
            'user_id' => $this->user->id,
            'event_type' => 'transaction',
            'action' => 'create',
        ]);
    }

    public function test_high_amount_transaction_triggers_fraud_detection()
    {
        $transactionData = [
            'type' => 'transfer',
            'currency_id' => $this->currency->id,
            'amount' => 100000, // High amount
            'receiver_name' => 'محمد أحمد',
            'receiver_phone' => '+966501234568',
            'payment_method' => 'wallet',
        ];

        $this->wallet->update(['balance' => 150000]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->postJson('/api/v1/transactions', $transactionData);

        $response->assertStatus(201);

        // Check if fraud detection was triggered
        $transaction = Transaction::where('sender_id', $this->user->id)
                                ->where('amount', 100000)
                                ->first();

        $this->assertNotNull($transaction);
        $this->assertTrue($transaction->is_suspicious || $transaction->status === 'pending_review');
    }

    public function test_user_can_get_transaction_receipt()
    {
        $transaction = Transaction::factory()->create([
            'sender_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'status' => 'completed',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
                        ->getJson("/api/v1/transactions/{$transaction->id}/receipt");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'receipt' => [
                            'transaction_number',
                            'amount',
                            'fees',
                            'net_amount',
                            'sender_info',
                            'receiver_info',
                            'timestamp',
                        ],
                    ],
                ]);
    }
}
