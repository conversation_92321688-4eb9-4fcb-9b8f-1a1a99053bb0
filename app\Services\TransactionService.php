<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\AuditLog;
use App\Models\Notification;
use App\Services\FraudDetectionService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TransactionService
{
    protected FraudDetectionService $fraudDetectionService;

    public function __construct(FraudDetectionService $fraudDetectionService)
    {
        $this->fraudDetectionService = $fraudDetectionService;
    }

    /**
     * Process a new transaction.
     */
    public function processTransaction(array $data): array
    {
        try {
            DB::beginTransaction();

            // Validate and prepare transaction data
            $validatedData = $this->validateTransactionData($data);

            // Create transaction record
            $transaction = $this->createTransaction($validatedData);

            // Perform fraud detection analysis
            $fraudAnalysis = $this->fraudDetectionService->analyzeTransaction($transaction);

            // Update transaction with fraud analysis results
            $transaction->update([
                'risk_score' => $fraudAnalysis['risk_level'],
                'is_suspicious' => $fraudAnalysis['requires_review'],
            ]);

            // Handle based on risk level
            if ($fraudAnalysis['auto_block']) {
                $transaction->update(['status' => 'blocked']);
                $this->createNotification($transaction, 'transaction_blocked');
                
                DB::commit();
                return [
                    'success' => false,
                    'message' => 'Transaction blocked due to high risk',
                    'transaction' => $transaction,
                    'fraud_analysis' => $fraudAnalysis,
                ];
            }

            // Process wallet operations
            $this->processWalletOperations($transaction);

            // Create audit log
            $this->createAuditLog($transaction, 'transaction_created');

            // Send notifications
            $this->sendTransactionNotifications($transaction);

            // If high risk but not auto-blocked, require manual review
            if ($fraudAnalysis['requires_review']) {
                $transaction->update(['status' => 'pending_review']);
                $this->createNotification($transaction, 'transaction_review_required');
            }

            DB::commit();

            return [
                'success' => true,
                'message' => 'Transaction processed successfully',
                'transaction' => $transaction->load(['currency', 'targetCurrency', 'sender', 'receiver']),
                'fraud_analysis' => $fraudAnalysis,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Transaction processing failed', [
                'error' => $e->getMessage(),
                'data' => $data,
            ]);

            throw $e;
        }
    }

    /**
     * Validate transaction data.
     */
    private function validateTransactionData(array $data): array
    {
        // Get sender and validate
        $sender = User::findOrFail($data['sender_id']);
        if ($sender->status !== 'active') {
            throw new \Exception('Sender account is not active');
        }

        // Get sender wallet
        $senderWallet = $sender->getWalletForCurrency($data['currency_id']);
        if (!$senderWallet) {
            throw new \Exception('Sender wallet not found for this currency');
        }

        // Check balance
        if (!$senderWallet->hasSufficientBalance($data['amount'])) {
            throw new \Exception('Insufficient balance');
        }

        // Check limits
        if (!$senderWallet->checkDailyLimit($data['amount'])) {
            throw new \Exception('Daily limit exceeded');
        }

        if (!$senderWallet->checkMonthlyLimit($data['amount'])) {
            throw new \Exception('Monthly limit exceeded');
        }

        // Calculate exchange rate and fees
        $exchangeData = $this->calculateExchangeAndFees($data);

        return array_merge($data, $exchangeData, [
            'sender' => $sender,
            'sender_wallet' => $senderWallet,
        ]);
    }

    /**
     * Calculate exchange rates and fees.
     */
    private function calculateExchangeAndFees(array $data): array
    {
        $fromCurrency = Currency::findOrFail($data['currency_id']);
        $toCurrency = Currency::findOrFail($data['target_currency_id'] ?? $data['currency_id']);

        $exchangeRate = 1.0;
        $targetAmount = $data['amount'];

        // Calculate exchange rate if different currencies
        if ($fromCurrency->id !== $toCurrency->id) {
            $rate = ExchangeRate::getRate($fromCurrency->id, $toCurrency->id);
            if (!$rate) {
                // Calculate via USD
                $exchangeRate = $toCurrency->rate_to_usd / $fromCurrency->rate_to_usd;
            } else {
                $exchangeRate = $rate;
            }
            $targetAmount = $data['amount'] * $exchangeRate;
        }

        // Calculate fees
        $commissionRate = $this->calculateCommissionRate($data);
        $commissionAmount = $data['amount'] * $commissionRate;
        
        $additionalFees = $this->calculateAdditionalFees($data);
        $totalFees = $commissionAmount + $additionalFees;
        $netAmount = $targetAmount - $totalFees;

        return [
            'exchange_rate' => $exchangeRate,
            'target_amount' => $targetAmount,
            'commission_rate' => $commissionRate,
            'commission_amount' => $commissionAmount,
            'additional_fees' => $additionalFees,
            'total_fees' => $totalFees,
            'net_amount' => $netAmount,
        ];
    }

    /**
     * Calculate commission rate based on various factors.
     */
    private function calculateCommissionRate(array $data): float
    {
        $baseRate = 0.02; // 2% base rate

        // Volume-based discounts
        $sender = User::find($data['sender_id']);
        $monthlyVolume = $sender->sentTransactions()
            ->whereMonth('created_at', now()->month)
            ->where('status', 'completed')
            ->sum('amount');

        if ($monthlyVolume > 100000) {
            $baseRate *= 0.8; // 20% discount for high volume
        } elseif ($monthlyVolume > 50000) {
            $baseRate *= 0.9; // 10% discount for medium volume
        }

        // User type discounts
        if ($sender->user_type === 'business') {
            $baseRate *= 0.85; // 15% discount for business users
        }

        // Payment method adjustments
        $paymentMethod = $data['payment_method'] ?? 'bank_transfer';
        switch ($paymentMethod) {
            case 'crypto':
                $baseRate *= 0.7; // Lower fees for crypto
                break;
            case 'cash':
                $baseRate *= 1.2; // Higher fees for cash
                break;
            case 'card':
                $baseRate *= 1.1; // Slightly higher for cards
                break;
        }

        return min($baseRate, 0.05); // Cap at 5%
    }

    /**
     * Calculate additional fees.
     */
    private function calculateAdditionalFees(array $data): float
    {
        $fees = 0;

        // Fixed processing fee
        $fees += 1.0;

        // Cross-border fee
        if (isset($data['sender_country_id']) && isset($data['receiver_country_id']) 
            && $data['sender_country_id'] !== $data['receiver_country_id']) {
            $fees += 5.0;
        }

        // Urgent processing fee
        if (($data['priority'] ?? 'normal') === 'urgent') {
            $fees += 10.0;
        }

        // Weekend/holiday fee
        if (now()->isWeekend()) {
            $fees += 2.0;
        }

        return $fees;
    }

    /**
     * Create transaction record.
     */
    private function createTransaction(array $data): Transaction
    {
        return Transaction::create([
            'transaction_number' => Transaction::generateTransactionNumber(),
            'reference_number' => Transaction::generateReferenceNumber(),
            'type' => $data['type'],
            'category' => $data['category'],
            'sender_id' => $data['sender_id'],
            'sender_wallet_id' => $data['sender_wallet']->id,
            'sender_name' => $data['sender']->full_name,
            'sender_phone' => $data['sender']->phone,
            'sender_national_id' => $data['sender']->national_id,
            'receiver_name' => $data['receiver_name'],
            'receiver_phone' => $data['receiver_phone'] ?? null,
            'receiver_national_id' => $data['receiver_national_id'] ?? null,
            'receiver_bank_account' => $data['receiver_bank_account'] ?? null,
            'receiver_bank_name' => $data['receiver_bank_name'] ?? null,
            'currency_id' => $data['currency_id'],
            'amount' => $data['amount'],
            'exchange_rate' => $data['exchange_rate'],
            'target_currency_id' => $data['target_currency_id'] ?? $data['currency_id'],
            'target_amount' => $data['target_amount'],
            'commission_amount' => $data['commission_amount'],
            'commission_rate' => $data['commission_rate'],
            'additional_fees' => $data['additional_fees'],
            'total_fees' => $data['total_fees'],
            'net_amount' => $data['net_amount'],
            'status' => 'pending',
            'payment_method' => $data['payment_method'],
            'sender_country_id' => $data['sender']->country_id,
            'receiver_country_id' => $data['receiver_country_id'] ?? null,
            'initiated_at' => now(),
            'purpose' => $data['purpose'] ?? null,
            'notes' => $data['notes'] ?? null,
        ]);
    }

    /**
     * Process wallet operations.
     */
    private function processWalletOperations(Transaction $transaction): void
    {
        // Freeze funds in sender wallet
        $transaction->senderWallet->freezeFunds($transaction->amount);

        // If receiver has a wallet, add pending balance
        if ($transaction->receiver_id) {
            $receiver = User::find($transaction->receiver_id);
            if ($receiver) {
                $receiverWallet = $receiver->getWalletForCurrency($transaction->target_currency_id);
                if ($receiverWallet) {
                    $receiverWallet->addPendingBalance($transaction->net_amount);
                    $transaction->update(['receiver_wallet_id' => $receiverWallet->id]);
                }
            }
        }
    }

    /**
     * Complete transaction.
     */
    public function completeTransaction(Transaction $transaction): bool
    {
        try {
            DB::beginTransaction();

            if ($transaction->status !== 'pending') {
                throw new \Exception('Transaction is not in pending status');
            }

            // Update transaction status
            $transaction->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            // Process final wallet operations
            if ($transaction->senderWallet) {
                $transaction->senderWallet->deductFunds($transaction->amount);
                $transaction->senderWallet->unfreezeFunds($transaction->amount);
            }

            if ($transaction->receiverWallet) {
                $transaction->receiverWallet->removePendingBalance($transaction->net_amount);
                $transaction->receiverWallet->addFunds($transaction->net_amount, 'received');
            }

            // Create audit log
            $this->createAuditLog($transaction, 'transaction_completed');

            // Send completion notifications
            $this->sendCompletionNotifications($transaction);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Transaction completion failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Cancel transaction.
     */
    public function cancelTransaction(Transaction $transaction, string $reason = null): bool
    {
        try {
            DB::beginTransaction();

            if (!in_array($transaction->status, ['pending', 'pending_review'])) {
                throw new \Exception('Transaction cannot be cancelled');
            }

            // Update transaction status
            $transaction->update([
                'status' => 'cancelled',
                'notes' => ($transaction->notes ? $transaction->notes . ' | ' : '') . 'Cancelled: ' . ($reason ?? 'User request'),
            ]);

            // Reverse wallet operations
            if ($transaction->senderWallet) {
                $transaction->senderWallet->unfreezeFunds($transaction->amount);
            }

            if ($transaction->receiverWallet) {
                $transaction->receiverWallet->removePendingBalance($transaction->net_amount);
            }

            // Create audit log
            $this->createAuditLog($transaction, 'transaction_cancelled', ['reason' => $reason]);

            // Send cancellation notifications
            $this->sendCancellationNotifications($transaction);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Transaction cancellation failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Create audit log entry.
     */
    private function createAuditLog(Transaction $transaction, string $eventType, array $additionalData = []): void
    {
        AuditLog::create([
            'user_id' => $transaction->sender_id,
            'event_type' => $eventType,
            'action' => 'create',
            'model_type' => 'Transaction',
            'model_id' => $transaction->id,
            'description' => "Transaction {$transaction->transaction_number} - {$eventType}",
            'metadata' => array_merge([
                'transaction_number' => $transaction->transaction_number,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency->code,
                'status' => $transaction->status,
            ], $additionalData),
            'ip_address' => request()->ip(),
        ]);
    }

    /**
     * Send transaction notifications.
     */
    private function sendTransactionNotifications(Transaction $transaction): void
    {
        // Notify sender
        $this->createNotification($transaction, 'transaction_initiated');

        // Notify receiver if they have an account
        if ($transaction->receiver_id) {
            $this->createNotification($transaction, 'transaction_received', $transaction->receiver_id);
        }
    }

    /**
     * Send completion notifications.
     */
    private function sendCompletionNotifications(Transaction $transaction): void
    {
        $this->createNotification($transaction, 'transaction_completed');
        
        if ($transaction->receiver_id) {
            $this->createNotification($transaction, 'funds_received', $transaction->receiver_id);
        }
    }

    /**
     * Send cancellation notifications.
     */
    private function sendCancellationNotifications(Transaction $transaction): void
    {
        $this->createNotification($transaction, 'transaction_cancelled');
        
        if ($transaction->receiver_id) {
            $this->createNotification($transaction, 'transaction_cancelled', $transaction->receiver_id);
        }
    }

    /**
     * Create notification.
     */
    private function createNotification(Transaction $transaction, string $type, int $userId = null): void
    {
        $userId = $userId ?? $transaction->sender_id;
        
        $messages = [
            'transaction_initiated' => 'Your transaction has been initiated',
            'transaction_completed' => 'Your transaction has been completed',
            'transaction_cancelled' => 'Your transaction has been cancelled',
            'transaction_blocked' => 'Your transaction has been blocked for security reasons',
            'transaction_review_required' => 'Your transaction requires manual review',
            'transaction_received' => 'You have received a new transaction',
            'funds_received' => 'Funds have been added to your wallet',
        ];

        Notification::create([
            'user_id' => $userId,
            'type' => 'transaction',
            'title' => $messages[$type] ?? 'Transaction Update',
            'message' => $messages[$type] ?? 'Transaction Update',
            'data' => [
                'transaction_id' => $transaction->id,
                'transaction_number' => $transaction->transaction_number,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency->code,
                'status' => $transaction->status,
            ],
            'channel' => 'database',
            'priority' => in_array($type, ['transaction_blocked', 'transaction_review_required']) ? 'high' : 'normal',
            'is_sent' => true,
            'sent_at' => now(),
        ]);
    }
}
