<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExchangeRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'from_currency_id',
        'to_currency_id',
        'rate',
        'buy_rate',
        'sell_rate',
        'spread',
        'source',
        'last_updated',
        'is_active',
        'metadata',
    ];

    protected $casts = [
        'rate' => 'decimal:8',
        'buy_rate' => 'decimal:8',
        'sell_rate' => 'decimal:8',
        'spread' => 'decimal:4',
        'last_updated' => 'datetime',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get the from currency.
     */
    public function fromCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'from_currency_id');
    }

    /**
     * Get the to currency.
     */
    public function toCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'to_currency_id');
    }

    /**
     * Scope for active rates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get rate for specific currency pair.
     */
    public static function getRate(int $fromCurrencyId, int $toCurrencyId): ?float
    {
        $rate = self::where('from_currency_id', $fromCurrencyId)
            ->where('to_currency_id', $toCurrencyId)
            ->where('is_active', true)
            ->first();

        return $rate ? $rate->rate : null;
    }

    /**
     * Convert amount using this exchange rate.
     */
    public function convertAmount(float $amount): float
    {
        return $amount * $this->rate;
    }

    /**
     * Get the effective rate with spread.
     */
    public function getEffectiveRate(string $type = 'buy'): float
    {
        return $type === 'buy' ? $this->buy_rate : $this->sell_rate;
    }

    /**
     * Scope for current rates (valid now).
     */
    public function scopeCurrent($query)
    {
        return $query->where('last_updated', '>=', now()->subHours(24));
    }

    /**
     * Scope for exchange rates between specific currencies.
     */
    public function scopeBetweenCurrencies($query, $fromCurrencyId, $toCurrencyId)
    {
        return $query->where('from_currency_id', $fromCurrencyId)
                    ->where('to_currency_id', $toCurrencyId);
    }

    /**
     * Get formatted rate.
     */
    public function getFormattedRateAttribute(): string
    {
        return number_format($this->rate, 6);
    }

    /**
     * Get currency pair string.
     */
    public function getCurrencyPairAttribute(): string
    {
        return $this->fromCurrency->code . '/' . $this->toCurrency->code;
    }

    /**
     * Check if rate is currently valid.
     */
    public function isValid(): bool
    {
        return $this->is_active && $this->last_updated >= now()->subHours(24);
    }

    /**
     * Update or create exchange rate.
     */
    public static function updateRate(
        int $fromCurrencyId,
        int $toCurrencyId,
        float $rate,
        float $buyRate = null,
        float $sellRate = null,
        string $source = 'manual'
    ): self {
        return static::updateOrCreate(
            [
                'from_currency_id' => $fromCurrencyId,
                'to_currency_id' => $toCurrencyId,
                'source' => $source,
            ],
            [
                'rate' => $rate,
                'buy_rate' => $buyRate ?? $rate,
                'sell_rate' => $sellRate ?? $rate,
                'spread' => $buyRate && $sellRate ? abs($buyRate - $sellRate) : 0,
                'last_updated' => now(),
                'is_active' => true,
            ]
        );
    }
}
