<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use App\Models\Transaction;

class SMSService
{
    protected array $config;
    protected string $provider;

    public function __construct()
    {
        $this->config = config('sms', []);
        $this->provider = $this->config['default_provider'] ?? 'twilio';
    }

    /**
     * Send SMS message.
     */
    public function send(string $to, string $message, array $options = []): array
    {
        try {
            // Validate phone number
            if (!$this->isValidPhoneNumber($to)) {
                throw new \Exception("Invalid phone number: {$to}");
            }

            // Check rate limiting
            if ($this->isRateLimited($to)) {
                throw new \Exception("Rate limit exceeded for phone number: {$to}");
            }

            // Format phone number
            $formattedPhone = $this->formatPhoneNumber($to);

            // Send based on provider
            $result = match($this->provider) {
                'twilio' => $this->sendViaTwilio($formattedPhone, $message, $options),
                'nexmo' => $this->sendViaNexmo($formattedPhone, $message, $options),
                'unifonic' => $this->sendViaUnifonic($formattedPhone, $message, $options),
                'taqnyat' => $this->sendViaTaqnyat($formattedPhone, $message, $options),
                default => throw new \Exception("Unsupported SMS provider: {$this->provider}")
            };

            // Log successful send
            Log::info('SMS sent successfully', [
                'provider' => $this->provider,
                'to' => $formattedPhone,
                'message_id' => $result['message_id'] ?? null,
                'cost' => $result['cost'] ?? null,
            ]);

            // Update rate limiting
            $this->updateRateLimit($to);

            return [
                'success' => true,
                'message_id' => $result['message_id'] ?? null,
                'provider' => $this->provider,
                'cost' => $result['cost'] ?? null,
                'status' => $result['status'] ?? 'sent',
            ];

        } catch (\Exception $e) {
            Log::error('SMS sending failed', [
                'provider' => $this->provider,
                'to' => $to,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'provider' => $this->provider,
            ];
        }
    }

    /**
     * Send transaction notification SMS.
     */
    public function sendTransactionNotification(Transaction $transaction): array
    {
        $user = $transaction->sender;
        
        if (!$user->phone || !$user->sms_notifications_enabled) {
            return [
                'success' => false,
                'error' => 'SMS notifications disabled or no phone number',
            ];
        }

        $message = $this->buildTransactionMessage($transaction);
        
        return $this->send($user->phone, $message, [
            'type' => 'transaction_notification',
            'transaction_id' => $transaction->id,
            'user_id' => $user->id,
        ]);
    }

    /**
     * Send OTP (One-Time Password).
     */
    public function sendOTP(string $phone, string $otp, int $expiryMinutes = 5): array
    {
        $message = $this->buildOTPMessage($otp, $expiryMinutes);
        
        return $this->send($phone, $message, [
            'type' => 'otp',
            'otp' => $otp,
            'expiry_minutes' => $expiryMinutes,
        ]);
    }

    /**
     * Send security alert SMS.
     */
    public function sendSecurityAlert(User $user, string $alertType, array $details = []): array
    {
        if (!$user->phone || !$user->security_alerts_enabled) {
            return [
                'success' => false,
                'error' => 'Security alerts disabled or no phone number',
            ];
        }

        $message = $this->buildSecurityAlertMessage($alertType, $details);
        
        return $this->send($user->phone, $message, [
            'type' => 'security_alert',
            'alert_type' => $alertType,
            'user_id' => $user->id,
        ]);
    }

    /**
     * Send via Twilio.
     */
    private function sendViaTwilio(string $to, string $message, array $options = []): array
    {
        $config = $this->config['providers']['twilio'] ?? [];
        
        if (!($config['enabled'] ?? false)) {
            throw new \Exception('Twilio SMS provider is disabled');
        }

        $response = Http::withBasicAuth($config['account_sid'], $config['auth_token'])
            ->asForm()
            ->post("https://api.twilio.com/2010-04-01/Accounts/{$config['account_sid']}/Messages.json", [
                'From' => $config['from_number'],
                'To' => $to,
                'Body' => $message,
            ]);

        if (!$response->successful()) {
            throw new \Exception('Twilio API error: ' . $response->body());
        }

        $data = $response->json();

        return [
            'message_id' => $data['sid'],
            'status' => $data['status'],
            'cost' => $data['price'] ?? null,
        ];
    }

    /**
     * Send via Nexmo (Vonage).
     */
    private function sendViaNexmo(string $to, string $message, array $options = []): array
    {
        $config = $this->config['providers']['nexmo'] ?? [];
        
        if (!($config['enabled'] ?? false)) {
            throw new \Exception('Nexmo SMS provider is disabled');
        }

        $response = Http::post('https://rest.nexmo.com/sms/json', [
            'api_key' => $config['api_key'],
            'api_secret' => $config['api_secret'],
            'from' => $config['from_number'],
            'to' => $to,
            'text' => $message,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Nexmo API error: ' . $response->body());
        }

        $data = $response->json();
        $messageData = $data['messages'][0] ?? [];

        if (($messageData['status'] ?? '') !== '0') {
            throw new \Exception('Nexmo error: ' . ($messageData['error-text'] ?? 'Unknown error'));
        }

        return [
            'message_id' => $messageData['message-id'],
            'status' => 'sent',
            'cost' => $messageData['message-price'] ?? null,
        ];
    }

    /**
     * Send via Unifonic (Arabic SMS provider).
     */
    private function sendViaUnifonic(string $to, string $message, array $options = []): array
    {
        $config = $this->config['providers']['unifonic'] ?? [];
        
        if (!($config['enabled'] ?? false)) {
            throw new \Exception('Unifonic SMS provider is disabled');
        }

        $response = Http::withHeaders([
            'Authorization' => 'Basic ' . base64_encode($config['app_sid'] . ':' . $config['auth_token']),
        ])->post('https://api.unifonic.com/v1/messages', [
            'AppSid' => $config['app_sid'],
            'SenderID' => $config['sender_id'],
            'Recipient' => $to,
            'Body' => $message,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Unifonic API error: ' . $response->body());
        }

        $data = $response->json();

        if (!($data['success'] ?? false)) {
            throw new \Exception('Unifonic error: ' . ($data['message'] ?? 'Unknown error'));
        }

        return [
            'message_id' => $data['data']['MessageID'] ?? null,
            'status' => 'sent',
            'cost' => $data['data']['Cost'] ?? null,
        ];
    }

    /**
     * Send via Taqnyat (Saudi SMS provider).
     */
    private function sendViaTaqnyat(string $to, string $message, array $options = []): array
    {
        $config = $this->config['providers']['taqnyat'] ?? [];
        
        if (!($config['enabled'] ?? false)) {
            throw new \Exception('Taqnyat SMS provider is disabled');
        }

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $config['api_key'],
            'Content-Type' => 'application/json',
        ])->post('https://api.taqnyat.sa/v1/messages', [
            'recipients' => [$to],
            'body' => $message,
            'sender' => $config['sender_name'],
        ]);

        if (!$response->successful()) {
            throw new \Exception('Taqnyat API error: ' . $response->body());
        }

        $data = $response->json();

        return [
            'message_id' => $data['messageId'] ?? null,
            'status' => 'sent',
            'cost' => $data['cost'] ?? null,
        ];
    }

    /**
     * Build transaction notification message.
     */
    private function buildTransactionMessage(Transaction $transaction): string
    {
        $statusText = match($transaction->status) {
            'pending' => 'قيد المعالجة',
            'completed' => 'مكتملة',
            'failed' => 'فاشلة',
            'cancelled' => 'ملغية',
            default => $transaction->status
        };

        $typeText = match($transaction->type) {
            'transfer' => 'تحويل',
            'deposit' => 'إيداع',
            'withdrawal' => 'سحب',
            default => $transaction->type
        };

        return "موني ترانسفر: {$typeText} بمبلغ {$transaction->amount} {$transaction->currency->code} - الحالة: {$statusText}. رقم المرجع: {$transaction->reference_number}";
    }

    /**
     * Build OTP message.
     */
    private function buildOTPMessage(string $otp, int $expiryMinutes): string
    {
        return "موني ترانسفر: رمز التحقق الخاص بك هو {$otp}. صالح لمدة {$expiryMinutes} دقائق. لا تشارك هذا الرمز مع أحد.";
    }

    /**
     * Build security alert message.
     */
    private function buildSecurityAlertMessage(string $alertType, array $details): string
    {
        return match($alertType) {
            'login_attempt' => "موني ترانسفر: تم محاولة تسجيل دخول جديدة إلى حسابك. إذا لم تكن أنت، يرجى تغيير كلمة المرور فوراً.",
            'password_changed' => "موني ترانسفر: تم تغيير كلمة مرور حسابك بنجاح. إذا لم تقم بهذا التغيير، يرجى التواصل معنا فوراً.",
            'fraud_detected' => "موني ترانسفر: تم اكتشاف نشاط مشبوه في حسابك. تم تجميد الحساب مؤقتاً للحماية. يرجى التواصل مع الدعم.",
            'large_transaction' => "موني ترانسفر: تم إجراء معاملة كبيرة في حسابك بمبلغ {$details['amount']} {$details['currency']}.",
            default => "موني ترانسفر: تنبيه أمني - يرجى مراجعة حسابك."
        };
    }

    /**
     * Validate phone number format.
     */
    private function isValidPhoneNumber(string $phone): bool
    {
        // Remove all non-digit characters except +
        $cleanPhone = preg_replace('/[^\d+]/', '', $phone);
        
        // Check if it starts with + and has 10-15 digits
        return preg_match('/^\+\d{10,15}$/', $cleanPhone) === 1;
    }

    /**
     * Format phone number to international format.
     */
    private function formatPhoneNumber(string $phone): string
    {
        // Remove all non-digit characters except +
        $cleanPhone = preg_replace('/[^\d+]/', '', $phone);
        
        // If it doesn't start with +, assume it's a Saudi number
        if (!str_starts_with($cleanPhone, '+')) {
            // Remove leading 0 if present
            $cleanPhone = ltrim($cleanPhone, '0');
            $cleanPhone = '+966' . $cleanPhone;
        }
        
        return $cleanPhone;
    }

    /**
     * Check if phone number is rate limited.
     */
    private function isRateLimited(string $phone): bool
    {
        $key = 'sms_rate_limit:' . $phone;
        $limit = $this->config['rate_limit']['per_hour'] ?? 10;
        
        $count = Cache::get($key, 0);
        
        return $count >= $limit;
    }

    /**
     * Update rate limit counter.
     */
    private function updateRateLimit(string $phone): void
    {
        $key = 'sms_rate_limit:' . $phone;
        $ttl = 3600; // 1 hour
        
        $count = Cache::get($key, 0);
        Cache::put($key, $count + 1, $ttl);
    }

    /**
     * Get SMS delivery status.
     */
    public function getDeliveryStatus(string $messageId, string $provider = null): array
    {
        $provider = $provider ?? $this->provider;
        
        try {
            return match($provider) {
                'twilio' => $this->getTwilioStatus($messageId),
                'nexmo' => $this->getNexmoStatus($messageId),
                'unifonic' => $this->getUnificonicStatus($messageId),
                'taqnyat' => $this->getTaqnyatStatus($messageId),
                default => ['status' => 'unknown', 'error' => 'Unsupported provider']
            };
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get Twilio message status.
     */
    private function getTwilioStatus(string $messageId): array
    {
        $config = $this->config['providers']['twilio'] ?? [];
        
        $response = Http::withBasicAuth($config['account_sid'], $config['auth_token'])
            ->get("https://api.twilio.com/2010-04-01/Accounts/{$config['account_sid']}/Messages/{$messageId}.json");

        if (!$response->successful()) {
            throw new \Exception('Failed to get Twilio message status');
        }

        $data = $response->json();

        return [
            'status' => $data['status'],
            'error_code' => $data['error_code'] ?? null,
            'error_message' => $data['error_message'] ?? null,
            'price' => $data['price'] ?? null,
            'date_sent' => $data['date_sent'] ?? null,
        ];
    }

    /**
     * Get Nexmo message status.
     */
    private function getNexmoStatus(string $messageId): array
    {
        // Nexmo status checking implementation
        return ['status' => 'delivered']; // Mock implementation
    }

    /**
     * Get Unifonic message status.
     */
    private function getUnificonicStatus(string $messageId): array
    {
        // Unifonic status checking implementation
        return ['status' => 'delivered']; // Mock implementation
    }

    /**
     * Get Taqnyat message status.
     */
    private function getTaqnyatStatus(string $messageId): array
    {
        // Taqnyat status checking implementation
        return ['status' => 'delivered']; // Mock implementation
    }
}
