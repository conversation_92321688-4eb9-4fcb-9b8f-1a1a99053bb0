<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class BranchController extends Controller
{
    /**
     * Get all active branches.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Branch::with(['country'])
            ->where('is_active', true);

        // Filter by country
        if ($request->has('country_id')) {
            $query->where('country_id', $request->country_id);
        }

        if ($request->has('country_code')) {
            $country = Country::where('code', $request->country_code)->first();
            if ($country) {
                $query->where('country_id', $country->id);
            }
        }

        // Filter by city
        if ($request->has('city')) {
            $city = $request->city;
            $query->where(function ($q) use ($city) {
                $q->where('city_ar', 'like', "%{$city}%")
                  ->orWhere('city_en', 'like', "%{$city}%");
            });
        }

        // Filter by services
        if ($request->has('service')) {
            $query->whereJsonContains('services', $request->service);
        }

        // Filter by currency support
        if ($request->has('currency')) {
            $query->whereJsonContains('supported_currencies', $request->currency);
        }

        $branches = $query->orderBy('is_main_branch', 'desc')
            ->orderBy('name_en')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $branches,
            'total' => $branches->count(),
        ]);
    }

    /**
     * Get specific branch details.
     */
    public function show($id): JsonResponse
    {
        $branch = Branch::with([
            'country',
            'users' => function ($query) {
                $query->where('status', 'active')->limit(10);
            },
            'transactions' => function ($query) {
                $query->latest()->limit(10);
            }
        ])->findOrFail($id);

        // Calculate statistics
        $stats = [
            'total_users' => $branch->users()->count(),
            'active_users' => $branch->users()->where('status', 'active')->count(),
            'total_transactions' => $branch->transactions()->count(),
            'transactions_today' => $branch->transactions()
                ->whereDate('created_at', today())
                ->count(),
            'volume_today' => $branch->transactions()
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->sum('amount'),
            'volume_this_month' => $branch->transactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', 'completed')
                ->sum('amount'),
            'average_transaction_amount' => $branch->transactions()
                ->where('status', 'completed')
                ->avg('amount'),
            'pending_transactions' => $branch->transactions()
                ->where('status', 'pending')
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'branch' => $branch,
                'statistics' => $stats,
            ],
        ]);
    }

    /**
     * Find nearby branches.
     */
    public function nearby(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:1|max:100', // km
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        $latitude = $request->latitude;
        $longitude = $request->longitude;
        $radius = $request->radius ?? 10; // Default 10km
        $limit = $request->limit ?? 10;

        $branches = Branch::where('is_active', true)
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->with(['country'])
            ->get()
            ->map(function ($branch) use ($latitude, $longitude) {
                $distance = $branch->getDistanceFrom($latitude, $longitude);
                $branch->distance = $distance;
                return $branch;
            })
            ->filter(function ($branch) use ($radius) {
                return $branch->distance <= $radius;
            })
            ->sortBy('distance')
            ->take($limit)
            ->values();

        return response()->json([
            'success' => true,
            'data' => $branches,
            'search_criteria' => [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'radius_km' => $radius,
                'limit' => $limit,
            ],
            'total_found' => $branches->count(),
        ]);
    }

    /**
     * Get branch working hours.
     */
    public function workingHours($id): JsonResponse
    {
        $branch = Branch::findOrFail($id);

        $workingHours = $branch->working_hours ?? [
            'sunday' => ['open' => '09:00', 'close' => '17:00'],
            'monday' => ['open' => '09:00', 'close' => '17:00'],
            'tuesday' => ['open' => '09:00', 'close' => '17:00'],
            'wednesday' => ['open' => '09:00', 'close' => '17:00'],
            'thursday' => ['open' => '09:00', 'close' => '17:00'],
            'friday' => ['open' => '14:00', 'close' => '17:00'],
            'saturday' => ['closed' => true],
        ];

        $currentDay = strtolower(now()->format('l'));
        $isOpen = false;
        $nextOpenTime = null;

        if (isset($workingHours[$currentDay]) && !isset($workingHours[$currentDay]['closed'])) {
            $openTime = $workingHours[$currentDay]['open'];
            $closeTime = $workingHours[$currentDay]['close'];
            $currentTime = now()->format('H:i');

            $isOpen = $currentTime >= $openTime && $currentTime <= $closeTime;
        }

        // Find next opening time if closed
        if (!$isOpen) {
            for ($i = 1; $i <= 7; $i++) {
                $nextDay = strtolower(now()->addDays($i)->format('l'));
                if (isset($workingHours[$nextDay]) && !isset($workingHours[$nextDay]['closed'])) {
                    $nextOpenTime = now()->addDays($i)->format('Y-m-d') . ' ' . $workingHours[$nextDay]['open'];
                    break;
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'branch' => $branch->only(['id', 'name_ar', 'name_en', 'code']),
                'working_hours' => $workingHours,
                'is_open_now' => $isOpen,
                'current_time' => now()->format('Y-m-d H:i:s'),
                'next_open_time' => $nextOpenTime,
            ],
        ]);
    }

    /**
     * Get branch services.
     */
    public function services($id): JsonResponse
    {
        $branch = Branch::findOrFail($id);

        $allServices = [
            'money_transfer' => 'تحويل الأموال',
            'currency_exchange' => 'صرف العملات',
            'cash_pickup' => 'استلام نقدي',
            'bank_deposit' => 'إيداع بنكي',
            'mobile_wallet' => 'محفظة موبايل',
            'crypto_exchange' => 'تبديل العملات الرقمية',
            'bill_payment' => 'دفع الفواتير',
            'prepaid_cards' => 'البطاقات المدفوعة مسبقاً',
            'western_union' => 'ويسترن يونيون',
            'moneygram' => 'موني جرام',
        ];

        $branchServices = $branch->services ?? array_keys($allServices);

        $services = collect($branchServices)->map(function ($service) use ($allServices) {
            return [
                'code' => $service,
                'name' => $allServices[$service] ?? $service,
                'available' => true,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'branch' => $branch->only(['id', 'name_ar', 'name_en', 'code']),
                'services' => $services,
                'total_services' => $services->count(),
            ],
        ]);
    }

    /**
     * Get branch statistics.
     */
    public function statistics($id): JsonResponse
    {
        $branch = Branch::findOrFail($id);

        $stats = [
            'overview' => [
                'total_users' => $branch->users()->count(),
                'active_users' => $branch->users()->where('status', 'active')->count(),
                'total_transactions' => $branch->transactions()->count(),
                'total_volume' => $branch->transactions()->where('status', 'completed')->sum('amount'),
            ],
            'today' => [
                'transactions' => $branch->transactions()->whereDate('created_at', today())->count(),
                'volume' => $branch->transactions()
                    ->whereDate('created_at', today())
                    ->where('status', 'completed')
                    ->sum('amount'),
                'new_customers' => $branch->users()->whereDate('created_at', today())->count(),
            ],
            'this_week' => [
                'transactions' => $branch->transactions()
                    ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                    ->count(),
                'volume' => $branch->transactions()
                    ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                    ->where('status', 'completed')
                    ->sum('amount'),
            ],
            'this_month' => [
                'transactions' => $branch->transactions()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
                'volume' => $branch->transactions()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->where('status', 'completed')
                    ->sum('amount'),
            ],
            'performance' => [
                'average_transaction_amount' => $branch->transactions()
                    ->where('status', 'completed')
                    ->avg('amount'),
                'completion_rate' => $this->calculateCompletionRate($branch),
                'customer_satisfaction' => rand(85, 98) / 100, // Mock data
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'branch' => $branch->only(['id', 'name_ar', 'name_en', 'code']),
                'statistics' => $stats,
                'generated_at' => now(),
            ],
        ]);
    }

    /**
     * Get branches by country.
     */
    public function byCountry($countryCode): JsonResponse
    {
        $country = Country::where('code', $countryCode)->firstOrFail();

        $branches = $country->branches()
            ->where('is_active', true)
            ->orderBy('is_main_branch', 'desc')
            ->orderBy('name_en')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'country' => $country->only(['id', 'name_ar', 'name_en', 'code']),
                'branches' => $branches,
                'total_branches' => $branches->count(),
                'main_branches' => $branches->where('is_main_branch', true)->count(),
            ],
        ]);
    }

    /**
     * Calculate branch completion rate.
     */
    private function calculateCompletionRate(Branch $branch): float
    {
        $totalTransactions = $branch->transactions()->count();
        if ($totalTransactions === 0) {
            return 0;
        }

        $completedTransactions = $branch->transactions()->where('status', 'completed')->count();
        return round(($completedTransactions / $totalTransactions) * 100, 2);
    }
}
