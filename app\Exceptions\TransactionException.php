<?php

namespace App\Exceptions;

class TransactionException extends BaseException
{
    protected int $statusCode = 400;

    /**
     * Create a new transaction exception for insufficient funds.
     */
    public static function insufficientFunds(float $available, float $required): self
    {
        return new self(
            "الرصيد غير كافي. الرصيد المتاح: {$available}، المطلوب: {$required}",
            [
                'available_balance' => $available,
                'required_amount' => $required,
                'shortage' => $required - $available,
            ]
        );
    }

    /**
     * Create a new transaction exception for invalid amount.
     */
    public static function invalidAmount(float $amount, float $min = null, float $max = null): self
    {
        $message = "مبلغ التحويل غير صحيح: {$amount}";
        $context = ['amount' => $amount];

        if ($min !== null) {
            $message .= "، الحد الأدنى: {$min}";
            $context['min_amount'] = $min;
        }

        if ($max !== null) {
            $message .= "، الحد الأقصى: {$max}";
            $context['max_amount'] = $max;
        }

        return new self($message, $context);
    }

    /**
     * Create a new transaction exception for currency not supported.
     */
    public static function currencyNotSupported(string $currency, string $country = null): self
    {
        $message = "العملة {$currency} غير مدعومة";
        $context = ['currency' => $currency];

        if ($country) {
            $message .= " في {$country}";
            $context['country'] = $country;
        }

        return new self($message, $context);
    }

    /**
     * Create a new transaction exception for daily limit exceeded.
     */
    public static function dailyLimitExceeded(float $currentTotal, float $limit, float $attemptedAmount): self
    {
        return new self(
            "تم تجاوز الحد اليومي للتحويلات. المجموع الحالي: {$currentTotal}، الحد المسموح: {$limit}",
            [
                'current_total' => $currentTotal,
                'daily_limit' => $limit,
                'attempted_amount' => $attemptedAmount,
                'remaining_limit' => max(0, $limit - $currentTotal),
            ]
        );
    }

    /**
     * Create a new transaction exception for monthly limit exceeded.
     */
    public static function monthlyLimitExceeded(float $currentTotal, float $limit, float $attemptedAmount): self
    {
        return new self(
            "تم تجاوز الحد الشهري للتحويلات. المجموع الحالي: {$currentTotal}، الحد المسموح: {$limit}",
            [
                'current_total' => $currentTotal,
                'monthly_limit' => $limit,
                'attempted_amount' => $attemptedAmount,
                'remaining_limit' => max(0, $limit - $currentTotal),
            ]
        );
    }

    /**
     * Create a new transaction exception for KYC verification required.
     */
    public static function kycVerificationRequired(string $requiredLevel = 'basic'): self
    {
        return new self(
            "يتطلب هذا التحويل التحقق من الهوية. المستوى المطلوب: {$requiredLevel}",
            [
                'required_kyc_level' => $requiredLevel,
                'action_required' => 'complete_kyc_verification',
            ]
        );
    }

    /**
     * Create a new transaction exception for blocked country.
     */
    public static function countryBlocked(string $country, string $reason = null): self
    {
        $message = "التحويل إلى {$country} غير مسموح";
        $context = ['blocked_country' => $country];

        if ($reason) {
            $message .= ". السبب: {$reason}";
            $context['block_reason'] = $reason;
        }

        return new self($message, $context);
    }

    /**
     * Create a new transaction exception for payment method not available.
     */
    public static function paymentMethodNotAvailable(string $method, string $country = null): self
    {
        $message = "طريقة الدفع {$method} غير متاحة";
        $context = ['payment_method' => $method];

        if ($country) {
            $message .= " في {$country}";
            $context['country'] = $country;
        }

        return new self($message, $context);
    }

    /**
     * Create a new transaction exception for fraud detection.
     */
    public static function fraudDetected(string $reason, array $riskFactors = []): self
    {
        return new self(
            "تم اكتشاف نشاط مشبوه. السبب: {$reason}",
            [
                'fraud_reason' => $reason,
                'risk_factors' => $riskFactors,
                'action_required' => 'manual_review',
            ]
        );
    }

    /**
     * Create a new transaction exception for exchange rate not available.
     */
    public static function exchangeRateNotAvailable(string $fromCurrency, string $toCurrency): self
    {
        return new self(
            "سعر الصرف غير متاح من {$fromCurrency} إلى {$toCurrency}",
            [
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'action_required' => 'contact_support',
            ]
        );
    }

    /**
     * Create a new transaction exception for transaction already processed.
     */
    public static function alreadyProcessed(string $transactionId): self
    {
        return new self(
            "التحويل {$transactionId} تم معالجته مسبقاً",
            [
                'transaction_id' => $transactionId,
                'status' => 'already_processed',
            ]
        );
    }

    /**
     * Create a new transaction exception for transaction not found.
     */
    public static function notFound(string $transactionId): self
    {
        return new self(
            "التحويل {$transactionId} غير موجود",
            [
                'transaction_id' => $transactionId,
                'status' => 'not_found',
            ]
        );
    }

    /**
     * Get the error code for this exception.
     */
    public function getErrorCode(): string
    {
        return 'TRANSACTION_ERROR';
    }
}
