<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\DB;

try {
    echo "🚀 Creating Final Demo Data for Dashboard\n";
    echo "=========================================\n\n";
    
    // Get admin user
    $user = User::where('email', '<EMAIL>')->first();
    if (!$user) {
        echo "❌ Admin user not found!\n";
        exit(1);
    }
    
    echo "✅ User found: {$user->email}\n";
    
    // Check table structure
    $columns = DB::select("PRAGMA table_info(transactions)");
    echo "✅ Table columns found: " . count($columns) . "\n";
    
    // Clear existing demo transactions
    DB::table('transactions')->where('sender_name', 'LIKE', '%تجريبي%')->delete();
    echo "✅ Cleared old demo transactions\n";
    
    // Get first currency
    $currency = DB::table('currencies')->first();
    $currencyId = $currency ? $currency->id : 1;
    
    // Create demo transactions with correct column names
    $demoTransactions = [
        ['أحمد محمد', 500, 'completed'],
        ['فاطمة علي', 750, 'completed'],
        ['محمد عبدالله', 300, 'pending'],
        ['سارة أحمد', 1200, 'completed'],
        ['علي حسن', 450, 'processing'],
        ['نورا سالم', 800, 'completed'],
    ];
    
    foreach ($demoTransactions as $index => $transactionData) {
        $recipientName = $transactionData[0];
        $amount = $transactionData[1];
        $status = $transactionData[2];
        $createdAt = now()->subDays(rand(0, 7));
        $transactionNumber = 'DEMO' . str_pad($index + 1, 6, '0', STR_PAD_LEFT);
        $referenceNumber = 'REF' . str_pad($index + 1, 8, '0', STR_PAD_LEFT);
        
        // Insert with correct column names based on migration
        DB::table('transactions')->insert([
            'transaction_number' => $transactionNumber,
            'reference_number' => $referenceNumber,
            'type' => 'transfer',
            'category' => 'international',
            'sender_id' => $user->id,
            'sender_name' => $user->first_name . ' ' . $user->last_name . ' (تجريبي)',
            'sender_phone' => '+966501234567',
            'receiver_name' => $recipientName,
            'receiver_phone' => '+1555000' . str_pad($index, 3, '0', STR_PAD_LEFT),
            'currency_id' => $currencyId,
            'amount' => $amount,
            'exchange_rate' => 3.75,
            'target_currency_id' => $currencyId,
            'target_amount' => round($amount / 3.75, 2),
            'commission_amount' => round($amount * 0.02, 2),
            'commission_rate' => 0.02,
            'total_fees' => round($amount * 0.02, 2),
            'net_amount' => $amount - round($amount * 0.02, 2),
            'status' => $status,
            'payment_method' => 'bank_transfer',
            'purpose' => 'family_support',
            'notes' => 'تحويل تجريبي للاختبار',
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
            'processed_at' => $status === 'completed' ? $createdAt->addMinutes(30) : null,
        ]);
        
        echo "✅ Transaction " . ($index + 1) . ": {$recipientName} - {$amount} SAR ({$status})\n";
    }
    
    // Calculate and display stats
    $totalTransactions = DB::table('transactions')->where('sender_id', $user->id)->count();
    $completedTransactions = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'completed')->count();
    $pendingTransactions = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'pending')->count();
    $processingTransactions = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'processing')->count();
    $totalAmount = DB::table('transactions')->where('sender_id', $user->id)->where('status', 'completed')->sum('amount');
    $successRate = $totalTransactions > 0 ? round(($completedTransactions / $totalTransactions) * 100, 1) : 0;
    
    echo "\n📊 Dashboard Statistics:\n";
    echo "========================\n";
    echo "📈 Total Transactions: {$totalTransactions}\n";
    echo "✅ Completed: {$completedTransactions}\n";
    echo "⏳ Pending: {$pendingTransactions}\n";
    echo "🔄 Processing: {$processingTransactions}\n";
    echo "💰 Total Amount: " . number_format($totalAmount, 2) . " SAR\n";
    echo "📊 Success Rate: {$successRate}%\n";
    
    echo "\n🎯 Test Dashboard Now:\n";
    echo "======================\n";
    echo "🔗 URL: http://localhost:8000/dashboard\n";
    echo "👤 Login: <EMAIL> / password123\n";
    echo "⚡ Should load FAST with real data!\n";
    
    echo "\n✅ Final demo data created successfully!\n";
    echo "🚀 Dashboard is now optimized and ready!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
