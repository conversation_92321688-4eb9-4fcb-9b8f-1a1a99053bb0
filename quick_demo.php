<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Transaction;
use App\Models\Country;

try {
    echo "⚡ Quick Demo Data for Dashboard\n";
    echo "===============================\n\n";
    
    // Get admin user
    $user = User::where('email', '<EMAIL>')->first();
    if (!$user) {
        echo "❌ Admin user not found!\n";
        exit(1);
    }
    
    // Get first country
    $country = Country::first();
    if (!$country) {
        echo "❌ No countries found!\n";
        exit(1);
    }
    
    echo "✅ User: {$user->email}\n";
    echo "✅ Country: {$country->name_en}\n";
    
    // Create simple transactions
    $recipients = [
        'أحمد محمد',
        'فاطمة علي',
        'محمد عبدالله',
        'سارة أحمد',
        'علي حسن'
    ];
    
    $statuses = ['completed', 'pending', 'processing'];
    
    for ($i = 1; $i <= 8; $i++) {
        $recipient = $recipients[array_rand($recipients)];
        $amount = rand(200, 1500);
        $status = $statuses[array_rand($statuses)];
        $createdAt = now()->subDays(rand(0, 10));
        
        Transaction::updateOrCreate([
            'transaction_id' => 'QUICK' . str_pad($i, 3, '0', STR_PAD_LEFT),
        ], [
            'user_id' => $user->id,
            'sender_name' => $user->first_name . ' ' . $user->last_name,
            'sender_phone' => '+966501234567',
            'sender_email' => $user->email,
            'sender_country_id' => $country->id,
            'recipient_name' => $recipient,
            'recipient_phone' => '+1555000' . str_pad($i, 3, '0', STR_PAD_LEFT),
            'recipient_email' => strtolower(str_replace(' ', '.', $recipient)) . '@example.com',
            'recipient_country_id' => $country->id,
            'amount' => $amount,
            'currency_from' => 'SAR',
            'currency_to' => 'USD',
            'exchange_rate' => 3.75,
            'fee' => round($amount * 0.02, 2),
            'total_amount' => round($amount * 1.02, 2),
            'payment_method' => 'bank_transfer',
            'purpose' => 'family_support',
            'status' => $status,
            'notes' => 'تحويل تجريبي سريع',
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
            'completed_at' => $status === 'completed' ? $createdAt->addMinutes(rand(10, 60)) : null,
        ]);
        
        echo "✅ Transaction {$i}: {$recipient} - {$amount} SAR ({$status})\n";
    }
    
    // Calculate stats
    $stats = [
        'total' => Transaction::where('user_id', $user->id)->count(),
        'completed' => Transaction::where('user_id', $user->id)->where('status', 'completed')->count(),
        'pending' => Transaction::where('user_id', $user->id)->where('status', 'pending')->count(),
        'processing' => Transaction::where('user_id', $user->id)->where('status', 'processing')->count(),
        'total_amount' => Transaction::where('user_id', $user->id)->where('status', 'completed')->sum('amount'),
    ];
    
    echo "\n📊 Dashboard Statistics:\n";
    echo "========================\n";
    echo "📈 Total Transactions: {$stats['total']}\n";
    echo "✅ Completed: {$stats['completed']}\n";
    echo "⏳ Pending: {$stats['pending']}\n";
    echo "🔄 Processing: {$stats['processing']}\n";
    echo "💰 Total Amount: " . number_format($stats['total_amount'], 2) . " SAR\n";
    
    $successRate = $stats['total'] > 0 ? round(($stats['completed'] / $stats['total']) * 100, 1) : 0;
    echo "📊 Success Rate: {$successRate}%\n";
    
    echo "\n🎯 Test Dashboard Now:\n";
    echo "======================\n";
    echo "🔗 URL: http://localhost:8000/dashboard\n";
    echo "👤 Login: <EMAIL> / password123\n";
    echo "⚡ Should load FAST with real data!\n";
    
    echo "\n✅ Quick demo data created successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 Line: " . $e->getLine() . "\n";
}
