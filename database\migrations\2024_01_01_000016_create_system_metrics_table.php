<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_metrics', function (Blueprint $table) {
            $table->id();
            $table->string('metric_name');
            $table->string('metric_type'); // counter, gauge, histogram
            $table->decimal('value', 15, 8);
            $table->json('labels')->nullable(); // Additional metadata
            $table->json('metadata')->nullable(); // Extra data
            $table->timestamp('recorded_at');
            $table->timestamps();

            // Indexes for performance
            $table->index(['metric_name', 'recorded_at']);
            $table->index(['metric_type', 'recorded_at']);
            $table->index('recorded_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_metrics');
    }
};
