<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\ExchangeRate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ExchangeRateService
{
    protected array $providers = [
        'fixer' => 'https://api.fixer.io/v1/latest',
        'currencyapi' => 'https://api.currencyapi.com/v3/latest',
        'coingecko' => 'https://api.coingecko.com/api/v3/simple/price',
    ];

    /**
     * Update all exchange rates.
     */
    public function updateAllRates(): array
    {
        $results = [
            'updated' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        try {
            // Update fiat currency rates
            $fiatResults = $this->updateFiatRates();
            $results['updated'] += $fiatResults['updated'];
            $results['failed'] += $fiatResults['failed'];
            $results['errors'] = array_merge($results['errors'], $fiatResults['errors']);

            // Update cryptocurrency rates
            $cryptoResults = $this->updateCryptoRates();
            $results['updated'] += $cryptoResults['updated'];
            $results['failed'] += $cryptoResults['failed'];
            $results['errors'] = array_merge($results['errors'], $cryptoResults['errors']);

            Log::info('Exchange rates update completed', $results);

        } catch (\Exception $e) {
            Log::error('Failed to update exchange rates', [
                'error' => $e->getMessage(),
            ]);
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Update fiat currency rates.
     */
    public function updateFiatRates(): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'errors' => []];

        try {
            $rates = $this->fetchFiatRates();
            
            if (empty($rates)) {
                $results['errors'][] = 'No fiat rates received from API';
                return $results;
            }

            foreach ($rates as $currencyCode => $rate) {
                try {
                    $currency = Currency::where('code', $currencyCode)
                                      ->where('is_crypto', false)
                                      ->first();

                    if ($currency) {
                        $this->updateCurrencyRate($currency, $rate);
                        $results['updated']++;
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Failed to update {$currencyCode}: " . $e->getMessage();
                }
            }

        } catch (\Exception $e) {
            $results['errors'][] = 'Failed to fetch fiat rates: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Update cryptocurrency rates.
     */
    public function updateCryptoRates(): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'errors' => []];

        try {
            $rates = $this->fetchCryptoRates();
            
            if (empty($rates)) {
                $results['errors'][] = 'No crypto rates received from API';
                return $results;
            }

            foreach ($rates as $currencyCode => $rate) {
                try {
                    $currency = Currency::where('code', strtoupper($currencyCode))
                                      ->where('is_crypto', true)
                                      ->first();

                    if ($currency) {
                        $this->updateCurrencyRate($currency, $rate);
                        $results['updated']++;
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Failed to update {$currencyCode}: " . $e->getMessage();
                }
            }

        } catch (\Exception $e) {
            $results['errors'][] = 'Failed to fetch crypto rates: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Fetch fiat currency rates from API.
     */
    protected function fetchFiatRates(): array
    {
        $apiKey = config('services.fixer.api_key');
        
        if (!$apiKey) {
            throw new \Exception('Fixer.io API key not configured');
        }

        $response = Http::timeout(30)->get($this->providers['fixer'], [
            'access_key' => $apiKey,
            'base' => 'USD',
            'symbols' => $this->getFiatCurrencyCodes(),
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch fiat rates: ' . $response->body());
        }

        $data = $response->json();

        if (!$data['success']) {
            throw new \Exception('API error: ' . ($data['error']['info'] ?? 'Unknown error'));
        }

        return $data['rates'] ?? [];
    }

    /**
     * Fetch cryptocurrency rates from API.
     */
    protected function fetchCryptoRates(): array
    {
        $cryptoCodes = $this->getCryptoCurrencyCodes();
        
        if (empty($cryptoCodes)) {
            return [];
        }

        $response = Http::timeout(30)->get($this->providers['coingecko'], [
            'ids' => implode(',', $this->getCoinGeckoIds()),
            'vs_currencies' => 'usd',
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch crypto rates: ' . $response->body());
        }

        $data = $response->json();
        $rates = [];

        // Map CoinGecko response to our currency codes
        $mapping = $this->getCoinGeckoMapping();
        
        foreach ($data as $coinId => $prices) {
            if (isset($mapping[$coinId]) && isset($prices['usd'])) {
                $rates[$mapping[$coinId]] = $prices['usd'];
            }
        }

        return $rates;
    }

    /**
     * Update currency rate.
     */
    protected function updateCurrencyRate(Currency $currency, float $rate): void
    {
        // Update currency rate
        $currency->update(['rate_to_usd' => $rate]);

        // Create exchange rate record
        ExchangeRate::create([
            'from_currency_id' => $currency->id,
            'to_currency_id' => $this->getUSDCurrency()->id,
            'rate' => $rate,
            'source' => $currency->is_crypto ? 'coingecko' : 'fixer',
            'is_active' => true,
        ]);

        Log::info('Updated exchange rate', [
            'currency' => $currency->code,
            'rate' => $rate,
            'is_crypto' => $currency->is_crypto,
        ]);
    }

    /**
     * Get current exchange rate between two currencies.
     */
    public function getExchangeRate(string $fromCurrency, string $toCurrency): ?float
    {
        $cacheKey = "exchange_rate_{$fromCurrency}_{$toCurrency}";
        
        return Cache::remember($cacheKey, 300, function () use ($fromCurrency, $toCurrency) {
            $fromCurrencyModel = Currency::where('code', $fromCurrency)->first();
            $toCurrencyModel = Currency::where('code', $toCurrency)->first();

            if (!$fromCurrencyModel || !$toCurrencyModel) {
                return null;
            }

            // If same currency, rate is 1
            if ($fromCurrency === $toCurrency) {
                return 1.0;
            }

            // Convert through USD
            $fromRate = $fromCurrencyModel->rate_to_usd;
            $toRate = $toCurrencyModel->rate_to_usd;

            if ($fromRate && $toRate) {
                return $toRate / $fromRate;
            }

            return null;
        });
    }

    /**
     * Convert amount between currencies.
     */
    public function convertAmount(float $amount, string $fromCurrency, string $toCurrency): ?float
    {
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);
        
        if ($rate === null) {
            return null;
        }

        return $amount * $rate;
    }

    /**
     * Get fiat currency codes.
     */
    protected function getFiatCurrencyCodes(): string
    {
        return Currency::where('is_crypto', false)
                      ->where('is_active', true)
                      ->pluck('code')
                      ->implode(',');
    }

    /**
     * Get crypto currency codes.
     */
    protected function getCryptoCurrencyCodes(): array
    {
        return Currency::where('is_crypto', true)
                      ->where('is_active', true)
                      ->pluck('code')
                      ->toArray();
    }

    /**
     * Get CoinGecko IDs for cryptocurrencies.
     */
    protected function getCoinGeckoIds(): array
    {
        return [
            'bitcoin',
            'ethereum',
            'tether',
            'usd-coin',
            'binancecoin',
        ];
    }

    /**
     * Get CoinGecko ID to currency code mapping.
     */
    protected function getCoinGeckoMapping(): array
    {
        return [
            'bitcoin' => 'BTC',
            'ethereum' => 'ETH',
            'tether' => 'USDT',
            'usd-coin' => 'USDC',
            'binancecoin' => 'BNB',
        ];
    }

    /**
     * Get USD currency model.
     */
    protected function getUSDCurrency(): Currency
    {
        return Currency::where('code', 'USD')->firstOrFail();
    }

    /**
     * Get latest rates for dashboard.
     */
    public function getLatestRates(): array
    {
        return Cache::remember('latest_exchange_rates', 300, function () {
            $rates = [];

            $currencies = Currency::where('is_active', true)
                                ->orderBy('is_crypto')
                                ->orderBy('code')
                                ->get();

            foreach ($currencies as $currency) {
                $rates[] = [
                    'code' => $currency->code,
                    'name' => $currency->name_en,
                    'symbol' => $currency->symbol,
                    'is_crypto' => $currency->is_crypto,
                    'rate_to_usd' => $currency->rate_to_usd,
                    'updated_at' => $currency->updated_at->toISOString(),
                ];
            }

            return $rates;
        });
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        return Currency::where('is_active', true)
            ->pluck('code')
            ->toArray();
    }

    /**
     * Get multiple exchange rates
     */
    public function getMultipleRates(string $baseCurrency, array $targetCurrencies): array
    {
        $rates = [];

        foreach ($targetCurrencies as $targetCurrency) {
            $rate = $this->getExchangeRate($baseCurrency, $targetCurrency);
            $rates[$targetCurrency] = [
                'success' => $rate !== null,
                'rate' => $rate,
                'from_currency' => $baseCurrency,
                'to_currency' => $targetCurrency,
            ];
        }

        return $rates;
    }

    /**
     * Get historical exchange rate
     */
    public function getHistoricalRate(string $fromCurrency, string $toCurrency, string $date): array
    {
        try {
            $fromCurrencyModel = Currency::where('code', $fromCurrency)->first();
            $toCurrencyModel = Currency::where('code', $toCurrency)->first();

            if (!$fromCurrencyModel || !$toCurrencyModel) {
                return [
                    'success' => false,
                    'error' => 'Currency not found',
                ];
            }

            $rate = ExchangeRate::where('from_currency_id', $fromCurrencyModel->id)
                ->where('to_currency_id', $toCurrencyModel->id)
                ->whereDate('created_at', $date)
                ->orderBy('created_at', 'desc')
                ->first();

            if ($rate) {
                return [
                    'success' => true,
                    'rate' => (float) $rate->rate,
                    'from_currency' => $fromCurrency,
                    'to_currency' => $toCurrency,
                    'date' => $date,
                    'source' => $rate->source,
                ];
            }

            return [
                'success' => false,
                'error' => 'Historical rate not found',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to fetch historical rate: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get exchange rate statistics
     */
    public function getStatistics(): array
    {
        try {
            $totalCurrencies = Currency::where('is_active', true)->count();
            $cryptoCurrencies = Currency::where('is_active', true)->where('is_crypto', true)->count();
            $fiatCurrencies = Currency::where('is_active', true)->where('is_crypto', false)->count();

            $recentUpdates = ExchangeRate::where('created_at', '>=', now()->subHour())->count();
            $lastUpdate = ExchangeRate::orderBy('created_at', 'desc')->first()?->created_at;

            return [
                'total_currencies' => $totalCurrencies,
                'crypto_currencies' => $cryptoCurrencies,
                'fiat_currencies' => $fiatCurrencies,
                'recent_updates' => $recentUpdates,
                'last_update' => $lastUpdate?->toISOString(),
                'cache_hit_rate' => $this->getCacheHitRate(),
            ];

        } catch (\Exception $e) {
            return [
                'error' => 'Failed to get statistics: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get cache hit rate
     */
    private function getCacheHitRate(): float
    {
        $hits = Cache::get('exchange_rate_cache_hits', 0);
        $misses = Cache::get('exchange_rate_cache_misses', 0);
        $total = $hits + $misses;

        return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
    }

    /**
     * Clear exchange rate cache
     */
    public function clearCache(): bool
    {
        try {
            $currencies = $this->getSupportedCurrencies();

            foreach ($currencies as $fromCurrency) {
                foreach ($currencies as $toCurrency) {
                    if ($fromCurrency !== $toCurrency) {
                        Cache::forget("exchange_rate_{$fromCurrency}_{$toCurrency}");
                    }
                }
            }

            Cache::forget('latest_exchange_rates');
            return true;

        } catch (\Exception $e) {
            Log::error('Failed to clear exchange rate cache', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
