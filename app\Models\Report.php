<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Report extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'type',
        'description',
        'parameters',
        'data',
        'file_path',
        'file_name',
        'file_size',
        'format',
        'status',
        'generated_at',
        'expires_at',
        'is_scheduled',
        'schedule_frequency',
        'next_run_at',
        'metadata',
    ];

    protected $casts = [
        'parameters' => 'array',
        'data' => 'array',
        'metadata' => 'array',
        'file_size' => 'integer',
        'is_scheduled' => 'boolean',
        'generated_at' => 'datetime',
        'expires_at' => 'datetime',
        'next_run_at' => 'datetime',
    ];

    /**
     * Get the user that created the report.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for reports by type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for completed reports.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending reports.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for failed reports.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for scheduled reports.
     */
    public function scopeScheduled($query)
    {
        return $query->where('is_scheduled', true);
    }

    /**
     * Scope for reports due for generation.
     */
    public function scopeDueForGeneration($query)
    {
        return $query->where('is_scheduled', true)
                    ->where('next_run_at', '<=', now())
                    ->where('status', 'pending');
    }

    /**
     * Mark report as completed.
     */
    public function markAsCompleted(string $filePath, string $fileName, int $fileSize): void
    {
        $this->update([
            'status' => 'completed',
            'file_path' => $filePath,
            'file_name' => $fileName,
            'file_size' => $fileSize,
            'generated_at' => now(),
            'expires_at' => now()->addDays(30), // Reports expire after 30 days
        ]);

        // Schedule next run if it's a scheduled report
        if ($this->is_scheduled) {
            $this->scheduleNextRun();
        }
    }

    /**
     * Mark report as failed.
     */
    public function markAsFailed(string $errorMessage = ''): void
    {
        $this->update([
            'status' => 'failed',
            'metadata' => array_merge($this->metadata ?? [], [
                'error_message' => $errorMessage,
                'failed_at' => now()->toISOString(),
            ]),
        ]);
    }

    /**
     * Schedule next run for scheduled reports.
     */
    public function scheduleNextRun(): void
    {
        if (!$this->is_scheduled) {
            return;
        }

        $nextRun = match ($this->schedule_frequency) {
            'daily' => now()->addDay(),
            'weekly' => now()->addWeek(),
            'monthly' => now()->addMonth(),
            'quarterly' => now()->addMonths(3),
            'yearly' => now()->addYear(),
            default => null,
        };

        if ($nextRun) {
            $this->update([
                'next_run_at' => $nextRun,
                'status' => 'pending',
            ]);
        }
    }

    /**
     * Get file download URL.
     */
    public function getDownloadUrlAttribute(): ?string
    {
        if ($this->status === 'completed' && $this->file_path) {
            return route('reports.download', $this->id);
        }
        return null;
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'N/A';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if report has expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get formatted report data.
     */
    public function getFormattedDataAttribute(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'description' => $this->description,
            'status' => $this->status,
            'format' => $this->format,
            'file_name' => $this->file_name,
            'file_size' => $this->formatted_file_size,
            'download_url' => $this->download_url,
            'is_scheduled' => $this->is_scheduled,
            'schedule_frequency' => $this->schedule_frequency,
            'generated_at' => $this->generated_at?->toISOString(),
            'expires_at' => $this->expires_at?->toISOString(),
            'next_run_at' => $this->next_run_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'is_expired' => $this->isExpired(),
            'user' => $this->user ? [
                'id' => $this->user->id,
                'name' => $this->user->first_name . ' ' . $this->user->last_name,
            ] : null,
        ];
    }

    /**
     * Create new report.
     */
    public static function createReport(array $data): self
    {
        return self::create(array_merge($data, [
            'status' => 'pending',
            'user_id' => auth()->id(),
        ]));
    }
}
