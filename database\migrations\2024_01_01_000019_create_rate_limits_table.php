<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rate_limits', function (Blueprint $table) {
            $table->id();
            $table->string('identifier'); // IP, user_id, api_key, etc.
            $table->string('type'); // ip, user, api_key, endpoint
            $table->string('endpoint')->nullable(); // Specific endpoint being limited
            $table->integer('attempts')->default(0);
            $table->integer('max_attempts');
            $table->integer('window_seconds'); // Time window in seconds
            $table->timestamp('window_start');
            $table->timestamp('reset_at');
            $table->boolean('is_blocked')->default(false);
            $table->timestamp('blocked_until')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['identifier', 'type', 'endpoint']);
            $table->index(['reset_at', 'is_blocked']);
            $table->index('blocked_until');
            $table->index('window_start');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rate_limits');
    }
};
