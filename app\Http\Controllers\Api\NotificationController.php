<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    /**
     * Get user notifications.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $query = $user->notifications();

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by read status
        if ($request->has('is_read')) {
            $query->where('is_read', $request->boolean('is_read'));
        }

        // Filter by priority
        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $notifications,
            'unread_count' => $user->notifications()->where('is_read', false)->count(),
        ]);
    }

    /**
     * Get specific notification.
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $notification = $user->notifications()->findOrFail($id);

        // Mark as read if not already
        if (!$notification->is_read) {
            $notification->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $notification,
        ]);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $notification = $user->notifications()->findOrFail($id);
        
        $notification->update([
            'is_read' => true,
            'read_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read',
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $updated = $user->notifications()
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);

        return response()->json([
            'success' => true,
            'message' => "Marked {$updated} notifications as read",
            'updated_count' => $updated,
        ]);
    }

    /**
     * Delete notification.
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $notification = $user->notifications()->findOrFail($id);
        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully',
        ]);
    }

    /**
     * Get notification statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();

        $stats = [
            'total' => $user->notifications()->count(),
            'unread' => $user->notifications()->where('is_read', false)->count(),
            'read' => $user->notifications()->where('is_read', true)->count(),
            'by_type' => $user->notifications()
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type'),
            'by_priority' => $user->notifications()
                ->selectRaw('priority, COUNT(*) as count')
                ->groupBy('priority')
                ->pluck('count', 'priority'),
            'today' => $user->notifications()
                ->whereDate('created_at', today())
                ->count(),
            'this_week' => $user->notifications()
                ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get notification preferences.
     */
    public function preferences(Request $request): JsonResponse
    {
        $user = $request->user();

        // In a real implementation, these would be stored in user preferences
        $preferences = [
            'email_notifications' => true,
            'sms_notifications' => true,
            'push_notifications' => true,
            'notification_types' => [
                'transaction' => true,
                'security' => true,
                'system' => true,
                'marketing' => false,
            ],
            'quiet_hours' => [
                'enabled' => true,
                'start' => '22:00',
                'end' => '08:00',
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $preferences,
        ]);
    }

    /**
     * Update notification preferences.
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'notification_types' => 'array',
            'quiet_hours' => 'array',
        ]);

        // In a real implementation, save to user preferences table
        
        return response()->json([
            'success' => true,
            'message' => 'Notification preferences updated successfully',
            'data' => $request->all(),
        ]);
    }

    /**
     * Send test notification.
     */
    public function sendTest(Request $request): JsonResponse
    {
        $user = $request->user();

        $notification = Notification::create([
            'user_id' => $user->id,
            'type' => 'system',
            'title' => 'Test Notification',
            'message' => 'This is a test notification sent at ' . now()->format('Y-m-d H:i:s'),
            'data' => [
                'test' => true,
                'timestamp' => now(),
            ],
            'channel' => 'database',
            'priority' => 'normal',
            'is_sent' => true,
            'sent_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Test notification sent successfully',
            'data' => $notification,
        ]);
    }

    /**
     * Get recent notifications for dashboard.
     */
    public function recent(Request $request): JsonResponse
    {
        $user = $request->user();
        $limit = $request->get('limit', 5);

        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $notifications,
            'total_unread' => $user->notifications()->where('is_read', false)->count(),
        ]);
    }
}
