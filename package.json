{"$schema": "https://json.schemastore.org/package.json", "name": "mony-transfer-financial-system", "version": "1.0.0", "description": "Mony Transfer Global Financial System - Advanced Money Transfer Platform with Cryptocurrency Support", "private": true, "type": "module", "keywords": ["money-transfer", "cryptocurrency", "fintech", "laravel", "blockchain", "financial-services", "vue", "tailwindcss"], "author": "Mony Transfer Team", "license": "MIT", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint resources/js --ext .js,.vue", "lint:fix": "eslint resources/js --ext .js,.vue --fix", "format": "prettier --write resources/js/**/*.{js,vue,css}", "type-check": "vue-tsc --noEmit", "analyze": "vite-bundle-analyzer", "clean": "rm -rf node_modules package-lock.json && npm install", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@inertiajs/vue3": "^1.0.14", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@vueuse/core": "^10.7.0", "axios": "^1.8.2", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^3.0.6", "lodash": "^4.17.21", "pinia": "^2.1.7", "pusher-js": "^8.4.0-rc2", "qrcode": "^1.5.3", "socket.io-client": "^4.7.4", "vue": "^3.3.13", "vue-chartjs": "^5.3.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "vue-toastification": "^2.0.0-rc.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@eslint/js": "^8.56.0", "@tailwindcss/vite": "^4.0.0", "@types/lodash": "^4.14.202", "@types/node": "^20.10.6", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "@vitejs/plugin-vue": "^4.6.2", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "autoprefixer": "^10.4.16", "concurrently": "^9.0.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "sass": "^1.69.7", "tailwindcss": "^4.0.0", "typescript": "^5.3.3", "vite": "^7.0.4", "vite-bundle-analyzer": "^0.7.0", "vitepress": "^1.0.0-rc.36", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/javascript/setup.js"], "moduleFileExtensions": ["js", "json", "vue"], "transform": {"^.+\\.js$": "babel-jest", "^.+\\.vue$": "@vue/vue3-jest"}, "collectCoverageFrom": ["resources/js/**/*.{js,vue}", "!resources/js/app.js", "!**/node_modules/**"], "coverageReporters": ["html", "text", "lcov"], "coverageDirectory": "coverage"}, "eslintConfig": {"root": true, "env": {"node": true, "browser": true, "es2022": true}, "extends": ["eslint:recommended", "@vue/eslint-config-typescript", "@vue/eslint-config-prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-debugger": "warn", "vue/multi-word-component-names": "off", "vue/no-reserved-component-names": "off"}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100, "bracketSpacing": true, "arrowParens": "avoid"}, "repository": {"type": "git", "url": "https://github.com/your-repo/mony-transfer.git"}, "bugs": {"url": "https://github.com/your-repo/mony-transfer/issues"}, "homepage": "https://monytransfer.com"}