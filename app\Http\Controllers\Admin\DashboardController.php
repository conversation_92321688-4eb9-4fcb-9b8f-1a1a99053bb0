<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use App\Models\ExchangeRate;
use App\Models\AuditLog;
use App\Models\Country;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    /**
     * Get dashboard overview statistics
     */
    public function overview(): JsonResponse
    {
        $stats = Cache::remember('admin_dashboard_overview', 300, function () {
            return [
                'users' => $this->getUserStats(),
                'transactions' => $this->getTransactionStats(),
                'revenue' => $this->getRevenueStats(),
                'system' => $this->getSystemStats(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get real-time dashboard data
     */
    public function realTime(): JsonResponse
    {
        $data = [
            'active_users' => $this->getActiveUsersCount(),
            'pending_transactions' => $this->getPendingTransactionsCount(),
            'recent_activities' => $this->getRecentActivities(),
            'system_alerts' => $this->getSystemAlerts(),
            'exchange_rates_status' => $this->getExchangeRatesStatus(),
        ];

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * Get transaction analytics
     */
    public function transactionAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week'); // day, week, month, year
        $groupBy = $request->get('group_by', 'day'); // hour, day, week, month

        $analytics = Cache::remember("transaction_analytics_{$period}_{$groupBy}", 600, function () use ($period, $groupBy) {
            $startDate = $this->getStartDate($period);
            
            return [
                'volume_chart' => $this->getVolumeChart($startDate, $groupBy),
                'status_distribution' => $this->getStatusDistribution($startDate),
                'currency_breakdown' => $this->getCurrencyBreakdown($startDate),
                'country_breakdown' => $this->getCountryBreakdown($startDate),
                'payment_methods' => $this->getPaymentMethodsBreakdown($startDate),
                'risk_analysis' => $this->getRiskAnalysis($startDate),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Get user analytics
     */
    public function userAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'month');
        
        $analytics = Cache::remember("user_analytics_{$period}", 600, function () use ($period) {
            $startDate = $this->getStartDate($period);
            
            return [
                'registration_trend' => $this->getRegistrationTrend($startDate),
                'user_activity' => $this->getUserActivity($startDate),
                'verification_status' => $this->getVerificationStatus(),
                'user_segments' => $this->getUserSegments(),
                'geographic_distribution' => $this->getGeographicDistribution(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Get financial reports
     */
    public function financialReports(Request $request): JsonResponse
    {
        $period = $request->get('period', 'month');
        
        $reports = Cache::remember("financial_reports_{$period}", 300, function () use ($period) {
            $startDate = $this->getStartDate($period);
            
            return [
                'revenue_summary' => $this->getRevenueSummary($startDate),
                'fee_analysis' => $this->getFeeAnalysis($startDate),
                'profit_margins' => $this->getProfitMargins($startDate),
                'currency_exposure' => $this->getCurrencyExposure($startDate),
                'top_corridors' => $this->getTopCorridors($startDate),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $reports,
        ]);
    }

    /**
     * Get system health metrics
     */
    public function systemHealth(): JsonResponse
    {
        $health = [
            'database' => $this->checkDatabaseHealth(),
            'cache' => $this->checkCacheHealth(),
            'queue' => $this->checkQueueHealth(),
            'storage' => $this->checkStorageHealth(),
            'api_performance' => $this->getApiPerformance(),
            'error_rates' => $this->getErrorRates(),
        ];

        return response()->json([
            'success' => true,
            'data' => $health,
        ]);
    }

    /**
     * Get audit logs
     */
    public function auditLogs(Request $request): JsonResponse
    {
        $query = AuditLog::with('user')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('severity')) {
            $query->where('severity', $request->severity);
        }

        if ($request->has('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        $logs = $query->paginate($request->get('per_page', 50));

        return response()->json([
            'success' => true,
            'data' => [
                'logs' => $logs->items(),
                'pagination' => [
                    'current_page' => $logs->currentPage(),
                    'last_page' => $logs->lastPage(),
                    'per_page' => $logs->perPage(),
                    'total' => $logs->total(),
                ],
            ],
        ]);
    }

    /**
     * Get user statistics
     */
    protected function getUserStats(): array
    {
        return [
            'total' => User::count(),
            'active' => User::where('status', 'active')->count(),
            'verified' => User::whereNotNull('email_verified_at')->count(),
            'new_today' => User::whereDate('created_at', today())->count(),
            'new_this_week' => User::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'new_this_month' => User::whereMonth('created_at', now()->month)->count(),
        ];
    }

    /**
     * Get transaction statistics
     */
    protected function getTransactionStats(): array
    {
        return [
            'total' => Transaction::count(),
            'pending' => Transaction::where('status', 'pending')->count(),
            'completed' => Transaction::where('status', 'completed')->count(),
            'failed' => Transaction::where('status', 'failed')->count(),
            'cancelled' => Transaction::where('status', 'cancelled')->count(),
            'today' => Transaction::whereDate('created_at', today())->count(),
            'this_week' => Transaction::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => Transaction::whereMonth('created_at', now()->month)->count(),
        ];
    }

    /**
     * Get revenue statistics
     */
    protected function getRevenueStats(): array
    {
        return [
            'total_fees' => Transaction::where('status', 'completed')->sum('fee'),
            'today_fees' => Transaction::where('status', 'completed')
                ->whereDate('created_at', today())
                ->sum('fee'),
            'this_week_fees' => Transaction::where('status', 'completed')
                ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->sum('fee'),
            'this_month_fees' => Transaction::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->sum('fee'),
            'average_transaction_value' => Transaction::where('status', 'completed')->avg('amount'),
            'average_fee' => Transaction::where('status', 'completed')->avg('fee'),
        ];
    }

    /**
     * Get system statistics
     */
    protected function getSystemStats(): array
    {
        return [
            'countries' => Country::where('is_active', true)->count(),
            'currencies' => Currency::where('is_active', true)->count(),
            'exchange_rates' => ExchangeRate::where('is_active', true)->count(),
            'audit_logs' => AuditLog::count(),
            'disk_usage' => $this->getDiskUsage(),
            'memory_usage' => $this->getMemoryUsage(),
        ];
    }

    /**
     * Get active users count (last 24 hours)
     */
    protected function getActiveUsersCount(): int
    {
        return User::where('last_login_at', '>=', now()->subDay())->count();
    }

    /**
     * Get pending transactions count
     */
    protected function getPendingTransactionsCount(): int
    {
        return Transaction::where('status', 'pending')->count();
    }

    /**
     * Get recent activities
     */
    protected function getRecentActivities(): array
    {
        return AuditLog::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->id,
                    'event_type' => $log->event_type,
                    'description' => $log->description,
                    'user' => $log->user ? $log->user->first_name . ' ' . $log->user->last_name : 'System',
                    'created_at' => $log->created_at,
                    'severity' => $log->severity,
                ];
            })
            ->toArray();
    }

    /**
     * Get system alerts
     */
    protected function getSystemAlerts(): array
    {
        $alerts = [];

        // Check for high-risk transactions
        $highRiskCount = Transaction::where('risk_level', 'high')
            ->where('status', 'pending')
            ->count();

        if ($highRiskCount > 0) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'High Risk Transactions',
                'message' => "{$highRiskCount} high-risk transactions pending review",
                'action_url' => '/admin/transactions?risk_level=high&status=pending',
            ];
        }

        // Check for failed transactions
        $failedCount = Transaction::where('status', 'failed')
            ->whereDate('created_at', today())
            ->count();

        if ($failedCount > 10) {
            $alerts[] = [
                'type' => 'error',
                'title' => 'High Failure Rate',
                'message' => "{$failedCount} transactions failed today",
                'action_url' => '/admin/transactions?status=failed',
            ];
        }

        // Check for outdated exchange rates
        $outdatedRates = ExchangeRate::where('last_updated', '<', now()->subHours(2))
            ->where('is_active', true)
            ->count();

        if ($outdatedRates > 0) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Outdated Exchange Rates',
                'message' => "{$outdatedRates} exchange rates need updating",
                'action_url' => '/admin/exchange-rates',
            ];
        }

        return $alerts;
    }

    /**
     * Get exchange rates status
     */
    protected function getExchangeRatesStatus(): array
    {
        return [
            'total' => ExchangeRate::where('is_active', true)->count(),
            'updated_today' => ExchangeRate::where('is_active', true)
                ->whereDate('last_updated', today())
                ->count(),
            'outdated' => ExchangeRate::where('is_active', true)
                ->where('last_updated', '<', now()->subHours(2))
                ->count(),
            'last_update' => ExchangeRate::where('is_active', true)
                ->max('last_updated'),
        ];
    }

    /**
     * Get start date based on period
     */
    protected function getStartDate(string $period): \Carbon\Carbon
    {
        return match($period) {
            'day' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * Get volume chart data
     */
    protected function getVolumeChart(\Carbon\Carbon $startDate, string $groupBy): array
    {
        $dateFormat = match($groupBy) {
            'hour' => '%Y-%m-%d %H:00:00',
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m-%d',
        };

        return Transaction::where('created_at', '>=', $startDate)
            ->selectRaw("DATE_FORMAT(created_at, '{$dateFormat}') as period")
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(amount) as volume')
            ->selectRaw('SUM(fee) as fees')
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->toArray();
    }

    /**
     * Get status distribution
     */
    protected function getStatusDistribution(\Carbon\Carbon $startDate): array
    {
        return Transaction::where('created_at', '>=', $startDate)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * Get currency breakdown
     */
    protected function getCurrencyBreakdown(\Carbon\Carbon $startDate): array
    {
        return Transaction::where('created_at', '>=', $startDate)
            ->selectRaw('currency_from, currency_to, COUNT(*) as count, SUM(amount) as volume')
            ->groupBy('currency_from', 'currency_to')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get country breakdown
     */
    protected function getCountryBreakdown(\Carbon\Carbon $startDate): array
    {
        return Transaction::join('countries as sender_country', 'transactions.sender_country_id', '=', 'sender_country.id')
            ->join('countries as recipient_country', 'transactions.recipient_country_id', '=', 'recipient_country.id')
            ->where('transactions.created_at', '>=', $startDate)
            ->selectRaw('sender_country.name_en as from_country, recipient_country.name_en as to_country, COUNT(*) as count')
            ->groupBy('sender_country.id', 'recipient_country.id', 'sender_country.name_en', 'recipient_country.name_en')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get payment methods breakdown
     */
    protected function getPaymentMethodsBreakdown(\Carbon\Carbon $startDate): array
    {
        return Transaction::where('created_at', '>=', $startDate)
            ->selectRaw('payment_method, COUNT(*) as count')
            ->groupBy('payment_method')
            ->get()
            ->pluck('count', 'payment_method')
            ->toArray();
    }

    /**
     * Get risk analysis
     */
    protected function getRiskAnalysis(\Carbon\Carbon $startDate): array
    {
        return [
            'risk_distribution' => Transaction::where('created_at', '>=', $startDate)
                ->selectRaw('risk_level, COUNT(*) as count')
                ->groupBy('risk_level')
                ->get()
                ->pluck('count', 'risk_level')
                ->toArray(),
            'average_risk_score' => Transaction::where('created_at', '>=', $startDate)
                ->avg('risk_score'),
            'high_risk_pending' => Transaction::where('created_at', '>=', $startDate)
                ->where('risk_level', 'high')
                ->where('status', 'pending')
                ->count(),
        ];
    }

    /**
     * Helper methods for system health checks
     */
    protected function checkDatabaseHealth(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = (microtime(true) - $start) * 1000;
            
            return [
                'status' => 'healthy',
                'response_time' => round($responseTime, 2) . 'ms',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    protected function checkCacheHealth(): array
    {
        try {
            Cache::put('health_check', 'ok', 60);
            $value = Cache::get('health_check');
            
            return [
                'status' => $value === 'ok' ? 'healthy' : 'unhealthy',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    protected function checkQueueHealth(): array
    {
        // This would check queue status - simplified for now
        return [
            'status' => 'healthy',
            'pending_jobs' => 0,
        ];
    }

    protected function checkStorageHealth(): array
    {
        try {
            $diskSpace = disk_free_space(storage_path());
            $totalSpace = disk_total_space(storage_path());
            $usedPercentage = (($totalSpace - $diskSpace) / $totalSpace) * 100;
            
            return [
                'status' => $usedPercentage < 90 ? 'healthy' : 'warning',
                'used_percentage' => round($usedPercentage, 2),
                'free_space' => $this->formatBytes($diskSpace),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    protected function getApiPerformance(): array
    {
        // This would analyze API response times - simplified for now
        return [
            'average_response_time' => '150ms',
            'success_rate' => '99.5%',
        ];
    }

    protected function getErrorRates(): array
    {
        // This would analyze error logs - simplified for now
        return [
            'error_rate' => '0.5%',
            'critical_errors' => 0,
        ];
    }

    protected function getDiskUsage(): string
    {
        $bytes = disk_total_space(storage_path()) - disk_free_space(storage_path());
        return $this->formatBytes($bytes);
    }

    protected function getMemoryUsage(): string
    {
        return $this->formatBytes(memory_get_usage(true));
    }

    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
