<?php

namespace App\Providers;

use App\Events\FraudAlertTriggered;
use App\Events\TransactionCreated;
use App\Events\TransactionStatusUpdated;
use App\Listeners\SendTransactionNotification;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Observers\TransactionObserver;
use App\Services\FraudDetectionService;
use App\Services\TransactionService;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class FinancialServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register fraud detection service
        $this->app->singleton(FraudDetectionService::class, function ($app) {
            return new FraudDetectionService();
        });

        // Register transaction service
        $this->app->singleton(TransactionService::class, function ($app) {
            return new TransactionService($app->make(FraudDetectionService::class));
        });

        // Register financial configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/financial.php', 'financial'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register model observers
        $this->registerObservers();

        // Register event listeners
        $this->registerEventListeners();

        // Register custom validation rules
        $this->registerValidationRules();

        // Register custom blade directives
        $this->registerBladeDirectives();

        // Register macros
        $this->registerMacros();
    }

    /**
     * Register model observers.
     */
    private function registerObservers(): void
    {
        Transaction::observe(TransactionObserver::class);
    }

    /**
     * Register event listeners.
     */
    private function registerEventListeners(): void
    {
        Event::listen(
            [TransactionCreated::class, TransactionStatusUpdated::class],
            SendTransactionNotification::class
        );

        Event::listen(
            FraudAlertTriggered::class,
            function (FraudAlertTriggered $event) {
                // Send immediate alert to security team
                \Illuminate\Support\Facades\Log::channel('security')->critical(
                    'Fraud alert triggered',
                    [
                        'alert_id' => $event->fraudAlert->id,
                        'transaction_id' => $event->transaction->id,
                        'user_id' => $event->user->id,
                        'risk_level' => $event->fraudAlert->risk_level,
                        'risk_score' => $event->fraudAlert->risk_score,
                    ]
                );
            }
        );
    }

    /**
     * Register custom validation rules.
     */
    private function registerValidationRules(): void
    {
        \Illuminate\Support\Facades\Validator::extend('sufficient_balance', function ($attribute, $value, $parameters, $validator) {
            $userId = $parameters[0] ?? null;
            $currencyId = $parameters[1] ?? null;

            if (!$userId || !$currencyId) {
                return false;
            }

            $user = User::find($userId);
            if (!$user) {
                return false;
            }

            $wallet = $user->getWalletForCurrency($currencyId);
            if (!$wallet) {
                return false;
            }

            return $wallet->hasSufficientBalance($value);
        });

        \Illuminate\Support\Facades\Validator::extend('within_daily_limit', function ($attribute, $value, $parameters, $validator) {
            $userId = $parameters[0] ?? null;

            if (!$userId) {
                return false;
            }

            $user = User::find($userId);
            if (!$user) {
                return false;
            }

            $dailyUsage = $user->sentTransactions()
                ->whereDate('created_at', today())
                ->where('status', '!=', 'failed')
                ->sum('amount');

            return ($dailyUsage + $value) <= $user->daily_limit;
        });

        \Illuminate\Support\Facades\Validator::extend('within_monthly_limit', function ($attribute, $value, $parameters, $validator) {
            $userId = $parameters[0] ?? null;

            if (!$userId) {
                return false;
            }

            $user = User::find($userId);
            if (!$user) {
                return false;
            }

            $monthlyUsage = $user->sentTransactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', '!=', 'failed')
                ->sum('amount');

            return ($monthlyUsage + $value) <= $user->monthly_limit;
        });

        \Illuminate\Support\Facades\Validator::extend('valid_currency_pair', function ($attribute, $value, $parameters, $validator) {
            $fromCurrencyId = $parameters[0] ?? null;
            $toCurrencyId = $value;

            if (!$fromCurrencyId || !$toCurrencyId) {
                return false;
            }

            // Check if exchange rate exists or can be calculated
            $exchangeRate = \App\Models\ExchangeRate::where('from_currency_id', $fromCurrencyId)
                ->where('to_currency_id', $toCurrencyId)
                ->where('is_active', true)
                ->first();

            if ($exchangeRate) {
                return true;
            }

            // Check reverse rate
            $reverseRate = \App\Models\ExchangeRate::where('from_currency_id', $toCurrencyId)
                ->where('to_currency_id', $fromCurrencyId)
                ->where('is_active', true)
                ->first();

            return $reverseRate !== null;
        });
    }

    /**
     * Register custom blade directives.
     */
    private function registerBladeDirectives(): void
    {
        \Illuminate\Support\Facades\Blade::directive('currency', function ($expression) {
            return "<?php echo number_format($expression, 2); ?>";
        });

        \Illuminate\Support\Facades\Blade::directive('transactionStatus', function ($expression) {
            return "<?php echo ucfirst(str_replace('_', ' ', $expression)); ?>";
        });

        \Illuminate\Support\Facades\Blade::directive('riskLevel', function ($expression) {
            $colors = [
                'low' => 'success',
                'medium' => 'warning',
                'high' => 'danger',
            ];
            return "<?php 
                \$level = $expression;
                \$colors = " . var_export($colors, true) . ";
                \$color = \$colors[\$level] ?? 'secondary';
                echo '<span class=\"badge bg-' . \$color . '\">' . ucfirst(\$level) . '</span>';
            ?>";
        });
    }

    /**
     * Register macros.
     */
    private function registerMacros(): void
    {
        // Collection macro for calculating transaction fees
        \Illuminate\Support\Collection::macro('calculateTotalFees', function () {
            return $this->sum('total_fees');
        });

        // Collection macro for filtering by currency
        \Illuminate\Support\Collection::macro('byCurrency', function ($currencyCode) {
            return $this->filter(function ($item) use ($currencyCode) {
                return $item->currency->code === $currencyCode;
            });
        });

        // Collection macro for filtering by status
        \Illuminate\Support\Collection::macro('byStatus', function ($status) {
            return $this->where('status', $status);
        });

        // Request macro for getting user's IP with proxy support
        \Illuminate\Http\Request::macro('getRealIp', function () {
            $headers = [
                'HTTP_CF_CONNECTING_IP',
                'HTTP_X_FORWARDED_FOR',
                'HTTP_X_FORWARDED',
                'HTTP_X_CLUSTER_CLIENT_IP',
                'HTTP_FORWARDED_FOR',
                'HTTP_FORWARDED',
                'REMOTE_ADDR'
            ];

            foreach ($headers as $header) {
                if (!empty($_SERVER[$header])) {
                    $ips = explode(',', $_SERVER[$header]);
                    $ip = trim($ips[0]);
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                        return $ip;
                    }
                }
            }

            return $this->ip();
        });
    }
}
