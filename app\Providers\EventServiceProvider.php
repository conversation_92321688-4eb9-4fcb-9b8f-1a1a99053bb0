<?php

namespace App\Providers;

use App\Events\FraudAlertTriggered;
use App\Events\LoginAttempt;
use App\Events\TransactionCreated;
use App\Events\TransactionStatusUpdated;
use App\Events\UserRegistered;
use App\Listeners\LogLoginAttempt;
use App\Listeners\LogUserActivity;
use App\Listeners\SendTransactionNotification;
use App\Listeners\SendWelcomeNotification;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // User Events
        UserRegistered::class => [
            SendWelcomeNotification::class,
            LogUserActivity::class,
        ],

        LoginAttempt::class => [
            LogLoginAttempt::class,
        ],

        // Transaction Events
        TransactionCreated::class => [
            SendTransactionNotification::class,
            LogUserActivity::class,
        ],

        TransactionStatusUpdated::class => [
            SendTransactionNotification::class,
            LogUserActivity::class,
        ],

        // Security Events
        FraudAlertTriggered::class => [
            SendTransactionNotification::class,
            LogUserActivity::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        parent::boot();

        // Register model events
        $this->registerModelEvents();

        // Register custom events
        $this->registerCustomEvents();
    }

    /**
     * Register model events.
     */
    protected function registerModelEvents(): void
    {
        // User model events
        Event::listen('eloquent.created: App\Models\User', function ($user) {
            event(new UserRegistered($user, [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'source' => 'web',
            ]));
        });

        // Transaction model events
        Event::listen('eloquent.created: App\Models\Transaction', function ($transaction) {
            event(new TransactionCreated($transaction));
        });

        Event::listen('eloquent.updated: App\Models\Transaction', function ($transaction) {
            if ($transaction->wasChanged('status')) {
                event(new TransactionStatusUpdated($transaction, $transaction->getOriginal('status')));
            }
        });

        // Fraud detection events
        Event::listen('eloquent.created: App\Models\FraudDetection', function ($fraudDetection) {
            if ($fraudDetection->risk_level === 'critical' || $fraudDetection->risk_level === 'high') {
                event(new FraudAlertTriggered($fraudDetection));
            }
        });
    }

    /**
     * Register custom events.
     */
    protected function registerCustomEvents(): void
    {
        // Login attempt tracking
        Event::listen('auth.attempt', function ($credentials, $remember, $login) {
            $user = null;
            if ($login) {
                $user = auth()->user();
            }

            event(new LoginAttempt(
                $user,
                $credentials['email'] ?? '',
                $login,
                [
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'is_new_device' => $this->isNewDevice($user),
                    'is_suspicious' => $this->isSuspiciousLogin($user),
                ]
            ));
        });

        // System maintenance events
        Event::listen('system.maintenance.start', function () {
            // Notify users about maintenance
        });

        Event::listen('system.maintenance.end', function () {
            // Notify users that maintenance is complete
        });
    }

    /**
     * Check if this is a new device for the user.
     */
    protected function isNewDevice($user): bool
    {
        if (!$user) {
            return false;
        }

        $currentUserAgent = request()->userAgent();
        $loginHistory = $user->login_history ?? [];

        foreach ($loginHistory as $login) {
            if (isset($login['user_agent']) && $login['user_agent'] === $currentUserAgent) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if this is a suspicious login.
     */
    protected function isSuspiciousLogin($user): bool
    {
        if (!$user) {
            return false;
        }

        $currentIp = request()->ip();
        $loginHistory = $user->login_history ?? [];

        // Check for rapid location changes
        if (!empty($loginHistory)) {
            $lastLogin = $loginHistory[0] ?? null;
            if ($lastLogin && isset($lastLogin['ip_address'])) {
                $lastIp = $lastLogin['ip_address'];
                $lastLoginTime = $lastLogin['timestamp'] ?? null;

                // If different IP and last login was within 1 hour
                if ($lastIp !== $currentIp && $lastLoginTime) {
                    $lastLoginCarbon = \Carbon\Carbon::parse($lastLoginTime);
                    if ($lastLoginCarbon->diffInHours(now()) < 1) {
                        return true;
                    }
                }
            }
        }

        // Check for known suspicious IPs (this would be more sophisticated in production)
        $suspiciousIps = [
            // Add known suspicious IP ranges
        ];

        foreach ($suspiciousIps as $suspiciousIp) {
            if (str_starts_with($currentIp, $suspiciousIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
