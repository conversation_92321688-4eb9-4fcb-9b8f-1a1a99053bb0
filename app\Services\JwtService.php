<?php

namespace App\Services;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use App\Models\User;
use App\Models\UserSession;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class JwtService
{
    private string $secretKey;
    private string $algorithm;
    private int $accessTokenTtl;
    private int $refreshTokenTtl;

    public function __construct()
    {
        $this->secretKey = config('app.jwt_secret', env('JWT_SECRET', 'your-secret-key'));
        $this->algorithm = config('app.jwt_algorithm', 'HS256');
        $this->accessTokenTtl = config('app.jwt_access_ttl', 3600); // 1 hour
        $this->refreshTokenTtl = config('app.jwt_refresh_ttl', 604800); // 7 days
    }

    /**
     * Generate JWT tokens for user
     */
    public function generateTokens(User $user, array $deviceInfo = []): array
    {
        $now = time();
        $sessionId = uniqid('session_', true);

        // Create user session
        $session = UserSession::createSession(
            $user->id,
            $sessionId,
            $deviceInfo
        );

        // Access token payload
        $accessPayload = [
            'iss' => config('app.url'),
            'aud' => config('app.url'),
            'iat' => $now,
            'exp' => $now + $this->accessTokenTtl,
            'sub' => $user->id,
            'session_id' => $sessionId,
            'type' => 'access',
            'permissions' => $this->getUserPermissions($user),
            'device_id' => $deviceInfo['device_id'] ?? null,
        ];

        // Refresh token payload
        $refreshPayload = [
            'iss' => config('app.url'),
            'aud' => config('app.url'),
            'iat' => $now,
            'exp' => $now + $this->refreshTokenTtl,
            'sub' => $user->id,
            'session_id' => $sessionId,
            'type' => 'refresh',
        ];

        $accessToken = JWT::encode($accessPayload, $this->secretKey, $this->algorithm);
        $refreshToken = JWT::encode($refreshPayload, $this->secretKey, $this->algorithm);

        // Store tokens in cache for quick validation
        $this->cacheToken($accessToken, $accessPayload);
        $this->cacheToken($refreshToken, $refreshPayload);

        // Log token generation
        AuditLogService::logUserAction(
            'jwt_tokens_generated',
            'JWT tokens generated for user',
            [
                'session_id' => $sessionId,
                'device_info' => $deviceInfo,
                'expires_at' => date('Y-m-d H:i:s', $now + $this->accessTokenTtl),
            ],
            $user->id
        );

        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'token_type' => 'Bearer',
            'expires_in' => $this->accessTokenTtl,
            'session_id' => $sessionId,
        ];
    }

    /**
     * Validate JWT token
     */
    public function validateToken(string $token): array
    {
        try {
            // Check cache first
            $cachedPayload = $this->getCachedToken($token);
            if ($cachedPayload) {
                return [
                    'valid' => true,
                    'payload' => $cachedPayload,
                    'user' => User::find($cachedPayload['sub']),
                ];
            }

            // Decode token
            $payload = JWT::decode($token, new Key($this->secretKey, $this->algorithm));
            $payloadArray = (array) $payload;

            // Validate token type
            if (!isset($payloadArray['type']) || !in_array($payloadArray['type'], ['access', 'refresh'])) {
                throw new \Exception('Invalid token type');
            }

            // Check if token is blacklisted
            if ($this->isTokenBlacklisted($token)) {
                throw new \Exception('Token is blacklisted');
            }

            // Validate session
            $session = UserSession::where('session_id', $payloadArray['session_id'])
                ->where('user_id', $payloadArray['sub'])
                ->active()
                ->first();

            if (!$session) {
                throw new \Exception('Session not found or expired');
            }

            // Update session activity
            $session->updateActivity();

            // Get user
            $user = User::find($payloadArray['sub']);
            if (!$user || $user->status !== 'active') {
                throw new \Exception('User not found or inactive');
            }

            // Cache valid token
            $this->cacheToken($token, $payloadArray);

            return [
                'valid' => true,
                'payload' => $payloadArray,
                'user' => $user,
                'session' => $session,
            ];

        } catch (ExpiredException $e) {
            return [
                'valid' => false,
                'error' => 'Token expired',
                'code' => 'TOKEN_EXPIRED',
            ];
        } catch (SignatureInvalidException $e) {
            return [
                'valid' => false,
                'error' => 'Invalid token signature',
                'code' => 'INVALID_SIGNATURE',
            ];
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => $e->getMessage(),
                'code' => 'INVALID_TOKEN',
            ];
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshToken(string $refreshToken): array
    {
        $validation = $this->validateToken($refreshToken);

        if (!$validation['valid']) {
            return [
                'success' => false,
                'error' => $validation['error'],
                'code' => $validation['code'],
            ];
        }

        $payload = $validation['payload'];

        // Ensure it's a refresh token
        if ($payload['type'] !== 'refresh') {
            return [
                'success' => false,
                'error' => 'Invalid token type for refresh',
                'code' => 'INVALID_TOKEN_TYPE',
            ];
        }

        $user = $validation['user'];
        $session = $validation['session'];

        // Generate new access token
        $now = time();
        $newAccessPayload = [
            'iss' => config('app.url'),
            'aud' => config('app.url'),
            'iat' => $now,
            'exp' => $now + $this->accessTokenTtl,
            'sub' => $user->id,
            'session_id' => $payload['session_id'],
            'type' => 'access',
            'permissions' => $this->getUserPermissions($user),
            'device_id' => $session->device_id,
        ];

        $newAccessToken = JWT::encode($newAccessPayload, $this->secretKey, $this->algorithm);

        // Cache new token
        $this->cacheToken($newAccessToken, $newAccessPayload);

        // Log token refresh
        AuditLogService::logUserAction(
            'jwt_token_refreshed',
            'JWT access token refreshed',
            [
                'session_id' => $payload['session_id'],
                'old_exp' => $payload['exp'],
                'new_exp' => $newAccessPayload['exp'],
            ],
            $user->id
        );

        return [
            'success' => true,
            'access_token' => $newAccessToken,
            'token_type' => 'Bearer',
            'expires_in' => $this->accessTokenTtl,
        ];
    }

    /**
     * Revoke token (add to blacklist)
     */
    public function revokeToken(string $token): bool
    {
        try {
            $payload = JWT::decode($token, new Key($this->secretKey, $this->algorithm));
            $payloadArray = (array) $payload;

            // Add to blacklist
            $this->blacklistToken($token, $payloadArray['exp']);

            // Remove from cache
            $this->removeCachedToken($token);

            // If it's a refresh token, also invalidate the session
            if ($payloadArray['type'] === 'refresh') {
                UserSession::where('session_id', $payloadArray['session_id'])
                    ->update(['is_active' => false, 'expires_at' => now()]);
            }

            // Log token revocation
            AuditLogService::logUserAction(
                'jwt_token_revoked',
                'JWT token revoked',
                [
                    'session_id' => $payloadArray['session_id'] ?? null,
                    'token_type' => $payloadArray['type'] ?? 'unknown',
                ],
                $payloadArray['sub'] ?? null
            );

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to revoke JWT token', [
                'error' => $e->getMessage(),
                'token' => substr($token, 0, 20) . '...',
            ]);
            return false;
        }
    }

    /**
     * Revoke all user tokens
     */
    public function revokeAllUserTokens(int $userId): int
    {
        $revokedCount = 0;

        // Get all active sessions for user
        $sessions = UserSession::where('user_id', $userId)
            ->active()
            ->get();

        foreach ($sessions as $session) {
            // Terminate session
            $session->terminate();
            $revokedCount++;
        }

        // Log mass token revocation
        AuditLogService::logUserAction(
            'jwt_all_tokens_revoked',
            'All JWT tokens revoked for user',
            [
                'revoked_sessions' => $revokedCount,
            ],
            $userId
        );

        return $revokedCount;
    }

    /**
     * Get user permissions for JWT payload
     */
    private function getUserPermissions(User $user): array
    {
        $permissions = ['user'];

        // Add role-based permissions
        if ($user->role === 'admin') {
            $permissions[] = 'admin';
            $permissions[] = 'manage_users';
            $permissions[] = 'view_reports';
        }

        if ($user->role === 'manager') {
            $permissions[] = 'manager';
            $permissions[] = 'view_reports';
        }

        // Add KYC-based permissions
        if ($user->hasCompletedKyc()) {
            $permissions[] = 'verified_user';
            $permissions[] = 'send_money';
            $permissions[] = 'receive_money';
        }

        return $permissions;
    }

    /**
     * Cache token for quick validation
     */
    private function cacheToken(string $token, array $payload): void
    {
        $key = 'jwt_token:' . hash('sha256', $token);
        $ttl = $payload['exp'] - time();

        if ($ttl > 0) {
            Cache::put($key, $payload, $ttl);
        }
    }

    /**
     * Get cached token payload
     */
    private function getCachedToken(string $token): ?array
    {
        $key = 'jwt_token:' . hash('sha256', $token);
        return Cache::get($key);
    }

    /**
     * Remove token from cache
     */
    private function removeCachedToken(string $token): void
    {
        $key = 'jwt_token:' . hash('sha256', $token);
        Cache::forget($key);
    }

    /**
     * Add token to blacklist
     */
    private function blacklistToken(string $token, int $expiry): void
    {
        $key = 'jwt_blacklist:' . hash('sha256', $token);
        $ttl = $expiry - time();

        if ($ttl > 0) {
            Cache::put($key, true, $ttl);
        }
    }

    /**
     * Check if token is blacklisted
     */
    private function isTokenBlacklisted(string $token): bool
    {
        $key = 'jwt_blacklist:' . hash('sha256', $token);
        return Cache::has($key);
    }

    /**
     * Clean expired tokens from cache
     */
    public static function cleanExpiredTokens(): int
    {
        // This would typically be handled by cache TTL
        // But we can implement additional cleanup if needed
        return 0;
    }

    /**
     * Get JWT statistics
     */
    public static function getStatistics(): array
    {
        $activeTokens = Cache::get('jwt_stats:active_tokens', 0);
        $blacklistedTokens = Cache::get('jwt_stats:blacklisted_tokens', 0);

        return [
            'active_tokens' => $activeTokens,
            'blacklisted_tokens' => $blacklistedTokens,
            'active_sessions' => UserSession::active()->count(),
            'total_sessions' => UserSession::count(),
        ];
    }
}
