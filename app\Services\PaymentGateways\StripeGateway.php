<?php

namespace App\Services\PaymentGateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\Transaction;
use App\Models\PaymentTransaction;
use App\Exceptions\PaymentException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class StripeGateway implements PaymentGatewayInterface
{
    protected string $apiKey;
    protected string $webhookSecret;
    protected string $baseUrl = 'https://api.stripe.com/v1';

    public function __construct()
    {
        $this->apiKey = config('services.stripe.secret_key');
        $this->webhookSecret = config('services.stripe.webhook_secret');
        
        if (!$this->apiKey) {
            throw new PaymentException(
                'Stripe API key not configured',
                'STRIPE_CONFIG_MISSING'
            );
        }
    }

    /**
     * Process payment through Stripe
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        try {
            // Create payment intent
            $paymentIntent = $this->createPaymentIntent($transaction, $paymentData);
            
            // Confirm payment intent
            $confirmedPayment = $this->confirmPaymentIntent($paymentIntent['id'], $paymentData);
            
            if ($confirmedPayment['status'] === 'succeeded') {
                return [
                    'status' => 'success',
                    'reference' => $confirmedPayment['id'],
                    'gateway_response' => $confirmedPayment,
                    'amount' => $confirmedPayment['amount'] / 100, // Stripe uses cents
                    'currency' => strtoupper($confirmedPayment['currency']),
                    'processed_at' => now(),
                ];
            } else {
                return [
                    'status' => 'failed',
                    'error_code' => $confirmedPayment['last_payment_error']['code'] ?? 'unknown_error',
                    'error_message' => $confirmedPayment['last_payment_error']['message'] ?? 'Payment failed',
                    'gateway_response' => $confirmedPayment,
                ];
            }
        } catch (\Exception $e) {
            Log::error('Stripe payment processing failed', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'failed',
                'error_code' => 'processing_error',
                'error_message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Refund payment through Stripe
     */
    public function refundPayment(PaymentTransaction $paymentTransaction, float $amount): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->post($this->baseUrl . '/refunds', [
                'payment_intent' => $paymentTransaction->gateway_reference,
                'amount' => intval($amount * 100), // Convert to cents
                'reason' => 'requested_by_customer',
            ]);

            if ($response->successful()) {
                $refund = $response->json();
                
                return [
                    'status' => 'success',
                    'reference' => $refund['id'],
                    'amount' => $refund['amount'] / 100,
                    'gateway_response' => $refund,
                ];
            } else {
                $error = $response->json();
                return [
                    'status' => 'failed',
                    'error_code' => $error['error']['code'] ?? 'refund_failed',
                    'error_message' => $error['error']['message'] ?? 'Refund failed',
                ];
            }
        } catch (\Exception $e) {
            Log::error('Stripe refund failed', [
                'payment_transaction_id' => $paymentTransaction->id,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'failed',
                'error_code' => 'refund_error',
                'error_message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(string $paymentReference): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/payment_intents/' . $paymentReference);

            if ($response->successful()) {
                $paymentIntent = $response->json();
                
                return [
                    'status' => $this->mapStripeStatus($paymentIntent['status']),
                    'amount' => $paymentIntent['amount'] / 100,
                    'currency' => strtoupper($paymentIntent['currency']),
                    'gateway_response' => $paymentIntent,
                ];
            } else {
                return [
                    'status' => 'unknown',
                    'error' => 'Payment not found',
                ];
            }
        } catch (\Exception $e) {
            Log::error('Stripe payment verification failed', [
                'payment_reference' => $paymentReference,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(array $payload): bool
    {
        try {
            $signature = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';
            $payloadBody = json_encode($payload);
            
            // Verify signature using Stripe's method
            $expectedSignature = hash_hmac('sha256', $payloadBody, $this->webhookSecret);
            
            return hash_equals($expectedSignature, $signature);
        } catch (\Exception $e) {
            Log::error('Stripe webhook signature verification failed', [
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Process webhook notification
     */
    public function processWebhook(array $payload): array
    {
        try {
            $event = $payload;
            
            switch ($event['type']) {
                case 'payment_intent.succeeded':
                    return $this->handlePaymentSucceeded($event['data']['object']);
                    
                case 'payment_intent.payment_failed':
                    return $this->handlePaymentFailed($event['data']['object']);
                    
                case 'charge.dispute.created':
                    return $this->handleChargeDispute($event['data']['object']);
                    
                default:
                    return [
                        'status' => 'ignored',
                        'message' => 'Event type not handled: ' . $event['type'],
                    ];
            }
        } catch (\Exception $e) {
            Log::error('Stripe webhook processing failed', [
                'payload' => $payload,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get gateway configuration
     */
    public function getConfiguration(): array
    {
        return [
            'name' => 'Stripe',
            'supports_refunds' => true,
            'supports_partial_refunds' => true,
            'supports_webhooks' => true,
            'supported_currencies' => ['USD', 'EUR', 'GBP', 'SAR', 'AED'],
            'supported_countries' => ['US', 'GB', 'CA', 'AU', 'SA', 'AE'],
            'fee_structure' => [
                'percentage' => 2.9,
                'fixed' => 0.30,
            ],
        ];
    }

    /**
     * Test gateway connection
     */
    public function testConnection(): bool
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/account');

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Stripe connection test failed', [
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Create payment intent
     */
    protected function createPaymentIntent(Transaction $transaction, array $paymentData): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->post($this->baseUrl . '/payment_intents', [
            'amount' => intval($transaction->total_amount * 100), // Convert to cents
            'currency' => strtolower($transaction->currency_from),
            'payment_method_types' => ['card'],
            'description' => "Money transfer: {$transaction->transaction_id}",
            'metadata' => [
                'transaction_id' => $transaction->transaction_id,
                'user_id' => $transaction->user_id,
            ],
        ]);

        if (!$response->successful()) {
            $error = $response->json();
            throw new PaymentException(
                $error['error']['message'] ?? 'Failed to create payment intent',
                'STRIPE_PAYMENT_INTENT_FAILED'
            );
        }

        return $response->json();
    }

    /**
     * Confirm payment intent
     */
    protected function confirmPaymentIntent(string $paymentIntentId, array $paymentData): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->post($this->baseUrl . "/payment_intents/{$paymentIntentId}/confirm", [
            'payment_method_data' => [
                'type' => 'card',
                'card' => [
                    'number' => $paymentData['card_number'],
                    'exp_month' => $paymentData['expiry_month'],
                    'exp_year' => $paymentData['expiry_year'],
                    'cvc' => $paymentData['cvv'],
                ],
                'billing_details' => [
                    'name' => $paymentData['cardholder_name'],
                ],
            ],
        ]);

        if (!$response->successful()) {
            $error = $response->json();
            throw new PaymentException(
                $error['error']['message'] ?? 'Failed to confirm payment',
                'STRIPE_PAYMENT_CONFIRMATION_FAILED'
            );
        }

        return $response->json();
    }

    /**
     * Map Stripe status to our internal status
     */
    protected function mapStripeStatus(string $stripeStatus): string
    {
        return match($stripeStatus) {
            'succeeded' => 'completed',
            'processing' => 'processing',
            'requires_payment_method', 'requires_confirmation' => 'pending',
            'requires_action' => 'requires_action',
            'canceled' => 'cancelled',
            default => 'failed',
        };
    }

    /**
     * Handle payment succeeded webhook
     */
    protected function handlePaymentSucceeded(array $paymentIntent): array
    {
        return [
            'status' => 'completed',
            'transaction_reference' => $paymentIntent['id'],
            'amount' => $paymentIntent['amount'] / 100,
            'currency' => strtoupper($paymentIntent['currency']),
        ];
    }

    /**
     * Handle payment failed webhook
     */
    protected function handlePaymentFailed(array $paymentIntent): array
    {
        return [
            'status' => 'failed',
            'transaction_reference' => $paymentIntent['id'],
            'error_message' => $paymentIntent['last_payment_error']['message'] ?? 'Payment failed',
        ];
    }

    /**
     * Handle charge dispute webhook
     */
    protected function handleChargeDispute(array $dispute): array
    {
        return [
            'status' => 'disputed',
            'transaction_reference' => $dispute['charge'],
            'dispute_reason' => $dispute['reason'],
            'dispute_amount' => $dispute['amount'] / 100,
        ];
    }
}
