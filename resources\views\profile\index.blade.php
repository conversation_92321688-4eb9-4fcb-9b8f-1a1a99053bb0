@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>الملف الشخصي</h2>
                    <p class="text-muted">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
                </div>
                <div>
                    <a href="/dashboard" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-1"></i>العودة للوحة التحكم
                    </a>
                    <form method="POST" action="/logout" class="d-inline ms-2">
                        @csrf
                        <button type="submit" class="btn btn-outline-danger" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                            <i class="bi bi-box-arrow-right me-1"></i>تسجيل الخروج
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="avatar-lg mx-auto mb-3">
                                <div class="avatar-initial bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                                    {{ Auth::check() ? strtoupper(substr(Auth::user()->first_name, 0, 1) . substr(Auth::user()->last_name, 0, 1)) : 'U' }}
                                </div>
                            </div>
                            <h5>{{ Auth::check() ? Auth::user()->first_name . ' ' . Auth::user()->last_name : 'المستخدم' }}</h5>
                            <p class="text-muted">{{ Auth::check() ? Auth::user()->email : '<EMAIL>' }}</p>
                            <span class="badge bg-success">حساب مفعل</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">المعلومات الشخصية</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                هذه الصفحة قيد التطوير. سيتم إضافة نموذج تحديث المعلومات قريباً.
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الأول</label>
                                    <input type="text" class="form-control" value="{{ Auth::check() ? Auth::user()->first_name : '' }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الأخير</label>
                                    <input type="text" class="form-control" value="{{ Auth::check() ? Auth::user()->last_name : '' }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" value="{{ Auth::check() ? Auth::user()->email : '' }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" value="{{ Auth::check() && Auth::user()->phone ? Auth::user()->phone : 'غير محدد' }}" readonly>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button class="btn btn-primary me-2" disabled>
                                    <i class="bi bi-pencil me-1"></i>تحديث المعلومات
                                </button>
                                <button class="btn btn-outline-secondary" disabled>
                                    <i class="bi bi-key me-1"></i>تغيير كلمة المرور
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
