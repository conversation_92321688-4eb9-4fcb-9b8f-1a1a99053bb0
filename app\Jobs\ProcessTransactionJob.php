<?php

namespace App\Jobs;

use App\Models\Transaction;
use App\Services\TransactionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessTransactionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Transaction $transaction;
    protected array $options;

    /**
     * Create a new job instance.
     */
    public function __construct(Transaction $transaction, array $options = [])
    {
        $this->transaction = $transaction;
        $this->options = $options;
        
        // Set queue priority based on transaction amount
        if ($transaction->amount > 10000) {
            $this->onQueue('high-priority');
        } elseif ($transaction->amount > 1000) {
            $this->onQueue('medium-priority');
        } else {
            $this->onQueue('low-priority');
        }
    }

    /**
     * Execute the job.
     */
    public function handle(TransactionService $transactionService): void
    {
        try {
            Log::info('Processing transaction job started', [
                'transaction_id' => $this->transaction->id,
                'transaction_number' => $this->transaction->transaction_number,
            ]);

            // Check if transaction is still in processable state
            if (!in_array($this->transaction->status, ['pending', 'processing'])) {
                Log::warning('Transaction not in processable state', [
                    'transaction_id' => $this->transaction->id,
                    'status' => $this->transaction->status,
                ]);
                return;
            }

            // Update status to processing
            $this->transaction->update(['status' => 'processing']);

            // Simulate processing time based on payment method
            $this->simulateProcessingTime();

            // Process the transaction
            $result = $transactionService->completeTransaction($this->transaction);

            if ($result) {
                Log::info('Transaction processed successfully', [
                    'transaction_id' => $this->transaction->id,
                    'transaction_number' => $this->transaction->transaction_number,
                ]);

                // Dispatch follow-up jobs
                $this->dispatchFollowUpJobs();
            } else {
                Log::error('Transaction processing failed', [
                    'transaction_id' => $this->transaction->id,
                    'transaction_number' => $this->transaction->transaction_number,
                ]);

                $this->transaction->update(['status' => 'failed']);
            }

        } catch (\Exception $e) {
            Log::error('Transaction processing job failed', [
                'transaction_id' => $this->transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->transaction->update(['status' => 'failed']);
            throw $e;
        }
    }

    /**
     * Simulate processing time based on payment method and amount.
     */
    private function simulateProcessingTime(): void
    {
        $paymentMethod = $this->transaction->payment_method;
        $amount = $this->transaction->amount;

        $delay = 0;

        switch ($paymentMethod) {
            case 'crypto':
                $delay = rand(30, 120); // 30 seconds to 2 minutes
                break;
            case 'bank_transfer':
                $delay = rand(60, 300); // 1 to 5 minutes
                break;
            case 'card':
                $delay = rand(10, 60); // 10 seconds to 1 minute
                break;
            case 'cash':
                $delay = rand(5, 30); // 5 to 30 seconds
                break;
            default:
                $delay = rand(30, 90);
        }

        // Add extra delay for large amounts
        if ($amount > 10000) {
            $delay += rand(60, 180);
        }

        // In production, this would be actual processing time
        // For demo purposes, we'll just log the delay
        Log::info('Transaction processing delay', [
            'transaction_id' => $this->transaction->id,
            'delay_seconds' => $delay,
            'payment_method' => $paymentMethod,
        ]);
    }

    /**
     * Dispatch follow-up jobs after successful processing.
     */
    private function dispatchFollowUpJobs(): void
    {
        // Send email notification
        SendTransactionEmailJob::dispatch($this->transaction, 'completed');

        // Send SMS notification if phone number available
        if ($this->transaction->receiver_phone) {
            SendTransactionSMSJob::dispatch($this->transaction, 'completed');
        }

        // Update exchange rates if crypto transaction
        if ($this->transaction->currency->is_crypto || $this->transaction->targetCurrency->is_crypto) {
            UpdateCryptoRatesJob::dispatch();
        }

        // Generate receipt
        GenerateTransactionReceiptJob::dispatch($this->transaction);

        // Update user statistics
        UpdateUserStatisticsJob::dispatch($this->transaction->sender_id);
        
        if ($this->transaction->receiver_id) {
            UpdateUserStatisticsJob::dispatch($this->transaction->receiver_id);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Transaction processing job failed permanently', [
            'transaction_id' => $this->transaction->id,
            'transaction_number' => $this->transaction->transaction_number,
            'error' => $exception->getMessage(),
        ]);

        // Update transaction status
        $this->transaction->update([
            'status' => 'failed',
            'notes' => ($this->transaction->notes ? $this->transaction->notes . ' | ' : '') . 
                      'Processing failed: ' . $exception->getMessage(),
        ]);

        // Send failure notification
        SendTransactionEmailJob::dispatch($this->transaction, 'failed');
    }

    /**
     * Get the number of times the job may be attempted.
     */
    public function tries(): int
    {
        return 3;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 60, 120]; // 30 seconds, 1 minute, 2 minutes
    }
}
