<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'agent' => \App\Http\Middleware\AgentMiddleware::class,
            'kyc' => \App\Http\Middleware\KYCMiddleware::class,
            'compliance' => \App\Http\Middleware\ComplianceMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Handle custom exceptions
        $exceptions->render(function (\App\Exceptions\BaseException $e, $request) {
            if ($request->expectsJson()) {
                return $e->render($request);
            }
            return null;
        });

        // Handle authentication exceptions
        $exceptions->render(function (\Illuminate\Auth\AuthenticationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بالوصول',
                    'error_code' => 'UNAUTHENTICATED',
                    'timestamp' => now()->toISOString(),
                ], 401);
            }
            return null;
        });

        // Handle authorization exceptions
        $exceptions->render(function (\Illuminate\Auth\Access\AuthorizationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'ليس لديك صلاحية للقيام بهذا الإجراء',
                    'error_code' => 'UNAUTHORIZED',
                    'timestamp' => now()->toISOString(),
                ], 403);
            }
            return null;
        });

        // Handle validation exceptions
        $exceptions->render(function (\Illuminate\Validation\ValidationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'البيانات المدخلة غير صحيحة',
                    'errors' => $e->errors(),
                    'error_code' => 'VALIDATION_ERROR',
                    'timestamp' => now()->toISOString(),
                ], 422);
            }
            return null;
        });

        // Handle model not found exceptions
        $exceptions->render(function (\Illuminate\Database\Eloquent\ModelNotFoundException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'العنصر المطلوب غير موجود',
                    'error_code' => 'NOT_FOUND',
                    'timestamp' => now()->toISOString(),
                ], 404);
            }
            return null;
        });

        // Handle method not allowed exceptions
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'الطريقة المستخدمة غير مسموحة',
                    'error_code' => 'METHOD_NOT_ALLOWED',
                    'allowed_methods' => $e->getHeaders()['Allow'] ?? [],
                    'timestamp' => now()->toISOString(),
                ], 405);
            }
            return null;
        });

        // Handle rate limiting exceptions
        $exceptions->render(function (\Illuminate\Http\Exceptions\ThrottleRequestsException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'تم تجاوز الحد المسموح من الطلبات',
                    'error_code' => 'TOO_MANY_REQUESTS',
                    'retry_after' => $e->getHeaders()['Retry-After'] ?? null,
                    'timestamp' => now()->toISOString(),
                ], 429);
            }
            return null;
        });

        // Handle general HTTP exceptions
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\HttpException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage() ?: 'حدث خطأ في الخادم',
                    'error_code' => 'HTTP_ERROR',
                    'timestamp' => now()->toISOString(),
                ], $e->getStatusCode());
            }
            return null;
        });

        // Handle database exceptions
        $exceptions->render(function (\Illuminate\Database\QueryException $e, $request) {
            if ($request->expectsJson()) {
                $message = app()->environment('production')
                    ? 'حدث خطأ في قاعدة البيانات'
                    : $e->getMessage();

                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'error_code' => 'DATABASE_ERROR',
                    'timestamp' => now()->toISOString(),
                ], 500);
            }
            return null;
        });

        // Log custom exceptions
        $exceptions->reportable(function (\App\Exceptions\BaseException $e) {
            if ($e->shouldLog()) {
                \Log::error('Custom Exception: ' . $e->getMessage(), [
                    'exception' => get_class($e),
                    'error_code' => $e->getErrorCode(),
                    'context' => $e->getContext(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        });
    })->create();
