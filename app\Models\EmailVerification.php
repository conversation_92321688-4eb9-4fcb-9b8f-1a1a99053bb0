<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class EmailVerification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'email',
        'token',
        'type',
        'is_verified',
        'verified_at',
        'expires_at',
        'attempts',
        'max_attempts',
        'ip_address',
        'user_agent',
        'metadata',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the email verification
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get pending verifications
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('is_verified', false)
            ->where('expires_at', '>', now());
    }

    /**
     * Scope to get verified verifications
     */
    public function scopeVerified(Builder $query): Builder
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get expired verifications
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to filter by type
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Create a new email verification
     */
    public static function createVerification(
        int $userId,
        string $email,
        string $type = 'registration',
        int $expiresInHours = 24
    ): self {
        // Invalidate existing verifications for this user and type
        self::where('user_id', $userId)
            ->where('type', $type)
            ->where('is_verified', false)
            ->delete();

        return self::create([
            'user_id' => $userId,
            'email' => $email,
            'token' => Str::random(64),
            'type' => $type,
            'is_verified' => false,
            'expires_at' => now()->addHours($expiresInHours),
            'attempts' => 0,
            'max_attempts' => 3,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => [],
        ]);
    }

    /**
     * Verify the email with token
     */
    public static function verifyToken(string $token): array
    {
        $verification = self::where('token', $token)
            ->pending()
            ->first();

        if (!$verification) {
            return [
                'success' => false,
                'message' => 'Invalid or expired verification token',
                'code' => 'INVALID_TOKEN',
            ];
        }

        if ($verification->isExpired()) {
            return [
                'success' => false,
                'message' => 'Verification token has expired',
                'code' => 'EXPIRED_TOKEN',
            ];
        }

        if ($verification->hasExceededMaxAttempts()) {
            return [
                'success' => false,
                'message' => 'Maximum verification attempts exceeded',
                'code' => 'MAX_ATTEMPTS_EXCEEDED',
            ];
        }

        // Mark as verified
        $verification->markAsVerified();

        // Update user email if it's an email change verification
        if ($verification->type === 'change_email') {
            $verification->user->update(['email' => $verification->email]);
        }

        return [
            'success' => true,
            'message' => 'Email verified successfully',
            'verification' => $verification,
        ];
    }

    /**
     * Mark as verified
     */
    public function markAsVerified(): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        // Mark user email as verified if it's registration verification
        if ($this->type === 'registration') {
            $this->user->update(['email_verified_at' => now()]);
        }
    }

    /**
     * Record verification attempt
     */
    public function recordAttempt(): void
    {
        $this->increment('attempts');
    }

    /**
     * Check if expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if max attempts exceeded
     */
    public function hasExceededMaxAttempts(): bool
    {
        return $this->attempts >= $this->max_attempts;
    }

    /**
     * Resend verification
     */
    public function resend(int $expiresInHours = 24): self
    {
        if ($this->is_verified) {
            throw new \Exception('Cannot resend verification for already verified email');
        }

        // Generate new token and extend expiry
        $this->update([
            'token' => Str::random(64),
            'expires_at' => now()->addHours($expiresInHours),
            'attempts' => 0,
        ]);

        return $this;
    }

    /**
     * Get verification URL
     */
    public function getVerificationUrl(): string
    {
        return url("/email/verify/{$this->token}");
    }

    /**
     * Clean expired verifications
     */
    public static function cleanExpired(): int
    {
        return self::expired()->delete();
    }

    /**
     * Get verification statistics
     */
    public static function getStatistics(): array
    {
        return [
            'total_pending' => self::pending()->count(),
            'total_verified' => self::verified()->count(),
            'total_expired' => self::expired()->count(),
            'by_type' => self::selectRaw('type, COUNT(*) as count, AVG(attempts) as avg_attempts')
                ->groupBy('type')
                ->get()
                ->keyBy('type')
                ->map(function ($item) {
                    return [
                        'count' => $item->count,
                        'avg_attempts' => round($item->avg_attempts, 2),
                    ];
                })
                ->toArray(),
            'verification_rate' => self::getVerificationRate(),
        ];
    }

    /**
     * Get verification rate
     */
    public static function getVerificationRate(int $days = 30): float
    {
        $total = self::where('created_at', '>=', now()->subDays($days))->count();
        $verified = self::where('created_at', '>=', now()->subDays($days))
            ->where('is_verified', true)
            ->count();

        return $total > 0 ? round(($verified / $total) * 100, 2) : 0;
    }

    /**
     * Send verification email
     */
    public function sendVerificationEmail(): void
    {
        // This would typically use Laravel's Mail system
        // For now, we'll log it
        \Log::info('Email verification sent', [
            'user_id' => $this->user_id,
            'email' => $this->email,
            'type' => $this->type,
            'token' => $this->token,
            'verification_url' => $this->getVerificationUrl(),
        ]);

        // In a real implementation:
        // Mail::to($this->email)->send(new EmailVerificationMail($this));
    }

    /**
     * Check if user has pending verification
     */
    public static function hasPendingVerification(int $userId, string $type = 'registration'): bool
    {
        return self::where('user_id', $userId)
            ->where('type', $type)
            ->pending()
            ->exists();
    }

    /**
     * Get user's verification history
     */
    public static function getUserHistory(int $userId): array
    {
        $verifications = self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->get();

        return [
            'total' => $verifications->count(),
            'verified' => $verifications->where('is_verified', true)->count(),
            'pending' => $verifications->where('is_verified', false)->where('expires_at', '>', now())->count(),
            'expired' => $verifications->where('expires_at', '<=', now())->count(),
            'latest' => $verifications->first(),
            'verifications' => $verifications->toArray(),
        ];
    }
}
