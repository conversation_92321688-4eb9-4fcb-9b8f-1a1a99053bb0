<?php

namespace App\Services;

use App\Models\Report;
use App\Models\Transaction;
use App\Models\User;
use App\Models\FraudDetection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ReportService
{
    /**
     * Generate financial report.
     */
    public function generateFinancialReport(array $parameters): Report
    {
        $report = Report::createReport([
            'name' => $parameters['name'] ?? 'Financial Report',
            'type' => 'financial',
            'description' => 'Comprehensive financial report',
            'parameters' => $parameters,
            'format' => $parameters['format'] ?? 'pdf',
        ]);

        try {
            $data = $this->collectFinancialData($parameters);
            $filePath = $this->generateReportFile($report, $data);
            
            $report->markAsCompleted(
                $filePath,
                $this->generateFileName($report),
                Storage::size($filePath)
            );

        } catch (\Exception $e) {
            $report->markAsFailed($e->getMessage());
            Log::error('Failed to generate financial report', [
                'report_id' => $report->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $report;
    }

    /**
     * Generate transaction report.
     */
    public function generateTransactionReport(array $parameters): Report
    {
        $report = Report::createReport([
            'name' => $parameters['name'] ?? 'Transaction Report',
            'type' => 'transactions',
            'description' => 'Detailed transaction report',
            'parameters' => $parameters,
            'format' => $parameters['format'] ?? 'excel',
        ]);

        try {
            $data = $this->collectTransactionData($parameters);
            $filePath = $this->generateReportFile($report, $data);
            
            $report->markAsCompleted(
                $filePath,
                $this->generateFileName($report),
                Storage::size($filePath)
            );

        } catch (\Exception $e) {
            $report->markAsFailed($e->getMessage());
            Log::error('Failed to generate transaction report', [
                'report_id' => $report->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $report;
    }

    /**
     * Generate fraud detection report.
     */
    public function generateFraudReport(array $parameters): Report
    {
        $report = Report::createReport([
            'name' => $parameters['name'] ?? 'Fraud Detection Report',
            'type' => 'fraud',
            'description' => 'Fraud detection and security report',
            'parameters' => $parameters,
            'format' => $parameters['format'] ?? 'pdf',
        ]);

        try {
            $data = $this->collectFraudData($parameters);
            $filePath = $this->generateReportFile($report, $data);
            
            $report->markAsCompleted(
                $filePath,
                $this->generateFileName($report),
                Storage::size($filePath)
            );

        } catch (\Exception $e) {
            $report->markAsFailed($e->getMessage());
            Log::error('Failed to generate fraud report', [
                'report_id' => $report->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $report;
    }

    /**
     * Generate user activity report.
     */
    public function generateUserActivityReport(array $parameters): Report
    {
        $report = Report::createReport([
            'name' => $parameters['name'] ?? 'User Activity Report',
            'type' => 'user_activity',
            'description' => 'User activity and engagement report',
            'parameters' => $parameters,
            'format' => $parameters['format'] ?? 'excel',
        ]);

        try {
            $data = $this->collectUserActivityData($parameters);
            $filePath = $this->generateReportFile($report, $data);
            
            $report->markAsCompleted(
                $filePath,
                $this->generateFileName($report),
                Storage::size($filePath)
            );

        } catch (\Exception $e) {
            $report->markAsFailed($e->getMessage());
            Log::error('Failed to generate user activity report', [
                'report_id' => $report->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $report;
    }

    /**
     * Collect financial data.
     */
    protected function collectFinancialData(array $parameters): array
    {
        $startDate = Carbon::parse($parameters['start_date'] ?? now()->subMonth());
        $endDate = Carbon::parse($parameters['end_date'] ?? now());

        $transactions = Transaction::whereBetween('created_at', [$startDate, $endDate])
                                 ->with(['currency', 'sender', 'receiver'])
                                 ->get();

        return [
            'period' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
            'summary' => [
                'total_transactions' => $transactions->count(),
                'total_volume' => $transactions->sum('amount'),
                'completed_transactions' => $transactions->where('status', 'completed')->count(),
                'failed_transactions' => $transactions->where('status', 'failed')->count(),
                'pending_transactions' => $transactions->where('status', 'pending')->count(),
            ],
            'by_currency' => $transactions->groupBy('currency.code')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'volume' => $group->sum('amount'),
                ];
            }),
            'by_status' => $transactions->groupBy('status')->map->count(),
            'daily_volume' => $transactions->groupBy(function ($transaction) {
                return $transaction->created_at->toDateString();
            })->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'volume' => $group->sum('amount'),
                ];
            }),
            'transactions' => $transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency->code,
                    'status' => $transaction->status,
                    'created_at' => $transaction->created_at->toISOString(),
                    'sender' => $transaction->sender->first_name . ' ' . $transaction->sender->last_name,
                    'receiver_name' => $transaction->receiver_name,
                ];
            }),
        ];
    }

    /**
     * Collect transaction data.
     */
    protected function collectTransactionData(array $parameters): array
    {
        $startDate = Carbon::parse($parameters['start_date'] ?? now()->subMonth());
        $endDate = Carbon::parse($parameters['end_date'] ?? now());

        $query = Transaction::whereBetween('created_at', [$startDate, $endDate]);

        if (isset($parameters['status'])) {
            $query->where('status', $parameters['status']);
        }

        if (isset($parameters['currency_id'])) {
            $query->where('currency_id', $parameters['currency_id']);
        }

        $transactions = $query->with(['currency', 'sender', 'receiver', 'branch'])
                             ->orderBy('created_at', 'desc')
                             ->get();

        return [
            'transactions' => $transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'reference_number' => $transaction->reference_number,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency->code,
                    'status' => $transaction->status,
                    'type' => $transaction->type,
                    'sender_name' => $transaction->sender->first_name . ' ' . $transaction->sender->last_name,
                    'sender_email' => $transaction->sender->email,
                    'receiver_name' => $transaction->receiver_name,
                    'receiver_phone' => $transaction->receiver_phone,
                    'purpose' => $transaction->purpose,
                    'commission' => $transaction->commission,
                    'created_at' => $transaction->created_at->toISOString(),
                    'completed_at' => $transaction->completed_at?->toISOString(),
                ];
            }),
        ];
    }

    /**
     * Collect fraud data.
     */
    protected function collectFraudData(array $parameters): array
    {
        $startDate = Carbon::parse($parameters['start_date'] ?? now()->subMonth());
        $endDate = Carbon::parse($parameters['end_date'] ?? now());

        $fraudAlerts = FraudDetection::whereBetween('created_at', [$startDate, $endDate])
                                   ->with(['transaction', 'user'])
                                   ->get();

        return [
            'period' => [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
            'summary' => [
                'total_alerts' => $fraudAlerts->count(),
                'high_risk_alerts' => $fraudAlerts->where('risk_level', 'high')->count(),
                'critical_alerts' => $fraudAlerts->where('risk_level', 'critical')->count(),
                'resolved_alerts' => $fraudAlerts->where('status', 'resolved')->count(),
                'false_positives' => $fraudAlerts->where('status', 'false_positive')->count(),
            ],
            'by_risk_level' => $fraudAlerts->groupBy('risk_level')->map->count(),
            'by_alert_type' => $fraudAlerts->groupBy('alert_type')->map->count(),
            'alerts' => $fraudAlerts->map(function ($alert) {
                return [
                    'id' => $alert->id,
                    'transaction_id' => $alert->transaction_id,
                    'alert_type' => $alert->alert_type,
                    'risk_level' => $alert->risk_level,
                    'risk_score' => $alert->risk_score,
                    'status' => $alert->status,
                    'description' => $alert->description,
                    'created_at' => $alert->created_at->toISOString(),
                    'resolved_at' => $alert->resolved_at?->toISOString(),
                ];
            }),
        ];
    }

    /**
     * Collect user activity data.
     */
    protected function collectUserActivityData(array $parameters): array
    {
        $startDate = Carbon::parse($parameters['start_date'] ?? now()->subMonth());
        $endDate = Carbon::parse($parameters['end_date'] ?? now());

        $users = User::whereBetween('created_at', [$startDate, $endDate])
                    ->withCount(['sentTransactions', 'receivedTransactions'])
                    ->get();

        return [
            'users' => $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->first_name . ' ' . $user->last_name,
                    'email' => $user->email,
                    'user_type' => $user->user_type,
                    'status' => $user->status,
                    'sent_transactions_count' => $user->sent_transactions_count,
                    'received_transactions_count' => $user->received_transactions_count,
                    'created_at' => $user->created_at->toISOString(),
                    'last_login_at' => $user->last_login_at?->toISOString(),
                ];
            }),
        ];
    }

    /**
     * Generate report file.
     */
    protected function generateReportFile(Report $report, array $data): string
    {
        $fileName = $this->generateFileName($report);
        $filePath = "reports/{$fileName}";

        switch ($report->format) {
            case 'pdf':
                $this->generatePDFReport($filePath, $data, $report);
                break;
            case 'excel':
                $this->generateExcelReport($filePath, $data, $report);
                break;
            case 'csv':
                $this->generateCSVReport($filePath, $data, $report);
                break;
            default:
                $this->generateJSONReport($filePath, $data, $report);
        }

        return $filePath;
    }

    /**
     * Generate file name.
     */
    protected function generateFileName(Report $report): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $name = str_replace(' ', '_', $report->name);
        return "{$name}_{$timestamp}.{$report->format}";
    }

    /**
     * Generate PDF report.
     */
    protected function generatePDFReport(string $filePath, array $data, Report $report): void
    {
        // This would use a PDF library like DomPDF or TCPDF
        // For now, we'll create a simple JSON file
        Storage::put($filePath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Generate Excel report.
     */
    protected function generateExcelReport(string $filePath, array $data, Report $report): void
    {
        // This would use a library like PhpSpreadsheet or Laravel Excel
        // For now, we'll create a simple JSON file
        Storage::put($filePath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Generate CSV report.
     */
    protected function generateCSVReport(string $filePath, array $data, Report $report): void
    {
        // Convert data to CSV format
        $csv = $this->arrayToCSV($data);
        Storage::put($filePath, $csv);
    }

    /**
     * Generate JSON report.
     */
    protected function generateJSONReport(string $filePath, array $data, Report $report): void
    {
        Storage::put($filePath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Convert array to CSV.
     */
    protected function arrayToCSV(array $data): string
    {
        $output = '';
        
        if (isset($data['transactions']) && is_array($data['transactions'])) {
            $transactions = $data['transactions'];
            if (!empty($transactions)) {
                // Headers
                $headers = array_keys($transactions[0]);
                $output .= implode(',', $headers) . "\n";
                
                // Data rows
                foreach ($transactions as $transaction) {
                    $output .= implode(',', array_values($transaction)) . "\n";
                }
            }
        }
        
        return $output;
    }
}
