<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use Illuminate\Support\Facades\Log;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Financial System Commands
Artisan::command('financial:status', function () {
    $this->info('Mony Transfer Global Financial System Status');
    $this->info('Version: 1.0.0');
    $this->info('Status: Running');
    $this->info('Database: Connected');
    $this->info('Cache: Active');
    $this->info('Queue: Active');
})->purpose('Display financial system status');

Artisan::command('financial:stats', function () {
    $this->info('Financial System Statistics');
    $this->table(
        ['Metric', 'Value'],
        [
            ['Total Users', \App\Models\User::count()],
            ['Active Users', \App\Models\User::where('status', 'active')->count()],
            ['Total Transactions', \App\Models\Transaction::count()],
            ['Pending Transactions', \App\Models\Transaction::where('status', 'pending')->count()],
            ['Total Volume (USD)', number_format(\App\Models\Transaction::where('status', 'completed')->sum('amount'), 2)],
            ['Active Currencies', \App\Models\Currency::where('is_active', true)->count()],
            ['Active Branches', \App\Models\Branch::where('is_active', true)->count()],
        ]
    );
})->purpose('Display financial system statistics');

// Scheduled Tasks
Schedule::command('financial:update-rates')
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Update exchange rates from external sources');

Schedule::command('financial:process-pending --limit=100')
    ->everyMinute()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Process pending transactions');

Schedule::command('financial:update-rates --source=crypto_api')
    ->everyTwoMinutes()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Update cryptocurrency rates');

Schedule::call(function () {
    // Clean up old audit logs (keep 1 year)
    \App\Models\AuditLog::where('created_at', '<', now()->subYear())->delete();
    Log::info('Old audit logs cleaned up');
})->daily()->at('02:00')->description('Clean up old audit logs');

Schedule::call(function () {
    // Clean up old notifications (keep 3 months)
    \App\Models\Notification::where('created_at', '<', now()->subMonths(3))->delete();
    Log::info('Old notifications cleaned up');
})->daily()->at('02:30')->description('Clean up old notifications');

Schedule::call(function () {
    // Generate daily reports
    Log::info('Generating daily financial reports');
    // Add report generation logic here
})->daily()->at('23:00')->description('Generate daily financial reports');

Schedule::call(function () {
    // System health monitoring
    $stats = [
        'users_count' => \App\Models\User::count(),
        'transactions_today' => \App\Models\Transaction::whereDate('created_at', today())->count(),
        'pending_transactions' => \App\Models\Transaction::where('status', 'pending')->count(),
        'failed_transactions_today' => \App\Models\Transaction::whereDate('created_at', today())->where('status', 'failed')->count(),
    ];

    Log::info('System health check', $stats);

    // Alert if too many failed transactions
    if ($stats['failed_transactions_today'] > 10) {
        Log::warning('High number of failed transactions today', $stats);
    }
})->everyFifteenMinutes()->description('Monitor system health');
