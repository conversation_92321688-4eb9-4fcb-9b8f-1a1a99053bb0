<?php

namespace Database\Factories;

use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Currency>
 */
class CurrencyFactory extends Factory
{
    protected $model = Currency::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $currencies = [
            ['name_ar' => 'الدولار الأمريكي', 'name_en' => 'US Dollar', 'code' => 'USD', 'symbol' => '$', 'rate' => 1.00000000],
            ['name_ar' => 'الريال السعودي', 'name_en' => 'Saudi Riyal', 'code' => 'SAR', 'symbol' => 'ر.س', 'rate' => 3.75000000],
            ['name_ar' => 'الدرهم الإماراتي', 'name_en' => 'UAE Dirham', 'code' => 'AED', 'symbol' => 'د.إ', 'rate' => 3.67000000],
            ['name_ar' => 'الجنيه الإسترليني', 'name_en' => 'British Pound', 'code' => 'GBP', 'symbol' => '£', 'rate' => 0.79000000],
            ['name_ar' => 'اليورو', 'name_en' => 'Euro', 'code' => 'EUR', 'symbol' => '€', 'rate' => 0.85000000],
            ['name_ar' => 'الجنيه المصري', 'name_en' => 'Egyptian Pound', 'code' => 'EGP', 'symbol' => 'ج.م', 'rate' => 30.90000000],
        ];

        $currency = $this->faker->randomElement($currencies);

        return [
            'name_ar' => $currency['name_ar'],
            'name_en' => $currency['name_en'],
            'code' => $currency['code'],
            'symbol' => $currency['symbol'],
            'is_crypto' => false,
            'is_active' => $this->faker->boolean(90),
            'is_base_currency' => $currency['code'] === 'USD',
            'rate_to_usd' => $currency['rate'],
            'decimal_places' => $this->faker->numberBetween(2, 8),
            'crypto_network' => null,
            'contract_address' => null,
            'supported_countries' => ['SAU', 'ARE', 'USA', 'GBR'],
            'min_transfer_amount' => $this->faker->numberBetween(1, 10),
            'max_transfer_amount' => $this->faker->numberBetween(50000, 100000),
        ];
    }

    /**
     * Indicate that the currency is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the currency is a cryptocurrency.
     */
    public function crypto(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_crypto' => true,
            'crypto_network' => $this->faker->randomElement(['ethereum', 'bitcoin', 'binance_smart_chain']),
            'contract_address' => $this->faker->regexify('[0-9a-fA-F]{40}'),
            'decimal_places' => $this->faker->numberBetween(6, 18),
        ]);
    }

    /**
     * Indicate that the currency is the base currency.
     */
    public function base(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_base_currency' => true,
            'rate_to_usd' => 1.00000000,
        ]);
    }
}
