<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Commission extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'agent_id',
        'transaction_id',
        'amount',
        'rate',
        'status',
        'paid_at',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'rate' => 'decimal:4',
        'paid_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'pending',
    ];

    /**
     * Get the agent that owns the commission.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    /**
     * Get the transaction that generated this commission.
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Get the user who created this commission.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this commission.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for pending commissions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for paid commissions.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for cancelled commissions.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope for commissions by agent.
     */
    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * Scope for commissions in date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Mark commission as paid.
     */
    public function markAsPaid(): bool
    {
        return $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'updated_by' => auth()->id(),
        ]);
    }

    /**
     * Cancel commission.
     */
    public function cancel(string $reason = null): bool
    {
        return $this->update([
            'status' => 'cancelled',
            'notes' => $reason,
            'updated_by' => auth()->id(),
        ]);
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2);
    }

    /**
     * Get formatted rate.
     */
    public function getFormattedRateAttribute(): string
    {
        return number_format($this->rate * 100, 2) . '%';
    }

    /**
     * Check if commission is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if commission is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if commission is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'yellow',
            'paid' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get status text in Arabic.
     */
    public function getStatusTextAttribute(): array
    {
        return match($this->status) {
            'pending' => ['ar' => 'قيد الانتظار', 'en' => 'Pending'],
            'paid' => ['ar' => 'مدفوع', 'en' => 'Paid'],
            'cancelled' => ['ar' => 'ملغي', 'en' => 'Cancelled'],
            default => ['ar' => 'غير معروف', 'en' => 'Unknown']
        };
    }

    /**
     * Calculate total commissions for agent in period.
     */
    public static function totalForAgent(int $agentId, string $startDate = null, string $endDate = null): float
    {
        $query = static::byAgent($agentId)->paid();

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return $query->sum('amount');
    }

    /**
     * Calculate pending commissions for agent.
     */
    public static function pendingForAgent(int $agentId): float
    {
        return static::byAgent($agentId)->pending()->sum('amount');
    }

    /**
     * Get commission statistics.
     */
    public static function getStatistics(int $agentId = null): array
    {
        $query = static::query();

        if ($agentId) {
            $query->byAgent($agentId);
        }

        return [
            'total_commissions' => $query->sum('amount'),
            'paid_commissions' => $query->paid()->sum('amount'),
            'pending_commissions' => $query->pending()->sum('amount'),
            'cancelled_commissions' => $query->cancelled()->sum('amount'),
            'total_count' => $query->count(),
            'paid_count' => $query->paid()->count(),
            'pending_count' => $query->pending()->count(),
        ];
    }
}
