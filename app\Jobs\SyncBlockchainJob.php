<?php

namespace App\Jobs;

use App\Models\BlockchainTransaction;
use App\Models\Transaction;
use App\Services\BlockchainService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncBlockchainJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected ?int $blockchainTransactionId;
    protected ?string $transactionHash;
    protected string $network;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 5;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 180; // 3 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(?int $blockchainTransactionId = null, ?string $transactionHash = null, string $network = 'ethereum')
    {
        $this->blockchainTransactionId = $blockchainTransactionId;
        $this->transactionHash = $transactionHash;
        $this->network = $network;
    }

    /**
     * Execute the job.
     */
    public function handle(BlockchainService $blockchainService): void
    {
        try {
            if ($this->blockchainTransactionId) {
                $this->syncSpecificTransaction($blockchainService);
            } elseif ($this->transactionHash) {
                $this->syncByHash($blockchainService);
            } else {
                $this->syncPendingTransactions($blockchainService);
            }

        } catch (\Exception $e) {
            Log::error('Blockchain sync job failed', [
                'blockchain_transaction_id' => $this->blockchainTransactionId,
                'transaction_hash' => $this->transactionHash,
                'network' => $this->network,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Sync specific blockchain transaction.
     */
    protected function syncSpecificTransaction(BlockchainService $blockchainService): void
    {
        $blockchainTx = BlockchainTransaction::findOrFail($this->blockchainTransactionId);
        
        Log::info('Syncing specific blockchain transaction', [
            'blockchain_transaction_id' => $blockchainTx->id,
            'transaction_hash' => $blockchainTx->transaction_hash,
            'network' => $blockchainTx->blockchain_network,
        ]);

        if (!$blockchainTx->transaction_hash) {
            Log::warning('Blockchain transaction has no hash to sync', [
                'blockchain_transaction_id' => $blockchainTx->id,
            ]);
            return;
        }

        $this->updateTransactionStatus($blockchainService, $blockchainTx);
    }

    /**
     * Sync transaction by hash.
     */
    protected function syncByHash(BlockchainService $blockchainService): void
    {
        Log::info('Syncing blockchain transaction by hash', [
            'transaction_hash' => $this->transactionHash,
            'network' => $this->network,
        ]);

        $blockchainTx = BlockchainTransaction::where('transaction_hash', $this->transactionHash)
                                           ->where('blockchain_network', $this->network)
                                           ->first();

        if (!$blockchainTx) {
            Log::warning('Blockchain transaction not found for hash', [
                'transaction_hash' => $this->transactionHash,
                'network' => $this->network,
            ]);
            return;
        }

        $this->updateTransactionStatus($blockchainService, $blockchainTx);
    }

    /**
     * Sync all pending blockchain transactions.
     */
    protected function syncPendingTransactions(BlockchainService $blockchainService): void
    {
        Log::info('Syncing all pending blockchain transactions', [
            'network' => $this->network,
        ]);

        $pendingTransactions = BlockchainTransaction::needingConfirmationUpdate()
                                                  ->where('blockchain_network', $this->network)
                                                  ->limit(50) // Process in batches
                                                  ->get();

        Log::info('Found pending blockchain transactions to sync', [
            'count' => $pendingTransactions->count(),
            'network' => $this->network,
        ]);

        foreach ($pendingTransactions as $blockchainTx) {
            try {
                $this->updateTransactionStatus($blockchainService, $blockchainTx);
            } catch (\Exception $e) {
                Log::error('Failed to sync individual blockchain transaction', [
                    'blockchain_transaction_id' => $blockchainTx->id,
                    'transaction_hash' => $blockchainTx->transaction_hash,
                    'error' => $e->getMessage(),
                ]);
                // Continue with other transactions
            }
        }
    }

    /**
     * Update transaction status from blockchain.
     */
    protected function updateTransactionStatus(BlockchainService $blockchainService, BlockchainTransaction $blockchainTx): void
    {
        $txData = $blockchainService->getTransactionStatus(
            $blockchainTx->transaction_hash,
            $blockchainTx->blockchain_network
        );

        if (!$txData) {
            Log::warning('Could not retrieve transaction data from blockchain', [
                'blockchain_transaction_id' => $blockchainTx->id,
                'transaction_hash' => $blockchainTx->transaction_hash,
                'network' => $blockchainTx->blockchain_network,
            ]);
            return;
        }

        $oldStatus = $blockchainTx->status;
        $oldConfirmations = $blockchainTx->confirmation_count;

        // Update blockchain transaction with latest data
        $blockchainTx->update([
            'block_number' => $txData['block_number'] ?? $blockchainTx->block_number,
            'block_hash' => $txData['block_hash'] ?? $blockchainTx->block_hash,
            'confirmation_count' => $txData['confirmations'] ?? $blockchainTx->confirmation_count,
            'status' => $txData['status'] ?? $blockchainTx->status,
            'gas_used' => $txData['gas_used'] ?? $blockchainTx->gas_used,
            'gas_fee' => $txData['gas_fee'] ?? $blockchainTx->gas_fee,
            'receipt_data' => $txData['receipt'] ?? $blockchainTx->receipt_data,
            'error_message' => $txData['error'] ?? $blockchainTx->error_message,
        ]);

        // Log status changes
        if ($oldStatus !== $blockchainTx->status || $oldConfirmations !== $blockchainTx->confirmation_count) {
            Log::info('Blockchain transaction status updated', [
                'blockchain_transaction_id' => $blockchainTx->id,
                'transaction_hash' => $blockchainTx->transaction_hash,
                'old_status' => $oldStatus,
                'new_status' => $blockchainTx->status,
                'old_confirmations' => $oldConfirmations,
                'new_confirmations' => $blockchainTx->confirmation_count,
            ]);
        }

        // Update main transaction status if blockchain transaction is confirmed
        if ($blockchainTx->isConfirmed() && $blockchainTx->transaction) {
            $this->updateMainTransactionStatus($blockchainTx);
        }

        // Handle failed blockchain transactions
        if ($blockchainTx->status === 'failed' && $blockchainTx->transaction) {
            $this->handleFailedBlockchainTransaction($blockchainTx);
        }
    }

    /**
     * Update main transaction status based on blockchain confirmation.
     */
    protected function updateMainTransactionStatus(BlockchainTransaction $blockchainTx): void
    {
        $transaction = $blockchainTx->transaction;
        
        if ($transaction->status === 'pending' || $transaction->status === 'processing') {
            $transaction->update([
                'status' => 'completed',
                'completed_at' => now(),
                'blockchain_confirmed' => true,
            ]);

            Log::info('Main transaction marked as completed due to blockchain confirmation', [
                'transaction_id' => $transaction->id,
                'blockchain_transaction_id' => $blockchainTx->id,
                'transaction_hash' => $blockchainTx->transaction_hash,
            ]);
        }
    }

    /**
     * Handle failed blockchain transactions.
     */
    protected function handleFailedBlockchainTransaction(BlockchainTransaction $blockchainTx): void
    {
        $transaction = $blockchainTx->transaction;
        
        if ($transaction->status !== 'failed') {
            $transaction->update([
                'status' => 'failed',
                'failed_at' => now(),
                'failure_reason' => 'Blockchain transaction failed: ' . ($blockchainTx->error_message ?? 'Unknown error'),
            ]);

            Log::warning('Main transaction marked as failed due to blockchain failure', [
                'transaction_id' => $transaction->id,
                'blockchain_transaction_id' => $blockchainTx->id,
                'transaction_hash' => $blockchainTx->transaction_hash,
                'error' => $blockchainTx->error_message,
            ]);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SyncBlockchainJob failed permanently', [
            'blockchain_transaction_id' => $this->blockchainTransactionId,
            'transaction_hash' => $this->transactionHash,
            'network' => $this->network,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = [
            'blockchain-sync',
            'network:' . $this->network,
        ];

        if ($this->blockchainTransactionId) {
            $tags[] = 'blockchain-tx:' . $this->blockchainTransactionId;
        }

        if ($this->transactionHash) {
            $tags[] = 'hash:' . substr($this->transactionHash, 0, 8);
        }

        return $tags;
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHour(); // Allow retries for up to 1 hour
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 60, 120, 300, 600]; // Increasing delays for blockchain sync
    }

    /**
     * Create job for specific blockchain transaction.
     */
    public static function forTransaction(int $blockchainTransactionId): self
    {
        return new self($blockchainTransactionId);
    }

    /**
     * Create job for transaction hash.
     */
    public static function forHash(string $transactionHash, string $network): self
    {
        return new self(null, $transactionHash, $network);
    }

    /**
     * Create job for syncing all pending transactions.
     */
    public static function forPendingTransactions(string $network = 'ethereum'): self
    {
        return new self(null, null, $network);
    }
}
