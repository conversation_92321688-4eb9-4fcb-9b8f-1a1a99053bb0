<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

try {
    // Test login credentials
    $email = '<EMAIL>';
    $password = 'password123';
    
    echo "🔐 Testing Authentication System\n";
    echo "================================\n\n";
    
    $user = User::where('email', $email)->first();
    
    if ($user) {
        echo "✅ User found: " . $user->email . "\n";
        echo "   Name: " . $user->first_name . " " . $user->last_name . "\n";
        echo "   Type: " . $user->user_type . "\n";
        echo "   Status: " . $user->status . "\n";
        
        if (Hash::check($password, $user->password)) {
            echo "✅ Password is correct!\n";
        } else {
            echo "❌ Password is incorrect!\n";
        }
    } else {
        echo "❌ User not found!\n";
    }
    
    echo "\n🌐 Available Pages:\n";
    echo "==================\n";
    echo "🏠 Home: http://localhost:8000\n";
    echo "🔐 Login: http://localhost:8000/login\n";
    echo "📝 Register: http://localhost:8000/register\n";
    echo "📊 Dashboard: http://localhost:8000/dashboard\n";
    echo "👨‍💼 Admin: http://localhost:8000/admin/dashboard\n";
    
    echo "\n🔗 API Endpoints:\n";
    echo "=================\n";
    echo "🔐 Login API: http://localhost:8000/api/v1/auth/login\n";
    echo "📝 Register API: http://localhost:8000/api/v1/auth/register\n";
    echo "💰 Transactions: http://localhost:8000/api/v1/transactions\n";
    echo "💳 Payments: http://localhost:8000/api/v1/payments/methods\n";
    
    echo "\n🧪 Test Credentials:\n";
    echo "====================\n";
    echo "👨‍💼 Admin: <EMAIL> / password123\n";
    echo "👤 User: <EMAIL> / password123\n";
    
    echo "\n🚀 Quick Test Links:\n";
    echo "====================\n";
    echo "🔐 Admin Login: http://localhost:8000/login?demo=admin\n";
    echo "👤 User Login: http://localhost:8000/login?demo=user\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
