<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kyc_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('document_type'); // passport, national_id, driving_license, utility_bill
            $table->string('document_number')->nullable();
            $table->string('document_country')->nullable();
            $table->date('document_expiry')->nullable();
            $table->string('file_path'); // Encrypted file path
            $table->string('file_hash'); // File integrity check
            $table->string('mime_type');
            $table->integer('file_size');
            $table->enum('status', ['pending', 'under_review', 'approved', 'rejected', 'expired'])->default('pending');
            $table->text('rejection_reason')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->json('extracted_data')->nullable(); // OCR extracted data
            $table->decimal('confidence_score', 5, 4)->nullable(); // AI confidence score
            $table->boolean('is_verified')->default(false);
            $table->json('verification_metadata')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['document_type', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('expires_at');
            $table->index('reviewed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kyc_documents');
    }
};
