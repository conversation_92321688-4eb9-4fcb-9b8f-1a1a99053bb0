<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class SystemMetric extends Model
{
    use HasFactory;

    protected $fillable = [
        'metric_name',
        'metric_type',
        'value',
        'labels',
        'metadata',
        'recorded_at',
    ];

    protected $casts = [
        'value' => 'decimal:8',
        'labels' => 'array',
        'metadata' => 'array',
        'recorded_at' => 'datetime',
    ];

    /**
     * Scope to filter by metric name
     */
    public function scopeByName(Builder $query, string $name): Builder
    {
        return $query->where('metric_name', $name);
    }

    /**
     * Scope to filter by metric type
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('metric_type', $type);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeBetweenDates(Builder $query, $startDate, $endDate): Builder
    {
        return $query->whereBetween('recorded_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent metrics
     */
    public function scopeRecent(Builder $query, int $hours = 24): Builder
    {
        return $query->where('recorded_at', '>=', now()->subHours($hours));
    }

    /**
     * Record a new metric
     */
    public static function record(
        string $name,
        string $type,
        float $value,
        array $labels = [],
        array $metadata = []
    ): self {
        return self::create([
            'metric_name' => $name,
            'metric_type' => $type,
            'value' => $value,
            'labels' => $labels,
            'metadata' => $metadata,
            'recorded_at' => now(),
        ]);
    }

    /**
     * Get average value for a metric
     */
    public static function getAverage(string $name, int $hours = 24): float
    {
        return self::byName($name)
            ->recent($hours)
            ->avg('value') ?? 0.0;
    }

    /**
     * Get latest value for a metric
     */
    public static function getLatest(string $name): ?float
    {
        $metric = self::byName($name)
            ->latest('recorded_at')
            ->first();

        return $metric ? $metric->value : null;
    }

    /**
     * Get metric statistics
     */
    public static function getStats(string $name, int $hours = 24): array
    {
        $metrics = self::byName($name)
            ->recent($hours)
            ->get();

        if ($metrics->isEmpty()) {
            return [
                'count' => 0,
                'min' => null,
                'max' => null,
                'avg' => null,
                'sum' => null,
            ];
        }

        return [
            'count' => $metrics->count(),
            'min' => $metrics->min('value'),
            'max' => $metrics->max('value'),
            'avg' => $metrics->avg('value'),
            'sum' => $metrics->sum('value'),
        ];
    }

    /**
     * Clean old metrics
     */
    public static function cleanup(int $daysToKeep = 30): int
    {
        return self::where('recorded_at', '<', now()->subDays($daysToKeep))
            ->delete();
    }

    /**
     * Get metrics for dashboard
     */
    public static function getDashboardMetrics(): array
    {
        $metrics = [
            'database_response_time',
            'cache_hit_rate',
            'memory_usage',
            'cpu_usage',
            'active_users',
            'transaction_count',
            'error_rate',
        ];

        $result = [];
        foreach ($metrics as $metric) {
            $result[$metric] = [
                'current' => self::getLatest($metric),
                'average_24h' => self::getAverage($metric, 24),
                'stats' => self::getStats($metric, 24),
            ];
        }

        return $result;
    }

    /**
     * Record database metrics
     */
    public static function recordDatabaseMetrics(): void
    {
        $startTime = microtime(true);
        
        try {
            // Test database connection
            \DB::connection()->getPdo();
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            self::record('database_response_time', 'gauge', $responseTime, ['unit' => 'ms']);
            
            // Get connection count
            $connections = \DB::select("SHOW STATUS LIKE 'Threads_connected'")[0]->Value ?? 0;
            self::record('database_connections', 'gauge', (float)$connections);
            
            // Get slow queries
            $slowQueries = \DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'")[0]->Value ?? 0;
            self::record('database_slow_queries', 'counter', (float)$slowQueries);
            
        } catch (\Exception $e) {
            self::record('database_errors', 'counter', 1, ['error' => $e->getMessage()]);
        }
    }

    /**
     * Record cache metrics
     */
    public static function recordCacheMetrics(): void
    {
        try {
            $startTime = microtime(true);
            
            // Test cache operation
            $testKey = 'metric_test_' . time();
            \Cache::put($testKey, 'test', 60);
            \Cache::get($testKey);
            \Cache::forget($testKey);
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            self::record('cache_response_time', 'gauge', $responseTime, ['unit' => 'ms']);
            
            // Get Redis info if available
            if (\Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $redis = \Cache::getRedis();
                $info = $redis->info();
                
                $hits = $info['keyspace_hits'] ?? 0;
                $misses = $info['keyspace_misses'] ?? 0;
                $total = $hits + $misses;
                
                $hitRate = $total > 0 ? ($hits / $total) * 100 : 0;
                self::record('cache_hit_rate', 'gauge', $hitRate, ['unit' => 'percent']);
                
                $memoryUsage = ($info['used_memory'] ?? 0) / 1024 / 1024;
                self::record('cache_memory_usage', 'gauge', $memoryUsage, ['unit' => 'mb']);
            }
            
        } catch (\Exception $e) {
            self::record('cache_errors', 'counter', 1, ['error' => $e->getMessage()]);
        }
    }

    /**
     * Record system metrics
     */
    public static function recordSystemMetrics(): void
    {
        // Memory usage
        $memoryUsage = memory_get_usage(true) / 1024 / 1024;
        self::record('memory_usage', 'gauge', $memoryUsage, ['unit' => 'mb']);
        
        $memoryPeak = memory_get_peak_usage(true) / 1024 / 1024;
        self::record('memory_peak', 'gauge', $memoryPeak, ['unit' => 'mb']);
        
        // Active users
        $activeUsers = \DB::table('users')
            ->where('last_activity', '>=', now()->subHour())
            ->count();
        self::record('active_users', 'gauge', (float)$activeUsers);
        
        // Transaction metrics
        $transactionsToday = \DB::table('transactions')
            ->whereDate('created_at', today())
            ->count();
        self::record('transactions_today', 'gauge', (float)$transactionsToday);
        
        $successfulTransactions = \DB::table('transactions')
            ->whereDate('created_at', today())
            ->where('status', 'completed')
            ->count();
        self::record('successful_transactions_today', 'gauge', (float)$successfulTransactions);
    }
}
