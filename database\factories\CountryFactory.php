<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Country>
 */
class CountryFactory extends Factory
{
    protected $model = Country::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $countries = [
            ['name_ar' => 'المملكة العربية السعودية', 'name_en' => 'Saudi Arabia', 'code' => 'SAU', 'iso2' => 'SA', 'phone_code' => '+966', 'currency_code' => 'SAR'],
            ['name_ar' => 'الإمارات العربية المتحدة', 'name_en' => 'United Arab Emirates', 'code' => 'ARE', 'iso2' => 'AE', 'phone_code' => '+971', 'currency_code' => 'AED'],
            ['name_ar' => 'الولايات المتحدة الأمريكية', 'name_en' => 'United States', 'code' => 'USA', 'iso2' => 'US', 'phone_code' => '+1', 'currency_code' => 'USD'],
            ['name_ar' => 'المملكة المتحدة', 'name_en' => 'United Kingdom', 'code' => 'GBR', 'iso2' => 'GB', 'phone_code' => '+44', 'currency_code' => 'GBP'],
            ['name_ar' => 'مصر', 'name_en' => 'Egypt', 'code' => 'EGY', 'iso2' => 'EG', 'phone_code' => '+20', 'currency_code' => 'EGP'],
        ];

        $country = $this->faker->randomElement($countries);

        return [
            'name_ar' => $country['name_ar'],
            'name_en' => $country['name_en'],
            'code' => $country['code'],
            'iso2' => $country['iso2'],
            'phone_code' => $country['phone_code'],
            'currency_code' => $country['currency_code'],
            'is_active' => $this->faker->boolean(90),
            'supports_transfers' => $this->faker->boolean(85),
            'supports_crypto' => $this->faker->boolean(60),
            'max_transfer_amount' => $this->faker->numberBetween(50000, 1000000),
            'min_transfer_amount' => $this->faker->numberBetween(1, 100),
            'commission_rate' => $this->faker->randomFloat(3, 0.005, 0.025),
            'supported_payment_methods' => ['cash', 'bank_transfer', 'card'],
            'required_documents' => ['passport', 'national_id'],
        ];
    }

    /**
     * Indicate that the country is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the country supports transfers.
     */
    public function supportsTransfers(): static
    {
        return $this->state(fn (array $attributes) => [
            'supports_transfers' => true,
        ]);
    }

    /**
     * Indicate that the country supports crypto.
     */
    public function supportsCrypto(): static
    {
        return $this->state(fn (array $attributes) => [
            'supports_crypto' => true,
        ]);
    }
}
