<?php

use Illuminate\Support\Facades\Route;

// Import controllers
use App\Http\Controllers\Web\DashboardController;
use App\Http\Controllers\Web\TransactionController;
use App\Http\Controllers\Web\AuthController;

Route::get('/', function () {
    return response('
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>موني ترانسفير - Mony Transfer</title>

    <style>
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 800px;
        }

        .logo {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #28a745;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .quick-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .quick-link {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid #667eea;
        }

        .quick-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 0.9rem;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .feature {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .feature-icon {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .feature-title {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="welcome-card">
        <div class="logo">💰 موني ترانسفير</div>
        <div class="subtitle">
            نظام تحويل الأموال الآمن والموثوق
            <span class="status-indicator"></span>
            النظام يعمل
        </div>

        <p>مرحباً بك في نظام موني ترانسفير المتطور لتحويل الأموال بأمان وسرعة</p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🔒</div>
                <div class="feature-title">أمان متقدم</div>
                <div>حماية متعددة الطبقات</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🌍</div>
                <div class="feature-title">تحويل دولي</div>
                <div>200+ دولة حول العالم</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">سرعة فائقة</div>
                <div>معالجة فورية</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div class="feature-title">واجهة سهلة</div>
                <div>تصميم بديهي</div>
            </div>
        </div>

        <div class="quick-links">
            <a href="/login" class="quick-link">🔐 تسجيل الدخول</a>
            <a href="/register" class="quick-link">📝 إنشاء حساب</a>
            <a href="/login?demo=admin" class="quick-link">👨‍💼 دخول الإدارة</a>
            <a href="/api/health" class="quick-link">🔍 فحص النظام</a>
        </div>

        <div class="footer">
            <p>موني ترانسفير v1.0.0 | Laravel ' . app()->version() . ' | PHP ' . PHP_VERSION . '</p>
            <p>© 2024 جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>
    ')->header('Content-Type', 'text/html; charset=utf-8');
})->name('home');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('password.email');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPasswordForm'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Email verification routes
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', function () {
        return view('auth.verify-email');
    })->name('verification.notice');

    Route::post('/email/verification-notification', [AuthController::class, 'sendVerificationEmail'])
        ->name('verification.send');

    Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verifyEmail'])
        ->middleware(['signed'])
        ->name('verification.verify');

    Route::post('/email/verify/check', [AuthController::class, 'checkVerificationStatus'])
        ->name('verification.check');

    Route::get('/email/verify/status', [AuthController::class, 'getVerificationStatus'])
        ->name('verification.status');

    // Skip verification for demo purposes
    Route::get('/dashboard/skip-verification', function () {
        Auth::user()->markEmailAsVerified();
        return redirect()->route('dashboard')->with('success', 'تم تخطي التحقق من البريد الإلكتروني');
    })->name('dashboard.skip.verification');
});

// Protected routes (temporarily removed 'verified' middleware for demo)
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [DashboardController::class, 'data'])->name('dashboard.data');

    // Profile
    Route::get('/profile', [DashboardController::class, 'profile'])->name('profile');
    Route::put('/profile', [DashboardController::class, 'updateProfile'])->name('profile.update');

    // Settings
    Route::get('/settings', [DashboardController::class, 'settings'])->name('settings');
    Route::put('/settings', [DashboardController::class, 'updateSettings'])->name('settings.update');

    // Transactions
    Route::prefix('transactions')->name('transactions.')->group(function () {
        Route::get('/', [TransactionController::class, 'index'])->name('index');
        Route::get('/create', [TransactionController::class, 'create'])->name('create');
        Route::post('/', [TransactionController::class, 'store'])->name('store');
        Route::get('/{transactionId}', [TransactionController::class, 'show'])->name('show');
        Route::post('/{transactionId}/cancel', [TransactionController::class, 'cancel'])->name('cancel');
        Route::get('/{transactionId}/receipt', [TransactionController::class, 'receipt'])->name('receipt');
    });

    // Payments
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', function () {
            return view('payments.index');
        })->name('index');
    });

    // Recipients
    Route::prefix('recipients')->name('recipients.')->group(function () {
        Route::get('/', function () {
            return view('recipients.index');
        })->name('index');
    });

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', function () {
            return view('reports.index');
        })->name('index');
    });
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [\App\Http\Controllers\Admin\DashboardController::class, 'overview'])->name('dashboard.data');

    Route::get('/users', function () {
        return view('admin.users');
    })->name('users');

    Route::get('/transactions', function () {
        return view('admin.transactions');
    })->name('transactions');

    Route::get('/settings', function () {
        return view('admin.settings');
    })->name('settings');
});
