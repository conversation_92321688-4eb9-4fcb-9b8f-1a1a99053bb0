<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return response()->json([
        'success' => true,
        'message' => 'مرحباً بك في نظام موني ترانسفير - Welcome to Mony Transfer System',
        'system' => [
            'name' => 'Mony Transfer',
            'version' => '1.0.0',
            'status' => 'operational',
            'api_version' => 'v1',
            'documentation' => url('/api/documentation'),
            'health_check' => url('/api/health'),
        ],
        'features' => [
            'money_transfer' => 'تحويل الأموال',
            'multi_currency' => 'العملات المتعددة',
            'kyc_verification' => 'التحقق من الهوية',
            'fraud_detection' => 'كشف الاحتيال',
            'compliance' => 'الامتثال التنظيمي',
            'reporting' => 'التقارير المتقدمة',
            'notifications' => 'الإشعارات الذكية',
        ],
        'endpoints' => [
            'auth' => [
                'login' => 'POST /api/v1/auth/login',
                'register' => 'POST /api/v1/auth/register',
                'logout' => 'POST /api/v1/auth/logout',
            ],
            'transactions' => [
                'list' => 'GET /api/v1/transactions',
                'create' => 'POST /api/v1/transactions',
                'show' => 'GET /api/v1/transactions/{id}',
            ],
            'user' => [
                'profile' => 'GET /api/v1/user/profile',
                'documents' => 'GET /api/v1/documents',
                'notifications' => 'GET /api/v1/user/notifications',
            ],
        ],
        'timestamp' => now()->toISOString(),
    ]);
});
