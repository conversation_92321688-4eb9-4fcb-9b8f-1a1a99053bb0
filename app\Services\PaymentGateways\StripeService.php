<?php

namespace App\Services\PaymentGateways;

use Illuminate\Support\Facades\Log;
use App\Models\Transaction;
use App\Contracts\PaymentGatewayInterface;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Refund;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;

class StripeService implements PaymentGatewayInterface
{
    protected array $config;

    public function __construct()
    {
        $this->config = config('payment_gateways.stripe', []);
        Stripe::setApiKey($this->config['secret_key']);
    }

    /**
     * Process payment through Stripe.
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $this->convertToStripeAmount($transaction->amount + $transaction->fee, $transaction->currency->code),
                'currency' => strtolower($transaction->currency->code),
                'payment_method' => $paymentData['payment_method_id'] ?? null,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => $paymentData['return_url'] ?? config('app.url') . '/payment/success',
                'metadata' => [
                    'transaction_id' => $transaction->id,
                    'reference_number' => $transaction->reference_number,
                    'sender_id' => $transaction->sender_id,
                    'receiver_name' => $transaction->receiver_name,
                ],
                'description' => "Money transfer - {$transaction->reference_number}",
                'statement_descriptor' => 'MONY TRANSFER',
            ]);

            // Store payment intent ID
            $transaction->update([
                'gateway_transaction_id' => $paymentIntent->id,
                'gateway_response' => $paymentIntent->toArray(),
            ]);

            return $this->handlePaymentIntentResponse($paymentIntent);

        } catch (\Stripe\Exception\CardException $e) {
            Log::error('Stripe card error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'decline_code' => $e->getDeclineCode(),
            ]);

            return [
                'success' => false,
                'error' => $e->getError()->message,
                'error_code' => $e->getError()->code,
                'decline_code' => $e->getDeclineCode(),
            ];

        } catch (\Stripe\Exception\RateLimitException $e) {
            Log::error('Stripe rate limit error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Too many requests. Please try again later.',
                'error_code' => 'RATE_LIMIT_ERROR',
            ];

        } catch (\Stripe\Exception\InvalidRequestException $e) {
            Log::error('Stripe invalid request error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'INVALID_REQUEST_ERROR',
            ];

        } catch (\Stripe\Exception\AuthenticationException $e) {
            Log::error('Stripe authentication error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Payment gateway authentication failed.',
                'error_code' => 'AUTHENTICATION_ERROR',
            ];

        } catch (\Stripe\Exception\ApiConnectionException $e) {
            Log::error('Stripe API connection error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Network communication with payment gateway failed.',
                'error_code' => 'API_CONNECTION_ERROR',
            ];

        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe API error', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Payment gateway error occurred.',
                'error_code' => 'API_ERROR',
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment processing failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'Payment processing failed.',
                'error_code' => 'PROCESSING_ERROR',
            ];
        }
    }

    /**
     * Confirm payment intent.
     */
    public function confirmPayment(string $paymentIntentId, array $confirmData = []): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            if ($paymentIntent->status === 'requires_confirmation') {
                $paymentIntent = $paymentIntent->confirm($confirmData);
            }

            return $this->handlePaymentIntentResponse($paymentIntent);

        } catch (\Exception $e) {
            Log::error('Stripe payment confirmation failed', [
                'payment_intent_id' => $paymentIntentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'CONFIRMATION_ERROR',
            ];
        }
    }

    /**
     * Refund payment.
     */
    public function refundPayment(string $paymentIntentId, float $amount, string $currency = 'USD'): array
    {
        try {
            $refund = Refund::create([
                'payment_intent' => $paymentIntentId,
                'amount' => $this->convertToStripeAmount($amount, $currency),
                'reason' => 'requested_by_customer',
                'metadata' => [
                    'refunded_by' => 'mony_transfer_system',
                    'refund_timestamp' => now()->toISOString(),
                ],
            ]);

            return [
                'success' => true,
                'refund_id' => $refund->id,
                'status' => $refund->status,
                'amount' => $this->convertFromStripeAmount($refund->amount, $currency),
                'gateway_response' => $refund->toArray(),
            ];

        } catch (\Exception $e) {
            Log::error('Stripe refund failed', [
                'payment_intent_id' => $paymentIntentId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'REFUND_ERROR',
            ];
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentIntentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            return [
                'success' => true,
                'status' => $this->mapStripeStatus($paymentIntent->status),
                'gateway_status' => $paymentIntent->status,
                'amount' => $this->convertFromStripeAmount($paymentIntent->amount, $paymentIntent->currency),
                'gateway_response' => $paymentIntent->toArray(),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get Stripe payment status', [
                'payment_intent_id' => $paymentIntentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'STATUS_ERROR',
            ];
        }
    }

    /**
     * Validate webhook signature.
     */
    public function validateWebhook(array $headers, string $payload): bool
    {
        try {
            $signature = $headers['Stripe-Signature'] ?? '';
            $webhookSecret = $this->config['webhook_secret'] ?? '';

            if (!$webhookSecret) {
                Log::warning('Stripe webhook secret not configured');
                return false;
            }

            Webhook::constructEvent($payload, $signature, $webhookSecret);
            return true;

        } catch (SignatureVerificationException $e) {
            Log::error('Stripe webhook signature verification failed', [
                'error' => $e->getMessage(),
            ]);
            return false;

        } catch (\Exception $e) {
            Log::error('Stripe webhook validation error', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle payment intent response.
     */
    protected function handlePaymentIntentResponse(PaymentIntent $paymentIntent): array
    {
        switch ($paymentIntent->status) {
            case 'succeeded':
                return [
                    'success' => true,
                    'transaction_id' => $paymentIntent->id,
                    'status' => 'completed',
                    'gateway_response' => $paymentIntent->toArray(),
                ];

            case 'requires_action':
            case 'requires_source_action':
                return [
                    'success' => true,
                    'transaction_id' => $paymentIntent->id,
                    'status' => 'requires_action',
                    'client_secret' => $paymentIntent->client_secret,
                    'next_action' => $paymentIntent->next_action,
                    'gateway_response' => $paymentIntent->toArray(),
                ];

            case 'requires_payment_method':
                return [
                    'success' => false,
                    'error' => 'Payment method required or invalid.',
                    'error_code' => 'PAYMENT_METHOD_REQUIRED',
                    'client_secret' => $paymentIntent->client_secret,
                    'gateway_response' => $paymentIntent->toArray(),
                ];

            case 'requires_confirmation':
                return [
                    'success' => true,
                    'transaction_id' => $paymentIntent->id,
                    'status' => 'requires_confirmation',
                    'client_secret' => $paymentIntent->client_secret,
                    'gateway_response' => $paymentIntent->toArray(),
                ];

            case 'processing':
                return [
                    'success' => true,
                    'transaction_id' => $paymentIntent->id,
                    'status' => 'processing',
                    'gateway_response' => $paymentIntent->toArray(),
                ];

            case 'canceled':
                return [
                    'success' => false,
                    'error' => 'Payment was canceled.',
                    'error_code' => 'PAYMENT_CANCELED',
                    'gateway_response' => $paymentIntent->toArray(),
                ];

            default:
                return [
                    'success' => false,
                    'error' => "Unknown payment status: {$paymentIntent->status}",
                    'error_code' => 'UNKNOWN_STATUS',
                    'gateway_response' => $paymentIntent->toArray(),
                ];
        }
    }

    /**
     * Convert amount to Stripe format (cents).
     */
    protected function convertToStripeAmount(float $amount, string $currency): int
    {
        // Zero-decimal currencies (e.g., JPY, KRW)
        $zeroDecimalCurrencies = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF'];
        
        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return (int) round($amount);
        }
        
        return (int) round($amount * 100);
    }

    /**
     * Convert amount from Stripe format.
     */
    protected function convertFromStripeAmount(int $amount, string $currency): float
    {
        // Zero-decimal currencies
        $zeroDecimalCurrencies = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF'];
        
        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return (float) $amount;
        }
        
        return $amount / 100;
    }

    /**
     * Map Stripe status to our internal status.
     */
    protected function mapStripeStatus(string $stripeStatus): string
    {
        return match($stripeStatus) {
            'requires_payment_method', 'requires_confirmation', 'requires_action' => 'pending',
            'processing' => 'processing',
            'succeeded' => 'completed',
            'canceled' => 'cancelled',
            'requires_capture' => 'pending_capture',
            default => 'unknown'
        };
    }

    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(): array
    {
        return [
            'USD', 'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AUD', 'AWG', 'AZN',
            'BAM', 'BBD', 'BDT', 'BGN', 'BIF', 'BMD', 'BND', 'BOB', 'BRL', 'BSD', 'BWP',
            'BYN', 'BZD', 'CAD', 'CDF', 'CHF', 'CLP', 'CNY', 'COP', 'CRC', 'CVE', 'CZK',
            'DJF', 'DKK', 'DOP', 'DZD', 'EGP', 'ETB', 'EUR', 'FJD', 'FKP', 'GBP', 'GEL',
            'GHS', 'GIP', 'GMD', 'GNF', 'GTQ', 'GYD', 'HKD', 'HNL', 'HRK', 'HTG', 'HUF',
            'IDR', 'ILS', 'INR', 'ISK', 'JMD', 'JPY', 'KES', 'KGS', 'KHR', 'KMF', 'KRW',
            'KYD', 'KZT', 'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'MAD', 'MDL', 'MGA', 'MKD',
            'MMK', 'MNT', 'MOP', 'MRO', 'MUR', 'MVR', 'MWK', 'MXN', 'MYR', 'MZN', 'NAD',
            'NGN', 'NIO', 'NOK', 'NPR', 'NZD', 'PAB', 'PEN', 'PGK', 'PHP', 'PKR', 'PLN',
            'PYG', 'QAR', 'RON', 'RSD', 'RUB', 'RWF', 'SAR', 'SBD', 'SCR', 'SEK', 'SGD',
            'SHP', 'SLE', 'SLL', 'SOS', 'SRD', 'STD', 'SZL', 'THB', 'TJS', 'TOP', 'TRY',
            'TTD', 'TVD', 'TWD', 'TZS', 'UAH', 'UGX', 'UYU', 'UZS', 'VES', 'VND', 'VUV',
            'WST', 'XAF', 'XCD', 'XOF', 'XPF', 'YER', 'ZAR', 'ZMW'
        ];
    }

    /**
     * Get gateway configuration.
     */
    public function getConfig(): array
    {
        return [
            'name' => 'Stripe',
            'supports_refunds' => true,
            'supports_partial_refunds' => true,
            'supports_webhooks' => true,
            'supported_currencies' => $this->getSupportedCurrencies(),
            'min_amount' => 0.50,
            'max_amount' => 999999.99,
            'processing_fee_percentage' => 2.9,
            'processing_fee_fixed' => 0.30,
        ];
    }
}
