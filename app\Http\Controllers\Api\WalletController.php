<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Wallet;
use App\Models\Currency;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class WalletController extends Controller
{
    /**
     * Get all wallets for authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $wallets = $user->wallets()
            ->with(['currency'])
            ->where('is_active', true)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $wallets,
        ]);
    }

    /**
     * Get specific wallet details.
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $wallet = $user->wallets()
            ->with(['currency', 'sentTransactions', 'receivedTransactions'])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $wallet,
        ]);
    }

    /**
     * Create a new wallet for user.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'currency_id' => 'required|exists:currencies,id',
            'is_crypto_wallet' => 'boolean',
            'daily_limit' => 'nullable|numeric|min:0',
            'monthly_limit' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $currency = Currency::findOrFail($request->currency_id);

        // Check if user already has a wallet for this currency
        $existingWallet = $user->wallets()
            ->where('currency_id', $request->currency_id)
            ->first();

        if ($existingWallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet already exists for this currency',
            ], 400);
        }

        try {
            DB::beginTransaction();

            $walletData = [
                'user_id' => $user->id,
                'currency_id' => $request->currency_id,
                'wallet_number' => Wallet::generateWalletNumber(),
                'balance' => 0.00,
                'is_active' => true,
                'is_crypto_wallet' => $request->is_crypto_wallet ?? $currency->is_crypto,
                'daily_limit' => $request->daily_limit ?? $user->daily_limit,
                'monthly_limit' => $request->monthly_limit ?? $user->monthly_limit,
            ];

            // Generate crypto address if it's a crypto wallet
            if ($walletData['is_crypto_wallet']) {
                $walletData['crypto_address'] = $this->generateCryptoAddress($currency);
                $walletData['qr_code_path'] = $this->generateQRCode($walletData['crypto_address']);
            }

            $wallet = Wallet::create($walletData);

            DB::commit();

            $wallet->load('currency');

            return response()->json([
                'success' => true,
                'message' => 'Wallet created successfully',
                'data' => $wallet,
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create wallet',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update wallet settings.
     */
    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'daily_limit' => 'nullable|numeric|min:0',
            'monthly_limit' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $wallet = $user->wallets()->findOrFail($id);

        $wallet->update($request->only(['daily_limit', 'monthly_limit', 'is_active']));

        return response()->json([
            'success' => true,
            'message' => 'Wallet updated successfully',
            'data' => $wallet,
        ]);
    }

    /**
     * Get wallet balance and transaction history.
     */
    public function balance(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        $wallet = $user->wallets()->with('currency')->findOrFail($id);

        $recentTransactions = $wallet->sentTransactions()
            ->union($wallet->receivedTransactions())
            ->with(['currency', 'targetCurrency'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'wallet' => $wallet,
                'recent_transactions' => $recentTransactions,
                'available_balance' => $wallet->available_balance,
                'formatted_balance' => $wallet->formatted_balance,
            ],
        ]);
    }

    /**
     * Add funds to wallet (for testing purposes).
     */
    public function addFunds(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $wallet = $user->wallets()->findOrFail($id);

        try {
            DB::beginTransaction();

            $wallet->addFunds($request->amount, 'deposit');

            // Create a deposit transaction record
            $wallet->user->sentTransactions()->create([
                'transaction_number' => \App\Models\Transaction::generateTransactionNumber(),
                'reference_number' => \App\Models\Transaction::generateReferenceNumber(),
                'type' => 'deposit',
                'category' => 'cash',
                'sender_id' => $user->id,
                'sender_wallet_id' => $wallet->id,
                'sender_name' => $user->full_name,
                'receiver_id' => $user->id,
                'receiver_wallet_id' => $wallet->id,
                'receiver_name' => $user->full_name,
                'currency_id' => $wallet->currency_id,
                'amount' => $request->amount,
                'net_amount' => $request->amount,
                'status' => 'completed',
                'payment_method' => 'cash',
                'initiated_at' => now(),
                'completed_at' => now(),
                'notes' => $request->description ?? 'Funds added to wallet',
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Funds added successfully',
                'data' => [
                    'new_balance' => $wallet->fresh()->balance,
                    'amount_added' => $request->amount,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to add funds',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get wallet statistics.
     */
    public function statistics(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        $wallet = $user->wallets()->with('currency')->findOrFail($id);

        $stats = [
            'total_received' => $wallet->total_received,
            'total_sent' => $wallet->total_sent,
            'transaction_count' => $wallet->transaction_count,
            'last_transaction_at' => $wallet->last_transaction_at,
            'this_month_sent' => $wallet->sentTransactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
            'this_month_received' => $wallet->receivedTransactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('net_amount'),
            'daily_limit_used' => $wallet->sentTransactions()
                ->whereDate('created_at', today())
                ->sum('amount'),
            'daily_limit_remaining' => max(0, $wallet->daily_limit - $wallet->sentTransactions()
                ->whereDate('created_at', today())
                ->sum('amount')),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'wallet' => $wallet,
                'statistics' => $stats,
            ],
        ]);
    }

    /**
     * Generate crypto address for wallet.
     */
    private function generateCryptoAddress(Currency $currency): string
    {
        // This is a simplified implementation
        // In a real system, you would integrate with actual blockchain libraries
        
        switch ($currency->crypto_network) {
            case 'bitcoin':
                return '1' . str_pad(bin2hex(random_bytes(20)), 40, '0');
            case 'ethereum':
                return '0x' . bin2hex(random_bytes(20));
            default:
                return '0x' . bin2hex(random_bytes(20));
        }
    }

    /**
     * Generate QR code for crypto address.
     */
    private function generateQRCode(string $address): string
    {
        // This would generate an actual QR code image
        // For now, return a placeholder path
        return '/storage/qr_codes/' . md5($address) . '.png';
    }
}
