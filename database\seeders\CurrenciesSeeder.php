<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CurrenciesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'name_ar' => 'الدولار الأمريكي',
                'name_en' => 'US Dollar',
                'code' => 'USD',
                'symbol' => '$',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => true,
                'rate_to_usd' => 1.00000000,
                'decimal_places' => 2,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['USA', 'ECU', 'PAN', 'SLV'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 1000000,
            ],
            [
                'name_ar' => 'الريال السعودي',
                'name_en' => 'Saudi Riyal',
                'code' => 'SAR',
                'symbol' => 'ر.س',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 3.75000000,
                'decimal_places' => 2,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['SAU'],
                'min_transfer_amount' => 10,
                'max_transfer_amount' => 500000,
            ],
            [
                'name_ar' => 'الدرهم الإماراتي',
                'name_en' => 'UAE Dirham',
                'code' => 'AED',
                'symbol' => 'د.إ',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 3.67000000,
                'decimal_places' => 2,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['ARE'],
                'min_transfer_amount' => 10,
                'max_transfer_amount' => 1000000,
            ],
            [
                'name_ar' => 'الجنيه الإسترليني',
                'name_en' => 'British Pound',
                'code' => 'GBP',
                'symbol' => '£',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.79000000,
                'decimal_places' => 2,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['GBR'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 750000,
            ],
            [
                'name_ar' => 'اليورو',
                'name_en' => 'Euro',
                'code' => 'EUR',
                'symbol' => '€',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.85000000,
                'decimal_places' => 2,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['DEU', 'FRA', 'ITA', 'ESP', 'NLD'],
                'min_transfer_amount' => 1,
                'max_transfer_amount' => 500000,
            ],
            [
                'name_ar' => 'الجنيه المصري',
                'name_en' => 'Egyptian Pound',
                'code' => 'EGP',
                'symbol' => 'ج.م',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 30.90000000,
                'decimal_places' => 2,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['EGY'],
                'min_transfer_amount' => 50,
                'max_transfer_amount' => 100000,
            ],
            [
                'name_ar' => 'الدينار الأردني',
                'name_en' => 'Jordanian Dinar',
                'code' => 'JOD',
                'symbol' => 'د.أ',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.71000000,
                'decimal_places' => 3,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['JOR'],
                'min_transfer_amount' => 25,
                'max_transfer_amount' => 200000,
            ],
            [
                'name_ar' => 'الدينار الكويتي',
                'name_en' => 'Kuwaiti Dinar',
                'code' => 'KWD',
                'symbol' => 'د.ك',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.30000000,
                'decimal_places' => 3,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['KWT'],
                'min_transfer_amount' => 5,
                'max_transfer_amount' => 300000,
            ],
            [
                'name_ar' => 'الريال القطري',
                'name_en' => 'Qatari Riyal',
                'code' => 'QAR',
                'symbol' => 'ر.ق',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 3.64000000,
                'decimal_places' => 2,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['QAT'],
                'min_transfer_amount' => 10,
                'max_transfer_amount' => 400000,
            ],
            [
                'name_ar' => 'الدينار البحريني',
                'name_en' => 'Bahraini Dinar',
                'code' => 'BHD',
                'symbol' => 'د.ب',
                'is_crypto' => false,
                'is_active' => true,
                'is_base_currency' => false,
                'rate_to_usd' => 0.38000000,
                'decimal_places' => 3,
                'crypto_network' => null,
                'contract_address' => null,
                'supported_countries' => ['BHR'],
                'min_transfer_amount' => 5,
                'max_transfer_amount' => 250000,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::updateOrCreate(
                ['code' => $currency['code']],
                $currency
            );
        }
    }
}
