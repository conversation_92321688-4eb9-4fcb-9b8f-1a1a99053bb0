<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blockchain_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_id')->constrained()->onDelete('cascade');
            $table->string('blockchain_network'); // ethereum, bitcoin, polygon, etc.
            $table->string('transaction_hash')->unique();
            $table->string('block_hash')->nullable();
            $table->bigInteger('block_number')->nullable();
            $table->string('from_address');
            $table->string('to_address');
            $table->decimal('amount', 30, 18); // High precision for crypto
            $table->decimal('gas_price', 30, 18)->nullable();
            $table->bigInteger('gas_used')->nullable();
            $table->decimal('transaction_fee', 30, 18)->default(0);
            $table->integer('confirmations')->default(0);
            $table->enum('status', ['pending', 'confirmed', 'failed', 'dropped'])->default('pending');
            $table->string('contract_address')->nullable(); // For token transfers
            $table->json('raw_transaction')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('blockchain_timestamp')->nullable();
            $table->timestamps();
            
            $table->index(['transaction_hash', 'blockchain_network']);
            $table->index(['status', 'confirmations']);
            $table->index(['from_address', 'to_address']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blockchain_transactions');
    }
};
