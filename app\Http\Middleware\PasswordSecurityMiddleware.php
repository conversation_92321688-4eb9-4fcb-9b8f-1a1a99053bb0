<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class PasswordSecurityMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check password on registration and password change
        if ($this->shouldValidatePassword($request)) {
            $password = $request->input('password');
            
            if ($password && !$this->isPasswordSecure($password)) {
                return response()->json([
                    'error' => 'Password does not meet security requirements',
                    'requirements' => [
                        'minimum_length' => 8,
                        'must_contain' => [
                            'uppercase_letter',
                            'lowercase_letter', 
                            'number',
                            'special_character'
                        ]
                    ]
                ], 422);
            }
        }

        return $next($request);
    }

    /**
     * Check if we should validate password for this request
     */
    private function shouldValidatePassword(Request $request): bool
    {
        $routes = [
            'register',
            'password.update',
            'auth.change-password',
            'user.change-password'
        ];

        return in_array($request->route()?->getName(), $routes) ||
               $request->has('password') && $request->isMethod('POST');
    }

    /**
     * Check if password meets security requirements
     */
    private function isPasswordSecure(string $password): bool
    {
        $minLength = config('app.security.password_min_length', 8);
        
        // Check minimum length
        if (strlen($password) < $minLength) {
            return false;
        }

        // Check for uppercase letter
        if (!preg_match('/[A-Z]/', $password)) {
            return false;
        }

        // Check for lowercase letter
        if (!preg_match('/[a-z]/', $password)) {
            return false;
        }

        // Check for number
        if (!preg_match('/[0-9]/', $password)) {
            return false;
        }

        // Check for special character
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            return false;
        }

        // Check against common passwords
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];

        if (in_array(strtolower($password), $commonPasswords)) {
            return false;
        }

        return true;
    }
}
