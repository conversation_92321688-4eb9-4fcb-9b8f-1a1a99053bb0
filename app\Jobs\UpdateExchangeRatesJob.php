<?php

namespace App\Jobs;

use App\Models\Currency;
use App\Models\ExchangeRate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class UpdateExchangeRatesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $currencyCodes;
    protected string $source;

    /**
     * Create a new job instance.
     */
    public function __construct(array $currencyCodes = [], string $source = 'external_api')
    {
        $this->currencyCodes = $currencyCodes;
        $this->source = $source;
        
        $this->onQueue('exchange-rates');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting exchange rates update', [
                'source' => $this->source,
                'currencies' => $this->currencyCodes,
            ]);

            $currencies = $this->getCurrencies();
            $rates = $this->fetchExchangeRates($currencies);
            $updated = $this->updateRates($rates);

            Log::info('Exchange rates update completed', [
                'source' => $this->source,
                'currencies_processed' => count($currencies),
                'rates_updated' => $updated,
            ]);

            // Clear cache
            Cache::forget('exchange_rates_all');
            Cache::forget('real_time_rates');

        } catch (\Exception $e) {
            Log::error('Exchange rates update failed', [
                'source' => $this->source,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * Get currencies to update.
     */
    private function getCurrencies(): array
    {
        if (!empty($this->currencyCodes)) {
            return Currency::whereIn('code', $this->currencyCodes)
                ->where('is_active', true)
                ->get()
                ->toArray();
        }

        return Currency::where('is_active', true)
            ->orderBy('code')
            ->get()
            ->toArray();
    }

    /**
     * Fetch exchange rates from external API.
     */
    private function fetchExchangeRates(array $currencies): array
    {
        $rates = [];

        switch ($this->source) {
            case 'fixer_io':
                $rates = $this->fetchFromFixerIO($currencies);
                break;
            case 'currency_api':
                $rates = $this->fetchFromCurrencyAPI($currencies);
                break;
            case 'crypto_api':
                $rates = $this->fetchCryptoRates($currencies);
                break;
            default:
                $rates = $this->generateMockRates($currencies);
        }

        return $rates;
    }

    /**
     * Fetch rates from Fixer.io API.
     */
    private function fetchFromFixerIO(array $currencies): array
    {
        $apiKey = config('financial.exchange_rates.api_key');
        if (!$apiKey) {
            throw new \Exception('Fixer.io API key not configured');
        }

        $symbols = collect($currencies)->pluck('code')->implode(',');
        
        $response = Http::timeout(30)->get('http://data.fixer.io/api/latest', [
            'access_key' => $apiKey,
            'symbols' => $symbols,
            'format' => 1,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch rates from Fixer.io: ' . $response->body());
        }

        $data = $response->json();
        
        if (!$data['success']) {
            throw new \Exception('Fixer.io API error: ' . ($data['error']['info'] ?? 'Unknown error'));
        }

        return $this->processFixerIOResponse($data, $currencies);
    }

    /**
     * Process Fixer.io API response.
     */
    private function processFixerIOResponse(array $data, array $currencies): array
    {
        $rates = [];
        $baseCurrency = $data['base']; // Usually EUR
        $exchangeRates = $data['rates'];

        foreach ($currencies as $fromCurrency) {
            foreach ($currencies as $toCurrency) {
                if ($fromCurrency['code'] === $toCurrency['code']) {
                    continue;
                }

                $rate = $this->calculateCrossRate(
                    $fromCurrency['code'],
                    $toCurrency['code'],
                    $baseCurrency,
                    $exchangeRates
                );

                if ($rate) {
                    $rates[] = [
                        'from_currency_id' => $fromCurrency['id'],
                        'to_currency_id' => $toCurrency['id'],
                        'rate' => $rate,
                        'buy_rate' => $rate * 1.005, // Add 0.5% spread
                        'sell_rate' => $rate * 0.995,
                        'spread' => 0.01,
                        'source' => 'fixer_io',
                        'last_updated' => now(),
                    ];
                }
            }
        }

        return $rates;
    }

    /**
     * Calculate cross rate between two currencies.
     */
    private function calculateCrossRate(string $from, string $to, string $base, array $rates): ?float
    {
        if ($from === $base) {
            return $rates[$to] ?? null;
        }

        if ($to === $base) {
            return isset($rates[$from]) ? 1 / $rates[$from] : null;
        }

        if (isset($rates[$from]) && isset($rates[$to])) {
            return $rates[$to] / $rates[$from];
        }

        return null;
    }

    /**
     * Fetch cryptocurrency rates.
     */
    private function fetchCryptoRates(array $currencies): array
    {
        $cryptoCurrencies = array_filter($currencies, function ($currency) {
            return $currency['is_crypto'];
        });

        if (empty($cryptoCurrencies)) {
            return [];
        }

        // Mock crypto rates - in real implementation, use CoinGecko or similar
        $rates = [];
        $cryptoPrices = [
            'BTC' => 45000,
            'ETH' => 3000,
            'USDT' => 1,
            'BNB' => 300,
        ];

        foreach ($cryptoCurrencies as $crypto) {
            $price = $cryptoPrices[$crypto['code']] ?? rand(1, 1000);
            
            // Create rate to USD
            $usdCurrency = array_first($currencies, function ($c) {
                return $c['code'] === 'USD';
            });

            if ($usdCurrency) {
                $rates[] = [
                    'from_currency_id' => $crypto['id'],
                    'to_currency_id' => $usdCurrency['id'],
                    'rate' => $price,
                    'buy_rate' => $price * 1.01,
                    'sell_rate' => $price * 0.99,
                    'spread' => 0.02,
                    'source' => 'crypto_api',
                    'last_updated' => now(),
                ];

                $rates[] = [
                    'from_currency_id' => $usdCurrency['id'],
                    'to_currency_id' => $crypto['id'],
                    'rate' => 1 / $price,
                    'buy_rate' => (1 / $price) * 1.01,
                    'sell_rate' => (1 / $price) * 0.99,
                    'spread' => 0.02,
                    'source' => 'crypto_api',
                    'last_updated' => now(),
                ];
            }
        }

        return $rates;
    }

    /**
     * Generate mock rates for testing.
     */
    private function generateMockRates(array $currencies): array
    {
        $rates = [];
        $baseRates = [
            'USD' => 1.0,
            'EUR' => 0.85,
            'GBP' => 0.73,
            'SAR' => 3.75,
            'AED' => 3.67,
            'EGP' => 30.5,
            'BTC' => 0.000022,
            'ETH' => 0.00033,
            'USDT' => 1.0,
        ];

        foreach ($currencies as $fromCurrency) {
            foreach ($currencies as $toCurrency) {
                if ($fromCurrency['code'] === $toCurrency['code']) {
                    continue;
                }

                $fromRate = $baseRates[$fromCurrency['code']] ?? 1.0;
                $toRate = $baseRates[$toCurrency['code']] ?? 1.0;
                $rate = $toRate / $fromRate;

                // Add some random variation (±2%)
                $variation = (rand(-200, 200) / 10000);
                $rate = $rate * (1 + $variation);

                $rates[] = [
                    'from_currency_id' => $fromCurrency['id'],
                    'to_currency_id' => $toCurrency['id'],
                    'rate' => $rate,
                    'buy_rate' => $rate * 1.005,
                    'sell_rate' => $rate * 0.995,
                    'spread' => 0.01,
                    'source' => 'mock_data',
                    'last_updated' => now(),
                ];
            }
        }

        return $rates;
    }

    /**
     * Update exchange rates in database.
     */
    private function updateRates(array $rates): int
    {
        $updated = 0;

        foreach ($rates as $rateData) {
            try {
                ExchangeRate::updateOrCreate(
                    [
                        'from_currency_id' => $rateData['from_currency_id'],
                        'to_currency_id' => $rateData['to_currency_id'],
                    ],
                    array_merge($rateData, ['is_active' => true])
                );
                $updated++;
            } catch (\Exception $e) {
                Log::warning('Failed to update exchange rate', [
                    'from_currency_id' => $rateData['from_currency_id'],
                    'to_currency_id' => $rateData['to_currency_id'],
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $updated;
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Exchange rates update job failed permanently', [
            'source' => $this->source,
            'currencies' => $this->currencyCodes,
            'error' => $exception->getMessage(),
        ]);
    }

    /**
     * Get the number of times the job may be attempted.
     */
    public function tries(): int
    {
        return 3;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [60, 300, 900]; // 1 minute, 5 minutes, 15 minutes
    }
}
