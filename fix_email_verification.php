<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

try {
    echo "🔧 Fixing Email Verification Issue\n";
    echo "==================================\n\n";
    
    // Mark all existing users as email verified for demo purposes
    $users = User::whereNull('email_verified_at')->get();
    
    foreach ($users as $user) {
        $user->email_verified_at = now();
        $user->save();
        echo "✅ Marked {$user->email} as verified\n";
    }
    
    echo "\n📊 Summary:\n";
    echo "- Total users verified: " . $users->count() . "\n";
    echo "- All users now have verified emails\n";
    
    echo "\n🌐 Available Pages:\n";
    echo "==================\n";
    echo "🏠 Home: http://localhost:8000\n";
    echo "🔐 Login: http://localhost:8000/login\n";
    echo "📊 Dashboard: http://localhost:8000/dashboard\n";
    echo "👨‍💼 Admin: http://localhost:8000/admin/dashboard\n";
    echo "📧 Email Verify: http://localhost:8000/email/verify\n";
    
    echo "\n✅ Email verification issue fixed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
