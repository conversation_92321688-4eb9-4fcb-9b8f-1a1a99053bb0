<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Services\KYCService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class DocumentController extends Controller
{
    protected KYCService $kycService;

    public function __construct(KYCService $kycService)
    {
        $this->kycService = $kycService;
    }

    /**
     * Get user documents.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $documents = $user->documents()
                         ->orderBy('created_at', 'desc')
                         ->get()
                         ->map(function ($document) {
                             return $document->formatted_data;
                         });

        return response()->json([
            'success' => true,
            'data' => $documents,
            'kyc_status' => $this->kycService->getKYCStatus($user),
        ]);
    }

    /**
     * Upload document.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'document' => 'required|file|mimes:jpeg,png,jpg,pdf|max:10240', // 10MB
            'type' => 'required|string|in:national_id,passport,driving_license,utility_bill,bank_statement,selfie,address_proof,income_proof,business_license,tax_certificate,other',
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $user = $request->user();
            $file = $request->file('document');
            $type = $request->input('type');

            // Check if document type already exists and is verified
            $existingDocument = $user->documents()
                                   ->where('type', $type)
                                   ->where('verification_status', 'verified')
                                   ->first();

            if ($existingDocument) {
                return response()->json([
                    'success' => false,
                    'message' => 'يوجد مستند من هذا النوع مُتحقق منه بالفعل',
                ], 422);
            }

            $document = $this->kycService->uploadDocument(
                $user,
                $file,
                $type,
                [
                    'description' => $request->input('description'),
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'تم رفع المستند بنجاح',
                'data' => $document->formatted_data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في رفع المستند: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific document.
     */
    public function show(Request $request, int $id): JsonResponse
    {
        $user = $request->user();
        
        $document = $user->documents()->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $document->formatted_data,
        ]);
    }

    /**
     * Update document.
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $document = $user->documents()->findOrFail($id);

        // Only allow updates if document is not verified
        if ($document->verification_status === 'verified') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل مستند مُتحقق منه',
            ], 422);
        }

        $document->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المستند بنجاح',
            'data' => $document->fresh()->formatted_data,
        ]);
    }

    /**
     * Delete document.
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        $user = $request->user();
        $document = $user->documents()->findOrFail($id);

        // Only allow deletion if document is not verified
        if ($document->verification_status === 'verified') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف مستند مُتحقق منه',
            ], 422);
        }

        $document->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف المستند بنجاح',
        ]);
    }

    /**
     * Download document.
     */
    public function download(Request $request, int $id)
    {
        $user = $request->user();
        $document = $user->documents()->findOrFail($id);

        if (!Storage::disk('private')->exists($document->file_path)) {
            return response()->json([
                'success' => false,
                'message' => 'الملف غير موجود',
            ], 404);
        }

        return Storage::disk('private')->download(
            $document->file_path,
            $document->file_name
        );
    }

    /**
     * Preview document (for images only).
     */
    public function preview(Request $request, int $id)
    {
        $user = $request->user();
        $document = $user->documents()->findOrFail($id);

        // Only allow preview for image files
        if (!str_starts_with($document->mime_type, 'image/')) {
            return response()->json([
                'success' => false,
                'message' => 'معاينة غير متاحة لهذا النوع من الملفات',
            ], 422);
        }

        if (!Storage::disk('private')->exists($document->file_path)) {
            return response()->json([
                'success' => false,
                'message' => 'الملف غير موجود',
            ], 404);
        }

        $file = Storage::disk('private')->get($document->file_path);
        
        return response($file, 200)
            ->header('Content-Type', $document->mime_type)
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * Get document types and requirements.
     */
    public function types(Request $request): JsonResponse
    {
        $documentTypes = [
            'national_id' => [
                'name_ar' => 'الهوية الوطنية',
                'name_en' => 'National ID',
                'required' => true,
                'description_ar' => 'صورة واضحة للهوية الوطنية من الوجهين',
                'description_en' => 'Clear photo of national ID (both sides)',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG', 'PDF'],
            ],
            'passport' => [
                'name_ar' => 'جواز السفر',
                'name_en' => 'Passport',
                'required' => false,
                'description_ar' => 'صورة واضحة لجواز السفر',
                'description_en' => 'Clear photo of passport',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG', 'PDF'],
            ],
            'selfie' => [
                'name_ar' => 'صورة شخصية',
                'name_en' => 'Selfie',
                'required' => true,
                'description_ar' => 'صورة شخصية واضحة مع حمل الهوية',
                'description_en' => 'Clear selfie holding your ID',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG'],
            ],
            'address_proof' => [
                'name_ar' => 'إثبات العنوان',
                'name_en' => 'Address Proof',
                'required' => true,
                'description_ar' => 'فاتورة خدمات أو كشف حساب بنكي حديث',
                'description_en' => 'Recent utility bill or bank statement',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG', 'PDF'],
            ],
            'utility_bill' => [
                'name_ar' => 'فاتورة الخدمات',
                'name_en' => 'Utility Bill',
                'required' => false,
                'description_ar' => 'فاتورة كهرباء أو ماء أو غاز حديثة',
                'description_en' => 'Recent electricity, water, or gas bill',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG', 'PDF'],
            ],
            'bank_statement' => [
                'name_ar' => 'كشف حساب بنكي',
                'name_en' => 'Bank Statement',
                'required' => false,
                'description_ar' => 'كشف حساب بنكي من آخر 3 أشهر',
                'description_en' => 'Bank statement from last 3 months',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG', 'PDF'],
            ],
            'income_proof' => [
                'name_ar' => 'إثبات الدخل',
                'name_en' => 'Income Proof',
                'required' => false,
                'description_ar' => 'شهادة راتب أو إقرار ضريبي',
                'description_en' => 'Salary certificate or tax declaration',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG', 'PDF'],
            ],
            'business_license' => [
                'name_ar' => 'رخصة تجارية',
                'name_en' => 'Business License',
                'required' => false,
                'description_ar' => 'رخصة تجارية سارية للشركات',
                'description_en' => 'Valid business license for companies',
                'max_size' => '10MB',
                'formats' => ['JPEG', 'PNG', 'PDF'],
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $documentTypes,
        ]);
    }

    /**
     * Get KYC status and progress.
     */
    public function kycStatus(Request $request): JsonResponse
    {
        $user = $request->user();
        $status = $this->kycService->getKYCStatus($user);

        return response()->json([
            'success' => true,
            'data' => $status,
        ]);
    }
}
