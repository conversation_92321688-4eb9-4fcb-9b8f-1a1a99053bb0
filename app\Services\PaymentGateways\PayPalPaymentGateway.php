<?php

namespace App\Services\PaymentGateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\Transaction;
use App\Services\AuditLogService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class PayPalPaymentGateway implements PaymentGatewayInterface
{
    private string $clientId;
    private string $clientSecret;
    private string $baseUrl;
    private array $config;
    private bool $sandbox;

    public function __construct()
    {
        $this->clientId = config('payment_gateways.paypal.client_id');
        $this->clientSecret = config('payment_gateways.paypal.client_secret');
        $this->sandbox = config('payment_gateways.paypal.sandbox', true);
        $this->baseUrl = $this->sandbox 
            ? 'https://api-m.sandbox.paypal.com'
            : 'https://api-m.paypal.com';
        $this->config = config('payment_gateways.paypal', []);
    }

    /**
     * Get access token
     */
    private function getAccessToken(): ?string
    {
        $cacheKey = 'paypal_access_token';
        
        return Cache::remember($cacheKey, 3600, function () {
            try {
                $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
                    ->asForm()
                    ->post($this->baseUrl . '/v1/oauth2/token', [
                        'grant_type' => 'client_credentials',
                    ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $data['access_token'];
                }

                Log::error('PayPal access token request failed', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                ]);

                return null;

            } catch (\Exception $e) {
                Log::error('PayPal access token exception', [
                    'error' => $e->getMessage(),
                ]);
                return null;
            }
        });
    }

    /**
     * Initialize payment
     */
    public function initializePayment(array $paymentData): array
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get PayPal access token',
                ];
            }

            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'amount' => [
                            'currency_code' => $paymentData['currency'],
                            'value' => number_format($paymentData['amount'], 2, '.', ''),
                        ],
                        'description' => $paymentData['description'] ?? 'Money Transfer',
                        'custom_id' => $paymentData['transaction_id'] ?? '',
                    ]
                ],
                'application_context' => [
                    'return_url' => $paymentData['return_url'] ?? config('app.url') . '/payment/success',
                    'cancel_url' => $paymentData['cancel_url'] ?? config('app.url') . '/payment/cancel',
                    'brand_name' => config('app.name'),
                    'landing_page' => 'BILLING',
                    'user_action' => 'PAY_NOW',
                ],
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post($this->baseUrl . '/v2/checkout/orders', $orderData);

            if ($response->successful()) {
                $data = $response->json();
                $approvalUrl = collect($data['links'])->firstWhere('rel', 'approve')['href'] ?? '';

                return [
                    'success' => true,
                    'payment_id' => $data['id'],
                    'status' => $data['status'],
                    'approval_url' => $approvalUrl,
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to create PayPal order',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayPal payment initialization failed', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process payment
     */
    public function processPayment(string $paymentId, array $paymentData): array
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get PayPal access token',
                ];
            }

            // Capture the order
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post($this->baseUrl . "/v2/checkout/orders/{$paymentId}/capture");

            if ($response->successful()) {
                $data = $response->json();
                $captureData = $data['purchase_units'][0]['payments']['captures'][0] ?? [];

                return [
                    'success' => true,
                    'payment_id' => $data['id'],
                    'capture_id' => $captureData['id'] ?? '',
                    'status' => $data['status'],
                    'amount' => (float) ($captureData['amount']['value'] ?? 0),
                    'currency' => $captureData['amount']['currency_code'] ?? '',
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'PayPal payment capture failed',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayPal payment processing failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process payment through the gateway
     */
    public function processPayment(Transaction $transaction, array $paymentData): array
    {
        return $this->processPayment($transaction->gateway_transaction_id, $paymentData);
    }

    /**
     * Verify payment
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get PayPal access token',
                ];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get($this->baseUrl . "/v2/checkout/orders/{$paymentId}");

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'payment_id' => $data['id'],
                    'status' => $data['status'],
                    'verified' => $data['status'] === 'COMPLETED',
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'PayPal payment verification failed',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayPal payment verification failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Refund payment
     */
    public function refundPayment(string $transactionId, float $amount, string $currency = 'USD'): array
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get PayPal access token',
                ];
            }

            $refundData = [
                'amount' => [
                    'value' => number_format($amount, 2, '.', ''),
                    'currency_code' => $currency,
                ],
                'note_to_payer' => 'Refund for money transfer',
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'PayPal-Request-Id' => uniqid(),
            ])->post($this->baseUrl . "/v2/payments/captures/{$transactionId}/refund", $refundData);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'refund_id' => $data['id'],
                    'status' => $data['status'],
                    'amount' => (float) $data['amount']['value'],
                    'currency' => $data['amount']['currency_code'],
                    'gateway_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'PayPal refund failed',
                'gateway_response' => $response->json(),
            ];

        } catch (\Exception $e) {
            Log::error('PayPal refund failed', [
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $transactionId): array
    {
        return $this->verifyPayment($transactionId);
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhook(array $headers, string $payload): bool
    {
        try {
            // PayPal webhook validation would require certificate verification
            // This is a simplified version
            $webhookId = config('payment_gateways.paypal.webhook_id');
            $authAlgo = $headers['paypal-auth-algo'] ?? '';
            $transmission = $headers['paypal-transmission-id'] ?? '';
            $certId = $headers['paypal-cert-id'] ?? '';
            $signature = $headers['paypal-transmission-sig'] ?? '';
            $timestamp = $headers['paypal-transmission-time'] ?? '';

            // In a real implementation, you would verify the signature using PayPal's public key
            return !empty($signature) && !empty($timestamp);

        } catch (\Exception $e) {
            Log::error('PayPal webhook validation failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        return [
            'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK',
            'PLN', 'CZK', 'HUF', 'ILS', 'MXN', 'BRL', 'TWD', 'THB', 'SGD', 'HKD',
            'NZD', 'PHP', 'MYR', 'INR', 'KRW', 'RUB', 'CNY',
        ];
    }

    /**
     * Get gateway configuration
     */
    public function getConfig(): array
    {
        return [
            'name' => 'PayPal',
            'version' => 'v2',
            'sandbox' => $this->sandbox,
            'supported_methods' => ['paypal', 'card'],
            'supported_currencies' => $this->getSupportedCurrencies(),
            'supported_countries' => $this->getSupportedCountries(),
            'fees' => $this->config['fees'] ?? [],
            'limits' => $this->getTransactionLimits(),
        ];
    }

    /**
     * Get supported countries
     */
    public function getSupportedCountries(): array
    {
        return [
            'US', 'CA', 'GB', 'AU', 'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK',
            'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU',
            'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'CH', 'NO',
            'IS', 'JP', 'SG', 'HK', 'NZ', 'MX', 'BR', 'MY', 'TH', 'PH', 'IN',
            'KR', 'RU', 'CN', 'IL', 'TW', 'AR', 'CL', 'CO', 'CR', 'DO', 'EC',
            'SV', 'GT', 'HN', 'JM', 'NI', 'PA', 'PY', 'PE', 'UY', 'VE',
        ];
    }

    /**
     * Get payment methods
     */
    public function getPaymentMethods(): array
    {
        return [
            'paypal' => [
                'name' => 'PayPal Balance',
                'types' => ['balance', 'bank'],
                'fees' => '2.9% + fixed fee',
            ],
            'card' => [
                'name' => 'Credit/Debit Card',
                'types' => ['visa', 'mastercard', 'amex', 'discover'],
                'fees' => '2.9% + fixed fee',
            ],
        ];
    }

    /**
     * Calculate fees
     */
    public function calculateFees(float $amount, string $currency, string $method): array
    {
        // PayPal fee structure varies by country and currency
        $feeStructure = [
            'domestic' => ['percentage' => 2.9, 'fixed' => 0.30],
            'international' => ['percentage' => 4.4, 'fixed' => 0.30],
        ];

        $fees = $feeStructure['domestic']; // Simplified
        $percentageFee = ($amount * $fees['percentage']) / 100;
        $totalFee = $percentageFee + $fees['fixed'];

        return [
            'percentage_fee' => round($percentageFee, 2),
            'fixed_fee' => $fees['fixed'],
            'total_fee' => round($totalFee, 2),
            'net_amount' => round($amount - $totalFee, 2),
        ];
    }

    /**
     * Validate payment data
     */
    public function validatePaymentData(array $paymentData): array
    {
        $errors = [];

        if (!isset($paymentData['amount']) || $paymentData['amount'] <= 0) {
            $errors[] = 'Amount must be greater than 0';
        }

        if (!isset($paymentData['currency']) || !in_array($paymentData['currency'], $this->getSupportedCurrencies())) {
            $errors[] = 'Invalid or unsupported currency';
        }

        if (isset($paymentData['amount']) && $paymentData['amount'] > 10000) {
            $errors[] = 'Amount exceeds PayPal limit';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Test connection
     */
    public function testConnection(): array
    {
        try {
            $accessToken = $this->getAccessToken();
            
            if ($accessToken) {
                return [
                    'success' => true,
                    'message' => 'PayPal connection successful',
                    'sandbox' => $this->sandbox,
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to get PayPal access token',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'PayPal connection test failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Handle webhook
     */
    public function handleWebhook(array $webhookData): array
    {
        try {
            $eventType = $webhookData['event_type'] ?? '';
            $resource = $webhookData['resource'] ?? [];

            switch ($eventType) {
                case 'CHECKOUT.ORDER.APPROVED':
                    return $this->handleOrderApproved($resource);
                case 'PAYMENT.CAPTURE.COMPLETED':
                    return $this->handlePaymentCompleted($resource);
                case 'PAYMENT.CAPTURE.DENIED':
                    return $this->handlePaymentDenied($resource);
                default:
                    return ['success' => true, 'message' => 'Event type not handled'];
            }

        } catch (\Exception $e) {
            Log::error('PayPal webhook handling failed', [
                'error' => $e->getMessage(),
                'webhook_data' => $webhookData,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle order approved webhook
     */
    private function handleOrderApproved(array $resource): array
    {
        $orderId = $resource['id'] ?? '';
        
        // Update transaction status
        $transaction = Transaction::where('gateway_transaction_id', $orderId)->first();
        if ($transaction) {
            $transaction->update(['status' => 'processing']);
        }

        return ['success' => true, 'message' => 'Order approved processed'];
    }

    /**
     * Handle payment completed webhook
     */
    private function handlePaymentCompleted(array $resource): array
    {
        $captureId = $resource['id'] ?? '';
        
        // Find transaction by capture ID or order ID
        $transaction = Transaction::where('gateway_capture_id', $captureId)
            ->orWhere('gateway_transaction_id', $resource['supplementary_data']['related_ids']['order_id'] ?? '')
            ->first();

        if ($transaction) {
            $transaction->update([
                'status' => 'completed',
                'gateway_capture_id' => $captureId,
            ]);

            AuditLogService::logUserAction(
                'payment_completed',
                'Payment completed via PayPal webhook',
                ['capture_id' => $captureId],
                $transaction->sender_id
            );
        }

        return ['success' => true, 'message' => 'Payment completed processed'];
    }

    /**
     * Handle payment denied webhook
     */
    private function handlePaymentDenied(array $resource): array
    {
        $captureId = $resource['id'] ?? '';
        
        $transaction = Transaction::where('gateway_capture_id', $captureId)->first();
        if ($transaction) {
            $transaction->update(['status' => 'failed']);

            AuditLogService::logUserAction(
                'payment_denied',
                'Payment denied via PayPal webhook',
                ['capture_id' => $captureId],
                $transaction->sender_id
            );
        }

        return ['success' => true, 'message' => 'Payment denied processed'];
    }

    /**
     * Get transaction limits
     */
    public function getTransactionLimits(): array
    {
        return [
            'min_amount' => 0.01,
            'max_amount' => 10000.00,
            'daily_limit' => 60000.00,
            'monthly_limit' => 500000.00,
        ];
    }

    /**
     * Check if gateway is available
     */
    public function isAvailable(): bool
    {
        return !empty($this->clientId) && !empty($this->clientSecret);
    }

    /**
     * Get gateway name
     */
    public function getName(): string
    {
        return 'PayPal';
    }

    /**
     * Get gateway version
     */
    public function getVersion(): string
    {
        return 'v2';
    }
}
