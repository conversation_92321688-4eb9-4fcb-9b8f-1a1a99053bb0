<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class CountryController extends Controller
{
    /**
     * Get all active countries.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Country::active()->with(['branches' => function ($query) {
            $query->where('is_active', true);
        }]);

        // Filter by transfer support
        if ($request->has('supports_transfers')) {
            $query->where('supports_transfers', $request->boolean('supports_transfers'));
        }

        // Filter by crypto support
        if ($request->has('supports_crypto')) {
            $query->where('supports_crypto', $request->boolean('supports_crypto'));
        }

        // Search by name
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name_ar', 'like', "%{$search}%")
                  ->orWhere('name_en', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        $countries = $query->orderBy('name_en')->get();

        return response()->json([
            'success' => true,
            'data' => $countries,
            'total' => $countries->count(),
        ]);
    }

    /**
     * Get specific country details.
     */
    public function show($id): JsonResponse
    {
        $country = Country::with([
            'branches' => function ($query) {
                $query->where('is_active', true);
            },
            'users' => function ($query) {
                $query->where('status', 'active')->limit(10);
            },
            'sentTransactions' => function ($query) {
                $query->latest()->limit(5);
            },
            'receivedTransactions' => function ($query) {
                $query->latest()->limit(5);
            }
        ])->findOrFail($id);

        // Calculate statistics
        $stats = [
            'total_users' => $country->users()->count(),
            'active_users' => $country->users()->where('status', 'active')->count(),
            'total_branches' => $country->branches()->count(),
            'active_branches' => $country->branches()->where('is_active', true)->count(),
            'total_sent_transactions' => $country->sentTransactions()->count(),
            'total_received_transactions' => $country->receivedTransactions()->count(),
            'sent_volume_today' => $country->sentTransactions()
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->sum('amount'),
            'received_volume_today' => $country->receivedTransactions()
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->sum('net_amount'),
            'sent_volume_this_month' => $country->sentTransactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', 'completed')
                ->sum('amount'),
            'received_volume_this_month' => $country->receivedTransactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', 'completed')
                ->sum('net_amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'country' => $country,
                'statistics' => $stats,
            ],
        ]);
    }

    /**
     * Get countries that support transfers.
     */
    public function supportsTransfers(): JsonResponse
    {
        $countries = Cache::remember('countries_transfers', 3600, function () {
            return Country::supportsTransfers()
                ->active()
                ->orderBy('name_en')
                ->get(['id', 'name_ar', 'name_en', 'code', 'iso2', 'currency_code', 'flag_url']);
        });

        return response()->json([
            'success' => true,
            'data' => $countries,
        ]);
    }

    /**
     * Get countries that support crypto.
     */
    public function supportsCrypto(): JsonResponse
    {
        $countries = Cache::remember('countries_crypto', 3600, function () {
            return Country::supportsCrypto()
                ->active()
                ->orderBy('name_en')
                ->get(['id', 'name_ar', 'name_en', 'code', 'iso2', 'currency_code', 'flag_url']);
        });

        return response()->json([
            'success' => true,
            'data' => $countries,
        ]);
    }

    /**
     * Get transfer corridors (country pairs).
     */
    public function transferCorridors(): JsonResponse
    {
        $corridors = Cache::remember('transfer_corridors', 3600, function () {
            $countries = Country::supportsTransfers()->active()->get();
            $corridors = [];

            foreach ($countries as $fromCountry) {
                foreach ($countries as $toCountry) {
                    if ($fromCountry->id !== $toCountry->id) {
                        $corridors[] = [
                            'from_country' => [
                                'id' => $fromCountry->id,
                                'name' => $fromCountry->name,
                                'code' => $fromCountry->code,
                                'currency_code' => $fromCountry->currency_code,
                            ],
                            'to_country' => [
                                'id' => $toCountry->id,
                                'name' => $toCountry->name,
                                'code' => $toCountry->code,
                                'currency_code' => $toCountry->currency_code,
                            ],
                            'commission_rate' => max($fromCountry->commission_rate, $toCountry->commission_rate),
                            'estimated_time' => $this->calculateEstimatedTime($fromCountry, $toCountry),
                        ];
                    }
                }
            }

            return $corridors;
        });

        return response()->json([
            'success' => true,
            'data' => $corridors,
            'total_corridors' => count($corridors),
        ]);
    }

    /**
     * Get country regulations and requirements.
     */
    public function regulations($id): JsonResponse
    {
        $country = Country::findOrFail($id);

        $regulations = [
            'country' => $country->only(['id', 'name_ar', 'name_en', 'code']),
            'max_transfer_amount' => $country->max_transfer_amount,
            'min_transfer_amount' => $country->min_transfer_amount,
            'commission_rate' => $country->commission_rate,
            'supported_payment_methods' => $country->supported_payment_methods,
            'required_documents' => $country->required_documents,
            'regulations' => $country->regulations,
            'supports_crypto' => $country->supports_crypto,
            'kyc_required' => true, // Always required
            'aml_checks' => true, // Always required
            'daily_limits' => [
                'individual' => $country->max_transfer_amount * 0.1,
                'business' => $country->max_transfer_amount * 0.5,
            ],
            'monthly_limits' => [
                'individual' => $country->max_transfer_amount,
                'business' => $country->max_transfer_amount * 10,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $regulations,
        ]);
    }

    /**
     * Get popular destinations from a country.
     */
    public function popularDestinations($id): JsonResponse
    {
        $country = Country::findOrFail($id);

        // Get most popular destination countries based on transaction volume
        $destinations = $country->sentTransactions()
            ->selectRaw('receiver_country_id, COUNT(*) as transaction_count, SUM(amount) as total_volume')
            ->where('status', 'completed')
            ->where('created_at', '>=', now()->subMonths(3))
            ->groupBy('receiver_country_id')
            ->orderBy('transaction_count', 'desc')
            ->limit(10)
            ->with('receiverCountry')
            ->get()
            ->map(function ($transaction) {
                return [
                    'country' => $transaction->receiverCountry,
                    'transaction_count' => $transaction->transaction_count,
                    'total_volume' => $transaction->total_volume,
                    'average_amount' => $transaction->total_volume / $transaction->transaction_count,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'source_country' => $country->only(['id', 'name_ar', 'name_en', 'code']),
                'destinations' => $destinations,
                'period' => 'last_3_months',
            ],
        ]);
    }

    /**
     * Calculate estimated transfer time between countries.
     */
    private function calculateEstimatedTime(Country $fromCountry, Country $toCountry): string
    {
        // Simple logic for estimation
        $baseTime = 30; // 30 minutes base time

        // Add time based on distance (simplified)
        if ($fromCountry->code === $toCountry->code) {
            return 'Instant';
        }

        // Same region (simplified logic)
        $middleEastCountries = ['SAU', 'ARE', 'EGY', 'JOR', 'LBN', 'KWT', 'QAT', 'BHR', 'OMN'];
        $europeanCountries = ['GBR', 'FRA', 'DEU', 'ITA', 'ESP', 'NLD', 'BEL'];

        if (
            (in_array($fromCountry->code, $middleEastCountries) && in_array($toCountry->code, $middleEastCountries)) ||
            (in_array($fromCountry->code, $europeanCountries) && in_array($toCountry->code, $europeanCountries))
        ) {
            $baseTime += 30; // 1 hour total
        } else {
            $baseTime += 120; // 2.5 hours total
        }

        if ($baseTime <= 60) {
            return $baseTime . ' minutes';
        } else {
            $hours = intval($baseTime / 60);
            $minutes = $baseTime % 60;
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }
    }
}
