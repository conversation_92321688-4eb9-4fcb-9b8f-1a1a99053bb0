<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class ComplianceCheck extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'transaction_id',
        'check_type',
        'provider',
        'reference_id',
        'status',
        'result',
        'risk_score',
        'matches',
        'request_data',
        'response_data',
        'notes',
        'reviewed_by',
        'reviewed_at',
        'expires_at',
        'requires_manual_review',
        'metadata',
    ];

    protected $casts = [
        'risk_score' => 'decimal:4',
        'matches' => 'array',
        'request_data' => 'array',
        'response_data' => 'array',
        'reviewed_at' => 'datetime',
        'expires_at' => 'datetime',
        'requires_manual_review' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get the user being checked
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transaction being checked
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Get the reviewer
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope to get pending checks
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get completed checks
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get failed checks
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get checks requiring manual review
     */
    public function scopeRequiringReview(Builder $query): Builder
    {
        return $query->where('requires_manual_review', true)
            ->whereIn('status', ['completed', 'manual_review']);
    }

    /**
     * Scope to filter by check type
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('check_type', $type);
    }

    /**
     * Scope to filter by result
     */
    public function scopeByResult(Builder $query, string $result): Builder
    {
        return $query->where('result', $result);
    }

    /**
     * Scope to get high risk checks
     */
    public function scopeHighRisk(Builder $query, float $threshold = 0.7): Builder
    {
        return $query->where('risk_score', '>=', $threshold);
    }

    /**
     * Create AML check
     */
    public static function createAmlCheck(
        int $userId,
        ?int $transactionId = null,
        string $provider = 'internal'
    ): self {
        return self::create([
            'user_id' => $userId,
            'transaction_id' => $transactionId,
            'check_type' => 'aml',
            'provider' => $provider,
            'status' => 'pending',
            'metadata' => [
                'created_by' => 'system',
                'priority' => 'normal',
            ],
        ]);
    }

    /**
     * Create sanctions check
     */
    public static function createSanctionsCheck(
        int $userId,
        ?int $transactionId = null,
        string $provider = 'internal'
    ): self {
        return self::create([
            'user_id' => $userId,
            'transaction_id' => $transactionId,
            'check_type' => 'sanctions',
            'provider' => $provider,
            'status' => 'pending',
            'metadata' => [
                'created_by' => 'system',
                'priority' => 'high',
            ],
        ]);
    }

    /**
     * Create PEP (Politically Exposed Person) check
     */
    public static function createPepCheck(
        int $userId,
        ?int $transactionId = null,
        string $provider = 'internal'
    ): self {
        return self::create([
            'user_id' => $userId,
            'transaction_id' => $transactionId,
            'check_type' => 'pep',
            'provider' => $provider,
            'status' => 'pending',
            'metadata' => [
                'created_by' => 'system',
                'priority' => 'high',
            ],
        ]);
    }

    /**
     * Create adverse media check
     */
    public static function createAdverseMediaCheck(
        int $userId,
        ?int $transactionId = null,
        string $provider = 'internal'
    ): self {
        return self::create([
            'user_id' => $userId,
            'transaction_id' => $transactionId,
            'check_type' => 'adverse_media',
            'provider' => $provider,
            'status' => 'pending',
            'metadata' => [
                'created_by' => 'system',
                'priority' => 'normal',
            ],
        ]);
    }

    /**
     * Process compliance check
     */
    public function process(): array
    {
        $this->update(['status' => 'processing']);

        try {
            $result = $this->performCheck();
            
            $this->update([
                'status' => 'completed',
                'result' => $result['result'],
                'risk_score' => $result['risk_score'],
                'matches' => $result['matches'] ?? [],
                'response_data' => $result['response_data'] ?? [],
                'requires_manual_review' => $result['requires_manual_review'] ?? false,
            ]);

            return [
                'success' => true,
                'result' => $result,
            ];

        } catch (\Exception $e) {
            $this->update([
                'status' => 'failed',
                'metadata' => array_merge($this->metadata ?? [], [
                    'error' => $e->getMessage(),
                    'failed_at' => now()->toISOString(),
                ]),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Perform the actual compliance check
     */
    protected function performCheck(): array
    {
        switch ($this->check_type) {
            case 'aml':
                return $this->performAmlCheck();
            case 'sanctions':
                return $this->performSanctionsCheck();
            case 'pep':
                return $this->performPepCheck();
            case 'adverse_media':
                return $this->performAdverseMediaCheck();
            default:
                throw new \Exception("Unknown check type: {$this->check_type}");
        }
    }

    /**
     * Perform AML check
     */
    protected function performAmlCheck(): array
    {
        // This would integrate with real AML providers like World-Check, Dow Jones, etc.
        // For now, return mock data
        $user = $this->user;
        $riskScore = rand(0, 100) / 100;
        
        return [
            'result' => $riskScore > 0.7 ? 'match' : 'clear',
            'risk_score' => $riskScore,
            'matches' => $riskScore > 0.7 ? [
                [
                    'name' => $user->name,
                    'confidence' => $riskScore,
                    'reason' => 'Name similarity',
                    'source' => 'AML Database',
                ]
            ] : [],
            'requires_manual_review' => $riskScore > 0.5,
            'response_data' => [
                'provider_reference' => 'AML_' . uniqid(),
                'checked_at' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Perform sanctions check
     */
    protected function performSanctionsCheck(): array
    {
        // This would check against OFAC, UN, EU sanctions lists
        $user = $this->user;
        $riskScore = rand(0, 30) / 100; // Lower probability for sanctions
        
        return [
            'result' => $riskScore > 0.2 ? 'match' : 'clear',
            'risk_score' => $riskScore,
            'matches' => $riskScore > 0.2 ? [
                [
                    'name' => $user->name,
                    'confidence' => $riskScore,
                    'list' => 'OFAC SDN',
                    'reason' => 'Potential match',
                ]
            ] : [],
            'requires_manual_review' => $riskScore > 0.1,
            'response_data' => [
                'lists_checked' => ['OFAC', 'UN', 'EU'],
                'checked_at' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Perform PEP check
     */
    protected function performPepCheck(): array
    {
        $user = $this->user;
        $riskScore = rand(0, 40) / 100;
        
        return [
            'result' => $riskScore > 0.3 ? 'match' : 'clear',
            'risk_score' => $riskScore,
            'matches' => $riskScore > 0.3 ? [
                [
                    'name' => $user->name,
                    'confidence' => $riskScore,
                    'position' => 'Government Official',
                    'country' => 'Unknown',
                ]
            ] : [],
            'requires_manual_review' => $riskScore > 0.2,
            'response_data' => [
                'sources_checked' => ['Government Records', 'Public Databases'],
                'checked_at' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Perform adverse media check
     */
    protected function performAdverseMediaCheck(): array
    {
        $user = $this->user;
        $riskScore = rand(0, 60) / 100;
        
        return [
            'result' => $riskScore > 0.5 ? 'match' : 'clear',
            'risk_score' => $riskScore,
            'matches' => $riskScore > 0.5 ? [
                [
                    'name' => $user->name,
                    'confidence' => $riskScore,
                    'source' => 'News Article',
                    'headline' => 'Financial Investigation',
                    'date' => now()->subDays(rand(1, 365))->toDateString(),
                ]
            ] : [],
            'requires_manual_review' => $riskScore > 0.4,
            'response_data' => [
                'sources_searched' => ['News', 'Legal Databases', 'Court Records'],
                'checked_at' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Mark as reviewed
     */
    public function markAsReviewed(int $reviewerId, string $notes = ''): void
    {
        $this->update([
            'reviewed_by' => $reviewerId,
            'reviewed_at' => now(),
            'notes' => $notes,
            'requires_manual_review' => false,
        ]);
    }

    /**
     * Get compliance statistics
     */
    public static function getStatistics(): array
    {
        return [
            'total_checks' => self::count(),
            'pending' => self::pending()->count(),
            'completed' => self::completed()->count(),
            'failed' => self::failed()->count(),
            'requiring_review' => self::requireingReview()->count(),
            'by_type' => self::selectRaw('check_type, COUNT(*) as count')
                ->groupBy('check_type')
                ->pluck('count', 'check_type')
                ->toArray(),
            'by_result' => self::completed()
                ->selectRaw('result, COUNT(*) as count')
                ->groupBy('result')
                ->pluck('count', 'result')
                ->toArray(),
            'avg_risk_score' => self::completed()->avg('risk_score'),
            'high_risk_count' => self::highRisk()->count(),
        ];
    }

    /**
     * Run comprehensive compliance check for user
     */
    public static function runComprehensiveCheck(int $userId, ?int $transactionId = null): array
    {
        $checks = [
            self::createAmlCheck($userId, $transactionId),
            self::createSanctionsCheck($userId, $transactionId),
            self::createPepCheck($userId, $transactionId),
            self::createAdverseMediaCheck($userId, $transactionId),
        ];

        $results = [];
        foreach ($checks as $check) {
            $results[] = $check->process();
        }

        return $results;
    }
}
