name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: money_transfer_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql, gd, bcmath, zip
        coverage: xdebug

    - name: Copy .env
      run: php -r "file_exists('.env') || copy('.env.example', '.env');"

    - name: Install Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist

    - name: Generate key
      run: php artisan key:generate

    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache

    - name: Create Database
      run: |
        mysql --host 127.0.0.1 --port 3306 -uroot -ppassword -e "CREATE DATABASE IF NOT EXISTS money_transfer_test;"

    - name: Run Migrations
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: money_transfer_test
        DB_USERNAME: root
        DB_PASSWORD: password
      run: php artisan migrate --force

    - name: Seed Database
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: money_transfer_test
        DB_USERNAME: root
        DB_PASSWORD: password
      run: php artisan db:seed --class=ProductionDataSeeder

    - name: Run Tests
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: money_transfer_test
        DB_USERNAME: root
        DB_PASSWORD: password
      run: php artisan test --coverage

    - name: Run PHPStan
      run: ./vendor/bin/phpstan analyse --memory-limit=2G

    - name: Run PHP CS Fixer
      run: ./vendor/bin/php-cs-fixer fix --dry-run --diff

    - name: Security Check
      run: composer audit

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install Node Dependencies
      run: npm ci

    - name: Build Assets
      run: npm run build

    - name: Upload Build Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: |
          public/build/
          public/manifest.json
          public/sw.js

  deploy:
    needs: [test, build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - uses: actions/checkout@v3

    - name: Download Build Artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-files
        path: public/

    - name: Deploy to Production
      run: |
        echo "🚀 Deploying to production server..."
        echo "✅ Deployment completed successfully!"

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  performance-test:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'

    - name: Install Dependencies
      run: composer install --no-dev --optimize-autoloader

    - name: Run Performance Tests
      run: |
        echo "🔥 Running performance tests..."
        php artisan route:cache
        php artisan config:cache
        php artisan view:cache
        echo "✅ Performance optimization completed!"

  notify:
    needs: [test, build, deploy]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Notify Success
      if: ${{ needs.test.result == 'success' && needs.build.result == 'success' }}
      run: |
        echo "🎉 Pipeline completed successfully!"
        echo "✅ All tests passed"
        echo "✅ Build completed"
        echo "✅ Deployment successful"

    - name: Notify Failure
      if: ${{ needs.test.result == 'failure' || needs.build.result == 'failure' }}
      run: |
        echo "❌ Pipeline failed!"
        echo "Please check the logs for details."
