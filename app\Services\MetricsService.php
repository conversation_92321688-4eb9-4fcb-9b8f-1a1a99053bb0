<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use App\Models\Transaction;
use App\Models\User;
use App\Models\FraudDetection;
use App\Models\AuditLog;

class MetricsService
{
    protected array $metrics = [];

    /**
     * Collect all system metrics.
     */
    public function collectAllMetrics(): array
    {
        return [
            'timestamp' => now()->toISOString(),
            'system' => $this->collectSystemMetrics(),
            'application' => $this->collectApplicationMetrics(),
            'database' => $this->collectDatabaseMetrics(),
            'financial' => $this->collectFinancialMetrics(),
            'security' => $this->collectSecurityMetrics(),
            'performance' => $this->collectPerformanceMetrics(),
        ];
    }

    /**
     * Collect system-level metrics.
     */
    public function collectSystemMetrics(): array
    {
        return [
            'memory' => [
                'current_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
                'peak_usage_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'limit' => ini_get('memory_limit'),
                'usage_percentage' => $this->getMemoryUsagePercentage(),
            ],
            'cpu' => [
                'load_average' => $this->getLoadAverage(),
                'usage_percentage' => $this->getCpuUsage(),
            ],
            'disk' => [
                'free_space_gb' => round(disk_free_space(storage_path()) / 1024 / 1024 / 1024, 2),
                'total_space_gb' => round(disk_total_space(storage_path()) / 1024 / 1024 / 1024, 2),
                'usage_percentage' => $this->getDiskUsagePercentage(),
            ],
            'network' => [
                'active_connections' => $this->getActiveConnections(),
                'bandwidth_usage' => $this->getBandwidthUsage(),
            ],
        ];
    }

    /**
     * Collect application-level metrics.
     */
    public function collectApplicationMetrics(): array
    {
        return [
            'requests' => [
                'total_today' => $this->getRequestCountToday(),
                'per_minute' => $this->getRequestsPerMinute(),
                'average_response_time_ms' => $this->getAverageResponseTime(),
            ],
            'errors' => [
                'total_today' => $this->getErrorCountToday(),
                'error_rate_percentage' => $this->getErrorRate(),
                'critical_errors' => $this->getCriticalErrorCount(),
            ],
            'cache' => [
                'hit_ratio_percentage' => $this->getCacheHitRatio(),
                'memory_usage_mb' => $this->getCacheMemoryUsage(),
                'operations_per_second' => $this->getCacheOperationsPerSecond(),
            ],
            'sessions' => [
                'active_sessions' => $this->getActiveSessionCount(),
                'average_session_duration_minutes' => $this->getAverageSessionDuration(),
            ],
        ];
    }

    /**
     * Collect database metrics.
     */
    public function collectDatabaseMetrics(): array
    {
        return [
            'connections' => [
                'active' => $this->getDatabaseConnections(),
                'max' => $this->getMaxDatabaseConnections(),
                'usage_percentage' => $this->getDatabaseConnectionUsage(),
            ],
            'queries' => [
                'total_today' => $this->getQueryCountToday(),
                'slow_queries' => $this->getSlowQueryCount(),
                'average_query_time_ms' => $this->getAverageQueryTime(),
            ],
            'performance' => [
                'deadlocks' => $this->getDeadlockCount(),
                'lock_waits' => $this->getLockWaitCount(),
                'table_scans' => $this->getTableScanCount(),
            ],
            'storage' => [
                'database_size_mb' => $this->getDatabaseSize(),
                'index_usage_percentage' => $this->getIndexUsage(),
                'fragmentation_percentage' => $this->getFragmentation(),
            ],
        ];
    }

    /**
     * Collect financial system metrics.
     */
    public function collectFinancialMetrics(): array
    {
        return [
            'transactions' => [
                'total_today' => Transaction::whereDate('created_at', today())->count(),
                'completed_today' => Transaction::whereDate('created_at', today())
                    ->where('status', 'completed')->count(),
                'failed_today' => Transaction::whereDate('created_at', today())
                    ->where('status', 'failed')->count(),
                'pending' => Transaction::where('status', 'pending')->count(),
                'volume_today_usd' => $this->getTransactionVolumeToday(),
                'average_amount_usd' => $this->getAverageTransactionAmount(),
                'success_rate_percentage' => $this->getTransactionSuccessRate(),
            ],
            'fraud_detection' => [
                'alerts_today' => FraudDetection::whereDate('detected_at', today())->count(),
                'high_risk_alerts' => FraudDetection::whereDate('detected_at', today())
                    ->where('risk_level', 'high')->count(),
                'blocked_transactions' => Transaction::whereDate('created_at', today())
                    ->where('status', 'blocked')->count(),
                'false_positive_rate' => $this->getFalsePositiveRate(),
                'detection_accuracy' => $this->getFraudDetectionAccuracy(),
            ],
            'users' => [
                'new_registrations_today' => User::whereDate('created_at', today())->count(),
                'active_users_today' => $this->getActiveUsersToday(),
                'kyc_pending' => User::whereNull('kyc_verified_at')->count(),
                'high_risk_users' => User::where('risk_level', 'high')->count(),
            ],
            'wallets' => [
                'total_balance_usd' => $this->getTotalWalletBalance(),
                'active_wallets' => $this->getActiveWalletCount(),
                'low_balance_alerts' => $this->getLowBalanceAlerts(),
            ],
        ];
    }

    /**
     * Collect security metrics.
     */
    public function collectSecurityMetrics(): array
    {
        return [
            'authentication' => [
                'failed_login_attempts_today' => $this->getFailedLoginAttempts(),
                'successful_logins_today' => $this->getSuccessfulLogins(),
                'blocked_ips' => $this->getBlockedIpCount(),
                'suspicious_activities' => $this->getSuspiciousActivityCount(),
            ],
            'access_control' => [
                'admin_logins_today' => $this->getAdminLoginCount(),
                'privilege_escalation_attempts' => $this->getPrivilegeEscalationAttempts(),
                'unauthorized_access_attempts' => $this->getUnauthorizedAccessAttempts(),
            ],
            'data_protection' => [
                'encryption_status' => $this->getEncryptionStatus(),
                'data_breach_incidents' => $this->getDataBreachIncidents(),
                'compliance_violations' => $this->getComplianceViolations(),
            ],
        ];
    }

    /**
     * Collect performance metrics.
     */
    public function collectPerformanceMetrics(): array
    {
        return [
            'response_times' => [
                'api_average_ms' => $this->getApiAverageResponseTime(),
                'web_average_ms' => $this->getWebAverageResponseTime(),
                'p95_response_time_ms' => $this->getP95ResponseTime(),
                'p99_response_time_ms' => $this->getP99ResponseTime(),
            ],
            'throughput' => [
                'requests_per_second' => $this->getRequestsPerSecond(),
                'transactions_per_second' => $this->getTransactionsPerSecond(),
                'concurrent_users' => $this->getConcurrentUsers(),
            ],
            'queue' => [
                'jobs_pending' => $this->getQueueSize(),
                'jobs_failed_today' => $this->getFailedJobsToday(),
                'average_job_processing_time_ms' => $this->getAverageJobProcessingTime(),
                'queue_wait_time_ms' => $this->getQueueWaitTime(),
            ],
        ];
    }

    /**
     * Store metrics in time series format.
     */
    public function storeMetrics(array $metrics): void
    {
        $timestamp = now();
        $key = "metrics:{$timestamp->format('Y-m-d:H:i')}";
        
        Cache::put($key, $metrics, now()->addDays(30));
        
        // Also store in Redis for real-time access
        if (config('database.redis.default')) {
            Redis::setex($key, 2592000, json_encode($metrics)); // 30 days
        }
    }

    /**
     * Get metrics for a specific time range.
     */
    public function getMetricsForRange(\DateTime $start, \DateTime $end): array
    {
        $metrics = [];
        $current = clone $start;
        
        while ($current <= $end) {
            $key = "metrics:{$current->format('Y-m-d:H:i')}";
            $data = Cache::get($key);
            
            if ($data) {
                $metrics[$current->format('Y-m-d H:i')] = $data;
            }
            
            $current->modify('+1 minute');
        }
        
        return $metrics;
    }

    /**
     * Get aggregated metrics for dashboard.
     */
    public function getDashboardMetrics(): array
    {
        return [
            'overview' => [
                'total_users' => User::count(),
                'active_users_today' => $this->getActiveUsersToday(),
                'transactions_today' => Transaction::whereDate('created_at', today())->count(),
                'revenue_today_usd' => $this->getRevenueToday(),
                'system_health' => $this->getSystemHealthScore(),
            ],
            'real_time' => [
                'current_load' => $this->getCurrentSystemLoad(),
                'active_sessions' => $this->getActiveSessionCount(),
                'queue_size' => $this->getQueueSize(),
                'error_rate' => $this->getErrorRate(),
            ],
            'alerts' => [
                'critical_alerts' => $this->getCriticalAlerts(),
                'warning_alerts' => $this->getWarningAlerts(),
                'fraud_alerts' => $this->getActiveFraudAlerts(),
            ],
        ];
    }

    // Helper methods for metric calculations
    private function getMemoryUsagePercentage(): float
    {
        $limit = ini_get('memory_limit');
        if ($limit === '-1') return 0;
        
        $limitBytes = $this->convertToBytes($limit);
        $currentBytes = memory_get_usage(true);
        
        return round(($currentBytes / $limitBytes) * 100, 2);
    }

    private function convertToBytes(string $value): int
    {
        $unit = strtolower(substr($value, -1));
        $number = (int) substr($value, 0, -1);
        
        switch ($unit) {
            case 'g': return $number * 1024 * 1024 * 1024;
            case 'm': return $number * 1024 * 1024;
            case 'k': return $number * 1024;
            default: return (int) $value;
        }
    }

    private function getLoadAverage(): array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => round($load[0], 2),
                '5min' => round($load[1], 2),
                '15min' => round($load[2], 2),
            ];
        }
        
        return ['1min' => 0, '5min' => 0, '15min' => 0];
    }

    private function getCpuUsage(): float
    {
        // This is a simplified CPU usage calculation
        // In production, you might want to use more sophisticated methods
        $load = sys_getloadavg();
        return round($load[0] * 100 / 4, 2); // Assuming 4 cores
    }

    private function getDiskUsagePercentage(): float
    {
        $free = disk_free_space(storage_path());
        $total = disk_total_space(storage_path());
        
        return round((($total - $free) / $total) * 100, 2);
    }

    private function getActiveConnections(): int
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            return (int) ($result[0]->Value ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getBandwidthUsage(): array
    {
        // Mock implementation - in production, integrate with network monitoring tools
        return [
            'incoming_mbps' => rand(10, 100),
            'outgoing_mbps' => rand(5, 50),
        ];
    }

    private function getRequestCountToday(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('event_type', 'request')
            ->count();
    }

    private function getRequestsPerMinute(): float
    {
        $count = AuditLog::where('created_at', '>=', now()->subMinute())
            ->where('event_type', 'request')
            ->count();
        
        return round($count, 2);
    }

    private function getAverageResponseTime(): float
    {
        $avg = AuditLog::whereDate('created_at', today())
            ->where('event_type', 'request')
            ->avg('duration_ms');
        
        return round($avg ?? 0, 2);
    }

    private function getErrorCountToday(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('status_code', '>=', 400)
            ->count();
    }

    private function getErrorRate(): float
    {
        $total = $this->getRequestCountToday();
        $errors = $this->getErrorCountToday();
        
        return $total > 0 ? round(($errors / $total) * 100, 2) : 0;
    }

    private function getCriticalErrorCount(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('status_code', '>=', 500)
            ->count();
    }

    private function getCacheHitRatio(): float
    {
        // Mock implementation - integrate with actual cache statistics
        return rand(80, 95) + (rand(0, 99) / 100);
    }

    private function getCacheMemoryUsage(): float
    {
        // Mock implementation
        return rand(50, 200) + (rand(0, 99) / 100);
    }

    private function getCacheOperationsPerSecond(): float
    {
        // Mock implementation
        return rand(100, 1000) + (rand(0, 99) / 100);
    }

    private function getActiveSessionCount(): int
    {
        try {
            return DB::table('sessions')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getAverageSessionDuration(): float
    {
        // Mock implementation
        return rand(15, 45) + (rand(0, 99) / 100);
    }

    private function getDatabaseConnections(): int
    {
        return $this->getActiveConnections();
    }

    private function getMaxDatabaseConnections(): int
    {
        try {
            $result = DB::select("SHOW VARIABLES LIKE 'max_connections'");
            return (int) ($result[0]->Value ?? 100);
        } catch (\Exception $e) {
            return 100;
        }
    }

    private function getDatabaseConnectionUsage(): float
    {
        $active = $this->getDatabaseConnections();
        $max = $this->getMaxDatabaseConnections();
        
        return $max > 0 ? round(($active / $max) * 100, 2) : 0;
    }

    private function getQueryCountToday(): int
    {
        // This would require query logging to be enabled
        return rand(10000, 50000);
    }

    private function getSlowQueryCount(): int
    {
        // This would require slow query log analysis
        return rand(0, 100);
    }

    private function getAverageQueryTime(): float
    {
        // Mock implementation
        return rand(1, 10) + (rand(0, 99) / 100);
    }

    private function getDeadlockCount(): int
    {
        // Mock implementation
        return rand(0, 5);
    }

    private function getLockWaitCount(): int
    {
        // Mock implementation
        return rand(0, 20);
    }

    private function getTableScanCount(): int
    {
        // Mock implementation
        return rand(100, 1000);
    }

    private function getDatabaseSize(): float
    {
        try {
            $result = DB::select("
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
            ");
            
            return (float) ($result[0]->size_mb ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getIndexUsage(): float
    {
        // Mock implementation
        return rand(85, 98) + (rand(0, 99) / 100);
    }

    private function getFragmentation(): float
    {
        // Mock implementation
        return rand(1, 15) + (rand(0, 99) / 100);
    }

    private function getTransactionVolumeToday(): float
    {
        return Transaction::whereDate('created_at', today())
            ->where('status', 'completed')
            ->sum('amount') ?? 0;
    }

    private function getAverageTransactionAmount(): float
    {
        return Transaction::whereDate('created_at', today())
            ->where('status', 'completed')
            ->avg('amount') ?? 0;
    }

    private function getTransactionSuccessRate(): float
    {
        $total = Transaction::whereDate('created_at', today())->count();
        $completed = Transaction::whereDate('created_at', today())
            ->where('status', 'completed')->count();
        
        return $total > 0 ? round(($completed / $total) * 100, 2) : 0;
    }

    private function getFalsePositiveRate(): float
    {
        // Mock implementation - would require manual review data
        return rand(5, 15) + (rand(0, 99) / 100);
    }

    private function getFraudDetectionAccuracy(): float
    {
        // Mock implementation
        return rand(90, 98) + (rand(0, 99) / 100);
    }

    private function getActiveUsersToday(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('event_type', 'authentication')
            ->where('action', 'login')
            ->distinct('user_id')
            ->count('user_id');
    }

    private function getTotalWalletBalance(): float
    {
        return \App\Models\Wallet::sum('balance') ?? 0;
    }

    private function getActiveWalletCount(): int
    {
        return \App\Models\Wallet::where('is_active', true)->count();
    }

    private function getLowBalanceAlerts(): int
    {
        return \App\Models\Wallet::where('balance', '<', 100)->count();
    }

    private function getFailedLoginAttempts(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('event_type', 'authentication')
            ->where('action', 'login')
            ->where('status_code', '>=', 400)
            ->count();
    }

    private function getSuccessfulLogins(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('event_type', 'authentication')
            ->where('action', 'login')
            ->where('status_code', '<', 400)
            ->count();
    }

    private function getBlockedIpCount(): int
    {
        // Mock implementation
        return rand(10, 50);
    }

    private function getSuspiciousActivityCount(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('event_type', 'security')
            ->count();
    }

    private function getAdminLoginCount(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('event_type', 'admin_operation')
            ->where('action', 'login')
            ->count();
    }

    private function getPrivilegeEscalationAttempts(): int
    {
        // Mock implementation
        return rand(0, 5);
    }

    private function getUnauthorizedAccessAttempts(): int
    {
        return AuditLog::whereDate('created_at', today())
            ->where('status_code', 403)
            ->count();
    }

    private function getEncryptionStatus(): array
    {
        return [
            'data_at_rest' => true,
            'data_in_transit' => true,
            'key_rotation_status' => 'current',
        ];
    }

    private function getDataBreachIncidents(): int
    {
        // Mock implementation
        return 0;
    }

    private function getComplianceViolations(): int
    {
        // Mock implementation
        return rand(0, 2);
    }

    private function getApiAverageResponseTime(): float
    {
        return AuditLog::whereDate('created_at', today())
            ->where('route_name', 'like', 'api.%')
            ->avg('duration_ms') ?? 0;
    }

    private function getWebAverageResponseTime(): float
    {
        return AuditLog::whereDate('created_at', today())
            ->where('route_name', 'not like', 'api.%')
            ->avg('duration_ms') ?? 0;
    }

    private function getP95ResponseTime(): float
    {
        // Mock implementation - would require percentile calculation
        return rand(500, 1000) + (rand(0, 99) / 100);
    }

    private function getP99ResponseTime(): float
    {
        // Mock implementation
        return rand(1000, 2000) + (rand(0, 99) / 100);
    }

    private function getRequestsPerSecond(): float
    {
        $count = AuditLog::where('created_at', '>=', now()->subSecond())->count();
        return (float) $count;
    }

    private function getTransactionsPerSecond(): float
    {
        $count = Transaction::where('created_at', '>=', now()->subSecond())->count();
        return (float) $count;
    }

    private function getConcurrentUsers(): int
    {
        return $this->getActiveSessionCount();
    }

    private function getQueueSize(): int
    {
        try {
            return DB::table('jobs')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getFailedJobsToday(): int
    {
        try {
            return DB::table('failed_jobs')
                ->whereDate('failed_at', today())
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getAverageJobProcessingTime(): float
    {
        // Mock implementation
        return rand(100, 500) + (rand(0, 99) / 100);
    }

    private function getQueueWaitTime(): float
    {
        // Mock implementation
        return rand(50, 200) + (rand(0, 99) / 100);
    }

    private function getRevenueToday(): float
    {
        return Transaction::whereDate('created_at', today())
            ->where('status', 'completed')
            ->sum('total_fees') ?? 0;
    }

    private function getSystemHealthScore(): float
    {
        // Calculate overall system health based on various metrics
        $scores = [
            'memory' => $this->getMemoryUsagePercentage() < 80 ? 100 : 50,
            'cpu' => $this->getCpuUsage() < 70 ? 100 : 50,
            'disk' => $this->getDiskUsagePercentage() < 80 ? 100 : 50,
            'error_rate' => $this->getErrorRate() < 5 ? 100 : 50,
            'response_time' => $this->getAverageResponseTime() < 500 ? 100 : 50,
        ];
        
        return round(array_sum($scores) / count($scores), 1);
    }

    private function getCurrentSystemLoad(): array
    {
        return [
            'cpu_percentage' => $this->getCpuUsage(),
            'memory_percentage' => $this->getMemoryUsagePercentage(),
            'disk_percentage' => $this->getDiskUsagePercentage(),
        ];
    }

    private function getCriticalAlerts(): int
    {
        // Mock implementation
        return rand(0, 3);
    }

    private function getWarningAlerts(): int
    {
        // Mock implementation
        return rand(2, 10);
    }

    private function getActiveFraudAlerts(): int
    {
        return FraudDetection::where('status', 'open')
            ->where('risk_level', 'high')
            ->count();
    }
}
