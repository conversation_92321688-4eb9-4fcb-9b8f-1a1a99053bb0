# Load Balancer Configuration for Money Transfer System

# Upstream servers
upstream app_servers {
    # Load balancing method
    least_conn;
    
    # Application servers
    server app1:9000 weight=3 max_fails=3 fail_timeout=30s;
    server app2:9000 weight=3 max_fails=3 fail_timeout=30s;
    server app3:9000 weight=2 max_fails=3 fail_timeout=30s backup;
    
    # Health check
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# API servers for high-traffic endpoints
upstream api_servers {
    # Round robin for API
    server api1:9000 weight=1 max_fails=2 fail_timeout=20s;
    server api2:9000 weight=1 max_fails=2 fail_timeout=20s;
    server api3:9000 weight=1 max_fails=2 fail_timeout=20s;
    
    keepalive 64;
}

# WebSocket servers
upstream websocket_servers {
    ip_hash; # Sticky sessions for WebSocket
    server ws1:6001 weight=1 max_fails=2 fail_timeout=30s;
    server ws2:6001 weight=1 max_fails=2 fail_timeout=30s;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=transactions:10m rate=2r/s;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=perip:10m;
limit_conn_zone $server_name zone=perserver:10m;

server {
    listen 80;
    listen 443 ssl http2;
    server_name moneytransfer.com www.moneytransfer.com;
    
    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/moneytransfer.crt;
    ssl_certificate_key /etc/nginx/ssl/moneytransfer.key;
    
    # Connection limits
    limit_conn perip 10;
    limit_conn perserver 1000;
    
    # Security headers
    add_header X-Load-Balancer "nginx-lb" always;
    add_header X-Upstream-Server $upstream_addr always;
    add_header X-Response-Time $upstream_response_time always;
    
    # Main application
    location / {
        limit_req zone=general burst=20 nodelay;
        
        proxy_pass http://app_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffering
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        
        # Health check
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }
    
    # API endpoints with higher rate limits
    location /api/ {
        limit_req zone=api burst=50 nodelay;
        
        proxy_pass http://api_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API specific timeouts
        proxy_connect_timeout 3s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Disable buffering for real-time API responses
        proxy_buffering off;
        proxy_cache off;
        
        # Health check
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 5s;
    }
    
    # Authentication endpoints with strict rate limiting
    location ~ ^/(login|register|password|auth) {
        limit_req zone=auth burst=10 nodelay;
        
        proxy_pass http://app_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Longer timeouts for auth
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Transaction endpoints with very strict rate limiting
    location /api/transactions {
        limit_req zone=transactions burst=5 nodelay;
        
        proxy_pass http://api_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Extended timeouts for transactions
        proxy_connect_timeout 15s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # WebSocket connections
    location /ws {
        proxy_pass http://websocket_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific settings
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
        proxy_connect_timeout 5s;
    }
    
    # Static files with caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg|pdf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Try local files first, then proxy
        try_files $uri @proxy_static;
        
        access_log off;
    }
    
    # Proxy for static files if not found locally
    location @proxy_static {
        proxy_pass http://app_servers;
        proxy_set_header Host $host;
        proxy_cache static_cache;
        proxy_cache_valid 200 1d;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Load balancer status
    location /lb-status {
        access_log off;
        return 200 "load balancer active\n";
        add_header Content-Type text/plain;
        
        # Restrict access to monitoring systems
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
    }
    
    # Nginx status for monitoring
    location /nginx-status {
        stub_status on;
        access_log off;
        
        # Restrict access
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
    }
    
    # Error pages
    error_page 429 /429.html;
    error_page 502 503 504 /maintenance.html;
    
    # Custom error page for rate limiting
    location = /429.html {
        root /var/www/html/public/errors;
        internal;
    }
    
    # Maintenance page
    location = /maintenance.html {
        root /var/www/html/public/errors;
        internal;
    }
}

# Cache configuration
proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=1g inactive=60m use_temp_path=off;

# Logging format for load balancer
log_format lb_format '$remote_addr - $remote_user [$time_local] '
                     '"$request" $status $body_bytes_sent '
                     '"$http_referer" "$http_user_agent" '
                     'rt=$request_time uct="$upstream_connect_time" '
                     'uht="$upstream_header_time" urt="$upstream_response_time" '
                     'upstream="$upstream_addr" host="$host"';

# Apply logging format
access_log /var/log/nginx/load_balancer.log lb_format;
