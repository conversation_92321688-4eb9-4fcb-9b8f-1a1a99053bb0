<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Transaction;
use App\Models\Country;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\AuditLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $regularUser;
    protected Country $country;
    protected Currency $currency;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create country
        $this->country = Country::create([
            'name_ar' => 'المملكة العربية السعودية',
            'name_en' => 'Saudi Arabia',
            'code' => 'SAU',
            'iso2' => 'SA',
            'phone_code' => '+966',
            'currency_code' => 'SAR',
            'is_active' => true,
            'supports_transfers' => true,
        ]);

        // Create currency
        $this->currency = Currency::create([
            'code' => 'SAR',
            'name_ar' => 'ريال سعودي',
            'name_en' => 'Saudi Riyal',
            'symbol' => 'ر.س',
            'is_active' => true,
            'is_base_currency' => false,
            'rate_to_usd' => 0.27,
        ]);

        // Create admin user
        $this->admin = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);

        // Create regular user
        $this->regularUser = User::create([
            'first_name' => 'Regular',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+966501234568',
            'password' => bcrypt('password'),
            'user_type' => 'customer',
            'status' => 'active',
            'country_id' => $this->country->id,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);
    }

    public function test_admin_can_access_dashboard_overview()
    {
        // Create some test data
        Transaction::factory()->count(5)->create([
            'user_id' => $this->regularUser->id,
            'status' => 'completed',
        ]);

        Transaction::factory()->count(3)->create([
            'user_id' => $this->regularUser->id,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/overview');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'users' => [
                            'total',
                            'active',
                            'verified',
                            'new_today',
                            'new_this_week',
                            'new_this_month',
                        ],
                        'transactions' => [
                            'total',
                            'pending',
                            'completed',
                            'failed',
                            'cancelled',
                            'today',
                            'this_week',
                            'this_month',
                        ],
                        'revenue' => [
                            'total_fees',
                            'today_fees',
                            'this_week_fees',
                            'this_month_fees',
                            'average_transaction_value',
                            'average_fee',
                        ],
                        'system' => [
                            'countries',
                            'currencies',
                            'exchange_rates',
                            'audit_logs',
                            'disk_usage',
                            'memory_usage',
                        ],
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $data = $response->json('data');
        $this->assertEquals(8, $data['transactions']['total']);
        $this->assertEquals(5, $data['transactions']['completed']);
        $this->assertEquals(3, $data['transactions']['pending']);
    }

    public function test_admin_can_access_real_time_dashboard()
    {
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/real-time');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'active_users',
                        'pending_transactions',
                        'recent_activities',
                        'system_alerts',
                        'exchange_rates_status',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_admin_can_access_transaction_analytics()
    {
        // Create some transactions for analytics
        Transaction::factory()->count(10)->create([
            'user_id' => $this->regularUser->id,
            'created_at' => now()->subDays(5),
        ]);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/transaction-analytics?period=week&group_by=day');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'volume_chart',
                        'status_distribution',
                        'currency_breakdown',
                        'country_breakdown',
                        'payment_methods',
                        'risk_analysis',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_admin_can_access_user_analytics()
    {
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/user-analytics?period=month');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'registration_trend',
                        'user_activity',
                        'verification_status',
                        'user_segments',
                        'geographic_distribution',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_admin_can_access_financial_reports()
    {
        // Create completed transactions with fees
        Transaction::factory()->count(5)->create([
            'user_id' => $this->regularUser->id,
            'status' => 'completed',
            'amount' => 1000,
            'fee' => 50,
        ]);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/financial-reports?period=month');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'revenue_summary',
                        'fee_analysis',
                        'profit_margins',
                        'currency_exposure',
                        'top_corridors',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_admin_can_access_system_health()
    {
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/system-health');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'database' => [
                            'status',
                        ],
                        'cache' => [
                            'status',
                        ],
                        'queue' => [
                            'status',
                        ],
                        'storage' => [
                            'status',
                        ],
                        'api_performance',
                        'error_rates',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);
    }

    public function test_admin_can_access_audit_logs()
    {
        // Create some audit logs
        AuditLog::create([
            'user_id' => $this->regularUser->id,
            'event_type' => 'user_login',
            'description' => 'User logged in',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Browser',
            'url' => '/api/v1/auth/login',
            'method' => 'POST',
            'severity' => 'low',
        ]);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/audit-logs');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'logs',
                        'pagination' => [
                            'current_page',
                            'last_page',
                            'per_page',
                            'total',
                        ],
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);

        $this->assertNotEmpty($response->json('data.logs'));
    }

    public function test_admin_can_filter_audit_logs()
    {
        // Create audit logs with different event types
        AuditLog::create([
            'user_id' => $this->regularUser->id,
            'event_type' => 'user_login',
            'description' => 'User logged in',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Browser',
            'url' => '/api/v1/auth/login',
            'method' => 'POST',
            'severity' => 'low',
        ]);

        AuditLog::create([
            'user_id' => $this->regularUser->id,
            'event_type' => 'transaction_created',
            'description' => 'Transaction created',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Browser',
            'url' => '/api/v1/transactions',
            'method' => 'POST',
            'severity' => 'medium',
        ]);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/audit-logs?event_type=user_login');

        $response->assertStatus(200);
        
        $logs = $response->json('data.logs');
        $this->assertCount(1, $logs);
        $this->assertEquals('user_login', $logs[0]['event_type']);
    }

    public function test_regular_user_cannot_access_admin_dashboard()
    {
        $response = $this->actingAs($this->regularUser, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/overview');

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Admin access required',
                    'error_code' => 'INSUFFICIENT_PRIVILEGES',
                ]);
    }

    public function test_unauthenticated_user_cannot_access_admin_dashboard()
    {
        $response = $this->getJson('/api/v1/admin/dashboard/overview');

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Authentication required',
                    'error_code' => 'UNAUTHENTICATED',
                ]);
    }

    public function test_inactive_admin_cannot_access_dashboard()
    {
        $this->admin->update(['status' => 'inactive']);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/overview');

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Admin account is not active',
                    'error_code' => 'ACCOUNT_INACTIVE',
                ]);
    }

    public function test_admin_access_is_logged()
    {
        $initialLogCount = AuditLog::count();

        $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/overview');

        $this->assertEquals($initialLogCount + 1, AuditLog::count());

        $latestLog = AuditLog::latest()->first();
        $this->assertEquals('admin_access', $latestLog->event_type);
        $this->assertEquals($this->admin->id, $latestLog->user_id);
        $this->assertStringContains('admin/dashboard/overview', $latestLog->description);
    }

    public function test_dashboard_caching_works()
    {
        // First request should hit the database
        $response1 = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/overview');

        $response1->assertStatus(200);

        // Second request should use cache (we can't easily test this without mocking)
        $response2 = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/v1/admin/dashboard/overview');

        $response2->assertStatus(200);

        // Both responses should have the same data structure
        $this->assertEquals(
            $response1->json('data.users.total'),
            $response2->json('data.users.total')
        );
    }
}
